import { Response } from 'express';
export declare class StaticController {
    private readonly publicPath;
    getImage(type: string, subPath: string, filename: string, res: Response): Promise<void>;
    getNestedImage(type: string, subPath1: string, subPath2: string, filename: string, res: Response): Promise<void>;
    checkHealth(): {
        status: string;
        message: string;
        path: string;
        timestamp: string;
    };
}
