"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatDateYMDHM = exports.formatDatetime = exports.formatDate = exports.DateUtils = exports.DateFormatter = exports.safeParseDate = exports.APP_CONFIG = void 0;
const date_fns_1 = require("date-fns");
exports.APP_CONFIG = {
    TIMEZONE: process.env.TIMEZONE || 'Asia/Shanghai',
    LOCALE: process.env.LOCALE || 'zh-CN',
    DATE_FORMAT: process.env.DATE_FORMAT || 'yyyy-MM-dd',
    DATETIME_FORMAT: process.env.DATETIME_FORMAT || 'yyyy-MM-dd HH:mm:ss',
};
function safeParseDate(input) {
    if (!input)
        return null;
    try {
        let date;
        if (input instanceof Date) {
            date = input;
        }
        else if (typeof input === 'string') {
            if (input.includes('T') || input.includes('Z')) {
                date = (0, date_fns_1.parseISO)(input);
            }
            else {
                date = new Date(input);
            }
        }
        else if (typeof input === 'number') {
            date = new Date(input);
        }
        else {
            return null;
        }
        return (0, date_fns_1.isValid)(date) ? date : null;
    }
    catch (error) {
        console.warn('Date parsing error:', error, 'Input:', input);
        return null;
    }
}
exports.safeParseDate = safeParseDate;
exports.DateFormatter = {
    short: (input, fallback = 'Invalid Date') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return (0, date_fns_1.format)(date, exports.APP_CONFIG.DATE_FORMAT);
    },
    full: (input, fallback = 'Invalid Date') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return (0, date_fns_1.format)(date, exports.APP_CONFIG.DATETIME_FORMAT);
    },
    long: (input, fallback = 'Invalid Date') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return date.toLocaleDateString(exports.APP_CONFIG.LOCALE, {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    },
    relative: (input, fallback = 'Invalid Date') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return (0, date_fns_1.formatDistanceToNow)(date, { addSuffix: true });
    },
    time: (input, fallback = 'Invalid Time') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return (0, date_fns_1.format)(date, 'HH:mm:ss');
    },
    timeShort: (input, fallback = 'Invalid Time') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return (0, date_fns_1.format)(date, 'HH:mm');
    },
    withTimezone: (input, timezone, fallback = 'Invalid Date') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return new Intl.DateTimeFormat(exports.APP_CONFIG.LOCALE, {
            timeZone: timezone || exports.APP_CONFIG.TIMEZONE,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false,
        }).format(date);
    },
    iso: (input, fallback = '') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return date.toISOString();
    },
    custom: (input, formatString, fallback = 'Invalid Date') => {
        const date = safeParseDate(input);
        if (!date)
            return fallback;
        return (0, date_fns_1.format)(date, formatString);
    }
};
exports.DateUtils = {
    addDays: (date, days) => {
        const baseDate = safeParseDate(date) || new Date();
        return new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1000);
    },
    addHours: (date, hours) => {
        const baseDate = safeParseDate(date) || new Date();
        return new Date(baseDate.getTime() + hours * 60 * 60 * 1000);
    },
    addMinutes: (date, minutes) => {
        const baseDate = safeParseDate(date) || new Date();
        return new Date(baseDate.getTime() + minutes * 60 * 1000);
    },
    daysBetween: (date1, date2) => {
        const d1 = safeParseDate(date1);
        const d2 = safeParseDate(date2);
        if (!d1 || !d2)
            return 0;
        return Math.floor((d2.getTime() - d1.getTime()) / (1000 * 60 * 60 * 24));
    },
    isExpired: (date) => {
        const targetDate = safeParseDate(date);
        if (!targetDate)
            return false;
        return targetDate.getTime() < Date.now();
    }
};
function formatDate(input) {
    return exports.DateFormatter.long(input);
}
exports.formatDate = formatDate;
function formatDatetime(input) {
    return exports.DateFormatter.full(input);
}
exports.formatDatetime = formatDatetime;
function formatDateYMDHM(input) {
    return exports.DateFormatter.custom(input, 'yyyy/MM/dd HH:mm');
}
exports.formatDateYMDHM = formatDateYMDHM;
//# sourceMappingURL=date.utils.js.map