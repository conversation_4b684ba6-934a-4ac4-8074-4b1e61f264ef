"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LocalPackagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalPackagesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const utils_1 = require("../common/utils");
let LocalPackagesService = LocalPackagesService_1 = class LocalPackagesService {
    prisma;
    logger = new common_1.Logger(LocalPackagesService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getLocalPackages(query, ctx) {
        console.log('Context in getLocalPackages:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const whereConditions = {
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    {
                        category: {
                            name: {
                                in: ['Local Packages', '本地套餐', 'City Packages', '城市套餐', '地方套餐']
                            }
                        }
                    },
                    { name: { contains: '本地', mode: 'insensitive' } },
                    { name: { contains: 'local', mode: 'insensitive' } },
                    { name: { contains: '城市', mode: 'insensitive' } },
                    { name: { contains: 'city', mode: 'insensitive' } },
                    { name: { contains: '地区', mode: 'insensitive' } },
                    { name: { contains: 'regional', mode: 'insensitive' } },
                    { description: { contains: '本地', mode: 'insensitive' } },
                    { description: { contains: 'local', mode: 'insensitive' } },
                    { description: { contains: '城市', mode: 'insensitive' } },
                    { description: { contains: 'city', mode: 'insensitive' } },
                    { description: { contains: '地区专用', mode: 'insensitive' } },
                    { description: { contains: 'area exclusive', mode: 'insensitive' } },
                    {
                        AND: [
                            { country: { contains: 'china', mode: 'insensitive' } },
                            { countryCode: { equals: 'CN' } }
                        ]
                    },
                    {
                        OR: [
                            { name: { contains: '北京', mode: 'insensitive' } },
                            { name: { contains: 'beijing', mode: 'insensitive' } },
                            { name: { contains: '上海', mode: 'insensitive' } },
                            { name: { contains: 'shanghai', mode: 'insensitive' } },
                            { name: { contains: '广州', mode: 'insensitive' } },
                            { name: { contains: 'guangzhou', mode: 'insensitive' } },
                            { name: { contains: '深圳', mode: 'insensitive' } },
                            { name: { contains: 'shenzhen', mode: 'insensitive' } },
                        ]
                    }
                ],
            };
            if (query.city) {
                whereConditions.AND = whereConditions.AND || [];
                whereConditions.AND.push({
                    OR: [
                        { name: { contains: query.city, mode: 'insensitive' } },
                        { description: { contains: query.city, mode: 'insensitive' } },
                        { country: { contains: query.city, mode: 'insensitive' } },
                    ],
                });
            }
            if (query.province) {
                whereConditions.AND = whereConditions.AND || [];
                whereConditions.AND.push({
                    OR: [
                        { name: { contains: query.province, mode: 'insensitive' } },
                        { description: { contains: query.province, mode: 'insensitive' } },
                        { country: { contains: query.province, mode: 'insensitive' } },
                    ],
                });
            }
            if (query.planType) {
                whereConditions.planType = { equals: query.planType, mode: 'insensitive' };
            }
            const total = await this.prisma.product.count({
                where: whereConditions,
            });
            if (total === 0) {
                return {
                    packages: [],
                    pagination: {
                        total: 0,
                        page: query.page,
                        pageSize: query.pageSize,
                        hasMore: false,
                    },
                    filters: await this.getLocalPackageFilters(ctx),
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const skip = (query.page - 1) * query.pageSize;
            const products = await this.prisma.product.findMany({
                where: whereConditions,
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    variants: {
                        select: {
                            id: true,
                            price: true,
                            currency: true,
                        },
                        orderBy: {
                            price: 'asc',
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                        },
                    },
                },
                skip,
                take: query.pageSize,
                orderBy: this.buildOrderBy(query.sortBy, query.sortOrder),
            });
            const formattedPackages = products.map(product => this.formatProductAsLocalPackage(product, ctx, isZh));
            return {
                packages: formattedPackages,
                pagination: {
                    total,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: skip + formattedPackages.length < total,
                },
                filters: await this.getLocalPackageFilters(ctx),
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching local packages:', error);
            throw new Error('Failed to fetch local packages');
        }
    }
    async getPackageById(packageId, ctx) {
        console.log('Context in getPackageById:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const product = await this.prisma.product.findUnique({
                where: { id: packageId },
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    variants: {
                        select: {
                            id: true,
                            price: true,
                            currency: true,
                        },
                        orderBy: {
                            price: 'asc',
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                            comment: true,
                            createdAt: true,
                        },
                        orderBy: {
                            createdAt: 'desc',
                        },
                        take: 10,
                    },
                },
            });
            if (!product) {
                throw new Error('Local package not found');
            }
            const packageDetails = this.formatProductAsDetailedLocalPackage(product, ctx, isZh);
            return packageDetails;
        }
        catch (error) {
            this.logger.error('Error fetching local package details:', error);
            throw new Error('Failed to fetch local package details');
        }
    }
    async createLocalOrder(orderData, ctx) {
        console.log('Context in createLocalOrder:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const order = {
            id: `local_${Date.now()}`,
            packageId: orderData.packageId,
            city: orderData.city,
            province: orderData.province,
            status: 'pending',
            statusText: isZh ? '待支付' : 'Pending Payment',
            createdAt: utils_1.DateFormatter.iso(new Date()),
            estimatedActivationTime: utils_1.DateFormatter.iso(utils_1.DateUtils.addMinutes(new Date(), 5)),
        };
        return {
            order,
            message: isZh ? '本地套餐订单创建成功，请完成支付' : 'Local package order created successfully, please complete payment',
        };
    }
    formatProductAsLocalPackage(product, ctx, isZh) {
        const avgRating = product.reviews.length > 0
            ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
            : 0;
        const lowestPrice = product.variants.length > 0
            ? Math.min(...product.variants.map((v) => Number(v.price)))
            : Number(product.price);
        const currency = product.variants.length > 0
            ? product.variants[0].currency || ctx.currency
            : ctx.currency;
        let city = '';
        let province = '';
        let features = [];
        let validity = '1天';
        try {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            city = specs?.city || '';
            province = specs?.province || '';
            features = specs?.features || [];
            validity = specs?.validity || '1天';
        }
        catch (error) {
            this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
        }
        if (!city) {
            const cityNames = ['北京', 'beijing', '上海', 'shanghai', '广州', 'guangzhou', '深圳', 'shenzhen', '杭州', 'hangzhou', '南京', 'nanjing'];
            for (const cityName of cityNames) {
                if (product.name.toLowerCase().includes(cityName.toLowerCase()) ||
                    product.description.toLowerCase().includes(cityName.toLowerCase())) {
                    city = cityName.includes('北京') || cityName === 'beijing' ? 'beijing' :
                        cityName.includes('上海') || cityName === 'shanghai' ? 'shanghai' :
                            cityName.includes('广州') || cityName === 'guangzhou' ? 'guangzhou' :
                                cityName.includes('深圳') || cityName === 'shenzhen' ? 'shenzhen' :
                                    cityName.includes('杭州') || cityName === 'hangzhou' ? 'hangzhou' :
                                        cityName.includes('南京') || cityName === 'nanjing' ? 'nanjing' : 'unknown';
                    break;
                }
            }
        }
        if (!province) {
            if (city === 'beijing')
                province = 'beijing';
            else if (city === 'shanghai')
                province = 'shanghai';
            else if (city === 'guangzhou' || city === 'shenzhen')
                province = 'guangdong';
            else if (city === 'hangzhou')
                province = 'zhejiang';
            else if (city === 'nanjing')
                province = 'jiangsu';
        }
        if (features.length === 0) {
            features = [
                isZh ? `${this.getCityDisplayName(city, isZh)}地区专用` : `${this.getCityDisplayName(city, false)} area exclusive`,
                isZh ? '高速流量' : 'High-speed data',
                isZh ? '即时激活' : 'Instant activation',
                isZh ? '本地优化' : 'Locally optimized'
            ];
        }
        const planType = product.planType || this.determinePlanType(product.name, product.description);
        return {
            id: product.id,
            name: product.name,
            description: product.description,
            city: city,
            province: province,
            planType: planType,
            dataSize: product.dataSize ? this.formatDataSize(product.dataSize) : 'unlimited',
            price: lowestPrice,
            originalPrice: lowestPrice * 1.25,
            currency: currency,
            features: features,
            coverage: isZh ? `${this.getCityDisplayName(city, isZh)}全境` : `All of ${this.getCityDisplayName(city, false)}`,
            networkType: '4G/5G',
            validity: validity,
            imageUrl: product.images && product.images.length > 0
                ? product.images[0]
                : `/images/products/${city}-package.jpg`,
            isPopular: avgRating >= 4.5,
            rating: Math.round(avgRating * 10) / 10,
            reviewCount: product.reviews.length,
        };
    }
    formatProductAsDetailedLocalPackage(product, ctx, isZh) {
        const basicPackage = this.formatProductAsLocalPackage(product, ctx, isZh);
        return {
            ...basicPackage,
            detailedInfo: {
                activation: isZh ? '购买后立即激活' : 'Instant activation after purchase',
                coverage: isZh ? `${basicPackage.city}全覆盖` : `Full coverage in ${basicPackage.city}`,
                speed: isZh ? '5G网络下载速度最高1Gbps' : '5G network download speed up to 1Gbps',
                usage: isZh ? '适合短期出差、旅游使用' : 'Perfect for short business trips and tourism',
            },
            restrictions: [
                isZh ? `仅限${this.getCityDisplayName(basicPackage.city, isZh)}地区使用` : `Valid only in ${this.getCityDisplayName(basicPackage.city, false)} area`,
                isZh ? `${basicPackage.validity}后自动失效` : `Automatically expires after ${basicPackage.validity}`,
                isZh ? '不可转让或退款' : 'Non-transferable and non-refundable',
            ],
            variants: product.variants.map((variant) => ({
                id: variant.id,
                price: Number(variant.price),
                currency: variant.currency,
            })),
            reviews: product.reviews.map((review) => ({
                rating: review.rating,
                comment: review.comment,
                createdAt: review.createdAt,
            })),
        };
    }
    async getLocalPackageFilters(ctx) {
        const isZh = ctx.language.startsWith('zh');
        try {
            const planTypes = await this.prisma.product.findMany({
                where: {
                    status: 'ACTIVE',
                    off_shelve: false,
                    planType: { not: null },
                },
                select: { planType: true },
                distinct: ['planType'],
            });
            const formattedPlanTypes = planTypes
                .filter(p => p.planType)
                .map(p => ({
                value: p.planType,
                label: isZh
                    ? (p.planType === 'daily' ? '日套餐' : p.planType === 'weekly' ? '周套餐' : p.planType === 'monthly' ? '月套餐' : p.planType)
                    : (p.planType === 'daily' ? 'Daily' : p.planType === 'weekly' ? 'Weekly' : p.planType === 'monthly' ? 'Monthly' : p.planType),
            }));
            return {
                cities: [
                    { id: 'beijing', name: isZh ? '北京' : 'Beijing' },
                    { id: 'shanghai', name: isZh ? '上海' : 'Shanghai' },
                    { id: 'guangzhou', name: isZh ? '广州' : 'Guangzhou' },
                    { id: 'shenzhen', name: isZh ? '深圳' : 'Shenzhen' },
                    { id: 'hangzhou', name: isZh ? '杭州' : 'Hangzhou' },
                    { id: 'nanjing', name: isZh ? '南京' : 'Nanjing' },
                ],
                planTypes: formattedPlanTypes.length > 0 ? formattedPlanTypes : [
                    { value: 'daily', label: isZh ? '日套餐' : 'Daily' },
                    { value: 'weekly', label: isZh ? '周套餐' : 'Weekly' },
                    { value: 'monthly', label: isZh ? '月套餐' : 'Monthly' },
                ],
                dataSizes: [
                    { value: '1GB', label: '1GB' },
                    { value: '3GB', label: '3GB' },
                    { value: '5GB', label: '5GB' },
                    { value: '10GB', label: '10GB' },
                    { value: '20GB', label: '20GB' },
                    { value: 'unlimited', label: isZh ? '无限流量' : 'Unlimited' },
                ],
            };
        }
        catch (error) {
            this.logger.error('Error fetching local package filters:', error);
            return {
                cities: [
                    { id: 'beijing', name: isZh ? '北京' : 'Beijing' },
                    { id: 'shanghai', name: isZh ? '上海' : 'Shanghai' },
                    { id: 'guangzhou', name: isZh ? '广州' : 'Guangzhou' },
                    { id: 'shenzhen', name: isZh ? '深圳' : 'Shenzhen' },
                    { id: 'hangzhou', name: isZh ? '杭州' : 'Hangzhou' },
                    { id: 'nanjing', name: isZh ? '南京' : 'Nanjing' },
                ],
                planTypes: [
                    { value: 'daily', label: isZh ? '日套餐' : 'Daily' },
                    { value: 'weekly', label: isZh ? '周套餐' : 'Weekly' },
                    { value: 'monthly', label: isZh ? '月套餐' : 'Monthly' },
                ],
                dataSizes: [
                    { value: '1GB', label: '1GB' },
                    { value: '3GB', label: '3GB' },
                    { value: '5GB', label: '5GB' },
                    { value: '10GB', label: '10GB' },
                    { value: '20GB', label: '20GB' },
                    { value: 'unlimited', label: isZh ? '无限流量' : 'Unlimited' },
                ],
            };
        }
    }
    buildOrderBy(sortBy, sortOrder) {
        const order = sortOrder || 'asc';
        switch (sortBy) {
            case 'price':
                return { price: order };
            case 'rating':
                return { createdAt: order };
            case 'name':
                return { name: order };
            default:
                return { createdAt: 'desc' };
        }
    }
    getCityDisplayName(city, isZh) {
        const cityMap = {
            beijing: { zh: '北京', en: 'Beijing' },
            shanghai: { zh: '上海', en: 'Shanghai' },
            guangzhou: { zh: '广州', en: 'Guangzhou' },
            shenzhen: { zh: '深圳', en: 'Shenzhen' },
            hangzhou: { zh: '杭州', en: 'Hangzhou' },
            nanjing: { zh: '南京', en: 'Nanjing' },
        };
        return cityMap[city] ? (isZh ? cityMap[city].zh : cityMap[city].en) : city;
    }
    determinePlanType(name, description) {
        const text = (name + ' ' + description).toLowerCase();
        if (text.includes('日') || text.includes('daily'))
            return 'daily';
        if (text.includes('周') || text.includes('weekly'))
            return 'weekly';
        if (text.includes('月') || text.includes('monthly'))
            return 'monthly';
        return 'daily';
    }
    formatDataSize(dataSize) {
        if (dataSize >= 1024) {
            const sizeInGB = dataSize / 1024;
            return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
        }
        else {
            return `${dataSize}MB`;
        }
    }
};
LocalPackagesService = LocalPackagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], LocalPackagesService);
exports.LocalPackagesService = LocalPackagesService;
//# sourceMappingURL=local-packages.service.js.map