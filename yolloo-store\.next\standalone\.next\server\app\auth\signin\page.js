(()=>{var e={};e.id=8098,e.ids=[8098],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},55941:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(52092),s(35432),s(89090),s(26083),s(35866);var a=s(23191),r=s(88716),i=s(37922),n=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52092)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\signin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,35432)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\signin\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\signin\\page.tsx"],u="/auth/signin/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48738:(e,t,s)=>{Promise.resolve().then(s.bind(s,93418))},35303:()=>{},93418:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(10326),r=s(90434),i=s(17577),n=s(35047),l=s(77109),o=s(90772),c=s(54432),d=s(31048),u=s(57372),m=s(85999);function p({className:e,...t}){(0,n.useRouter)();let s=(0,n.useSearchParams)(),[p,x]=i.useState(!1),[h,g]=i.useState(!1),[f,j]=i.useState(!1),[v,w]=i.useState(!1),[y,b]=i.useState(""),[N,P]=i.useState(""),[S,C]=i.useState(!1),[k,_]=i.useState(0),q=s?.get("callbackUrl")||"/";async function E(e){e.preventDefault(),x(!0);let t=new FormData(e.currentTarget),s=t.get("email"),a=t.get("password");try{let e=await (0,l.signIn)("credentials",{email:s,password:a,redirect:!1,callbackUrl:q});if(e?.error){m.A.error("Invalid email or password");return}e?.url?window.location.href=e.url:q.startsWith("/")?window.location.href=q:window.location.href=`/${q}`,m.A.success("Signed in successfully")}catch(e){m.A.error("Something went wrong")}finally{x(!1)}}i.useEffect(()=>{let e;return k>0&&(e=setInterval(()=>{_(k-1)},1e3)),()=>clearInterval(e)},[k]);let A=async()=>{try{g(!0),await (0,l.signIn)("google",{callbackUrl:q,redirect:!0})}catch(e){m.A.error("Something went wrong with Google sign in"),g(!1)}},I=async()=>{if(!y){m.A.error("Please enter your email address");return}j(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:y})}),t=await e.json();t.success?(C(!0),_(60),m.A.success("Verification code sent to your email")):m.A.error(t.error||"Failed to send verification code")}catch(e){m.A.error("Something went wrong")}finally{j(!1)}},F=async()=>{if(!y||!N){m.A.error("Please enter both email and verification code");return}x(!0);try{let e=await (0,l.signIn)("email-code",{email:y,code:N,redirect:!1,callbackUrl:q});if(e?.error){m.A.error("Invalid verification code");return}e?.url?window.location.href=e.url:q&&q.startsWith("/")?window.location.href=q:window.location.href="/account",m.A.success("Signed in successfully")}catch(e){m.A.error("Something went wrong")}finally{x(!1)}};return(0,a.jsxs)("div",{className:"grid gap-6",...t,children:[v?(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[a.jsx(d._,{htmlFor:"code-email",children:"Email"}),a.jsx(c.I,{id:"code-email",placeholder:"<EMAIL>",type:"email",value:y,onChange:e=>b(e.target.value),disabled:p||S,required:!0})]}),S?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[a.jsx(d._,{htmlFor:"verification-code",children:"Verification Code"}),a.jsx(c.I,{id:"verification-code",placeholder:"Enter 6-digit code",type:"text",value:N,onChange:e=>P(e.target.value.replace(/\D/g,"").slice(0,6)),disabled:p,maxLength:6,required:!0})]}),(0,a.jsxs)(o.Button,{type:"button",disabled:p||6!==N.length,onClick:F,children:[p&&a.jsx(u.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Verify and Sign In"]}),(0,a.jsxs)(o.Button,{variant:"outline",type:"button",disabled:f||k>0,onClick:I,children:[f&&a.jsx(u.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),k>0?`Resend in ${k}s`:"Resend Code"]})]}):(0,a.jsxs)(o.Button,{type:"button",disabled:f||!y,onClick:I,children:[f&&a.jsx(u.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Send Verification Code"]}),a.jsx(o.Button,{variant:"ghost",type:"button",onClick:()=>{w(!1),C(!1),P(""),b(""),_(0)},children:"Back to Password Login"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("form",{onSubmit:E,children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[a.jsx(d._,{htmlFor:"email",children:"Email"}),a.jsx(c.I,{id:"email",name:"email",placeholder:"<EMAIL>",type:"email",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:p,required:!0})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx(d._,{htmlFor:"password",children:"Password"}),a.jsx(r.default,{href:"/auth/forgot-password",className:"text-xs text-muted-foreground hover:text-primary",children:"Forgot password?"})]}),a.jsx(c.I,{id:"password",name:"password",type:"password",autoCapitalize:"none",autoComplete:"current-password",autoCorrect:"off",disabled:p,required:!0})]}),(0,a.jsxs)(o.Button,{disabled:p,children:[p&&a.jsx(u.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Sign In with Password"]})]})}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("span",{className:"w-full border-t"})}),a.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:a.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Or"})})]}),(0,a.jsxs)(o.Button,{variant:"outline",type:"button",disabled:f,onClick:()=>w(!0),children:[f?a.jsx(u.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}):a.jsx(u.P.mail,{className:"mr-2 h-4 w-4"}),"Sign in with Email Code"]})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("span",{className:"w-full border-t"})}),a.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:a.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,a.jsxs)(o.Button,{variant:"outline",type:"button",disabled:h,onClick:A,children:[h?a.jsx(u.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}):a.jsx(u.P.google,{className:"mr-2 h-4 w-4"})," ","Sign in with Google"]})]})}let x=()=>{let{data:e,status:t}=(0,l.useSession)(),s=(0,n.useRouter)(),o=(0,n.useSearchParams)(),c=o?.get("callbackUrl"),d=c?`/auth/signup?callbackUrl=${encodeURIComponent(c)}`:"/auth/signup";return(0,i.useEffect)(()=>{if("authenticated"===t){let e=c||"/account";try{new URL(e),window.location.href=e}catch(t){e.startsWith("/")?s.replace(e):s.replace(`/${e}`)}}},[t,s,c]),(0,a.jsxs)("div",{className:"container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0",children:[(0,a.jsxs)("div",{className:"relative hidden h-full flex-col p-10 text-white lg:flex",children:[(0,a.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700",children:[a.jsx("div",{className:"absolute inset-0 bg-[url('/auth-bg-pattern.svg')] opacity-20"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30"})]}),a.jsx("div",{className:"relative z-20 flex items-center text-lg font-medium",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"rounded-lg bg-white/10 p-1.5 backdrop-blur-sm",children:a.jsx(u.P.zap,{className:"h-6 w-6"})}),(0,a.jsxs)("span",{className:"text-xl font-bold tracking-tight",children:["Yolloo",a.jsx("span",{className:"text-blue-200",children:"Store"})]})]})}),a.jsx("div",{className:"relative z-20 mt-auto",children:(0,a.jsxs)("blockquote",{className:"space-y-2",children:[a.jsx("p",{className:"text-lg",children:'"This store has transformed how I shop online. The experience is seamless and the products are amazing."'}),a.jsx("footer",{className:"text-sm",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("div",{className:"h-8 w-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center",children:a.jsx(u.P.user,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-semibold",children:"Sofia Davis"}),a.jsx("p",{className:"text-xs text-blue-200",children:"Happy Customer"})]})]})})]})})]}),a.jsx("div",{className:"lg:p-8",children:(0,a.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[a.jsx("h1",{className:"text-2xl font-semibold tracking-tight",children:"Welcome back"}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Sign in to your account to continue"})]}),a.jsx(p,{}),(0,a.jsxs)("p",{className:"px-8 text-center text-sm text-muted-foreground",children:["Don't have an account?"," ",a.jsx(r.default,{href:d,className:"underline underline-offset-4 hover:text-primary",children:"Sign up"})]})]})})]})}},31048:(e,t,s)=>{"use strict";s.d(t,{_:()=>c});var a=s(10326),r=s(17577),i=s(34478),n=s(79360),l=s(77863);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...t},s)=>a.jsx(i.f,{ref:s,className:(0,l.cn)(o(),e),...t}));c.displayName=i.f.displayName},35432:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r,metadata:()=>a});let a={title:"Sign In",description:"Sign in to your account"};function r({children:e}){return e}},52092:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var a=s(68570);let r=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\signin\page.tsx`),{__esModule:i,$$typeof:n}=r;r.default;let l=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\signin\page.tsx#default`)},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var a=s(17577),r=s(45226),i=s(10326),n=a.forwardRef((e,t)=>(0,i.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>s(55941));module.exports=a})();