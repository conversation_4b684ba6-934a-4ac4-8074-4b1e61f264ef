import { PrismaService } from '../../prisma.service';
export declare class WebPaymentsService {
    private prisma;
    constructor(prisma: PrismaService);
    createPayment(userId: string, createPaymentDto: any): Promise<{
        paymentId: string;
        message: string;
    }>;
    handleStripeWebhook(body: any, signature: string): Promise<{
        received: boolean;
        message: string;
    }>;
}
