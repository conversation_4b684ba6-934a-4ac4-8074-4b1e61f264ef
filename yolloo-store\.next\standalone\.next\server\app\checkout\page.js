(()=>{var e={};e.id=285,e.ids=[285],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},80399:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>o}),r(75113),r(89090),r(26083),r(35866);var t=r(23191),a=r(88716),i=r(37922),n=r.n(i),d=r(95231),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(s,l);let o=["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75113)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\checkout\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\checkout\\page.tsx"],m="/checkout/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},99196:(e,s,r)=>{Promise.resolve().then(r.bind(r,19441))},19441:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(10326),a=r(17577),i=r(35047),n=r(77109),d=r(90772),l=r(33071),o=r(77863),c=r(79492),m=r(88846),u=r(31048),p=r(54432),h=r(85999),x=r(57372);function f({addresses:e,items:s}){console.log("CheckoutForm rendered with addresses:",e),(0,i.useRouter)();let r=(0,a.useRef)(null),[n,o]=(0,a.useState)(!1),{items:f,clearCart:g}=(0,c.j)(),j=s??f,[v,y]=(0,a.useState)({}),[N,b]=(0,a.useState)(!0),w=j.every(e=>{let s=v[e.productId];return s&&["esim","data","effective_date","external_data","qr_code"].includes(s.category?.name?.toLowerCase()||"")}),[k,C]=(0,a.useState)(w?"digital-delivery":e&&e.length>0&&e[0]?.id||void 0),[P,S]=(0,a.useState)((!e||!e.length)&&!w);async function E(e){e.preventDefault(),console.log("Form submitted"),o(!0);try{console.log("Checking stock...");let e=await fetch("/api/stock/check",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({items:j.map(e=>({productId:e.productId,quantity:e.quantity}))})});if(!e.ok){let s=await e.json();throw Error(s.message||"Stock check failed")}let s=k;if(w){let e=await fetch("/api/addresses/default",{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to get digital delivery address");s=(await e.json()).id}else if(P&&r.current){console.log("Creating new address...");let e=new FormData(r.current),t={name:e.get("name"),phone:e.get("phone"),address1:e.get("address1"),address2:e.get("address2")||void 0,city:e.get("city"),state:e.get("state"),postalCode:e.get("postalCode"),country:e.get("country")};console.log("Address data:",t);let a=await fetch("/api/addresses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok)throw Error("Failed to create address");let i=await a.json();s=i.id,console.log("New address created:",i)}if(!s)throw Error("Please select an address");console.log("Creating order...");let t=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({addressId:s,items:j.map(e=>({productId:e.productId,quantity:e.quantity,price:e.price,variant:e.variant,variantId:e.variant?.id,uid:e.uid})),clearCart:!0})});if(!t.ok)throw Error("Failed to create order");let a=await t.json();console.log("Order created:",a),console.log("Creating payment...");let i=await fetch("/api/payments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:a.id})});if(!i.ok)throw Error("Failed to create payment");let{url:n}=await i.json();console.log("Payment session created, URL:",n),g(),console.log("Cart cleared"),console.log("Redirecting to payment page..."),window.location.href=n}catch(e){console.error("Error during checkout:",e),h.A.error(e instanceof Error?e.message:"Something went wrong")}finally{o(!1)}}return(0,t.jsxs)("form",{ref:r,onSubmit:E,className:"space-y-8",children:[N?t.jsx(l.Zb,{className:"p-6",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx(x.P.spinner,{className:"h-6 w-6 animate-spin"})})}):w?t.jsx(l.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-1",children:[t.jsx("h2",{className:"text-xl font-semibold tracking-tight",children:"Digital Delivery"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Your order contains only digital items that will be delivered electronically"})]})}):t.jsx(l.Zb,{className:"divide-y divide-border",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[t.jsx("h2",{className:"text-xl font-semibold tracking-tight",children:"Shipping Address"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Choose where you want your order delivered"})]}),e.length>0&&t.jsx(d.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>S(!P),children:P?"Use Saved Address":"Add New Address"})]}),e.length>0?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(m.E,{value:P?"new":"saved",onValueChange:e=>{console.log("Address type changed:",e),S("new"===e),"new"===e&&C(void 0)},className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[t.jsx("div",{className:"cursor-pointer h-full",children:t.jsx(u._,{htmlFor:"saved",className:"block h-full",children:(0,t.jsxs)("div",{className:`relative flex h-full rounded-lg border p-4 transition-all duration-200 ${P?"hover:border-primary/50 hover:shadow-sm":"border-primary bg-primary/5"}`,children:[t.jsx(m.m,{value:"saved",id:"saved",className:"absolute right-4 top-4"}),(0,t.jsxs)("div",{className:"flex flex-col justify-center min-h-[80px]",children:[t.jsx("span",{className:"text-base font-medium mb-2",children:"Saved Addresses"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Choose from your saved shipping addresses"})]})]})})}),t.jsx("div",{className:"cursor-pointer h-full",children:t.jsx(u._,{htmlFor:"new",className:"block h-full",children:(0,t.jsxs)("div",{className:`relative flex h-full rounded-lg border p-4 transition-all duration-200 ${P?"border-primary bg-primary/5":"hover:border-primary/50 hover:shadow-sm"}`,children:[t.jsx(m.m,{value:"new",id:"new",className:"absolute right-4 top-4"}),(0,t.jsxs)("div",{className:"flex flex-col justify-center min-h-[80px]",children:[t.jsx("span",{className:"text-base font-medium mb-2",children:"New Address"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Add a new shipping address"})]})]})})})]}),!P&&(0,t.jsxs)("div",{className:"rounded-lg border border-border/50 p-6 bg-background/50",children:[t.jsx("h3",{className:"text-base font-medium mb-4",children:"Select a Delivery Address"}),t.jsx(m.E,{value:k,onValueChange:e=>{console.log("Selected address:",e),C(e)},className:"grid gap-4",children:e.map(e=>t.jsx(u._,{htmlFor:e.id,className:"cursor-pointer",children:(0,t.jsxs)("div",{className:`relative flex items-start space-x-4 rounded-lg border p-4 transition-all duration-200 hover:border-primary/50 hover:shadow-sm ${k===e.id?"border-primary bg-primary/5":"border-border"}`,children:[t.jsx(m.m,{value:e.id,id:e.id,className:"mt-1"}),(0,t.jsxs)("div",{className:"grid gap-1.5 w-full",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-base font-medium",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Phone: ",e.phone]})]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsxs)("p",{className:"font-medium text-foreground/80",children:[e.address1,e.address2&&(0,t.jsxs)("span",{className:"text-muted-foreground",children:[", ",e.address2]})]}),(0,t.jsxs)("p",{children:[e.city,", ",e.state," ",e.postalCode]}),t.jsx("p",{children:e.country})]})]})]})},e.id))})]})]}):null,(P||0===e.length)&&(0,t.jsxs)("div",{className:"rounded-lg border border-border/50 p-6 bg-background/50",children:[t.jsx("h3",{className:"text-base font-medium mb-4",children:"Enter New Address"}),(0,t.jsxs)("div",{className:"grid gap-6",children:[(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"name",className:"text-sm font-medium",children:"Full Name"}),t.jsx(p.I,{id:"name",name:"name",required:!0,placeholder:"Enter your full name",className:"h-10"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"phone",className:"text-sm font-medium",children:"Phone Number"}),t.jsx(p.I,{id:"phone",name:"phone",type:"tel",required:!0,placeholder:"Enter your phone number",className:"h-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"address1",className:"text-sm font-medium",children:"Address Line 1"}),t.jsx(p.I,{id:"address1",name:"address1",required:!0,placeholder:"Street address or P.O. Box",className:"h-10"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"address2",className:"text-sm font-medium",children:"Address Line 2 (Optional)"}),t.jsx(p.I,{id:"address2",name:"address2",placeholder:"Apartment, suite, unit, etc.",className:"h-10"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"city",className:"text-sm font-medium",children:"City"}),t.jsx(p.I,{id:"city",name:"city",required:!0,className:"h-10"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"state",className:"text-sm font-medium",children:"State / Province"}),t.jsx(p.I,{id:"state",name:"state",required:!0,className:"h-10"})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"postalCode",className:"text-sm font-medium",children:"Postal Code"}),t.jsx(p.I,{id:"postalCode",name:"postalCode",required:!0,className:"h-10"})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[t.jsx(u._,{htmlFor:"country",className:"text-sm font-medium",children:"Country"}),t.jsx(p.I,{id:"country",name:"country",required:!0,className:"h-10"})]})]})]})]})]})}),t.jsx(d.Button,{type:"submit",className:"w-full",size:"lg",disabled:n||!P&&!k,children:t.jsx("div",{className:"flex items-center space-x-2",children:n?(0,t.jsxs)(t.Fragment,{children:[t.jsx(x.P.spinner,{className:"h-4 w-4 animate-spin"}),t.jsx("span",{children:"Processing..."})]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(x.P.creditCard,{className:"h-4 w-4"}),t.jsx("span",{children:"Continue to Payment"})]})})})]})}function g(){(0,i.useRouter)();let e=(0,i.useSearchParams)(),{data:s,status:r}=(0,n.useSession)(),{items:m}=(0,c.j)(),[u,p]=(0,a.useState)([]),[h,g]=(0,a.useState)(!0),[j,v]=(0,a.useState)(null),[y,N]=(0,a.useState)(!1),b="1"===e.get("buyNow");if(e.get("productId"),e.get("variantId"),e.get("quantity"),!b&&0===m.length)return null;if(b&&(!j||y))return(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[t.jsx(x.P.spinner,{className:"h-8 w-8 animate-spin"}),t.jsx("span",{className:"ml-4 text-lg",children:"Loading product..."})]});if("loading"===r)return console.log("正在加载认证状态"),null;if("unauthenticated"===r)return console.log("用户未认证"),null;let w=b?j||[]:m,k=w.reduce((e,s)=>e+s.price*s.quantity,0);return t.jsx("div",{className:"container py-8",children:(0,t.jsxs)("div",{className:"grid gap-8 lg:grid-cols-12",children:[t.jsx("div",{className:"lg:col-span-7",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-2xl font-bold",children:"Checkout"}),t.jsx("p",{className:"text-muted-foreground",children:"Complete your order by providing your shipping details."})]}),s?h?t.jsx(l.Zb,{className:"p-6",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"h-6 w-6 animate-spin rounded-full border-b-2 border-gray-900"})})}):t.jsx(f,{addresses:u,items:w}):t.jsx(l.Zb,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("h2",{className:"text-lg font-semibold",children:"Sign In Required"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Please sign in to continue with checkout."}),t.jsx(d.Button,{asChild:!0,children:t.jsx("a",{href:`/auth/signin?callbackUrl=${encodeURIComponent("/checkout")}`,children:"Sign In"})})]})})]})}),t.jsx("div",{className:"lg:col-span-5",children:(0,t.jsxs)(l.Zb,{className:"p-6",children:[t.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Order Summary"}),(0,t.jsxs)("div",{className:"space-y-4",children:[w.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[t.jsx("div",{className:"relative aspect-square h-16 w-16 min-w-fit overflow-hidden rounded-lg border",children:e.image?t.jsx("img",{src:e.image,alt:e.name,className:"h-full w-full object-cover"}):t.jsx("div",{className:"flex h-full items-center justify-center bg-secondary",children:t.jsx("span",{className:"text-muted-foreground",children:"No image"})})}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("p",{className:"line-clamp-1 font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.quantity," \xd7 ",(0,o.T4)(e.price)]}),e.variant&&t.jsx("div",{className:"mt-1 space-y-0.5",children:e.variant.duration&&e.variant.durationType&&(0,t.jsxs)("div",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs text-muted-foreground",children:[t.jsx("span",{className:"font-medium",children:"Duration:"}),(0,t.jsxs)("span",{className:"ml-1",children:[e.variant.duration," ",e.variant.durationType]})]})}),e.uid&&t.jsx("div",{className:"mt-1",children:(0,t.jsxs)("div",{className:"inline-flex flex-col sm:flex-row rounded-full border-primary/20 bg-primary/10 px-2.5 py-1 text-xs text-primary max-w-full",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(x.P.billing,{className:"mr-1 h-3 w-3 flex-shrink-0"}),t.jsx("span",{className:"font-medium",children:"UID:"})]}),t.jsx("div",{className:"mt-0.5 sm:mt-0 sm:ml-1 font-mono break-all",children:(0,o.QG)(e.uid)})]})})]}),t.jsx("p",{className:"font-medium",children:(0,o.T4)(e.price*e.quantity)})]},e.id)),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Subtotal"}),t.jsx("span",{className:"font-medium",children:(0,o.T4)(k)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-muted-foreground",children:"Shipping"}),t.jsx("span",{className:"font-medium",children:"Free"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[t.jsx("span",{className:"font-medium",children:"Total"}),t.jsx("span",{className:"font-bold",children:(0,o.T4)(k)})]})]})]})]})})]})})}},31048:(e,s,r)=>{"use strict";r.d(s,{_:()=>o});var t=r(10326),a=r(17577),i=r(34478),n=r(79360),d=r(77863);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef(({className:e,...s},r)=>t.jsx(i.f,{ref:r,className:(0,d.cn)(l(),e),...s}));o.displayName=i.f.displayName},88846:(e,s,r)=>{"use strict";r.d(s,{E:()=>l,m:()=>o});var t=r(10326),a=r(17577),i=r(18623),n=r(53982),d=r(77863);let l=a.forwardRef(({className:e,...s},r)=>t.jsx(i.fC,{className:(0,d.cn)("grid gap-2",e),...s,ref:r}));l.displayName=i.fC.displayName;let o=a.forwardRef(({className:e,...s},r)=>t.jsx(i.ck,{ref:r,className:(0,d.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:t.jsx(i.z$,{className:"flex items-center justify-center",children:t.jsx(n.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));o.displayName=i.ck.displayName},75113:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>d});var t=r(68570);let a=(0,t.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\checkout\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let d=(0,t.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\checkout\page.tsx#default`)},34478:(e,s,r)=>{"use strict";r.d(s,{f:()=>d});var t=r(17577),a=r(45226),i=r(10326),n=t.forwardRef((e,s)=>(0,i.jsx)(a.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var d=n},18623:(e,s,r)=>{"use strict";r.d(s,{ck:()=>L,fC:()=>O,z$:()=>G});var t=r(17577),a=r(82561),i=r(48051),n=r(93095),d=r(45226),l=r(15594),o=r(52067),c=r(17124),m=r(2566),u=r(53405),p=r(9815),h=r(10326),x="Radio",[f,g]=(0,n.b)(x),[j,v]=f(x),y=t.forwardRef((e,s)=>{let{__scopeRadio:r,name:n,checked:l=!1,required:o,disabled:c,value:m="on",onCheck:u,form:p,...x}=e,[f,g]=t.useState(null),v=(0,i.e)(s,e=>g(e)),y=t.useRef(!1),N=!f||p||!!f.closest("form");return(0,h.jsxs)(j,{scope:r,checked:l,disabled:c,children:[(0,h.jsx)(d.WV.button,{type:"button",role:"radio","aria-checked":l,"data-state":k(l),"data-disabled":c?"":void 0,disabled:c,value:m,...x,ref:v,onClick:(0,a.M)(e.onClick,e=>{l||u?.(),N&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),N&&(0,h.jsx)(w,{control:f,bubbles:!y.current,name:n,value:m,checked:l,required:o,disabled:c,form:p,style:{transform:"translateX(-100%)"}})]})});y.displayName=x;var N="RadioIndicator",b=t.forwardRef((e,s)=>{let{__scopeRadio:r,forceMount:t,...a}=e,i=v(N,r);return(0,h.jsx)(p.z,{present:t||i.checked,children:(0,h.jsx)(d.WV.span,{"data-state":k(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:s})})});b.displayName=N;var w=e=>{let{control:s,checked:r,bubbles:a=!0,...i}=e,n=t.useRef(null),d=(0,u.D)(r),l=(0,m.t)(s);return t.useEffect(()=>{let e=n.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==r&&s){let t=new Event("click",{bubbles:a});s.call(e,r),e.dispatchEvent(t)}},[d,r,a]),(0,h.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:n,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function k(e){return e?"checked":"unchecked"}var C=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],P="RadioGroup",[S,E]=(0,n.b)(P,[l.Pc,g]),q=(0,l.Pc)(),_=g(),[R,I]=S(P),F=t.forwardRef((e,s)=>{let{__scopeRadioGroup:r,name:t,defaultValue:a,value:i,required:n=!1,disabled:m=!1,orientation:u,dir:p,loop:x=!0,onValueChange:f,...g}=e,j=q(r),v=(0,c.gm)(p),[y,N]=(0,o.T)({prop:i,defaultProp:a,onChange:f});return(0,h.jsx)(R,{scope:r,name:t,required:n,disabled:m,value:y,onValueChange:N,children:(0,h.jsx)(l.fC,{asChild:!0,...j,orientation:u,dir:v,loop:x,children:(0,h.jsx)(d.WV.div,{role:"radiogroup","aria-required":n,"aria-orientation":u,"data-disabled":m?"":void 0,dir:v,...g,ref:s})})})});F.displayName=P;var A="RadioGroupItem",T=t.forwardRef((e,s)=>{let{__scopeRadioGroup:r,disabled:n,...d}=e,o=I(A,r),c=o.disabled||n,m=q(r),u=_(r),p=t.useRef(null),x=(0,i.e)(s,p),f=o.value===d.value,g=t.useRef(!1);return t.useEffect(()=>{let e=e=>{C.includes(e.key)&&(g.current=!0)},s=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",s),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",s)}},[]),(0,h.jsx)(l.ck,{asChild:!0,...m,focusable:!c,active:f,children:(0,h.jsx)(y,{disabled:c,required:o.required,checked:f,...u,...d,name:o.name,ref:x,onCheck:()=>o.onValueChange(d.value),onKeyDown:(0,a.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,a.M)(d.onFocus,()=>{g.current&&p.current?.click()})})})});T.displayName=A;var D=t.forwardRef((e,s)=>{let{__scopeRadioGroup:r,...t}=e,a=_(r);return(0,h.jsx)(b,{...a,...t,ref:s})});D.displayName="RadioGroupIndicator";var O=F,L=T,G=D},53405:(e,s,r)=>{"use strict";r.d(s,{D:()=>a});var t=r(17577);function a(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1615,5772,7624,5634,6621,4824,3442],()=>r(80399));module.exports=t})();