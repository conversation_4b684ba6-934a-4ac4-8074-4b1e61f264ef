import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER } from '@nestjs/core';
import { PrismaService } from './prisma.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { HomeModule } from './home/<USER>';
import { ProductsModule } from './products/products.module';
import { CartModule } from './cart/cart.module';
import { OrdersModule } from './orders/orders.module';
import { CardsModule } from './cards/cards.module';
import { HealthModule } from './health/health.module';
import { LocationModule } from './location/location.module';
import { PagesModule } from './pages/pages.module';
import { NumberRetentionModule } from './number-retention/number-retention.module';
import { MobileRechargeModule } from './mobile-recharge/mobile-recharge.module';
import { TravelPackagesModule } from './travel-packages/travel-packages.module';
import { LocalPackagesModule } from './local-packages/local-packages.module';
import { DataBoostersModule } from './data-boosters/data-boosters.module';
import { UserPackagesModule } from './user-packages/user-packages.module';
import { GeographyModule } from './geography/geography.module';
import { ArticlesModule } from './articles/articles.module';
import { UploadModule } from './upload/upload.module';
import { StaticModule } from './static/static.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { RedisModule } from './redis/redis.module';
import { ContextMiddleware } from './common/middleware/context.middleware';
import { StaticController } from './common/controllers/static.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      // 显式加载.env文件，确保环境变量正确加载
      envFilePath: ['.env.local', '.env'],
    }),
    RedisModule,
    HttpModule,
    AuthModule,
    UsersModule,
    HomeModule,
    ProductsModule,
    CartModule,
    OrdersModule,
    CardsModule,
    HealthModule,
    LocationModule,
    PagesModule,
    NumberRetentionModule,
    MobileRechargeModule,
    TravelPackagesModule,
    LocalPackagesModule,
    DataBoostersModule,
    UserPackagesModule,
    GeographyModule,
    ArticlesModule,
    UploadModule,
    StaticModule,
  ],
  controllers: [StaticController],
  providers: [
    PrismaService,
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
  ],
  exports: [PrismaService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply ContextMiddleware to all routes
    consumer.apply(ContextMiddleware).forRoutes('*');
  }
}
