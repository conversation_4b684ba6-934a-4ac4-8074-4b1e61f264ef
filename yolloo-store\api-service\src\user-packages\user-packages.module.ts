import { Module } from '@nestjs/common';
import { UserPackagesController } from './user-packages.controller';
import { UserPackagesService } from './user-packages.service';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [UserPackagesController],
  providers: [UserPackagesService, PrismaService],
  exports: [UserPackagesService],
})
export class UserPackagesModule {}
