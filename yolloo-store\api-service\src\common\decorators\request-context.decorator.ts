import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { RequestContext } from '../interfaces/context.interface';

/**
 * Decorator to extract the RequestContext from the request object.
 * This can be used in controllers to access the language, theme, and currency
 * preferences set by the client.
 * 
 * Usage:
 * ```
 * @Get('endpoint')
 * getEndpoint(@RequestCtx() ctx: RequestContext) {
 *   // Access ctx.language, ctx.theme, ctx.currency
 * }
 * ```
 */
export const RequestCtx = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): RequestContext => {
    const request = ctx.switchToHttp().getRequest();
    return request.context;
  },
);
