(()=>{var e={};e.id=4070,e.ids=[4070],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},44269:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>l}),r(94844),r(89090),r(26083),r(35866);var s=r(23191),a=r(88716),n=r(37922),o=r.n(n),i=r(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l=["",{children:["bind-card",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94844)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\bind-card\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\bind-card\\page.tsx"],p="/bind-card/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/bind-card/page",pathname:"/bind-card",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},41468:(e,t,r)=>{Promise.resolve().then(r.bind(r,36625))},36625:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(10326),a=r(17577),n=r(35047),o=r(77109),i=r(90772),d=r(85999);function l(){let e=(0,n.useSearchParams)(),t=(0,n.useRouter)(),{data:r,status:l}=(0,o.useSession)(),[c,p]=(0,a.useState)(!1),u=e.get("uid"),m=async()=>{if(u&&r?.user?.id)try{p(!0);let e=await fetch("/api/cards/bind",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({uid:u})});if(!e.ok){let t=await e.text();throw Error(t)}d.A.success("Card bound successfully"),t.push("/cards")}catch(e){d.A.error(e instanceof Error?e.message:"Failed to bind card")}finally{p(!1)}};return"loading"===l||"unauthenticated"===l?s.jsx("div",{className:"container py-10",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):s.jsx("div",{className:"container py-10",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Bind Yolloo Card"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("p",{className:"text-gray-600 mb-4",children:["You are about to bind the Yolloo card with ID: ",s.jsx("br",{}),s.jsx("span",{className:"font-mono bg-gray-100 px-2 py-1 rounded",children:u})]}),(0,s.jsxs)("div",{className:"flex justify-end",children:[s.jsx(i.Button,{onClick:()=>t.push("/cards"),variant:"outline",className:"mr-2",children:"Cancel"}),s.jsx(i.Button,{onClick:m,disabled:c,children:c?"Binding...":"Bind Card"})]})]})]})})}},94844:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>i});var s=r(68570);let a=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\bind-card\page.tsx`),{__esModule:n,$$typeof:o}=a;a.default;let i=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\bind-card\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(44269));module.exports=s})();