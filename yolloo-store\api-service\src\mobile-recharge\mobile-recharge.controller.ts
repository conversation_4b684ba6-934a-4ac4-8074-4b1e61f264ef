import { Controller, Get, Post, Body, Query, UseGuards } from '@nestjs/common';
import { MobileRechargeService } from './mobile-recharge.service';
import { RechargeQueryDto, RechargeOrderDto } from './dto/recharge-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('mobile-recharge')
export class MobileRechargeController {
  constructor(private readonly mobileRechargeService: MobileRechargeService) {}

  @Public()
  @Get('options')
  getRechargeOptions(
    @Query() query: RechargeQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.mobileRechargeService.getRechargeOptions(query, ctx);
  }

  @Public()
  @Post('order')
  createRechargeOrder(
    @Body() orderData: RechargeOrderDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.mobileRechargeService.createRechargeOrder(orderData, ctx);
  }

  @Public()
  @Get('history')
  getRechargeHistory(
    @CurrentUser('id') userId: string,
    @Query() query: RechargeQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.mobileRechargeService.getRechargeHistory(userId, query, ctx);
  }
}
