(()=>{var e={};e.id=7301,e.ids=[7301],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},763:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),s(62358),s(89090),s(26083),s(35866);var r=s(23191),o=s(88716),a=s(37922),n=s.n(a),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62358)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\about\\page.tsx"],m="/about/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},35303:()=>{},62358:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(19510);function o(){return r.jsx("div",{className:"py-16 bg-background",children:r.jsx("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h1",{className:"text-4xl font-bold heading-gradient mb-4",children:"About Yolloo"}),r.jsx("p",{className:"text-xl text-muted-foreground",children:"Connecting the world through innovative eSIM technology"})]}),(0,r.jsxs)("div",{className:"mb-16",children:[r.jsx("h2",{className:"text-3xl font-semibold text-gray-900 mb-6",children:"Our Story"}),(0,r.jsxs)("div",{className:"prose prose-lg max-w-none",children:[r.jsx("p",{className:"mb-4",children:"Founded in 2023, Yolloo emerged from a simple yet powerful vision: to make global connectivity seamless and accessible to everyone. Our founders, experienced in telecommunications and technology, recognized the challenges travelers and digital nomads face with traditional SIM cards."}),r.jsx("p",{className:"mb-4",children:"What started as a solution for international travelers has grown into a comprehensive eSIM platform serving customers worldwide. Today, Yolloo is at the forefront of the digital transformation in mobile connectivity."})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 mb-16",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Our Mission"}),r.jsx("p",{className:"text-gray-600",children:"To revolutionize global connectivity by providing seamless, affordable, and instant access to mobile networks worldwide through cutting-edge eSIM technology."})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Our Vision"}),r.jsx("p",{className:"text-gray-600",children:"A world where staying connected across borders is effortless and accessible to everyone, enabling people to explore, work, and connect without boundaries."})]})]}),(0,r.jsxs)("div",{className:"mb-16",children:[r.jsx("h2",{className:"text-3xl font-semibold text-gray-900 mb-8",children:"Our Core Values"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Innovation"}),r.jsx("p",{className:"text-gray-600",children:"We continuously push the boundaries of technology to provide the best connectivity solutions."})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Reliability"}),r.jsx("p",{className:"text-gray-600",children:"We ensure our services are dependable and our support is always available when needed."})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Customer First"}),r.jsx("p",{className:"text-gray-600",children:"We put our customers at the heart of everything we do, ensuring their needs drive our decisions."})]})]})]}),(0,r.jsxs)("div",{className:"mb-16",children:[r.jsx("h2",{className:"text-3xl font-semibold text-gray-900 mb-6",children:"Global Coverage"}),(0,r.jsxs)("div",{className:"prose prose-lg max-w-none",children:[r.jsx("p",{className:"mb-4",children:"Our eSIM service provides coverage in over 100 countries, partnering with leading mobile network operators to ensure reliable connectivity wherever you go. From bustling cities to remote locations, we've got you covered."}),(0,r.jsxs)("ul",{className:"grid md:grid-cols-2 gap-4 list-none pl-0 mt-6",children:[(0,r.jsxs)("li",{className:"flex items-center text-gray-600",children:[r.jsx("svg",{className:"w-5 h-5 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"100+ Countries Covered"]}),(0,r.jsxs)("li",{className:"flex items-center text-gray-600",children:[r.jsx("svg",{className:"w-5 h-5 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"24/7 Global Support"]}),(0,r.jsxs)("li",{className:"flex items-center text-gray-600",children:[r.jsx("svg",{className:"w-5 h-5 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"4G/5G Networks"]}),(0,r.jsxs)("li",{className:"flex items-center text-gray-600",children:[r.jsx("svg",{className:"w-5 h-5 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),"Instant Activation"]})]})]})]}),(0,r.jsxs)("div",{className:"text-center bg-card p-8 rounded-lg",children:[r.jsx("h2",{className:"text-3xl font-semibold heading-gradient mb-4",children:"Join the Future of Connectivity"}),r.jsx("p",{className:"text-muted-foreground mb-6",children:"Experience the freedom of borderless communication with Yolloo eSIM. Whether you're a frequent traveler, digital nomad, or business professional, we're here to keep you connected."}),r.jsx("a",{href:"/products",className:"button-gradient inline-block px-8 py-3 rounded-md transition-all duration-200",children:"Explore Our Plans"})]})]})})})}s(71159)},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(66621);let o=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>s(763));module.exports=r})();