"use strict";(()=>{var e={};e.id=6354,e.ids=[6354],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},50852:e=>{e.exports=require("async_hooks")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},41808:e=>{e.exports=require("net")},84492:e=>{e.exports=require("node:stream")},22037:e=>{e.exports=require("os")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},57183:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>C,patchFetch:()=>j,requestAsyncStorage:()=>w,routeModule:()=>_,serverHooks:()=>S,staticGenerationAsyncStorage:()=>A});var s={};r.r(s),r.d(s,{POST:()=>b,dynamic:()=>m,fetchCache:()=>h,revalidate:()=>f});var i=r(49303),n=r(88716),o=r(60670),a=r(87070),u=r(7410),l=r(72331),p=r(62197),c=r.n(p),d=r(60682);let m="force-dynamic",h="force-no-store",f=0,g=new(c())(process.env.REDIS_URL||"redis://localhost:6379"),y=process.env.CACHE_KEY||"yolloo-cache";async function x(e){let t=`${y}:ratelimit:${e}`;try{let e=await g.get(t),r=e?parseInt(e):0;if(r>=5)return!1;return 0===r?await g.setex(t,60,"1"):await g.incr(t),!0}catch(e){return console.error("Rate limit error:",e),!0}}let v=u.z.object({email:u.z.string().email("Invalid email address"),referralCode:u.z.string().optional()});async function b(e){try{let t=e.headers.get("x-forwarded-for")||"anonymous";if(!await x(t))return a.NextResponse.json({message:"Too many requests. Please try again later."},{status:429});let r=await e.json(),s=v.safeParse(r);if(!s.success)return a.NextResponse.json({message:"Invalid input data"},{status:400});let{email:i,referralCode:n}=s.data;if(await l._.preSaleSubscription.findUnique({where:{email:i}}))return a.NextResponse.json({message:"This email is already subscribed"},{status:400});let o=function(){let e=Math.random().toString(36).substring(2,8).toUpperCase();return`YOLLOO${e}`}();return await l._.preSaleSubscription.create({data:{email:i,referralCode:n,discountCode:o,status:"PENDING",ipAddress:t,userAgent:e.headers.get("user-agent")||void 0}}),await (0,d.xs)(i,o),a.NextResponse.json({message:"Subscription successful",discountCode:o},{status:201})}catch(e){return console.error("Subscription error:",e),a.NextResponse.json({message:"Internal server error"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/presale/subscribe/route",pathname:"/api/presale/subscribe",filename:"route",bundlePath:"app/api/presale/subscribe/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\presale\\subscribe\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:A,serverHooks:S}=_,C="/api/presale/subscribe/route";function j(){return(0,o.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:A})}},72331:(e,t,r)=>{r.d(t,{_:()=>i});var s=r(53524);let i=global.prisma||new s.PrismaClient({log:["error"]})},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,n={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),s=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?s:`${s}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[s,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(s,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[s,i],...n]=a(e),{domain:o,expires:u,httponly:c,maxage:d,path:m,samesite:h,secure:f,partitioned:g,priority:y}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:s,value:decodeURIComponent(i),domain:o,...u&&{expires:new Date(u)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:m,...h&&{sameSite:l.includes(t=(t=h).toLowerCase())?t:void 0},...f&&{secure:!0},...y&&{priority:p.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var s in r)t(e,s,{get:r[s],enumerable:!0})})(n,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>u,stringifyCookie:()=>o}),e.exports=((e,n,o,a)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let o of s(n))i.call(e,o)||void 0===o||t(e,o,{get:()=>n[o],enumerable:!(a=r(n,o))||a.enumerable});return e})(t({},"__esModule",{value:!0}),n);var l=["strict","lax","none"],p=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===s).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,s=this._parsed;return s.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(s).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,s;this._parsed=new Map,this._headers=e;let i=null!=(s=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?s:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,s,i,n,o=[],a=0;function u(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,n=!1;u();)if(","===(r=e.charAt(a))){for(s=a,a+=1,u(),i=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(n=!0,a=i,o.push(e.substring(t,s)),t=a):a=s+1}else a+=1;(!n||a>=e.length)&&o.push(e.substring(t,e.length))}return o}(i)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===s)}has(e){return this._parsed.has(e)}set(...e){let[t,r,s]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...s})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,s]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:s,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return s.RequestCookies},ResponseCookies:function(){return s.ResponseCookies}});let s=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,9092,5972,2197,5772,7410,5637,682],()=>r(57183));module.exports=s})();