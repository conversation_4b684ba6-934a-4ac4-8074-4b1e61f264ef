"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebPaymentsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let WebPaymentsService = class WebPaymentsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createPayment(userId, createPaymentDto) {
        const { orderId, amount, currency = 'USD', paymentMethod, returnUrl, } = createPaymentDto;
        const order = await this.prisma.order.findFirst({
            where: {
                id: orderId,
                userId,
            },
        });
        if (!order) {
            throw new Error('Order not found');
        }
        const payment = await this.prisma.payment.create({
            data: {
                amount,
                currency,
                provider: 'stripe',
                paymentMethod,
                status: 'PENDING',
                orders: {
                    connect: { id: orderId }
                }
            },
        });
        return {
            paymentId: payment.id,
            message: 'Payment created successfully (Stripe integration pending)',
        };
    }
    async handleStripeWebhook(body, signature) {
        return {
            received: true,
            message: 'Webhook processed (Stripe integration pending)',
        };
    }
};
WebPaymentsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WebPaymentsService);
exports.WebPaymentsService = WebPaymentsService;
//# sourceMappingURL=web-payments.service.js.map