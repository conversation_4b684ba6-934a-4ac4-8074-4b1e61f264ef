import { PrismaService } from '../../prisma.service';
export declare class WebAdminService {
    private prisma;
    constructor(prisma: PrismaService);
    getDashboardData(): Promise<{
        stats: {
            totalUsers: number;
            totalOrders: number;
            totalProducts: number;
        };
        recentOrders: ({
            user: {
                id: string;
                name: string | null;
                email: string | null;
            };
            items: (import("@prisma/client/runtime").GetResult<{
                id: string;
                orderId: string;
                productId: string | null;
                productCode: string | null;
                variantCode: string | null;
                variantText: string | null;
                quantity: number;
                price: number;
                uid: string | null;
                lpaString: string | null;
            }, unknown> & {})[];
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            total: number;
            status: import(".prisma/client").OrderStatus;
            addressId: string | null;
            shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
            paymentId: string | null;
            createdAt: Date;
            updatedAt: Date;
            referralCode: string | null;
        }, unknown> & {})[];
        userStats: (import(".prisma/client").Prisma.PickArray<import(".prisma/client").Prisma.UserGroupByOutputType, "role"[]> & {
            _count: number;
        })[];
        orderStats: (import(".prisma/client").Prisma.PickArray<import(".prisma/client").Prisma.OrderGroupByOutputType, "status"[]> & {
            _count: number;
        })[];
    }>;
    getUsers(query: any): Promise<{
        users: {
            id: string;
            name: string | null;
            email: string | null;
            role: import(".prisma/client").UserRole;
            image: string | null;
            createdAt: Date;
            _count: {
                orders: number;
                reviews: number;
            };
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
    }>;
    getUserById(userId: string): Promise<({
        orders: ({
            items: (import("@prisma/client/runtime").GetResult<{
                id: string;
                orderId: string;
                productId: string | null;
                productCode: string | null;
                variantCode: string | null;
                variantText: string | null;
                quantity: number;
                price: number;
                uid: string | null;
                lpaString: string | null;
            }, unknown> & {})[];
            payment: (import("@prisma/client/runtime").GetResult<{
                id: string;
                amount: number;
                currency: string;
                status: import(".prisma/client").PaymentStatus;
                provider: string;
                paymentMethod: string;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {}) | null;
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            total: number;
            status: import(".prisma/client").OrderStatus;
            addressId: string | null;
            shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
            paymentId: string | null;
            createdAt: Date;
            updatedAt: Date;
            referralCode: string | null;
        }, unknown> & {})[];
        addresses: (import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string | null;
            type: import(".prisma/client").AddressType;
            name: string;
            phone: string;
            address1: string;
            address2: string | null;
            city: string;
            state: string;
            postalCode: string;
            country: string;
            isDefault: boolean;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        _count: {
            orders: number;
            reviews: number;
            wishlist: number;
        };
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        name: string | null;
        email: string | null;
        emailVerified: Date | null;
        image: string | null;
        role: import(".prisma/client").UserRole;
        createdAt: Date;
        updatedAt: Date;
        hashedPassword: string | null;
    }, unknown> & {}) | null>;
    createUser(createUserDto: any): Promise<{
        id: string;
        name: string | null;
        email: string | null;
        role: import(".prisma/client").UserRole;
        createdAt: Date;
    }>;
    updateUser(userId: string, updateUserDto: any): Promise<{
        id: string;
        name: string | null;
        email: string | null;
        role: import(".prisma/client").UserRole;
        image: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    deleteUser(userId: string): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        name: string | null;
        email: string | null;
        emailVerified: Date | null;
        image: string | null;
        role: import(".prisma/client").UserRole;
        createdAt: Date;
        updatedAt: Date;
        hashedPassword: string | null;
    }, unknown> & {}>;
    getOrders(query: any): Promise<{
        orders: ({
            user: {
                id: string;
                name: string | null;
                email: string | null;
            };
            items: (import("@prisma/client/runtime").GetResult<{
                id: string;
                orderId: string;
                productId: string | null;
                productCode: string | null;
                variantCode: string | null;
                variantText: string | null;
                quantity: number;
                price: number;
                uid: string | null;
                lpaString: string | null;
            }, unknown> & {})[];
            payment: (import("@prisma/client/runtime").GetResult<{
                id: string;
                amount: number;
                currency: string;
                status: import(".prisma/client").PaymentStatus;
                provider: string;
                paymentMethod: string;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {}) | null;
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            total: number;
            status: import(".prisma/client").OrderStatus;
            addressId: string | null;
            shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
            paymentId: string | null;
            createdAt: Date;
            updatedAt: Date;
            referralCode: string | null;
        }, unknown> & {})[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
    }>;
    getProducts(query: any): Promise<{
        products: ({
            category: import("@prisma/client/runtime").GetResult<{
                id: string;
                name: string;
                description: string | null;
                image: string | null;
                parentId: string | null;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {};
            variants: (import("@prisma/client/runtime").GetResult<{
                id: string;
                price: import("@prisma/client/runtime").Decimal;
                currency: string;
                productId: string;
                variantCode: string | null;
                duration: number | null;
                durationType: string | null;
                attributes: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {})[];
            _count: {
                reviews: number;
            };
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string;
            websiteDescription: string;
            price: number;
            images: string[];
            categoryId: string;
            stock: number;
            specifications: import(".prisma/client").Prisma.JsonValue;
            status: import(".prisma/client").ProductStatus;
            sku: string;
            requiredUID: boolean;
            createdAt: Date;
            updatedAt: Date;
            mcc: string | null;
            off_shelve: boolean;
            dataSize: number | null;
            planType: string | null;
            country: string | null;
            countryCode: string | null;
            odooLastSyncAt: Date | null;
            popularityScore: number | null;
            isPopular: boolean;
        }, unknown> & {})[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
    }>;
}
