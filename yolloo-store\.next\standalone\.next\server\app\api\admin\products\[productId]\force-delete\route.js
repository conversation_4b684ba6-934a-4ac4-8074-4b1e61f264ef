"use strict";(()=>{var e={};e.id=6281,e.ids=[6281],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},49802:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>E,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>w,staticGenerationAsyncStorage:()=>f});var o={};t.r(o),t.d(o,{DELETE:()=>u});var a=t(49303),n=t(88716),i=t(60670),s=t(87070),l=t(75571),c=t(90455),d=t(72331);async function u(e,{params:r}){try{let e=await (0,l.getServerSession)(c.L);if(!e||"ADMIN"!==e.user.role)return new s.NextResponse("Unauthorized",{status:401});let t=await d._.product.findUnique({where:{id:r.productId},include:{category:!0,variants:!0}});if(!t)return new s.NextResponse("Product not found",{status:404});return console.log(`[FORCE_DELETE] Starting force deletion process for product ID: ${r.productId} (${t.name})`),await d._.$transaction(async e=>{let o=await e.orderItem.findMany({where:{productCode:t.sku},include:{order:!0}});o.length>0&&(console.log(`[FORCE_DELETE] Product has ${o.length} associated order items`),console.log(`[FORCE_DELETE] Preserving productCode and variantCode in ${o.length} order items for historical reference`));let a=await e.esim.findMany({where:{productId:r.productId}});a.length>0&&(console.log(`[FORCE_DELETE] Product has ${a.length} associated eSIMs, deleting them`),await e.esim.deleteMany({where:{productId:r.productId}}),console.log(`[FORCE_DELETE] Deleted ${a.length} eSIMs`));let n=await e.cartItem.deleteMany({where:{productId:r.productId}});console.log(`[FORCE_DELETE] Deleted ${n.count} cart items`);let i=await e.wishlistItem.deleteMany({where:{productId:r.productId}});console.log(`[FORCE_DELETE] Deleted ${i.count} wishlist items`);let s=await e.review.deleteMany({where:{productId:r.productId}});console.log(`[FORCE_DELETE] Deleted ${s.count} reviews`);let l=await e.productParameter.deleteMany({where:{productId:r.productId}});console.log(`[FORCE_DELETE] Deleted ${l.count} product parameters`);let c=await e.productVariant.deleteMany({where:{productId:r.productId}});console.log(`[FORCE_DELETE] Deleted ${c.count} product variants`),await e.product.delete({where:{id:r.productId}}),console.log(`[FORCE_DELETE] Successfully deleted product: ${r.productId}`)}),new s.NextResponse(null,{status:204})}catch(e){if(console.error("[FORCE_DELETE]",e),e instanceof Error)return new s.NextResponse(`Error force deleting product: ${e.message}`,{status:500});return new s.NextResponse("Internal error",{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/products/[productId]/force-delete/route",pathname:"/api/admin/products/[productId]/force-delete",filename:"route",bundlePath:"app/api/admin/products/[productId]/force-delete/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\products\\[productId]\\force-delete\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:w}=p,h="/api/admin/products/[productId]/force-delete/route";function E(){return(0,i.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:f})}},90455:(e,r,t)=>{t.d(r,{L:()=>d});var o=t(7585),a=t(72331),n=t(77234),i=t(53797),s=t(42023),l=t.n(s),c=t(93475);let d={adapter:{...(0,o.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,i.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,i.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,c.Ak)(e.email);if(!r||r!==e.code)return null;await (0,c.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:o,request:n}){try{if(t&&t.id){let r=n?.headers||new Headers,i=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",c="unknown";o?c=o.code&&!o.password?"email_code":"password":e&&(c=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:i||null,loginMethod:c,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],o=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(o).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let o=new URL(e);if(t.some(e=>o.hostname===e||o.hostname.includes(e)||o.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(o);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return o}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:o}){if("update"===t&&o)return{...e,...o.user};let n=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>a});var o=t(53524);let a=global.prisma||new o.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>c,yz:()=>d});var o=t(62197),a=t.n(o);let n=null;function i(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,r,t=300){try{let o=i(),a=`verification_code:${e}`;return await o.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=i(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function c(e){try{let r=i(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function d(e,r,t){try{let o=i(),a=`rate_limit:${e}`,n=await o.get(a),s=n?parseInt(n):0;if(s>=r)return!1;return 0===s?await o.setex(a,t,"1"):await o.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=a?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(o,n,s):o[n]=e[n]}return o.default=e,t&&t.set(e,o),o}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>t(49802));module.exports=o})();