"use strict";(()=>{var e={};e.id=9118,e.ids=[9118],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50852:e=>{e.exports=require("async_hooks")},6113:e=>{e.exports=require("crypto")},84492:e=>{e.exports=require("node:stream")},12781:e=>{e.exports=require("stream")},73837:e=>{e.exports=require("util")},43822:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>_,patchFetch:()=>O,requestAsyncStorage:()=>D,routeModule:()=>w,serverHooks:()=>A,staticGenerationAsyncStorage:()=>x});var n={};r.r(n),r.d(n,{POST:()=>v,dynamic:()=>f,fetchCache:()=>h,revalidate:()=>g});var i=r(49303),o=r(88716),a=r(60670),s=r(87070),l=r(72331),u=r(7410),d=r(6113),p=r.n(d),m=r(60682),c=r(50650);let f="force-dynamic",h="force-no-store",g=0,y=u.z.object({email:u.z.string().email("Invalid email address")});async function v(e){try{let t=await e.json(),r=y.safeParse(t);if(!r.success)return s.NextResponse.json({error:"Invalid email address"},{status:400});let{email:n}=r.data,i=await l._.user.findUnique({where:{email:n}});if(!i)return s.NextResponse.json({success:!0,message:"If your email is in our system, you will receive a password reset link shortly."});await l._.passwordResetToken.deleteMany({where:{userId:i.id}});let o=p().randomBytes(32).toString("hex"),a=c.ED.addDays(new Date,7);await l._.passwordResetToken.create({data:{userId:i.id,token:o,expiresAt:a}});let u=`http://localhost:8000/auth/reset-password?token=${o}`;try{await (0,m.LS)(i.email,i.name||"User",u)}catch(e){console.error("Error sending password reset email:",e)}return s.NextResponse.json({success:!0,message:"If your email is in our system, you will receive a password reset link shortly."})}catch(e){return console.error("Error processing forgot password request:",e),s.NextResponse.json({error:"Failed to process request"},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/forgot-password/route",pathname:"/api/auth/forgot-password",filename:"route",bundlePath:"app/api/auth/forgot-password/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\auth\\forgot-password\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:D,staticGenerationAsyncStorage:x,serverHooks:A}=w,_="/api/auth/forgot-password/route";function O(){return(0,a.patchFetch)({serverHooks:A,staticGenerationAsyncStorage:x})}},72331:(e,t,r)=>{r.d(t,{_:()=>i});var n=r(53524);let i=global.prisma||new n.PrismaClient({log:["error"]})},50650:(e,t,r)=>{r.d(t,{CN:()=>f,ED:()=>h,QG:()=>y,T4:()=>d,cn:()=>u,eP:()=>w,mo:()=>v,vI:()=>g});var n=r(55761),i=r(62386),o=r(6180),a=r(4284),s=r(35772),l=r(21740);function u(...e){return(0,i.m6)((0,n.W)(e))}function d(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let p={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function m(){return p.TIMEZONE}function c(e){if(!e)return null;try{let t;if(e instanceof Date)t=e;else if("string"==typeof e)t=e.includes("T")||e.includes("Z")?(0,o.D)(e):new Date(e);else{if("number"!=typeof e)return null;t=new Date(e)}return(0,a.J)(t)?t:null}catch(t){return console.warn("Date parsing error:",t,"Input:",e),null}}let f={short:(e,t="Invalid Date")=>{let r=c(e);return r?(0,s.WU)(r,p.DATE_FORMAT):t},full:(e,t="Invalid Date")=>{let r=c(e);return r?(0,s.WU)(r,p.DATETIME_FORMAT):t},long:(e,t="Invalid Date")=>{let r=c(e);return r?r.toLocaleDateString(p.LOCALE,{year:"numeric",month:"long",day:"numeric"}):t},relative:(e,t="Invalid Date")=>{let r=c(e);return r?(0,l.Q)(r,{addSuffix:!0}):t},time:(e,t="Invalid Time")=>{let r=c(e);return r?(0,s.WU)(r,"HH:mm:ss"):t},timeShort:(e,t="Invalid Time")=>{let r=c(e);return r?(0,s.WU)(r,"HH:mm"):t},withTimezone:(e,t,r="Invalid Date")=>{let n=c(e);return n?new Intl.DateTimeFormat(p.LOCALE,{timeZone:t||p.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(n):r},forUser:(e,t="Invalid Date")=>{let r=c(e);if(!r)return t;let n=m();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserShort:(e,t="Invalid Date")=>{let r=c(e);if(!r)return t;let n=m();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit"}).format(r)},forUserSafe:(e,t="Invalid Date")=>{let r=c(e);if(!r)return t;let n=p.TIMEZONE;return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserRelative:(e,t="Invalid Date")=>{let r=c(e);return r?(0,l.Q)(r,{addSuffix:!0}):t},iso:(e,t="")=>{let r=c(e);return r?r.toISOString():t},custom:(e,t,r="Invalid Date")=>{let n=c(e);return n?(0,s.WU)(n,t):r}},h={addDays:(e,t)=>new Date((c(e)||new Date).getTime()+864e5*t),addHours:(e,t)=>new Date((c(e)||new Date).getTime()+36e5*t),addMinutes:(e,t)=>new Date((c(e)||new Date).getTime()+6e4*t),daysBetween:(e,t)=>{let r=c(e),n=c(t);return r&&n?Math.floor((n.getTime()-r.getTime())/864e5):0},isExpired:e=>{let t=c(e);return!!t&&t.getTime()<Date.now()}};function g(e){return e?e.replace(/\D/g,""):""}function y(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function v(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function w(e){let t=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return t?{badge:t[1].toUpperCase(),name:t[2]}:{name:e}}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,i],...o]=s(e),{domain:a,expires:l,httponly:p,maxage:m,path:c,samesite:f,secure:h,partitioned:g,priority:y}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:a,...l&&{expires:new Date(l)},...p&&{httpOnly:!0},..."string"==typeof m&&{maxAge:Number(m)},path:c,...f&&{sameSite:u.includes(t=(t=f).toLowerCase())?t:void 0},...h&&{secure:!0},...y&&{priority:d.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(o,{RequestCookies:()=>p,ResponseCookies:()=>m,parseCookie:()=>s,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,o,a,s)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let a of n(o))i.call(e,a)||void 0===a||t(e,a,{get:()=>o[a],enumerable:!(s=r(o,a))||s.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u=["strict","lax","none"],d=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},m=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,o,a=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,o=!1;l();)if(","===(r=e.charAt(s))){for(n=s,s+=1,l(),i=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(o=!0,s=i,a.push(e.substring(t,n)),t=s):s=n+1}else s+=1;(!o||s>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,5972,5772,7624,7410,5637,682],()=>r(43822));module.exports=n})();