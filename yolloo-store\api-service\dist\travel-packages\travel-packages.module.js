"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TravelPackagesModule = void 0;
const common_1 = require("@nestjs/common");
const travel_packages_service_1 = require("./travel-packages.service");
const travel_packages_controller_1 = require("./travel-packages.controller");
const prisma_service_1 = require("../prisma.service");
const auth_module_1 = require("../auth/auth.module");
const rating_module_1 = require("../rating/rating.module");
let TravelPackagesModule = class TravelPackagesModule {
};
TravelPackagesModule = __decorate([
    (0, common_1.Module)({
        imports: [auth_module_1.AuthModule, rating_module_1.RatingModule],
        controllers: [travel_packages_controller_1.TravelPackagesController],
        providers: [travel_packages_service_1.TravelPackagesService, prisma_service_1.PrismaService],
    })
], TravelPackagesModule);
exports.TravelPackagesModule = TravelPackagesModule;
//# sourceMappingURL=travel-packages.module.js.map