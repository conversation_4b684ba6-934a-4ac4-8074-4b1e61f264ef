"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebPaymentsController = void 0;
const common_1 = require("@nestjs/common");
const web_payments_service_1 = require("./web-payments.service");
const public_decorator_1 = require("../../common/decorators/public.decorator");
let WebPaymentsController = class WebPaymentsController {
    webPaymentsService;
    constructor(webPaymentsService) {
        this.webPaymentsService = webPaymentsService;
    }
    async createPayment(createPaymentDto, req, res) {
        try {
            const user = req['user'];
            if (!user) {
                return res.status(common_1.HttpStatus.UNAUTHORIZED).json({
                    error: 'Unauthorized',
                });
            }
            const payment = await this.webPaymentsService.createPayment(user.id, createPaymentDto);
            return res.status(common_1.HttpStatus.CREATED).json(payment);
        }
        catch (error) {
            console.error('[WEB_PAYMENT_CREATE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to create payment',
            });
        }
    }
    async handleWebhook(body, req, res) {
        try {
            const signature = req.headers['stripe-signature'];
            const result = await this.webPaymentsService.handleStripeWebhook(body, signature);
            return res.json(result);
        }
        catch (error) {
            console.error('[WEB_PAYMENT_WEBHOOK]', error);
            return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                error: 'Webhook processing failed',
            });
        }
    }
};
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebPaymentsController.prototype, "createPayment", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('webhook'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebPaymentsController.prototype, "handleWebhook", null);
WebPaymentsController = __decorate([
    (0, common_1.Controller)('api/web/payments'),
    __metadata("design:paramtypes", [web_payments_service_1.WebPaymentsService])
], WebPaymentsController);
exports.WebPaymentsController = WebPaymentsController;
//# sourceMappingURL=web-payments.controller.js.map