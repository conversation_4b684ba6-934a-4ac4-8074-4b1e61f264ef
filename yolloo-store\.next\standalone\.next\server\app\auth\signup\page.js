(()=>{var e={};e.id=5271,e.ids=[5271],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},13163:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),a(80197),a(66265),a(89090),a(26083),a(35866);var r=a(23191),s=a(88716),n=a(37922),o=a.n(n),i=a(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,80197)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\signup\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,66265)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\signup\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\signup\\page.tsx"],u="/auth/signup/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19149:(e,t,a)=>{Promise.resolve().then(a.bind(a,41371))},35303:()=>{},41371:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(10326),s=a(90434),n=a(73078),o=a(17577),i=a(35047),l=a(77109),c=a(90772),d=a(54432),u=a(31048),m=a(57372),p=a(85999);function x({className:e,...t}){(0,i.useRouter)();let[a,s]=o.useState(!1),[n,x]=o.useState(!1),h=(0,i.useSearchParams)(),g=h?.get("callbackUrl")||"/";async function f(e){e.preventDefault(),s(!0);try{let t=new FormData(e.currentTarget),a=t.get("name"),r=t.get("email"),n=t.get("password"),o=t.get("confirmPassword");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)){p.A.error("Please enter a valid email address in <NAME_EMAIL>"),s(!1);return}if(n!==o){p.A.error("Passwords do not match"),s(!1);return}if(n.length<8){p.A.error("Password must be at least 8 characters long"),s(!1);return}let i=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:a,email:r,password:n})});if(!i.ok){let e=await i.json();throw Error(e.message||"Something went wrong")}let c=await (0,l.signIn)("credentials",{email:r,password:n,redirect:!1,callbackUrl:g});if(c?.error){p.A.error("Failed to sign in");return}c?.url?window.location.href=c.url:window.location.href=g,p.A.success("Account created successfully")}catch(e){e instanceof Error?p.A.error(e.message):p.A.error("Something went wrong")}finally{s(!1)}}let w=async()=>{try{x(!0);let e=await (0,l.signIn)("google",{callbackUrl:g,redirect:!1});e?.url&&(window.location.href=e.url)}catch(e){p.A.error("Something went wrong with Google sign in")}finally{x(!1)}};return(0,r.jsxs)("div",{className:"grid gap-6",...t,children:[r.jsx("form",{onSubmit:f,children:(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(u._,{htmlFor:"name",children:"Name"}),r.jsx(d.I,{id:"name",name:"name",placeholder:"John Doe",type:"text",autoCapitalize:"none",autoComplete:"name",autoCorrect:"off",disabled:a,required:!0})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(u._,{htmlFor:"email",children:"Email"}),r.jsx(d.I,{id:"email",name:"email",placeholder:"<EMAIL>",type:"email",autoCapitalize:"none",autoComplete:"email",autoCorrect:"off",disabled:a,required:!0,pattern:"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",title:"Please enter a valid email address in <NAME_EMAIL>"})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(u._,{htmlFor:"password",children:"Password"}),r.jsx(d.I,{id:"password",name:"password",type:"password",autoCapitalize:"none",autoComplete:"new-password",autoCorrect:"off",disabled:a,required:!0,minLength:8}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Must be at least 8 characters long"})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(u._,{htmlFor:"confirmPassword",children:"Confirm Password"}),r.jsx(d.I,{id:"confirmPassword",name:"confirmPassword",type:"password",autoCapitalize:"none",autoComplete:"new-password",autoCorrect:"off",disabled:a,required:!0,minLength:8})]}),(0,r.jsxs)(c.Button,{disabled:a,children:[a&&r.jsx(m.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Sign Up with Email"]})]})}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("span",{className:"w-full border-t"})}),r.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:r.jsx("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,r.jsxs)(c.Button,{variant:"outline",type:"button",disabled:n,onClick:w,children:[n?r.jsx(m.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}):r.jsx(m.P.google,{className:"mr-2 h-4 w-4"})," ","Sign up with Google"]})]})}function h(){let e=(0,i.useSearchParams)(),t=e?.get("callbackUrl"),a=t?`/auth/signin?callbackUrl=${encodeURIComponent(t)}`:"/auth/signin";return(0,r.jsxs)("div",{className:"container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0",children:[(0,r.jsxs)("div",{className:"relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r",children:[r.jsx("div",{className:"absolute inset-0 bg-zinc-900"}),(0,r.jsxs)("div",{className:"relative z-20 flex items-center text-lg font-medium",children:[r.jsx(n.Z,{className:"mr-2 h-6 w-6"})," Yolloo Store"]}),r.jsx("div",{className:"relative z-20 mt-auto",children:(0,r.jsxs)("blockquote",{className:"space-y-2",children:[r.jsx("p",{className:"text-lg",children:'"Join our community and discover amazing products with great deals."'}),r.jsx("footer",{className:"text-sm",children:"New Customer"})]})})]}),r.jsx("div",{className:"lg:p-8",children:(0,r.jsxs)("div",{className:"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-2 text-center",children:[r.jsx("h1",{className:"text-2xl font-semibold tracking-tight",children:"Create an account"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter your information below to create your account"})]}),r.jsx(x,{}),(0,r.jsxs)("p",{className:"px-8 text-center text-sm text-muted-foreground",children:["Already have an account?"," ",r.jsx(s.default,{href:a,className:"underline underline-offset-4 hover:text-primary",children:"Sign in"})]})]})})]})}},31048:(e,t,a)=>{"use strict";a.d(t,{_:()=>c});var r=a(10326),s=a(17577),n=a(34478),o=a(79360),i=a(77863);let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef(({className:e,...t},a)=>r.jsx(n.f,{ref:a,className:(0,i.cn)(l(),e),...t}));c.displayName=n.f.displayName},66265:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s,metadata:()=>r});let r={title:"Sign Up",description:"Create a new account"};function s({children:e}){return e}},80197:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>i});var r=a(68570);let s=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\signup\page.tsx`),{__esModule:n,$$typeof:o}=s;s.default;let i=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\signup\page.tsx#default`)},57481:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},34478:(e,t,a)=>{"use strict";a.d(t,{f:()=>i});var r=a(17577),s=a(45226),n=a(10326),o=r.forwardRef((e,t)=>(0,n.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>a(13163));module.exports=r})();