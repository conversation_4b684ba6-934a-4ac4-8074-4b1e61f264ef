(()=>{var e={};e.id=1569,e.ids=[1569],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},6587:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(52437),t(89090),t(26083),t(35866);var a=t(23191),r=t(88716),n=t(37922),l=t.n(n),o=t(95231),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let d=["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52437)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\orders\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\orders\\page.tsx"],m="/orders/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},52932:(e,s,t)=>{Promise.resolve().then(t.bind(t,51971))},51971:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(10326),r=t(17577),n=t(90434),l=t(35047),o=t(33071),i=t(90772),d=t(567),c=t(57372),m=t(77863),u=t(85999),p=t(99440);function x({order:e}){let s=(0,l.useRouter)(),[t,n]=(0,r.useState)(!1),[o,d]=(0,r.useState)(!1);async function m(){n(!0);try{if(!(await fetch(`/api/orders/${e.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"CANCELLED"})})).ok)throw Error("Failed to cancel order");u.A.success("Order cancelled successfully"),window.location.reload()}catch(e){u.A.error("Failed to cancel order")}finally{n(!1),d(!1)}}return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"flex flex-col w-full gap-2 sm:flex-row sm:w-auto",children:"PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(i.Button,{size:"sm",onClick:t=>{t.stopPropagation(),s.push(`/orders/${e.id}`)},className:"w-full sm:w-auto relative z-20",children:"Complete Payment"}),(0,a.jsxs)(i.Button,{variant:"outline",size:"sm",disabled:t,onClick:e=>{e.stopPropagation(),d(!0)},className:"w-full sm:w-auto relative z-20",children:[t&&a.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Cancel Order"]})]})}),a.jsx(p.aR,{open:o,onOpenChange:d,children:(0,a.jsxs)(p._T,{className:"z-50",children:[(0,a.jsxs)(p.fY,{children:[a.jsx(p.f$,{children:"Are you sure?"}),a.jsx(p.yT,{children:"This action cannot be undone. This will permanently cancel your order."})]}),(0,a.jsxs)(p.xo,{children:[a.jsx(p.le,{disabled:t,children:"Cancel"}),a.jsx(p.OL,{onClick:m,disabled:t,children:t?"Cancelling...":"Confirm"})]})]})})]})}var f=t(54432),h=t(34474);function g(){let e=(0,l.useRouter)(),s=(0,l.useSearchParams)(),[t,u]=(0,r.useState)(!0),[p,g]=(0,r.useState)(null),[j,v]=(0,r.useState)(s.get("search")||""),y=(0,r.useRef)(),N=Number(s.get("page"))||1,b=s.get("status")||"all",w=s.get("sort")||"newest",P=s.get("search")||"",C=t=>{let a=new URLSearchParams(s.toString());Object.entries(t).forEach(([e,s])=>{null===s?a.delete(e):a.set(e,s)}),e.push(`/orders?${a.toString()}`)};if(t)return a.jsx("div",{className:"container py-8",children:a.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:a.jsx(c.P.spinner,{className:"h-8 w-8 animate-spin"})})});if(!p)return null;let{orders:E,total:k,totalOrders:D,totalPages:R,statusCounts:_}=p,z=e=>{let s=_.find(s=>s.status===e);return s?s._count:0};return a.jsx("div",{className:"container py-8",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold",children:"My Orders"}),a.jsx("p",{className:"text-muted-foreground",children:"View and manage your orders"})]}),a.jsx(i.Button,{asChild:!0,children:a.jsx(n.default,{href:"/products",children:"Continue Shopping"})})]}),(0,a.jsxs)("div",{className:"flex gap-4 flex-col md:flex-row",children:[a.jsx("div",{className:"flex-1",children:a.jsx(f.I,{placeholder:"Search orders by ID or product name...",value:j,onChange:e=>{let s=e.target.value;v(s),y.current&&clearTimeout(y.current),y.current=setTimeout(()=>{C({search:s||null,page:null})},500)}})}),(0,a.jsxs)(h.Ph,{value:w,onValueChange:e=>{C({sort:e,page:null})},children:[a.jsx(h.i4,{className:"w-[180px]",children:a.jsx(h.ki,{placeholder:"Sort by"})}),(0,a.jsxs)(h.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[a.jsx(h.Ql,{value:"newest",children:"Newest First"}),a.jsx(h.Ql,{value:"oldest",children:"Oldest First"})]})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"flex gap-2 border-b pb-2 overflow-x-auto",children:[(0,a.jsxs)(i.Button,{variant:"all"===b?"secondary":"ghost",size:"sm",onClick:()=>{C({status:null,page:null})},children:["All (",D,")"]}),(0,a.jsxs)(i.Button,{variant:"PENDING"===b?"secondary":"ghost",size:"sm",onClick:()=>{C({status:"PENDING",page:null})},children:["Pending (",z("PENDING"),")"]}),(0,a.jsxs)(i.Button,{variant:"PAID"===b?"secondary":"ghost",size:"sm",onClick:()=>{C({status:"PAID",page:null})},children:["Paid (",z("PAID"),")"]}),(0,a.jsxs)(i.Button,{variant:"SHIPPED"===b?"secondary":"ghost",size:"sm",onClick:()=>{C({status:"SHIPPED",page:null})},children:["Shipped (",z("SHIPPED"),")"]}),(0,a.jsxs)(i.Button,{variant:"DELIVERED"===b?"secondary":"ghost",size:"sm",onClick:()=>{C({status:"DELIVERED",page:null})},children:["Delivered (",z("DELIVERED"),")"]}),(0,a.jsxs)(i.Button,{variant:"CANCELLED"===b?"secondary":"ghost",size:"sm",onClick:()=>{C({status:"CANCELLED",page:null})},children:["Cancelled (",z("CANCELLED"),")"]})]}),a.jsx("div",{className:"mt-6",children:0===E.length?a.jsx(o.Zb,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(c.P.package,{className:"mx-auto h-12 w-12 text-muted-foreground/50"}),a.jsx("p",{className:"mt-4 text-lg font-semibold",children:"No orders found"}),a.jsx("p",{className:"text-muted-foreground",children:P?"Try adjusting your search terms":"all"===b?"You haven't placed any orders yet":`You don't have any ${b.toLowerCase()} orders`})]})}):a.jsx("div",{className:"space-y-4",children:E.map(s=>a.jsx("div",{className:"w-full",children:(0,a.jsxs)(o.Zb,{className:"group relative p-4 sm:p-6 rounded-xl shadow hover:shadow-lg transition cursor-pointer",onClick:()=>e.push(`/orders/${s.id}`),tabIndex:0,role:"button","aria-label":`View details for order ${s.id}`,children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"font-semibold text-base",children:["Order #",s.id.slice(0,8)]}),a.jsx("span",{className:"text-xs text-muted-foreground",children:m.CN.forUserSafe(s.createdAt)})]}),a.jsx(d.C,{variant:"DELIVERED"===s.status||"SHIPPED"===s.status||"PAID"===s.status?"success":"PENDING"===s.status?"warning":"destructive",className:"h-6 px-3 text-xs whitespace-nowrap self-end sm:self-auto sm:ml-auto",children:s.status})]}),a.jsx("div",{className:"mt-3 flex flex-col gap-2",children:s.items.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"relative h-12 w-12 min-w-[3rem] rounded-lg overflow-hidden border bg-gray-100 flex items-center justify-center",children:a.jsx(c.P.package,{className:"h-6 w-6 text-gray-400"})}),a.jsx("div",{className:"flex-1 min-w-0",children:a.jsx("span",{className:"block font-medium truncate text-sm",children:e.variantText||"Unknown Product"})}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["x",e.quantity]}),a.jsx("span",{className:"ml-2 font-medium text-sm",children:(0,m.T4)(e.price*e.quantity)})]},e.id))}),(0,a.jsxs)("div",{className:"mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 border-t pt-4",children:[a.jsx("span",{className:"font-bold text-lg text-primary",children:(0,m.T4)(s.total)}),a.jsx("div",{className:"flex flex-col sm:flex-row gap-2 w-full sm:w-auto relative z-20",onClick:e=>e.stopPropagation(),onKeyDown:e=>e.stopPropagation(),children:a.jsx(x,{order:s})})]}),a.jsx("span",{className:"absolute inset-0 z-10 pointer-events-none"})]})},s.id))})})]}),R>1&&a.jsx("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[N>1&&(0,a.jsxs)(i.Button,{variant:"outline",size:"sm",onClick:()=>{C({page:String(N-1)})},children:[a.jsx(c.P.chevronLeft,{className:"h-4 w-4 mr-2"}),"Previous"]}),(0,a.jsxs)("p",{className:"text-sm",children:["Page ",N," of ",R]}),N<R&&(0,a.jsxs)(i.Button,{variant:"outline",size:"sm",onClick:()=>{C({page:String(N+1)})},children:["Next",a.jsx(c.P.chevronRight,{className:"h-4 w-4 ml-2"})]})]})})]})})}},99440:(e,s,t)=>{"use strict";t.d(s,{OL:()=>h,_T:()=>m,aR:()=>o,f$:()=>x,fY:()=>u,le:()=>g,vW:()=>i,xo:()=>p,yT:()=>f});var a=t(10326),r=t(17577),n=t(12194),l=t(77863);let o=n.fC,i=n.xz,d=n.h_,c=r.forwardRef(({className:e,...s},t)=>a.jsx(n.aV,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:t}));c.displayName=n.aV.displayName;let m=r.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(d,{children:[a.jsx(c,{}),a.jsx(n.VY,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));m.displayName=n.VY.displayName;let u=({className:e,...s})=>a.jsx("div",{className:(0,l.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});u.displayName="AlertDialogHeader";let p=({className:e,...s})=>a.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});p.displayName="AlertDialogFooter";let x=r.forwardRef(({className:e,...s},t)=>a.jsx(n.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold",e),...s}));x.displayName=n.Dx.displayName;let f=r.forwardRef(({className:e,...s},t)=>a.jsx(n.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));f.displayName=n.dk.displayName;let h=r.forwardRef(({className:e,...s},t)=>a.jsx(n.aU,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",e),...s}));h.displayName=n.aU.displayName;let g=r.forwardRef(({className:e,...s},t)=>a.jsx(n.$j,{ref:t,className:(0,l.cn)("mt-2 inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-semibold ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 sm:mt-0",e),...s}));g.displayName=n.$j.displayName},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>o});var a=t(10326);t(17577);var r=t(79360),n=t(77863);let l=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function o({className:e,variant:s,...t}){return a.jsx("div",{className:(0,n.cn)(l({variant:s}),e),...t})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>o,SZ:()=>d,Zb:()=>l,aY:()=>c,eW:()=>m,ll:()=>i});var a=t(10326),r=t(17577),n=t(77863);let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));l.displayName="Card";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let i=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},34474:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>u,Ph:()=>d,Ql:()=>p,i4:()=>m,ki:()=>c});var a=t(10326),r=t(17577),n=t(18792),l=t(941),o=t(32933),i=t(77863);let d=n.fC;n.ZA;let c=n.B4,m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(n.xz,{ref:r,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:[s,a.jsx(n.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=n.xz.displayName;let u=r.forwardRef(({className:e,children:s,position:t="popper",...r},l)=>a.jsx(n.h_,{children:a.jsx(n.VY,{ref:l,className:(0,i.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:a.jsx(n.l_,{className:(0,i.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:s})})}));u.displayName=n.VY.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(n.__,{ref:t,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=n.__.displayName;let p=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(n.ck,{ref:r,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(n.wU,{children:a.jsx(o.Z,{className:"h-4 w-4"})})}),a.jsx(n.eT,{children:s})]}));p.displayName=n.ck.displayName,r.forwardRef(({className:e,...s},t)=>a.jsx(n.Z0,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=n.Z0.displayName},52437:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>n,default:()=>o});var a=t(68570);let r=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\orders\page.tsx`),{__esModule:n,$$typeof:l}=r;r.default;let o=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\orders\page.tsx#default`)},57481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,1615,5772,7624,5634,6621,8792,2194,4824],()=>t(6587));module.exports=a})();