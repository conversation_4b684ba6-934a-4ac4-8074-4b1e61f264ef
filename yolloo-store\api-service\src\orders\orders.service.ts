import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { DateFormatter } from '../common/utils';

@Injectable()
export class OrdersService {
  constructor(private prisma: PrismaService) {}

  async createOrder(userId: string, createOrderDto: CreateOrderDto) {
    // 获取用户购物车中的商品
    const cartItems = await this.prisma.cartItem.findMany({
      where: { userId },
      include: {
        product: true,
        variant: true,
      },
    });

    if (cartItems.length === 0) {
      throw new BadRequestException('购物车为空，无法创建订单');
    }

    // 计算订单总金额
    let total = 0;
    let currency = 'USD';

    const orderItems = cartItems.map(item => {
      const price = item.variant ? Number(item.variant.price) : item.product.price;
      const itemCurrency = item.variant ? item.variant.currency : 'USD';

      // 使用第一个商品的货币作为订单货币
      if (total === 0) {
        currency = itemCurrency;
      }

      total += price * item.quantity;

      return {
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        price: price,
        name: item.product.name,
      };
    });

    // 创建订单
    const order = await this.prisma.order.create({
      data: {
        userId,
        total,
        status: 'PENDING',
        shippingAddressSnapshot: {

          name: createOrderDto.shippingAddress.name,
          phone: createOrderDto.shippingAddress.phone,
          address1: createOrderDto.shippingAddress.address1,
          address2: createOrderDto.shippingAddress.address2,
          city: createOrderDto.shippingAddress.city,
          state: createOrderDto.shippingAddress.state,
          postalCode: createOrderDto.shippingAddress.postalCode,
          country: createOrderDto.shippingAddress.country,

          // 如果提供了账单地址，则保存账单地址
          billingName: createOrderDto.billingAddress?.name || createOrderDto.shippingAddress.name,
          billingPhone: createOrderDto.billingAddress?.phone || createOrderDto.shippingAddress.phone,
          billingAddress1: createOrderDto.billingAddress?.address1 || createOrderDto.shippingAddress.address1,
          billingAddress2: createOrderDto.billingAddress?.address2 || createOrderDto.shippingAddress.address2,
          billingCity: createOrderDto.billingAddress?.city || createOrderDto.shippingAddress.city,
          billingState: createOrderDto.billingAddress?.state || createOrderDto.shippingAddress.state,
          billingPostalCode: createOrderDto.billingAddress?.postalCode || createOrderDto.shippingAddress.postalCode,
          billingCountry: createOrderDto.billingAddress?.country || createOrderDto.shippingAddress.country,
        },

        // 创建订单项目
        items: {
          create: orderItems.map(item => ({
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            productId: item.productId,
            variantId: item.variantId,
          })),
        },
      },
    });

    // 清空购物车
    await this.prisma.cartItem.deleteMany({
      where: { userId },
    });

    // 在实际应用中，这里会创建支付意向并返回支付信息
    // 这里我们模拟支付信息
    const paymentIntentId = 'pi_' + Date.now();
    const clientSecret = paymentIntentId + '_secret_' + Math.random().toString(36).substring(2);
    const paymentUrl = 'https://example.com/payment/' + order.id;

    return {
      orderId: order.id,
      paymentIntentId,
      clientSecret,
      total: order.total,
      currency: 'USD',
      paymentUrl,
    };
  }

  async getOrderById(userId: string, orderId: string) {
    // 查询订单详情
    const order = await this.prisma.order.findFirst({
      where: {
        id: orderId,
        userId,
      },
      include: {
        items: true,
      },
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    // 格式化订单项目
    const formattedItems = order.items.map(item => ({
      id: item.id,
      productCode: item.productCode || 'Unknown SKU',
      name: item.variantText || 'Unknown Product',
      price: item.price,
      quantity: item.quantity,
      imageUrl: 'https://example.com/default-product.jpg', // 由于没有 product 关系，使用默认图片
    }));

    // 查询与订单相关的eSIM
    // 注意：在实际应用中，您需要建立订单和eSIM之间的关系
    // 这里我们使用模拟数据
    const formattedEsims = [];

    return {
      id: order.id,
      status: order.status,
      paymentStatus: 'PENDING', // Default payment status
      total: order.total,
      currency: 'USD', // Default currency
      items: formattedItems,
      esims: formattedEsims,
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
    };
  }

  async getUserOrders(userId: string, query: OrderQueryDto) {
    const { status, page = 1, pageSize = 20 } = query;
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = { userId };

    if (status) {
      where.status = status;
    }

    // 查询用户订单
    const [orders, total] = await Promise.all([
      this.prisma.order.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          items: {
            select: {
              id: true,
              productCode: true,
              variantText: true,
              quantity: true,
              price: true,
            },
          },
        },
      }),
      this.prisma.order.count({ where }),
    ]);

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      status: order.status,
      total: order.total,
      currency: 'USD', // Default currency
      items: order.items.map(item => ({
        id: item.id,
        productCode: item.productCode || 'Unknown SKU',
        productName: item.variantText || 'Unknown Product',
        quantity: item.quantity,
        price: item.price,
      })),
      createdAt: DateFormatter.iso(order.createdAt),
    }));

    return {
      orders: formattedOrders,
      pagination: {
        total,
        page,
        pageSize,
        hasMore: skip + orders.length < total,
      },
    };
  }
}
