(()=>{var e={};e.id=1974,e.ids=[1974],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},88324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(96767),r(89090),r(26083),r(35866);var s=r(23191),a=r(88716),i=r(37922),n=r.n(i),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["orders",{children:["[orderId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96767)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\orders\\[orderId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\orders\\[orderId]\\page.tsx"],u="/orders/[orderId]/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/orders/[orderId]/page",pathname:"/orders/[orderId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},38014:(e,t,r)=>{Promise.resolve().then(r.bind(r,85670)),Promise.resolve().then(r.bind(r,39775)),Promise.resolve().then(r.bind(r,23814)),Promise.resolve().then(r.bind(r,62737))},85670:(e,t,r)=>{"use strict";r.d(t,{BackButton:()=>n});var s=r(10326),a=r(35047),i=r(90772);function n(){let e=(0,a.useRouter)();return(0,s.jsxs)(i.Button,{variant:"ghost",size:"sm",onClick:()=>e.push("/orders"),className:"flex items-center gap-2",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s.jsx("path",{d:"m15 18-6-6 6-6"})}),"Back to Orders"]})}},39775:(e,t,r)=>{"use strict";r.d(t,{CompletePaymentButton:()=>l});var s=r(10326),a=r(17577),i=r(90772),n=r(57372),o=r(85999);function l({orderId:e}){let[t,r]=(0,a.useState)(!1);async function l(){r(!0);try{let t=await fetch("/api/payments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:e})});if(!t.ok)throw Error("Failed to create payment session");let r=await t.json();window.location.href=r.url}catch(e){o.A.error("Something went wrong. Please try again.")}finally{r(!1)}}return(0,s.jsxs)(i.Button,{className:"w-full",size:"lg",onClick:l,disabled:t,children:[t&&s.jsx(n.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Complete Payment"]})}},23814:(e,t,r)=>{"use strict";r.d(t,{QRCodeDisplay:()=>m});var s=r(10326),a=r(17577),i=r(33071),n=r(90772),o=r(57372),l=r(92447),d=r(32933),c=r(43810),u=r(34789),p=r(31270);function m({orderId:e,orderRef:t,status:r,isQrCodeProduct:m}){let[h,x]=(0,a.useState)([]),[f,g]=(0,a.useState)(!1),[v,j]=(0,a.useState)(null),[w,y]=(0,a.useState)(null),[b,N]=(0,a.useState)({}),{toast:C}=(0,u.pm)(),S=m&&("delivered"===r||"processing"===r),k=e=>e&&""!==e.trim()&&e.startsWith("LPA:"),A=async(e,t)=>{try{let r=await p.toDataURL(e,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});N(e=>({...e,[t]:r}))}catch(e){console.error("Error generating QR code:",e)}},P=async()=>{if(S){g(!0),j(null);try{let e=await fetch("/api/odoo/orders/qrcode",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({customer_order_ref:t})});if(!e.ok)throw Error("Failed to fetch QR codes");let r=await e.json();if(r?.result?.data?.[0]?.qrcode){let e=r.result.data[0].qrcode;x(e),e.forEach((e,t)=>{k(e.qrCodeContent)&&A(e.qrCodeContent,t)})}else j("No QR codes available yet")}catch(e){console.error("Error fetching QR codes:",e),j("Failed to load QR codes")}finally{g(!1)}}},_=async(e,t)=>{try{await navigator.clipboard.writeText(e),y(t),C({title:"Copied!",description:"LPA string copied to clipboard"}),setTimeout(()=>y(null),2e3)}catch(e){C({title:"Failed to copy",description:"Please copy the text manually",variant:"destructive"})}},Z=e=>{if(b[e]){let t=document.createElement("a");t.href=b[e],t.download=`esim-qrcode-${e+1}.png`,document.body.appendChild(t),t.click(),document.body.removeChild(t)}};return S?s.jsx(i.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(l.Z,{className:"h-5 w-5 text-blue-600"}),s.jsx("h2",{className:"font-semibold",children:"eSIM QR Codes"})]}),(0,s.jsxs)(n.Button,{variant:"outline",size:"sm",onClick:P,disabled:f,children:[f?s.jsx(o.P.spinner,{className:"h-4 w-4 animate-spin"}):s.jsx(o.P.refresh,{className:"h-4 w-4"}),"Refresh"]})]}),f&&0===h.length&&(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[s.jsx(o.P.spinner,{className:"h-6 w-6 animate-spin mr-2"}),s.jsx("span",{children:"Loading QR codes..."})]}),v&&(0,s.jsxs)("div",{className:"text-center py-8",children:[s.jsx("p",{className:"text-muted-foreground",children:v}),s.jsx(n.Button,{variant:"outline",size:"sm",onClick:P,className:"mt-2",children:"Try Again"})]}),h.length>0&&(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("div",{className:"text-sm text-muted-foreground",children:"Your eSIM activation codes are ready. Use it to activate your eSIM."}),h.map((e,t)=>{let r=k(e.qrCodeContent),a=b[t];return(0,s.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[r&&a&&s.jsx("div",{className:"flex justify-center",children:s.jsx("div",{className:"bg-white p-4 rounded-lg border",children:s.jsx("img",{src:a,alt:`eSIM QR Code ${t+1}`,className:"w-48 h-48"})})}),(0,s.jsxs)("div",{className:"flex justify-center gap-2 flex-wrap",children:[(0,s.jsxs)(n.Button,{variant:"outline",size:"sm",onClick:()=>_(e.qrCodeContent,t),className:"flex items-center gap-1",children:[w===t?s.jsx(d.Z,{className:"h-4 w-4 text-green-600"}):s.jsx(c.Z,{className:"h-4 w-4"}),w===t?"Copied!":"Copy LPA"]}),r&&a&&(0,s.jsxs)(n.Button,{variant:"outline",size:"sm",onClick:()=>Z(t),children:[s.jsx(l.Z,{className:"h-4 w-4 mr-1"}),"Download QR"]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded p-3",children:[s.jsx("p",{className:"text-xs text-muted-foreground mb-1",children:"LPA String:"}),s.jsx("p",{className:"font-mono text-sm break-all select-all",children:e.qrCodeContent})]}),s.jsx("div",{className:"text-xs text-muted-foreground",children:s.jsx("p",{children:"\uD83D\uDCF1 To activate: Go to Settings → Cellular → Add eSIM → Use QR Code"})}),!r&&s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded p-3",children:s.jsx("p",{className:"text-sm text-yellow-800",children:"⚠️ Invalid LPA format. QR code cannot be generated."})})]},t)})]})]})}):null}},62737:(e,t,r)=>{"use strict";r.d(t,{ProductLink:()=>n});var s=r(10326),a=r(17577),i=r(90434);function n({productCode:e,children:t}){let[r,n]=(0,a.useState)(null),[o,l]=(0,a.useState)(!0);return e&&!o&&r?s.jsx(i.default,{href:`/products/${r}`,target:"_blank",className:"font-medium hover:text-primary hover:underline",children:t}):s.jsx("span",{className:"font-medium",children:t})}},33071:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>l});var s=r(10326),a=r(17577),i=r(77863);let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},96767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var s=r(19510),a=r(45609),i=r(58585),n=r(72331),o=r(90455),l=r(21822),d=r(50650),c=r(46697),u=r(56881),p=r(18307),m=r(68570);let h=(0,m.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\back-button.tsx`),{__esModule:x,$$typeof:f}=h;h.default;let g=(0,m.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\back-button.tsx#BackButton`),v=(0,m.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\complete-payment-button.tsx`),{__esModule:j,$$typeof:w}=v;v.default;let y=(0,m.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\complete-payment-button.tsx#CompletePaymentButton`);var b=r(51170),N=r(88561);let C=e=>({PENDING:{label:"Pending",variant:"warning"},PAID:{label:"Paid",variant:"success"},CANCELLED:{label:"Cancelled",variant:"destructive"},todelivery:{label:"To Delivery",variant:"warning"},delivered:{label:"Delivered",variant:"success"},activated:{label:"Activated",variant:"success"},failed:{label:"Failed",variant:"destructive"}})[e]||{label:e,variant:"default"},S=e=>({waiting:{label:"Waiting",variant:"warning"},active:{label:"Active",variant:"success"},expired:{label:"Expired",variant:"destructive"},suspended:{label:"Suspended",variant:"destructive"}})[e]||{label:e,variant:"default"},k=e=>{if(!e)return null;let t=e.match(/(.+?)\s+(\d+)\s+(day|month)s?$/i);return t?{productName:t[1].trim(),duration:parseInt(t[2],10),durationType:t[3].toLowerCase()}:null},A=(e,t)=>e?.name==="System Default Address"&&e?.userId===null||!!t&&"object"==typeof t&&"name"in t&&"System Default Address"===t.name;function P({order:e,searchParams:t}){return s.jsx("div",{className:"container py-8",children:(0,s.jsxs)("div",{className:"space-y-8",children:[s.jsx("div",{className:"flex items-center gap-4",children:s.jsx(g,{})}),t.success&&s.jsx(c.Zb,{className:"p-6 bg-green-50",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 rounded-full bg-green-500 flex items-center justify-center",children:s.jsx(p.P.check,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold text-green-800",children:"Payment Successful"}),s.jsx("p",{className:"text-green-700",children:"Thank you for your purchase! Your order has been confirmed."})]})]})}),t.canceled&&s.jsx(c.Zb,{className:"p-6 bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 rounded-full bg-red-500 flex items-center justify-center",children:s.jsx(p.P.close,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold text-red-800",children:"Payment Canceled"}),s.jsx("p",{className:"text-red-700",children:"Your payment was canceled. Please try again."})]})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:"Order Details"}),(0,s.jsxs)("p",{className:"text-muted-foreground",children:["Order #",e.id," • Placed on"," ",d.CN.forUserSafe(e.createdAt)]})]}),s.jsx(u.C,{variant:C(e.status).variant,className:"h-7 px-3 text-sm",children:C(e.status).label})]}),"PENDING"===e.status&&s.jsx(c.Zb,{className:"p-6 bg-yellow-50 border-yellow-300",children:(0,s.jsxs)("div",{className:"flex flex-col items-start gap-4",children:[s.jsx("h2",{className:"text-lg font-semibold text-yellow-800 mb-2",children:"Complete Your Order"}),s.jsx(y,{orderId:e.id})]})}),(0,s.jsxs)("div",{className:"grid gap-8 lg:grid-cols-12",children:[(0,s.jsxs)("div",{className:"lg:col-span-8 space-y-6",children:[s.jsx(c.Zb,{children:s.jsx("div",{className:"divide-y",children:e.items.map(e=>(console.log(`[ORDER_DETAILS] Item ${e.id}: variantText=${e.variantText}`),(0,s.jsxs)("div",{className:"flex gap-4 p-6",children:[s.jsx("div",{className:"relative aspect-square h-24 w-24 overflow-hidden rounded-lg bg-gray-200 flex items-center justify-center",children:s.jsx(p.P.package,{className:"h-12 w-12 text-gray-400"})}),(0,s.jsxs)("div",{className:"flex flex-1 flex-col justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("div",{className:"flex items-center gap-2",children:s.jsx(b.o,{productCode:e.productCode,children:e.variantText||"Unknown Product"})}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Quantity: ",e.quantity]}),(0,s.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[(()=>{let t=k(e.variantText);return t?.duration&&t?.durationType?s.jsx("div",{className:"inline-flex flex-row rounded-lg bg-blue-100 px-3 py-2 text-sm text-blue-700 max-w-full",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(p.P.zap,{className:"mr-1 h-3 w-3 flex-shrink-0"}),(0,s.jsxs)("span",{className:"font-medium",children:[t.duration," ",t.durationType]})]})}):null})(),e.uid&&(0,s.jsxs)("div",{className:"inline-flex flex-col sm:flex-row rounded-lg bg-purple-100 px-3 py-2 text-sm text-purple-700 max-w-full",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(p.P.creditCard,{className:"mr-1 h-3 w-3 flex-shrink-0"}),s.jsx("span",{className:"font-medium",children:"UID:"})]}),s.jsx("div",{className:"mt-1 sm:mt-0 sm:ml-1 font-mono text-xs break-all",children:(0,d.QG)(e.uid)})]})]})]}),s.jsx("div",{className:"mt-2",children:s.jsx("p",{className:"font-medium",children:(0,d.T4)(e.price)})})]})]},e.id)))})}),(()=>{let t=e.items.some(e=>e.product?.category?.name?.toLowerCase()==="qr_code");if(!t)return null;let r=e.odooStatuses.find(t=>{let r=e.items.find(e=>e.variantCode===t.variantCode&&(null===t.uid||t.uid===e.uid));return r?.product?.category?.name?.toLowerCase()==="qr_code"});if(!r)return null;let a=r.variantCode||"default",i=r.uid||"no-uid",n=`${e.id}-${a}:::${i}`;return s.jsx(N.x,{orderId:e.id,orderRef:n,status:r.status,isQrCodeProduct:t})})()]}),(0,s.jsxs)("div",{className:"space-y-6 lg:col-span-4",children:[s.jsx(c.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"font-semibold",children:"Order Summary"}),(0,s.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{className:"text-muted-foreground",children:"Subtotal"}),s.jsx("span",{children:(0,d.T4)(e.total)})]}),(0,s.jsxs)("div",{className:"flex justify-between font-medium",children:[s.jsx("span",{children:"Total"}),s.jsx("span",{children:(0,d.T4)(e.total)})]})]})]})}),(e.shippingAddress||e.shippingAddressSnapshot)&&!A(e.shippingAddress,e.shippingAddressSnapshot)&&s.jsx(c.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[s.jsx("h2",{className:"font-semibold",children:"Shipping Address"}),s.jsx("div",{className:"mt-4 text-sm text-muted-foreground",children:e.shippingAddress?(0,s.jsxs)(s.Fragment,{children:[s.jsx("p",{children:e.shippingAddress.name}),s.jsx("p",{children:e.shippingAddress.phone}),s.jsx("p",{children:e.shippingAddress.address1}),e.shippingAddress.address2&&s.jsx("p",{children:e.shippingAddress.address2}),(0,s.jsxs)("p",{children:[e.shippingAddress.city,","," ",e.shippingAddress.state," ",e.shippingAddress.postalCode]}),s.jsx("p",{children:e.shippingAddress.country})]}):e.shippingAddressSnapshot?(0,s.jsxs)(s.Fragment,{children:[s.jsx("p",{children:e.shippingAddressSnapshot.name}),s.jsx("p",{children:e.shippingAddressSnapshot.phone}),s.jsx("p",{children:e.shippingAddressSnapshot.address1}),e.shippingAddressSnapshot.address2&&s.jsx("p",{children:e.shippingAddressSnapshot.address2}),(0,s.jsxs)("p",{children:[e.shippingAddressSnapshot.city,","," ",e.shippingAddressSnapshot.state," ",e.shippingAddressSnapshot.postalCode]}),s.jsx("p",{children:e.shippingAddressSnapshot.country})]}):null})]})}),(()=>{let t=e.odooStatuses.filter(e=>"default"!==e.variantCode),r=(t,r)=>e.items.find(e=>e.variantCode===t&&(null===r||e.uid===r)),a=e=>{let t=r(e.variantCode,e.uid);return t?.variantText?t.variantText:e.productName||e.variantCode},i=e.items.map((e,r)=>{let s=t.find(t=>t.variantCode===e.variantCode&&(null===t.uid||t.uid===e.uid));return s?{...s,itemIndex:r}:null}).filter(Boolean);return 0===i.length?null:s.jsx("div",{className:"space-y-4",children:i.map(t=>{let r=a(t);return s.jsx(c.Zb,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"font-semibold",children:"Shipping Status"}),i.length>1&&(0,s.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Item ",t.itemIndex+1," of ",e.items.length]})]}),t.lastCheckedAt&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Updated: ",d.CN.forUserSafe(t.lastCheckedAt)]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[i.length>1&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Product"}),s.jsx("p",{className:"text-sm font-medium",children:r})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Status"}),s.jsx(u.C,{variant:C(t.status).variant,className:"h-6 px-2 text-xs",children:C(t.status).label})]}),t.description&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Description"}),s.jsx("p",{className:"text-sm",children:t.description})]}),t.planState&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Plan State"}),s.jsx(u.C,{variant:S(t.planState).variant,className:"h-6 px-2 text-xs",children:S(t.planState).label})]}),t.trackingNumber&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Tracking Number"}),s.jsx("p",{className:"text-sm font-medium",children:t.trackingNumber})]}),t.uid&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Card UIDs"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:t.uid.split(",").map(e=>e.trim()).filter(e=>e.length>0).map((e,t)=>(0,s.jsxs)(u.C,{variant:"outline",className:"flex flex-col px-2 py-1.5 h-auto text-xs bg-purple-50 max-w-full",children:[s.jsx("span",{className:"font-medium",children:"UID:"}),s.jsx("span",{className:"mt-0.5 font-mono break-all",children:(0,d.QG)(e)})]},t))})]})]})]})},t.id)})})})()]})]})]})})}var _=r(7410);let Z=_.z.object({name:_.z.string(),phone:_.z.string(),address1:_.z.string(),address2:_.z.string().nullable().optional(),city:_.z.string(),state:_.z.string(),postalCode:_.z.string(),country:_.z.string()}).nullable();async function q(e){let t=await (0,a.getServerSession)(o.L);t||(0,i.redirect)("/api/auth/signin");let r=await n._.order.findUnique({where:{id:e},select:{id:!0,userId:!0,total:!0,status:!0,createdAt:!0,updatedAt:!0,addressId:!0,paymentId:!0,referralCode:!0,shippingAddressSnapshot:!0,items:{select:{id:!0,orderId:!0,productCode:!0,variantCode:!0,variantText:!0,quantity:!0,price:!0,uid:!0,lpaString:!0}},shippingAddress:!0,payment:!0,odooStatuses:{select:{id:!0,orderId:!0,variantCode:!0,odooOrderRef:!0,status:!0,description:!0,productName:!0,isDigital:!0,deliveredQty:!0,trackingNumber:!0,planState:!0,uid:!0,lastCheckedAt:!0,createdAt:!0,updatedAt:!0}}}});r&&r.userId===t.user.id||(0,i.notFound)();try{let e=await n._.odooOrderStatus.findMany({where:{orderId:r.id},select:{variantCode:!0,uid:!0}}),t=[];if(e.length>0)for(let s of e){let e=s.variantCode||"default",a=s.uid||"no-uid",i=`${r.id}-${e}:::${a}`;t.push(i)}else t.push(r.id);console.log(`[OrderDetails] Fetching Odoo status for order: ${r.id} with refs:`,t);let s=await l.h.queryOrderStatusMultiple(t);if(console.log(`[OrderDetails] Odoo API response for order ${r.id}:`,JSON.stringify(s,null,2)),s?.status==="success"||s?.status==="ok"){let e=s.result?.data;if(e&&e.length>0){for(let t of e){if(!t.order_lines||0===t.order_lines.length){console.log(`[OrderDetails] No order lines for order detail: ${t.customer_order_ref}`);continue}let e="default",s=null;if(t.customer_order_ref.includes(":::")){let r=t.customer_order_ref.split("-");if(r.length>1){let[t,a]=r.slice(1).join("-").split(":::");e=t||"default",s="no-uid"===a?null:a}}for(let a of t.order_lines){console.log(`[OrderDetails] Updating Odoo status for order ${r.id}, variant ${e}:`,{status:a.status,description:a.description,planState:a.data?.[0]?.plan_state});let t=[],i=s;!i&&a.data&&(Array.isArray(a.data)?(t=a.data.filter(e=>e&&e.uid).map(e=>e.uid)).length>0&&(i=t.join(",")):"object"==typeof a.data&&a.data.uid&&(i=a.data.uid));let o=i?i.replace(/[^0-9,]/g,""):null;console.log(`[OrderDetails] Processing order ${r.id}, variant ${e}, uid=${o||"none"}`);let l=await n._.odooOrderStatus.findFirst({where:{orderId:r.id,variantCode:e,uid:o}});l?await n._.odooOrderStatus.update({where:{id:l.id},data:{status:a.status,description:a.description,productName:a.product_name,isDigital:a.is_digital||!1,deliveredQty:a.delivered_qty||0,trackingNumber:a.tracking_number,planState:a.data?.[0]?.plan_state,lastCheckedAt:new Date}}):await n._.odooOrderStatus.create({data:{orderId:r.id,variantCode:e,status:a.status,description:a.description,productName:a.product_name,isDigital:a.is_digital||!1,deliveredQty:a.delivered_qty||0,trackingNumber:a.tracking_number,planState:a.data?.[0]?.plan_state,uid:o,lastCheckedAt:new Date}})}}console.log(`[OrderDetails] Odoo status updated successfully for order ${r.id}`)}else console.warn(`[OrderDetails] No order data found in Odoo response for order ${r.id}`)}else console.error(`[OrderDetails] Invalid Odoo API response for order ${r.id}:`,s)}catch(e){console.error(`[OrderDetails] Error fetching Odoo order status for order ${r.id}:`,e)}let s=await Promise.all(r.items.map(async e=>{if(!e.productCode)return{...e,product:null};let t=await n._.product.findFirst({where:{sku:e.productCode},include:{category:!0}});return{...e,product:t}}));try{let e=Z.parse(r.shippingAddressSnapshot);return{...r,items:s,shippingAddressSnapshot:e,odooStatus:null}}catch(e){return console.error(`[OrderDetails] Invalid shippingAddressSnapshot format for order ${r.id}:`,e),{...r,items:s,shippingAddressSnapshot:null,odooStatus:null}}}async function R({params:e,searchParams:t}){let r=await q(e.orderId);return r?s.jsx(P,{order:r,searchParams:t}):null}},18307:(e,t,r)=>{"use strict";r.d(t,{P:()=>Y});var s=r(19510),a=r(27698),i=r(69379),n=r(19541),o=r(55848),l=r(60394),d=r(92088),c=r(59023),u=r(83113),p=r(81747),m=r(33167),h=r(17612),x=r(26299),f=r(86678),g=r(94126),v=r(24869),j=r(69013),w=r(67314),y=r(14120),b=r(98969),N=r(22486),C=r(1733),S=r(97968),k=r(99721),A=r(63129),P=r(26978),_=r(44506),Z=r(2720),q=r(77666),R=r(83218),E=r(31958),I=r(15109),O=r(71807),D=r(72298),L=r(62014),$=r(90204),T=r(50657),z=r(23474),U=r(32268),M=r(5476),B=r(30039),F=r(49223),Q=r(23698),G=r(49622),H=r(74780),W=r(26359),V=r(44244);let Y={logo:a.Z,close:i.Z,spinner:n.Z,chevronLeft:o.Z,chevronRight:l.Z,chevronDown:d.Z,trash:c.Z,post:u.Z,page:p.Z,media:m.Z,settings:h.Z,billing:x.Z,creditCard:x.Z,ellipsis:f.Z,add:g.Z,warning:v.Z,user:j.Z,category:w.Z,clock:y.Z,info:b.Z,inbox:N.Z,alertTriangle:v.Z,users:({...e})=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[s.jsx("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),s.jsx("circle",{cx:"9",cy:"7",r:"4"}),s.jsx("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),s.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]}),arrowRight:C.Z,help:S.Z,pizza:k.Z,sun:A.Z,moon:P.Z,laptop:_.Z,cart:Z.Z,globe:q.Z,shield:R.Z,zap:E.Z,minus:I.Z,image:m.Z,logout:O.Z,package:D.Z,edit:L.Z,check:$.Z,download:T.Z,refresh:z.Z,truck:U.Z,eye:M.Z,eyeOff:B.Z,undo:F.Z,variantIcon:Q.Z,qrCode:G.Z,dollarSign:H.Z,trendingUp:W.Z,trendingDown:V.Z,twitter:({...e})=>s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",...e,children:s.jsx("path",{fill:"currentColor",d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),facebook:({...e})=>s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",...e,children:s.jsx("path",{fill:"currentColor",d:"M9.101 23.691v-7.98H6.627v-3.667h2.474v-1.58c0-4.085 1.848-5.978 5.858-5.978.401 0 .955.042 1.468.103a8.68 8.68 0 0 1 1.141.195v3.325a8.623 8.623 0 0 0-.653-.036 26.805 26.805 0 0 0-.733-.009c-.707 0-1.259.096-1.675.309a1.686 1.686 0 0 0-.679.622c-.258.42-.374.995-.374 1.752v1.297h3.919l-.386 2.103-.287 1.564h-3.246v8.245C19.396 23.238 24 18.179 24 12.044c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.628 3.874 10.35 9.101 11.647Z"})}),instagram:({...e})=>s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",...e,children:s.jsx("path",{fill:"currentColor",d:"M12 2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153a4.908 4.908 0 0 1 1.153 1.772c.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 0 1-1.153 1.772 4.915 4.915 0 0 1-1.772 1.153c-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 0 1-1.772-1.153 4.904 4.904 0 0 1-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 0 1 1.153-1.772A4.897 4.897 0 0 1 5.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2zm0 1.802c-2.67 0-2.986.01-4.04.059-.976.045-1.505.207-1.858.344-.466.182-.8.398-1.15.748-.35.35-.566.684-.748 1.15-.137.353-.3.882-.344 1.857-.048 1.055-.058 1.37-.058 4.04 0 2.67.01 2.986.058 4.04.045.976.207 1.505.344 1.858.182.466.399.8.748 1.15.35.35.684.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.04.058 2.67 0 2.987-.01 4.04-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.684.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.04 0-2.67-.01-2.986-.058-4.04-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 0 0-.748-1.15 3.098 3.098 0 0 0-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.054-.048-1.37-.058-4.04-.058zm0 3.063a5.135 5.135 0 1 1 0 10.27 5.135 5.135 0 0 1 0-10.27zm0 8.468a3.333 3.333 0 1 0 0-6.666 3.333 3.333 0 0 0 0 6.666zm6.538-8.671a1.2 1.2 0 1 1-2.4 0 1.2 1.2 0 0 1 2.4 0z"})}),linkedin:({...e})=>s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",...e,children:s.jsx("path",{fill:"currentColor",d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 0 1-2.063-2.065 2.064 2.064 0 1 1 2.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),mail:({...e})=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[s.jsx("rect",{width:"20",height:"16",x:"2",y:"4",rx:"2"}),s.jsx("path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"})]}),mapPin:({...e})=>(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[s.jsx("path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"}),s.jsx("circle",{cx:"12",cy:"10",r:"3"})]}),phone:({...e})=>s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:s.jsx("path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"})}),google:({...e})=>s.jsx("svg",{"aria-hidden":"true",focusable:"false","data-prefix":"fab","data-icon":"google",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 488 512",...e,children:s.jsx("path",{fill:"currentColor",d:"M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"})})}},88561:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\orders\qr-code-display.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let o=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\orders\qr-code-display.tsx#QRCodeDisplay`)},51170:(e,t,r)=>{"use strict";r.d(t,{o:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\product-link.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let o=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\product-link.tsx#ProductLink`)},56881:(e,t,r)=>{"use strict";let s,a;r.d(t,{C:()=>u});var i=r(19510);r(71159);var n=r(55761);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.W;var d=r(50650);let c=(s="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",a={variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}},e=>{var t;if((null==a?void 0:a.variants)==null)return l(s,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:i}=a,n=Object.keys(r).map(t=>{let s=null==e?void 0:e[t],a=null==i?void 0:i[t];if(null===s)return null;let n=o(s)||o(a);return r[t][n]}),d=e&&Object.entries(e).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return l(s,n,null==a?void 0:null===(t=a.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...d}[t]):({...i,...d})[t]===r})?[...e,r,s]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function u({className:e,variant:t,...r}){return i.jsx("div",{className:(0,d.cn)(c({variant:t}),e),...r})}},46697:(e,t,r)=>{"use strict";r.d(t,{Zb:()=>n});var s=r(19510),a=r(71159),i=r(50650);let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t})).displayName="CardContent",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},90455:(e,t,r)=>{"use strict";r.d(t,{L:()=>c});var s=r(7585),a=r(72331),i=r(77234),n=r(53797),o=r(42023),l=r.n(o),d=r(93475);let c={adapter:{...(0,s.N)(a._),getUser:async e=>{let t=await a._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await a._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,n.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await a._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await l().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,d.Ak)(e.email);if(!t||t!==e.code)return null;await (0,d.qc)(e.email);let r=await a._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await a._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await a._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:s,request:i}){try{if(r&&r.id){let t=i?.headers||new Headers,n=t.get("user-agent")||"",o=t.get("x-forwarded-for"),l=o?o.split(/, /)[0]:t.get("REMOTE_ADDR")||"",d="unknown";s?d=s.code&&!s.password?"email_code":"password":e&&(d=e.provider),await a._.userLoginHistory.create({data:{userId:r.id,ipAddress:l||null,userAgent:n||null,loginMethod:d,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],s=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,a=new URL(s).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let s=new URL(e);if(r.some(e=>s.hostname===e||s.hostname.includes(e)||s.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(s);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return s}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:s}){if("update"===r&&s)return{...e,...s.user};let i=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return i?{id:i.id,name:i.name,email:i.email,picture:i.image,role:i.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},21822:(e,t,r)=>{"use strict";r.d(t,{h:()=>s.h2});var s=r(10835)},93475:(e,t,r)=>{"use strict";r.d(t,{AL:()=>o,Ak:()=>l,qc:()=>d,yz:()=>c});var s=r(62197),a=r.n(s);let i=null;function n(){if(!i){let e=process.env.REDIS_URL||"redis://localhost:6379";(i=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),i.on("connect",()=>{console.log("Successfully connected to Redis")})}return i}async function o(e,t,r=300){try{let s=n(),a=`verification_code:${e}`;return await s.setex(a,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let t=n(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function d(e){try{let t=n(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let s=n(),a=`rate_limit:${e}`,i=await s.get(a),o=i?parseInt(i):0;if(o>=t)return!1;return 0===o?await s.setex(a,r,"1"):await s.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,7410,9862,9712,6499,4824,835],()=>r(88324));module.exports=s})();