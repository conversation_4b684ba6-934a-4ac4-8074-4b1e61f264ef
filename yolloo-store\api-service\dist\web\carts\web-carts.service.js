"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebCartsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let WebCartsService = class WebCartsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getCart(userId) {
        const cartItems = await this.prisma.cartItem.findMany({
            where: { userId },
            include: {
                product: true,
                variant: true,
            },
            orderBy: { createdAt: 'desc' },
        });
        const total = cartItems.reduce((sum, item) => {
            const price = item.variant?.price || item.product.price;
            return sum + (Number(price) * item.quantity);
        }, 0);
        return {
            items: cartItems,
            total,
            count: cartItems.length,
        };
    }
    async addToCart(userId, addToCartDto) {
        const { productId, variantId, quantity = 1 } = addToCartDto;
        const existingItem = await this.prisma.cartItem.findUnique({
            where: {
                userId_productId_variantId: {
                    userId,
                    productId,
                    variantId: variantId || null,
                },
            },
        });
        if (existingItem) {
            return await this.prisma.cartItem.update({
                where: { id: existingItem.id },
                data: { quantity: existingItem.quantity + quantity },
                include: {
                    product: true,
                    variant: true,
                },
            });
        }
        else {
            return await this.prisma.cartItem.create({
                data: {
                    userId,
                    productId,
                    variantId,
                    quantity,
                },
                include: {
                    product: true,
                    variant: true,
                },
            });
        }
    }
    async updateCartItem(cartItemId, updateCartItemDto) {
        return await this.prisma.cartItem.update({
            where: { id: cartItemId },
            data: updateCartItemDto,
            include: {
                product: true,
                variant: true,
            },
        });
    }
    async removeFromCart(cartItemId) {
        return await this.prisma.cartItem.delete({
            where: { id: cartItemId },
        });
    }
};
WebCartsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WebCartsService);
exports.WebCartsService = WebCartsService;
//# sourceMappingURL=web-carts.service.js.map