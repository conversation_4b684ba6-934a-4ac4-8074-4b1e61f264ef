import { Module } from '@nestjs/common';
import { GeographyController } from './geography.controller';
import { GeographyService } from './geography.service';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';
import { RatingModule } from '../rating/rating.module';

@Module({
  imports: [AuthModule, RatingModule],
  controllers: [GeographyController],
  providers: [GeographyService, PrismaService],
  exports: [GeographyService],
})
export class GeographyModule {}
