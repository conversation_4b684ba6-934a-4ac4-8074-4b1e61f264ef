"use strict";(()=>{var e={};e.id=8573,e.ids=[8573],e.modules={53524:e=>{e.exports=require("@prisma/client")},47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},64196:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>d}),t(88917),t(75718),t(85460),t(89090),t(26083),t(35866);var o=t(23191),s=t(88716),n=t(37922),a=t.n(n),i=t(95231),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);t.d(r,p);let d=["",{children:["admin",{children:["products",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,88917)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\new\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,75718)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\new\\page.tsx"],u="/admin/products/new/page",c={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/admin/products/new/page",pathname:"/admin/products/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88917:(e,r,t)=>{t.r(r),t.d(r,{default:()=>p,metadata:()=>a});var o=t(19510),s=t(72331),n=t(20045);let a={title:"New Product",description:"Create a new product"};async function i(){return await s._.category.findMany({orderBy:{name:"asc"}})}async function p(){let e=await i();return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[o.jsx("h3",{className:"text-lg font-medium",children:"New Product"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Add a new product to your store"})]}),o.jsx(n.H,{categories:e})]})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,3239,4824,7123,6208,1453],()=>t(64196));module.exports=o})();