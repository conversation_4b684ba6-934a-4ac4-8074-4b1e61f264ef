(()=>{var e={};e.id=6140,e.ids=[6140],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},7162:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),s(82740),s(85460),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),i=s(37922),o=s.n(i),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d=["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,82740)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\settings\\page.tsx"],m="/admin/settings/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},32404:(e,t,s)=>{Promise.resolve().then(s.bind(s,32984))},32984:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(10326),a=s(17577),i=s(74064),o=s(74723),n=s(27256),l=s(90772),d=s(33071),c=s(57372),m=s(85999),u=s(11412),x=s(15940),p=s(99440),f=s(567);function h(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[o,n]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[j,y]=(0,a.useState)(null),[b,v]=(0,a.useState)({}),N=async()=>{i(!0);try{let e=await fetch("/api/admin/categories");if(!e.ok)throw Error("Failed to fetch categories");let s=await e.json();t(s);let r={};await Promise.all(s.map(async e=>{let t=await fetch(`/api/admin/categories/${e.id}`);if(t.ok){let s=await t.json();r[e.id]=s._count.products}})),v(r)}catch(e){console.error(e),m.A.error("Failed to load categories")}finally{i(!1)}},w=async()=>{if(j)try{let e=await fetch(`/api/admin/categories/${j.id}`,{method:"DELETE"});if(!e.ok){let t=await e.text();throw Error(t||"Failed to delete category")}m.A.success("Category deleted successfully"),N()}catch(e){console.error(e),m.A.error(e instanceof Error?e.message:"Something went wrong")}finally{g(!1),y(null)}},C=e=>{y(e),g(!0)};return(0,r.jsxs)(d.Zb,{className:"p-6 mt-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium",children:"Category Management"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage product categories for your store"})]}),(0,r.jsxs)(l.Button,{onClick:()=>n(!0),size:"sm",children:[r.jsx(c.P.add,{className:"mr-2 h-4 w-4"}),"Add Category"]})]}),r.jsx(d.aY,{className:"p-0",children:s?r.jsx("div",{className:"flex justify-center items-center py-8",children:r.jsx(c.P.spinner,{className:"h-6 w-6 animate-spin"})}):0===e.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[r.jsx(c.P.category,{className:"h-10 w-10 text-muted-foreground mb-2"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"No categories found"}),r.jsx(l.Button,{onClick:()=>n(!0),variant:"outline",size:"sm",className:"mt-2",children:"Add your first category"})]}):r.jsx("div",{className:"rounded-md border",children:(0,r.jsxs)(x.iA,{children:[r.jsx(x.xD,{children:(0,r.jsxs)(x.SC,{children:[r.jsx(x.ss,{className:"pl-6",children:"Name"}),r.jsx(x.ss,{children:"Description"}),r.jsx(x.ss,{className:"text-center",children:"Products"}),r.jsx(x.ss,{className:"text-right pr-6",children:"Actions"})]})}),r.jsx(x.RM,{children:e.map(e=>(0,r.jsxs)(x.SC,{children:[r.jsx(x.pj,{className:"font-medium pl-6",children:e.name}),r.jsx(x.pj,{className:"max-w-[300px] truncate",children:e.description||"—"}),r.jsx(x.pj,{className:"text-center",children:r.jsx(f.C,{variant:"outline",children:b[e.id]||0})}),r.jsx(x.pj,{className:"text-right pr-6",children:r.jsx(l.Button,{variant:"destructive",size:"sm",onClick:()=>C(e),disabled:b[e.id]>0,title:b[e.id]>0?"Cannot delete category with associated products":"Delete category",children:r.jsx(c.P.trash,{className:"h-4 w-4"})})})]},e.id))})]})})})]}),r.jsx(u.a,{open:o,onOpenChange:n,onSuccess:N}),r.jsx(p.aR,{open:h,onOpenChange:g,children:(0,r.jsxs)(p._T,{children:[(0,r.jsxs)(p.fY,{children:[r.jsx(p.f$,{children:"Are you sure?"}),(0,r.jsxs)(p.yT,{children:['This will permanently delete the category "',j?.name,'". This action cannot be undone.']})]}),(0,r.jsxs)(p.xo,{children:[r.jsx(p.le,{children:"Cancel"}),r.jsx(p.OL,{onClick:w,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete"})]})]})})]})}var g=s(55632),j=s(34474),y=s(68483);let b=n.Ry({siteName:n.Z_().min(2).max(50),language:n.Z_(),theme:n.Z_()}),v=[{label:"English",value:"en"},{label:"中文",value:"zh"}],N=[{label:"Light",value:"light"},{label:"Dark",value:"dark"},{label:"System",value:"system"}];function w(){let[e,t]=(0,a.useState)(!0),s=(0,o.cI)({resolver:(0,i.F)(b),defaultValues:{siteName:"",language:"en",theme:"system"}});async function n(e){try{if(!(await fetch("/api/admin/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Failed to update settings");m.A.success("Settings updated successfully")}catch(e){console.error(e),m.A.error("Failed to update settings")}}return e?r.jsx("div",{className:"flex h-[450px] items-center justify-center",children:r.jsx(c.P.spinner,{className:"h-8 w-8 animate-spin"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-medium",children:"Settings"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage your website settings"})]}),r.jsx(y.Separator,{}),r.jsx(g.l0,{...s,children:(0,r.jsxs)("form",{onSubmit:s.handleSubmit(n),className:"space-y-8",children:[r.jsx(d.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium",children:"General Settings"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Configure general website settings"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(g.Wi,{control:s.control,name:"language",render:({field:e})=>(0,r.jsxs)(g.xJ,{children:[r.jsx(g.lX,{children:"Language"}),(0,r.jsxs)(j.Ph,{onValueChange:e.onChange,defaultValue:e.value,children:[r.jsx(g.NI,{children:r.jsx(j.i4,{children:r.jsx(j.ki,{placeholder:"Select a language"})})}),r.jsx(j.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:v.map(e=>r.jsx(j.Ql,{value:e.value,children:e.label},e.value))})]}),r.jsx(g.pf,{children:"Select the default language for your website"}),r.jsx(g.zG,{})]})}),r.jsx(g.Wi,{control:s.control,name:"theme",render:({field:e})=>(0,r.jsxs)(g.xJ,{children:[r.jsx(g.lX,{children:"Theme"}),(0,r.jsxs)(j.Ph,{onValueChange:e.onChange,defaultValue:e.value,children:[r.jsx(g.NI,{children:r.jsx(j.i4,{children:r.jsx(j.ki,{placeholder:"Select a theme"})})}),r.jsx(j.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:N.map(e=>r.jsx(j.Ql,{value:e.value,children:e.label},e.value))})]}),r.jsx(g.pf,{children:"Select the default theme for your website"}),r.jsx(g.zG,{})]})})]})]})}),r.jsx("div",{className:"flex justify-end",children:(0,r.jsxs)(l.Button,{type:"submit",children:[s.formState.isSubmitting&&r.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Save Changes"]})})]})}),r.jsx(h,{})]})}},99440:(e,t,s)=>{"use strict";s.d(t,{OL:()=>h,_T:()=>m,aR:()=>n,f$:()=>p,fY:()=>u,le:()=>g,vW:()=>l,xo:()=>x,yT:()=>f});var r=s(10326),a=s(17577),i=s(12194),o=s(77863);let n=i.fC,l=i.xz,d=i.h_,c=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aV,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));c.displayName=i.aV.displayName;let m=a.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(d,{children:[r.jsx(c,{}),r.jsx(i.VY,{ref:s,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));m.displayName=i.VY.displayName;let u=({className:e,...t})=>r.jsx("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});u.displayName="AlertDialogHeader";let x=({className:e,...t})=>r.jsx("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});x.displayName="AlertDialogFooter";let p=a.forwardRef(({className:e,...t},s)=>r.jsx(i.Dx,{ref:s,className:(0,o.cn)("text-lg font-semibold",e),...t}));p.displayName=i.Dx.displayName;let f=a.forwardRef(({className:e,...t},s)=>r.jsx(i.dk,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=i.dk.displayName;let h=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aU,{ref:s,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",e),...t}));h.displayName=i.aU.displayName;let g=a.forwardRef(({className:e,...t},s)=>r.jsx(i.$j,{ref:s,className:(0,o.cn)("mt-2 inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-semibold ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 sm:mt-0",e),...t}));g.displayName=i.$j.displayName},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var r=s(10326);s(17577);var a=s(79360),i=s(77863);let o=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...s}){return r.jsx("div",{className:(0,i.cn)(o({variant:t}),e),...s})}},55632:(e,t,s)=>{"use strict";s.d(t,{NI:()=>h,Wi:()=>m,l0:()=>d,lX:()=>f,pf:()=>g,xJ:()=>p,zG:()=>j});var r=s(10326),a=s(17577),i=s(34214),o=s(74723),n=s(77863),l=s(31048);let d=o.RV,c=a.createContext({}),m=({...e})=>r.jsx(c.Provider,{value:{name:e.name},children:r.jsx(o.Qr,{...e})}),u=()=>{let e=a.useContext(c),t=a.useContext(x),{getFieldState:s,formState:r}=(0,o.Gc)(),i=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...i}},x=a.createContext({}),p=a.forwardRef(({className:e,...t},s)=>{let i=a.useId();return r.jsx(x.Provider,{value:{id:i},children:r.jsx("div",{ref:s,className:(0,n.cn)("space-y-2",e),...t})})});p.displayName="FormItem";let f=a.forwardRef(({className:e,...t},s)=>{let{error:a,formItemId:i}=u();return r.jsx(l._,{ref:s,className:(0,n.cn)(a&&"text-destructive",e),htmlFor:i,...t})});f.displayName="FormLabel";let h=a.forwardRef(({...e},t)=>{let{error:s,formItemId:a,formDescriptionId:o,formMessageId:n}=u();return r.jsx(i.g7,{ref:t,id:a,"aria-describedby":s?`${o} ${n}`:`${o}`,"aria-invalid":!!s,...e})});h.displayName="FormControl";let g=a.forwardRef(({className:e,...t},s)=>{let{formDescriptionId:a}=u();return r.jsx("p",{ref:s,id:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})});g.displayName="FormDescription";let j=a.forwardRef(({className:e,children:t,...s},a)=>{let{error:i,formMessageId:o}=u(),l=i?String(i?.message):t;return l?r.jsx("p",{ref:a,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",e),...s,children:l}):null});j.displayName="FormMessage"},68483:(e,t,s)=>{"use strict";s.d(t,{Separator:()=>c});var r=s(10326),a=s(17577),i=s(45226),o="horizontal",n=["horizontal","vertical"],l=a.forwardRef((e,t)=>{let{decorative:s,orientation:a=o,...l}=e,d=n.includes(a)?a:o;return(0,r.jsx)(i.WV.div,{"data-orientation":d,...s?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var d=s(77863);let c=a.forwardRef(({className:e,orientation:t="horizontal",decorative:s=!0,...a},i)=>r.jsx(l,{ref:i,decorative:s,orientation:t,className:(0,d.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));c.displayName=l.displayName},15940:(e,t,s)=>{"use strict";s.d(t,{RM:()=>l,SC:()=>d,iA:()=>o,pj:()=>m,ss:()=>c,xD:()=>n});var r=s(10326),a=s(17577),i=s(77863);let o=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...t})}));o.displayName="Table";let n=a.forwardRef(({className:e,...t},s)=>r.jsx("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...t}));n.displayName="TableHeader";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>r.jsx("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("th",{ref:s,className:(0,i.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));c.displayName="TableHead";let m=a.forwardRef(({className:e,...t},s)=>r.jsx("td",{ref:s,className:(0,i.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));m.displayName="TableCell",a.forwardRef(({className:e,...t},s)=>r.jsx("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},82740:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>i,default:()=>n});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\settings\page.tsx`),{__esModule:i,$$typeof:o}=a;a.default;let n=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\settings\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,6908,2194,4824,7123,6208],()=>s(7162));module.exports=r})();