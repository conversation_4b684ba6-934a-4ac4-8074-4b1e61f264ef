import { PrismaService } from '../prisma.service';
import { UserPackagesQueryDto, ActivatePackageDto, UsageStatsQueryDto, UsageHistoryQueryDto } from './dto/user-packages-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class UserPackagesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getUserPackages(userId: string, query: UserPackagesQueryDto, ctx: RequestContext): Promise<{
        packages: never[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        statusCounts?: undefined;
    } | {
        packages: {
            id: any;
            name: any;
            description: string;
            packageType: string;
            status: string;
            statusText: string;
            dataSize: string;
            usedData: string;
            usagePercentage: number;
            validUntil: string;
            activatedAt: any;
            price: number;
            currency: string;
            features: string[];
            imageUrl: any;
            progressColor: string;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        statusCounts: {
            activating: number;
            to_be_activated: number;
            expired: number;
            all: number;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getPackageById(userId: string, packageId: string, ctx: RequestContext): Promise<{
        remainingData: string;
        detailedInfo: {
            activation: string;
            coverage: string;
            speed: string;
            usage: string;
        };
        usageHistory: {
            date: string;
            usage: string;
            description: string;
        }[];
        restrictions: string[];
        id: any;
        name: any;
        description: string;
        packageType: string;
        status: string;
        statusText: string;
        dataSize: string;
        usedData: string;
        usagePercentage: number;
        validUntil: string;
        activatedAt: any;
        price: number;
        currency: string;
        features: string[];
        imageUrl: any;
        progressColor: string;
    } | {
        error: string;
        packageId: string;
    }>;
    activatePackage(userId: string, activateData: ActivatePackageDto, ctx: RequestContext): Promise<{
        activation: {
            id: string;
            packageId: string;
            userId: string;
            status: string;
            statusText: string;
            activatedAt: string;
            estimatedCompletionTime: string;
        };
        message: string;
    }>;
    getPackageUsageStats(userId: string, packageId: string, query: UsageStatsQueryDto, ctx: RequestContext): Promise<{
        packageInfo: {
            id: any;
            name: any;
            description: string;
            validUntil: any;
            status: any;
            statusText: string;
            canUpgrade: boolean;
            upgradeText: string;
        };
        usageOverview: {
            totalData: string;
            usedData: string;
            remainingData: string;
            usagePercentage: number;
            remainingPercentage: number;
            usedText: string;
            remainingText: string;
            progressColor: string;
        };
        actions: {
            id: string;
            text: string;
            icon: string;
            type: string;
            action: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    } | {
        error: string;
        packageId: string;
    }>;
    private formatUsageStats;
    getPackageUsageHistory(userId: string, packageId: string, query: UsageHistoryQueryDto, ctx: RequestContext): Promise<{
        period: "monthly" | "daily" | "weekly";
        chartTitle: string;
        chartData: any[];
        summary: {
            totalUsage: string;
            averageDaily: string;
            peakDay: string;
            peakUsage: string;
        };
        chartConfig: {
            xAxisLabel: string;
            yAxisLabel: string;
            barColor: string;
            highlightColor: string;
            gridColor: string;
        };
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    private formatOrderItemAsPackage;
    private formatOrderItemAsDetailedPackage;
    private determinePackageStatus;
    private getStatusText;
    private getProgressColor;
    private determinePackageType;
    private generateUsagePercentage;
    private calculateUsedData;
    private calculateRemainingData;
    private calculateValidUntil;
    private generateUsageHistory;
    private parseDataSize;
    private formatDataSize;
}
