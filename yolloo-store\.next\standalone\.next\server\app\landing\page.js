(()=>{var e={};e.id=8582,e.ids=[8582],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},55238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(78913),s(89090),s(26083),s(35866);var i=s(23191),r=s(88716),n=s(37922),a=s.n(n),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["landing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,78913)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\landing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\landing\\page.tsx"],u="/landing/page",h={require:s,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/landing/page",pathname:"/landing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88975:(e,t,s)=>{Promise.resolve().then(s.bind(s,15986)),Promise.resolve().then(s.bind(s,19476)),Promise.resolve().then(s.bind(s,73598)),Promise.resolve().then(s.bind(s,11734)),Promise.resolve().then(s.bind(s,95419)),Promise.resolve().then(s.bind(s,89823)),Promise.resolve().then(s.bind(s,21749)),Promise.resolve().then(s.bind(s,93318))},15986:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var i=s(10326),r=s(77626),n=s.n(r),a=s(46226),o=s(17577);let l=o.forwardRef(function({title:e,titleId:t,...s},i){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 19.5 8.25 12l7.5-7.5"}))}),d=o.forwardRef(function({title:e,titleId:t,...s},i){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))});function c(){let[e,t]=(0,o.useState)(0),s=[{icon:"/cs7.svg",title:"Global Coverage Made Simple",description:"Access mobile data worldwide with one Yolloo Card – enjoy seamless connectivity across 190+ countries without changing physical SIM cards."},{icon:"/cs8.svg",title:"eSIM-Like Convenience",description:"Switch between countries and plans as easily as downloading an eSIM – no physical limitations, just digital flexibility for all your travels."},{icon:"/cs9.svg",title:"Device Freedom",description:"Works with all compatible phones regardless of region – no device restrictions or compatibility issues to worry about."},{icon:"/cs10.svg",title:"Better Value, Lower Cost",description:"Enjoy premium connectivity at competitive prices – our optimized network partnerships mean significant savings on your global data."},{icon:"/cs11.svg",title:"Borderless Experience",description:"Travel freely between countries without hunting for local SIMs – your Yolloo Card provides seamless connectivity wherever business takes you."},{icon:"/cs12.svg",title:"Secure & Reliable Connection",description:"Advanced encryption and proven technology ensures your connection remains private, secure, and fast wherever you travel."}];return i.jsx("section",{id:"choose-us",className:"py-20 bg-white",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 container mx-auto px-4",children:[i.jsx("div",{className:"jsx-f265edf51ed40379 text-center mb-16",children:i.jsx("h2",{className:"jsx-f265edf51ed40379 text-4xl sm:text-5xl font-bold mb-6 text-[#D63A5A] leading-tight",children:"Why choose Yolloo?"})}),i.jsx("div",{className:"jsx-f265edf51ed40379 max-w-6xl mx-auto mb-20",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 grid lg:grid-cols-2 gap-12 items-center",children:[i.jsx("div",{className:"jsx-f265edf51ed40379 flex justify-center",children:i.jsx("div",{className:"jsx-f265edf51ed40379 w-32 h-32 relative",children:i.jsx(a.default,{src:"/cs1.svg",alt:"Yolloo character",fill:!0,className:"object-contain"})})}),(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 text-right",children:[i.jsx("h3",{className:"jsx-f265edf51ed40379 text-3xl font-bold text-[#D63A5A] mb-4",children:"Digital Connectivity Made Simple"}),i.jsx("p",{className:"jsx-f265edf51ed40379 text-lg text-gray-600 leading-relaxed",children:"Experience the future of mobile connectivity with our cutting-edge eSIM technology, designed for modern travelers and digital nomads."})]})]})}),i.jsx("div",{className:"jsx-f265edf51ed40379 max-w-7xl mx-auto mb-20 relative",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 relative min-h-[700px] flex items-center justify-center",children:[i.jsx("div",{style:{transform:"translate(150px, 100px)"},className:"jsx-f265edf51ed40379 absolute top-0 left-0 w-96 z-10 animate-float-1",children:i.jsx("div",{className:"jsx-f265edf51ed40379 bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex items-start gap-4",children:[i.jsx("div",{className:"jsx-f265edf51ed40379 w-16 h-16 relative flex-shrink-0",children:i.jsx(a.default,{src:"/cs3.svg",alt:"Global Network",fill:!0,className:"object-contain"})}),(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379",children:[i.jsx("h3",{className:"jsx-f265edf51ed40379 font-bold text-xl mb-3 text-[#D63A5A]",children:"Global Network"}),i.jsx("p",{className:"jsx-f265edf51ed40379 text-gray-600 leading-relaxed",children:"Access premium carriers in 190+ countries with seamless network switching"})]})]})})}),i.jsx("div",{style:{transform:"translate(-150px, 50px)"},className:"jsx-f265edf51ed40379 absolute top-0 right-0 w-96 z-10 animate-float-2",children:i.jsx("div",{className:"jsx-f265edf51ed40379 bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex items-start gap-4",children:[i.jsx("div",{className:"jsx-f265edf51ed40379 w-16 h-16 relative flex-shrink-0",children:i.jsx(a.default,{src:"/cs2.svg",alt:"Instant Activation",fill:!0,className:"object-contain"})}),(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379",children:[i.jsx("h3",{className:"jsx-f265edf51ed40379 font-bold text-xl mb-3 text-[#D63A5A]",children:"Instant Activation"}),i.jsx("p",{className:"jsx-f265edf51ed40379 text-gray-600 leading-relaxed",children:"Get connected within minutes using our streamlined QR code activation process"})]})]})})}),i.jsx("div",{className:"jsx-f265edf51ed40379 w-[500px] h-[500px] relative z-20 transform rotate-12 animate-pulse-slow",children:i.jsx(a.default,{src:"/yolloo.svg",alt:"Yolloo Card",fill:!0,className:"object-contain drop-shadow-2xl"})}),i.jsx("div",{style:{transform:"translate(150px, -50px)"},className:"jsx-f265edf51ed40379 absolute bottom-0 left-0 w-96 z-10 animate-float-3",children:i.jsx("div",{className:"jsx-f265edf51ed40379 bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex items-start gap-4",children:[i.jsx("div",{className:"jsx-f265edf51ed40379 w-16 h-16 relative flex-shrink-0",children:i.jsx(a.default,{src:"/cs5.svg",alt:"Travel Freedom",fill:!0,className:"object-contain"})}),(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379",children:[i.jsx("h3",{className:"jsx-f265edf51ed40379 font-bold text-xl mb-3 text-[#D63A5A]",children:"Travel Freedom"}),i.jsx("p",{className:"jsx-f265edf51ed40379 text-gray-600 leading-relaxed",children:"Say goodbye to physical SIMs and enjoy hassle-free international roaming"})]})]})})}),i.jsx("div",{style:{transform:"translate(-150px, -100px)"},className:"jsx-f265edf51ed40379 absolute bottom-0 right-0 w-96 z-10 animate-float-4",children:i.jsx("div",{className:"jsx-f265edf51ed40379 bg-[#FFF0F0] p-6 rounded-2xl border border-pink-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex items-start gap-4",children:[i.jsx("div",{className:"jsx-f265edf51ed40379 w-16 h-16 relative flex-shrink-0",children:i.jsx(a.default,{src:"/cs4.svg",alt:"Smart Compatible",fill:!0,className:"object-contain"})}),(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379",children:[i.jsx("h3",{className:"jsx-f265edf51ed40379 font-bold text-xl mb-3 text-[#D63A5A]",children:"Smart Compatible"}),i.jsx("p",{className:"jsx-f265edf51ed40379 text-gray-600 leading-relaxed",children:"Works flawlessly with all modern eSIM-enabled devices and tablets"})]})]})})})]})}),i.jsx(n(),{id:"f265edf51ed40379",children:"@-webkit-keyframes float-1{0%,100%{-webkit-transform:translate(150px,100px)translatey(0px);transform:translate(150px,100px)translatey(0px)}50%{-webkit-transform:translate(150px,100px)translatey(-10px);transform:translate(150px,100px)translatey(-10px)}}@-moz-keyframes float-1{0%,100%{-moz-transform:translate(150px,100px)translatey(0px);transform:translate(150px,100px)translatey(0px)}50%{-moz-transform:translate(150px,100px)translatey(-10px);transform:translate(150px,100px)translatey(-10px)}}@-o-keyframes float-1{0%,100%{-o-transform:translate(150px,100px)translatey(0px);transform:translate(150px,100px)translatey(0px)}50%{-o-transform:translate(150px,100px)translatey(-10px);transform:translate(150px,100px)translatey(-10px)}}@keyframes float-1{0%,100%{-webkit-transform:translate(150px,100px)translatey(0px);-moz-transform:translate(150px,100px)translatey(0px);-o-transform:translate(150px,100px)translatey(0px);transform:translate(150px,100px)translatey(0px)}50%{-webkit-transform:translate(150px,100px)translatey(-10px);-moz-transform:translate(150px,100px)translatey(-10px);-o-transform:translate(150px,100px)translatey(-10px);transform:translate(150px,100px)translatey(-10px)}}@-webkit-keyframes float-2{0%,100%{-webkit-transform:translate(-150px,50px)translatey(0px);transform:translate(-150px,50px)translatey(0px)}50%{-webkit-transform:translate(-150px,50px)translatey(-15px);transform:translate(-150px,50px)translatey(-15px)}}@-moz-keyframes float-2{0%,100%{-moz-transform:translate(-150px,50px)translatey(0px);transform:translate(-150px,50px)translatey(0px)}50%{-moz-transform:translate(-150px,50px)translatey(-15px);transform:translate(-150px,50px)translatey(-15px)}}@-o-keyframes float-2{0%,100%{-o-transform:translate(-150px,50px)translatey(0px);transform:translate(-150px,50px)translatey(0px)}50%{-o-transform:translate(-150px,50px)translatey(-15px);transform:translate(-150px,50px)translatey(-15px)}}@keyframes float-2{0%,100%{-webkit-transform:translate(-150px,50px)translatey(0px);-moz-transform:translate(-150px,50px)translatey(0px);-o-transform:translate(-150px,50px)translatey(0px);transform:translate(-150px,50px)translatey(0px)}50%{-webkit-transform:translate(-150px,50px)translatey(-15px);-moz-transform:translate(-150px,50px)translatey(-15px);-o-transform:translate(-150px,50px)translatey(-15px);transform:translate(-150px,50px)translatey(-15px)}}@-webkit-keyframes float-3{0%,100%{-webkit-transform:translate(150px,-50px)translatey(0px);transform:translate(150px,-50px)translatey(0px)}50%{-webkit-transform:translate(150px,-50px)translatey(-12px);transform:translate(150px,-50px)translatey(-12px)}}@-moz-keyframes float-3{0%,100%{-moz-transform:translate(150px,-50px)translatey(0px);transform:translate(150px,-50px)translatey(0px)}50%{-moz-transform:translate(150px,-50px)translatey(-12px);transform:translate(150px,-50px)translatey(-12px)}}@-o-keyframes float-3{0%,100%{-o-transform:translate(150px,-50px)translatey(0px);transform:translate(150px,-50px)translatey(0px)}50%{-o-transform:translate(150px,-50px)translatey(-12px);transform:translate(150px,-50px)translatey(-12px)}}@keyframes float-3{0%,100%{-webkit-transform:translate(150px,-50px)translatey(0px);-moz-transform:translate(150px,-50px)translatey(0px);-o-transform:translate(150px,-50px)translatey(0px);transform:translate(150px,-50px)translatey(0px)}50%{-webkit-transform:translate(150px,-50px)translatey(-12px);-moz-transform:translate(150px,-50px)translatey(-12px);-o-transform:translate(150px,-50px)translatey(-12px);transform:translate(150px,-50px)translatey(-12px)}}@-webkit-keyframes float-4{0%,100%{-webkit-transform:translate(-150px,-100px)translatey(0px);transform:translate(-150px,-100px)translatey(0px)}50%{-webkit-transform:translate(-150px,-100px)translatey(-8px);transform:translate(-150px,-100px)translatey(-8px)}}@-moz-keyframes float-4{0%,100%{-moz-transform:translate(-150px,-100px)translatey(0px);transform:translate(-150px,-100px)translatey(0px)}50%{-moz-transform:translate(-150px,-100px)translatey(-8px);transform:translate(-150px,-100px)translatey(-8px)}}@-o-keyframes float-4{0%,100%{-o-transform:translate(-150px,-100px)translatey(0px);transform:translate(-150px,-100px)translatey(0px)}50%{-o-transform:translate(-150px,-100px)translatey(-8px);transform:translate(-150px,-100px)translatey(-8px)}}@keyframes float-4{0%,100%{-webkit-transform:translate(-150px,-100px)translatey(0px);-moz-transform:translate(-150px,-100px)translatey(0px);-o-transform:translate(-150px,-100px)translatey(0px);transform:translate(-150px,-100px)translatey(0px)}50%{-webkit-transform:translate(-150px,-100px)translatey(-8px);-moz-transform:translate(-150px,-100px)translatey(-8px);-o-transform:translate(-150px,-100px)translatey(-8px);transform:translate(-150px,-100px)translatey(-8px)}}@-webkit-keyframes pulse-slow{0%,100%{-webkit-transform:rotate(12deg)scale(1);transform:rotate(12deg)scale(1)}50%{-webkit-transform:rotate(12deg)scale(1.05);transform:rotate(12deg)scale(1.05)}}@-moz-keyframes pulse-slow{0%,100%{-moz-transform:rotate(12deg)scale(1);transform:rotate(12deg)scale(1)}50%{-moz-transform:rotate(12deg)scale(1.05);transform:rotate(12deg)scale(1.05)}}@-o-keyframes pulse-slow{0%,100%{-o-transform:rotate(12deg)scale(1);transform:rotate(12deg)scale(1)}50%{-o-transform:rotate(12deg)scale(1.05);transform:rotate(12deg)scale(1.05)}}@keyframes pulse-slow{0%,100%{-webkit-transform:rotate(12deg)scale(1);-moz-transform:rotate(12deg)scale(1);-o-transform:rotate(12deg)scale(1);transform:rotate(12deg)scale(1)}50%{-webkit-transform:rotate(12deg)scale(1.05);-moz-transform:rotate(12deg)scale(1.05);-o-transform:rotate(12deg)scale(1.05);transform:rotate(12deg)scale(1.05)}}.animate-float-1.jsx-f265edf51ed40379{-webkit-animation:float-1 6s ease-in-out infinite;-moz-animation:float-1 6s ease-in-out infinite;-o-animation:float-1 6s ease-in-out infinite;animation:float-1 6s ease-in-out infinite}.animate-float-2.jsx-f265edf51ed40379{-webkit-animation:float-2 8s ease-in-out infinite;-moz-animation:float-2 8s ease-in-out infinite;-o-animation:float-2 8s ease-in-out infinite;animation:float-2 8s ease-in-out infinite}.animate-float-3.jsx-f265edf51ed40379{-webkit-animation:float-3 7s ease-in-out infinite;-moz-animation:float-3 7s ease-in-out infinite;-o-animation:float-3 7s ease-in-out infinite;animation:float-3 7s ease-in-out infinite}.animate-float-4.jsx-f265edf51ed40379{-webkit-animation:float-4 9s ease-in-out infinite;-moz-animation:float-4 9s ease-in-out infinite;-o-animation:float-4 9s ease-in-out infinite;animation:float-4 9s ease-in-out infinite}.animate-pulse-slow.jsx-f265edf51ed40379{-webkit-animation:pulse-slow 4s ease-in-out infinite;-moz-animation:pulse-slow 4s ease-in-out infinite;-o-animation:pulse-slow 4s ease-in-out infinite;animation:pulse-slow 4s ease-in-out infinite}"}),i.jsx("div",{className:"jsx-f265edf51ed40379 max-w-6xl mx-auto mb-16",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex-1",children:[i.jsx("h3",{className:"jsx-f265edf51ed40379 text-3xl font-bold text-[#D63A5A] mb-6",children:"Global Connectivity Without Limitations"}),i.jsx("p",{className:"jsx-f265edf51ed40379 text-lg text-gray-600 leading-relaxed max-w-2xl",children:"With Yolloo Card, enjoy seamless global mobile data, easy eSIM switching, and freedom from physical SIM constraints – all at better prices and with superior experience."})]}),i.jsx("div",{className:"jsx-f265edf51ed40379 flex-shrink-0 ml-8",children:i.jsx("div",{className:"jsx-f265edf51ed40379 w-32 h-32 relative",children:i.jsx(a.default,{src:"/cs6.svg",alt:"Yolloo character",fill:!0,className:"object-contain"})})})]})}),i.jsx("div",{className:"jsx-f265edf51ed40379 max-w-6xl mx-auto",children:i.jsx("div",{className:"jsx-f265edf51ed40379 relative flex justify-center items-center min-h-[500px]",children:i.jsx("div",{className:"jsx-f265edf51ed40379 absolute left-0 right-0 top-1/2 transform -translate-y-1/2",children:(0,i.jsxs)("div",{className:"jsx-f265edf51ed40379 flex items-center justify-center gap-8",children:[i.jsx("button",{onClick:()=>{t(e=>(e-1+s.length)%s.length)},className:"jsx-f265edf51ed40379 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors z-20",children:i.jsx(l,{className:"w-6 h-6 text-[#D63A5A]"})}),i.jsx("div",{className:"jsx-f265edf51ed40379 flex items-center gap-4",children:[-1,0,1].map(t=>{let r=(e+t+s.length)%s.length,n=s[r],o=0===t;return i.jsx("div",{className:`jsx-f265edf51ed40379 transition-all duration-500 ${o?"opacity-100 scale-110 z-20":"opacity-70 scale-85 z-10"}`,children:(0,i.jsxs)("div",{className:`jsx-f265edf51ed40379 ${o?"w-96 h-96":"w-80 h-80"} bg-white rounded-full p-8 shadow-xl border border-gray-100 flex flex-col items-center justify-center text-center transform ${o?"shadow-2xl":"shadow-lg"}`,children:[i.jsx("div",{className:"jsx-f265edf51ed40379 w-16 h-16 relative mb-4",children:i.jsx(a.default,{src:n.icon,alt:n.title,fill:!0,className:"object-contain"})}),i.jsx("h5",{className:`jsx-f265edf51ed40379 font-bold mb-3 text-[#D63A5A] ${o?"text-xl":"text-lg"}`,children:n.title}),i.jsx("p",{className:`jsx-f265edf51ed40379 text-gray-600 leading-relaxed ${o?"text-sm px-2":"text-xs px-1"}`,children:n.description})]})},r)})}),i.jsx("button",{onClick:()=>{t(e=>(e+1)%s.length)},className:"jsx-f265edf51ed40379 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors z-20",children:i.jsx(d,{className:"w-6 h-6 text-[#D63A5A]"})})]})})})})]})})}},19476:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var i=s(10326),r=s(46226),n=s(64892),a=s(17577);let o=a.forwardRef(function({title:e,titleId:t,...s},i){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:i,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),l=()=>i.jsx("div",{className:"w-5 h-5 relative",children:i.jsx(r.default,{src:"/Frame.svg",alt:"Infinity",fill:!0,className:"object-contain"})});function d(){return i.jsx("section",{id:"compare-plans",className:"py-20 bg-gradient-to-b from-white to-[#FFF0F5]",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[i.jsx("h2",{className:"text-4xl sm:text-5xl font-bold mb-6 text-[#D63A5A] leading-tight",children:"Compare Plans"}),i.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed",children:"Find the perfect Yolloo card for your needs"})]}),(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"max-w-6xl mx-auto",children:[i.jsx("div",{className:"hidden md:block overflow-x-auto relative",children:(0,i.jsxs)("table",{className:"w-full border-collapse bg-white rounded-2xl shadow-lg overflow-hidden",children:[i.jsx("thead",{children:(0,i.jsxs)("tr",{children:[i.jsx("th",{className:"p-6 text-left bg-gray-50 border-b border-gray-200 font-semibold text-gray-700 relative",children:i.jsx("div",{className:"absolute top-2 left-2 z-10",children:i.jsx("div",{className:"w-32 h-20 relative",children:i.jsx(r.default,{src:"/image1.svg",alt:"Comparison illustration",fill:!0,className:"object-contain"})})})}),(0,i.jsxs)("th",{className:"p-6 text-center bg-gray-50 border-b border-gray-200",children:[i.jsx("div",{className:"text-xl font-bold text-gray-700",children:"Lite"}),i.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"Basic"})]}),(0,i.jsxs)("th",{className:"p-6 text-center bg-gray-50 border-b border-gray-200",children:[i.jsx("div",{className:"text-xl font-bold text-gray-700",children:"Plus"}),i.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"Popular"})]}),(0,i.jsxs)("th",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A] to-[#B82E4E] border-b border-[#D63A5A]",children:[i.jsx("div",{className:"text-xl font-bold text-white",children:"Max"}),i.jsx("div",{className:"text-sm text-white/80 mt-1",children:"Best Value"})]})]})}),(0,i.jsxs)("tbody",{children:[(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"Price"}),(0,i.jsxs)("td",{className:"p-6 text-center",children:[i.jsx("span",{className:"text-2xl font-bold text-[#D63A5A]",children:"$12"}),i.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"USD"})]}),(0,i.jsxs)("td",{className:"p-6 text-center",children:[i.jsx("span",{className:"text-2xl font-bold text-[#D63A5A]",children:"$21"}),i.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"USD"})]}),(0,i.jsxs)("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10",children:[i.jsx("span",{className:"text-2xl font-bold text-[#D63A5A]",children:"$23"}),i.jsx("span",{className:"text-sm text-gray-500 ml-1",children:"USD"})]})]}),(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"eSIM Downloads"}),i.jsx("td",{className:"p-6 text-center",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx(l,{}),i.jsx("span",{className:"text-sm font-medium",children:"Unlimited"})]})}),i.jsx("td",{className:"p-6 text-center",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx(l,{}),i.jsx("span",{className:"text-sm font-medium",children:"Unlimited"})]})}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx(l,{}),i.jsx("span",{className:"text-sm font-medium text-white",children:"Unlimited"})]})})]}),(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"Device Support"}),i.jsx("td",{className:"p-6 text-center",children:i.jsx("div",{className:"flex items-center justify-center",children:i.jsx("div",{className:"w-6 h-6 relative",children:i.jsx(r.default,{src:"/android .svg",alt:"Android",fill:!0,className:"object-contain"})})})}),i.jsx("td",{className:"p-6 text-center",children:i.jsx("div",{className:"flex items-center justify-center",children:i.jsx("div",{className:"w-6 h-6 relative",children:i.jsx(r.default,{src:"/android .svg",alt:"Android",fill:!0,className:"object-contain"})})})}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[i.jsx("div",{className:"w-6 h-6 relative",children:i.jsx(r.default,{src:"/android .svg",alt:"Android",fill:!0,className:"object-contain"})}),i.jsx("div",{className:"w-6 h-6 relative",children:i.jsx(r.default,{src:"/apple.svg",alt:"Apple",fill:!0,className:"object-contain"})})]})})]}),(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"eSIM Storage"}),i.jsx("td",{className:"p-6 text-center text-sm font-medium",children:"5 profiles"}),i.jsx("td",{className:"p-6 text-center text-sm font-medium",children:"15 profiles"}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10 text-sm font-medium",children:"30 profiles"})]}),(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"Global Coverage"}),i.jsx("td",{className:"p-6 text-center",children:i.jsx(o,{className:"w-6 h-6 text-green-500 mx-auto"})}),i.jsx("td",{className:"p-6 text-center",children:i.jsx(o,{className:"w-6 h-6 text-green-500 mx-auto"})}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10",children:i.jsx(o,{className:"w-6 h-6 text-white bg-[#D63A5A] rounded-full p-1 mx-auto"})})]}),(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"Support"}),i.jsx("td",{className:"p-6 text-center text-sm font-medium",children:"Basic"}),i.jsx("td",{className:"p-6 text-center text-sm font-medium",children:"Priority"}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10 text-sm font-medium",children:"24/7 Priority"})]}),(0,i.jsxs)("tr",{className:"border-b border-gray-100",children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"Free Updates"}),i.jsx("td",{className:"p-6 text-center",children:i.jsx("span",{className:"text-gray-400",children:"-"})}),i.jsx("td",{className:"p-6 text-center",children:i.jsx(o,{className:"w-6 h-6 text-green-500 mx-auto"})}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10",children:i.jsx(o,{className:"w-6 h-6 text-white bg-[#D63A5A] rounded-full p-1 mx-auto"})})]}),(0,i.jsxs)("tr",{children:[i.jsx("td",{className:"p-6 font-semibold text-gray-700",children:"Advanced Features"}),i.jsx("td",{className:"p-6 text-center",children:i.jsx("span",{className:"text-gray-400",children:"-"})}),i.jsx("td",{className:"p-6 text-center",children:i.jsx("span",{className:"text-gray-400",children:"-"})}),i.jsx("td",{className:"p-6 text-center bg-gradient-to-br from-[#D63A5A]/10 to-[#B82E4E]/10",children:i.jsx(o,{className:"w-6 h-6 text-white bg-[#D63A5A] rounded-full p-1 mx-auto"})})]})]})]})}),i.jsx("div",{className:"md:hidden space-y-6",children:[{name:"Lite",price:"$12",color:"bg-gray-50"},{name:"Plus",price:"$21",color:"bg-gray-50"},{name:"Max",price:"$23",color:"bg-gradient-to-br from-[#D63A5A] to-[#B82E4E]"}].map((e,t)=>i.jsx("div",{className:`p-6 rounded-2xl ${e.color} ${"Max"===e.name?"text-white":"text-gray-700"}`,children:(0,i.jsxs)("div",{className:"text-center mb-4",children:[i.jsx("h3",{className:"text-xl font-bold",children:e.name}),(0,i.jsxs)("p",{className:"text-2xl font-bold mt-2",children:[e.price," ",i.jsx("span",{className:"text-sm font-normal",children:"USD"})]})]})},t))})]})]})})}},73598:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var i=s(10326),r=s(46226),n=s(64892);let a=[{id:1,title:"Choose Plan",description:"Select your ideal data plan from our range of global coverage options",image:"/Frame 207.svg",position:"left"},{id:2,title:"Instant Delivery",description:"Receive your eSIM QR code immediately in your email after purchase",image:"/Frame 212.svg",position:"right"},{id:3,title:"Quick Install",description:"Scan the QR code with your phone camera to install the eSIM profile",image:"/Frame 214.svg",position:"left"},{id:4,title:"Ready to Go",description:"Activate your eSIM with one tap and enjoy global connectivity",image:"/Frame 213.svg",position:"right"}];function o(){return i.jsx("section",{id:"get-start",className:"py-20 bg-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[i.jsx("div",{className:"inline-block px-4 py-2 bg-pink-100 text-pink-600 rounded-full text-sm font-medium mb-6",children:"Simple Setup Process"}),i.jsx("h2",{className:"text-4xl md:text-5xl font-bold mb-6",style:{background:"linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Get Started in Minutes"}),i.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Our streamlined activation process ensures you can start using your eSIM right away, with no technical expertise required."})]}),(0,i.jsxs)("div",{className:"relative max-w-6xl mx-auto",children:[i.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-pink-300 to-pink-500 h-full hidden md:block"}),i.jsx("div",{className:"space-y-16 md:space-y-24",children:a.map((e,t)=>(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*t},viewport:{once:!0},className:`flex flex-col md:flex-row items-center gap-8 ${"right"===e.position?"md:flex-row-reverse":""}`,children:[(0,i.jsxs)("div",{className:`flex-1 text-center ${"right"===e.position?"md:text-right":"md:text-left"}`,children:[(0,i.jsxs)("div",{className:`inline-block px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm font-medium mb-4 ${"right"===e.position?"md:ml-auto":""}`,children:["Step ",e.id]}),i.jsx("h3",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:e.title}),i.jsx("p",{className:`text-gray-600 text-lg leading-relaxed max-w-md ${"right"===e.position?"mx-auto md:ml-auto md:mr-0":"mx-auto md:mx-0"}`,children:e.description})]}),i.jsx("div",{className:"relative z-10 hidden md:block",children:i.jsx("div",{className:"w-6 h-6 bg-pink-500 rounded-full border-4 border-white shadow-lg"})}),i.jsx("div",{className:"flex-1 flex justify-center",children:i.jsx("div",{className:"w-64 h-64 md:w-80 md:h-80 relative",children:i.jsx(r.default,{src:e.image,alt:e.title,fill:!0,className:"object-contain"})})})]},e.id))})]})]})})}},11734:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var i=s(10326),r=s(90434),n=s(64892);function a(){return(0,i.jsxs)("section",{className:"relative h-[70vh] min-h-[600px] flex items-center justify-start overflow-hidden",style:{backgroundImage:"url('/bg_banner.svg')",backgroundSize:"cover",backgroundPosition:"top",backgroundRepeat:"no-repeat"},children:[i.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:(0,i.jsxs)("div",{className:"max-w-2xl",children:[(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"inline-flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 mb-8 shadow-lg",children:[i.jsx("div",{className:"w-2 h-2 bg-[#FF6B9D] rounded-full"}),i.jsx("span",{className:"text-sm font-medium text-gray-700",children:"One Card, One World"})]}),(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"mb-6",children:[i.jsx("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight",children:"Global travel"}),i.jsx("h2",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-white leading-tight mt-2",children:"One card is all you need"})]}),i.jsx(n.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"text-lg sm:text-xl text-white/90 mb-10 leading-relaxed max-w-lg",children:"Yolloo eSIM makes it easy to stay online wherever you are."}),(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"flex flex-col sm:flex-row gap-4",children:[i.jsx(r.default,{href:"/yolloo-smart",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-[#FF6B9D] to-[#FF8FA3]    hover:from-[#FF5A8C] hover:to-[#FF7E92] text-white font-semibold rounded-full    shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1    text-lg min-w-[200px]",children:"Browse Yolloo Cards"}),i.jsx("button",{onClick:()=>{let e=document.getElementById("get-start");if(e){let t=e.getBoundingClientRect().top+window.scrollY-20;window.scrollTo({top:t,behavior:"smooth"})}},className:"inline-flex items-center justify-center px-8 py-4 bg-white/20 backdrop-blur-sm    hover:bg-white/30 text-white font-semibold rounded-full border-2 border-white/30    hover:border-white/50 shadow-lg hover:shadow-xl transition-all duration-300    transform hover:-translate-y-1 text-lg min-w-[200px]",children:"How It Works"})]})]})}),i.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-transparent z-0"})]})}},95419:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var i=s(10326),r=s(17577),n=s(77109),a=s(90434),o=s(46226),l=s(41135),d=s(64892);let c=({title:e,description:t,price:s,originalPrice:r,imageSrc:d,isPopular:c=!1,isPlus:u=!1,productLink:h,buttonText:m})=>{let{status:p}=(0,n.useSession)(),f="authenticated"===p?h:`/auth/signin?callbackUrl=${encodeURIComponent(h)}`;return(0,i.jsxs)("div",{className:(0,l.Z)("relative p-8 rounded-3xl transition-all duration-500 hover:-translate-y-2 flex flex-col",c?"bg-gradient-to-br from-[#D63A5A] to-[#B82E4E] shadow-[0_12px_40px_rgba(214,58,90,0.3)]":u?"shadow-[0_8px_32px_rgba(0,0,0,0.1)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.15)]":"bg-white shadow-[0_8px_32px_rgba(0,0,0,0.1)] hover:shadow-[0_12px_40px_rgba(0,0,0,0.15)]"),style:u?{backgroundColor:"#FFC7CD"}:{},children:[i.jsx("div",{className:(0,l.Z)("inline-flex items-center justify-center px-4 py-2 rounded-full text-sm font-semibold mb-6",c?"bg-white/20 text-white":"bg-[#FFE8ED] text-[#D63A5A]"),children:e}),i.jsx("p",{className:(0,l.Z)("text-sm mb-6 font-medium text-center",c?"text-white/90":"text-gray-600"),children:t}),i.jsx("div",{className:"flex justify-center mb-8",children:i.jsx("div",{className:"w-24 h-24 relative",children:i.jsx(o.default,{src:d,alt:`${e} character`,fill:!0,className:"object-contain"})})}),i.jsx("div",{className:"text-center mb-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsxs)("span",{className:(0,l.Z)("text-4xl font-bold",c?"text-white":"text-[#D63A5A]"),children:["$",s]}),i.jsx("span",{className:(0,l.Z)("text-lg",c?"text-white/80":"text-gray-500"),children:"USD"})]})}),i.jsx(a.default,{href:f,className:(0,l.Z)("block w-full py-4 px-6 text-center rounded-full font-semibold transition-all duration-300 transform hover:-translate-y-1 text-lg",c?"bg-white text-[#D63A5A] hover:bg-gray-50 shadow-lg hover:shadow-xl":"bg-gradient-to-r from-[#D63A5A] to-[#F799A6] text-white hover:from-[#C23350] hover:to-[#E88A97] shadow-lg hover:shadow-xl"),children:m})]})};function u(){let[e,t]=(0,r.useState)({lite:"",plus:"",max:""});return i.jsx("section",{id:"pricing",className:"py-20 bg-gradient-to-b from-[#FFF0F5] to-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(d.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[i.jsx("div",{className:"inline-flex items-center justify-center px-6 py-2 bg-[#FFE8ED] rounded-full text-[#D63A5A] font-semibold text-sm mb-6",children:"Pricing Cards"}),i.jsx("h2",{className:"text-4xl sm:text-5xl font-bold mb-6 text-[#D63A5A] leading-tight",children:"Choose Your Perfect Yolloo Card"}),i.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Select your ideal Yolloo card with eSIM capabilities. Our cards allow you to store and manage multiple eSIM profiles, making international travel seamless and affordable."})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto",children:[i.jsx(d.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},children:i.jsx(c,{title:"Lite",description:"Perfect for light travelers",price:12,imageSrc:"/Frame 113.svg",productLink:e.lite,buttonText:"Get Card"})}),i.jsx(d.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},children:i.jsx(c,{title:"Plus",description:"Best for regular travelers",price:21,imageSrc:"/Frame 121.svg",isPlus:!0,productLink:e.plus,buttonText:"Get Card"})}),i.jsx(d.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},children:i.jsx(c,{title:"Max",description:"Ultimate flexibility",price:23,imageSrc:"/Frame 123.svg",isPopular:!0,productLink:e.max,buttonText:"Get Card"})})]}),i.jsx("div",{className:"text-center mt-12",children:i.jsx("p",{className:"text-sm text-gray-500",children:"* Physical card supported with all plans. eSIM data plans are sold separately."})})]})})}},89823:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var i=s(10326),r=s(17577),n=s(64892);let a=[{id:1,question:"What is Yolloo eSIM?",answer:"Yolloo eSIM is a digital SIM card that allows you to connect to mobile networks without a physical SIM card. It's embedded directly into your device and can be activated instantly through a QR code."},{id:2,question:"How does Yolloo eSIM work?",answer:"Simply purchase a data plan, receive a QR code via email, scan it with your device's camera, and you're connected! No need to visit stores or wait for physical SIM cards to arrive."},{id:3,question:"Which devices support Yolloo eSIM?",answer:"Most modern smartphones and tablets support eSIM technology, including iPhone XS and newer, Google Pixel 3 and newer, Samsung Galaxy S20 and newer, and many other devices. Check your device compatibility on our website."},{id:4,question:"Can I use Yolloo eSIM while traveling?",answer:"Absolutely! Yolloo eSIM is perfect for travelers. You can purchase local data plans for over 190 countries and regions, avoiding expensive roaming charges and staying connected wherever you go."}];function o(){let[e,t]=(0,r.useState)([]),s=e=>{t(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return i.jsx("section",{id:"qa",className:"py-20 bg-gray-50",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[i.jsx("h2",{className:"text-4xl md:text-5xl font-bold mb-6",style:{background:"linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Got Questions? We've Got Answers"}),i.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Let's clear up the confusion—quick and easy."})]}),i.jsx("div",{className:"max-w-4xl mx-auto",children:i.jsx("div",{className:"space-y-4",children:a.map((t,r)=>{let a=e.includes(t.id);return(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*r},viewport:{once:!0},className:"bg-white rounded-2xl border border-pink-200 hover:shadow-lg transition-shadow duration-300",children:[(0,i.jsxs)("button",{onClick:()=>s(t.id),className:"w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200",children:[i.jsx("h3",{className:"text-lg font-semibold text-gray-900 pr-4",children:t.question}),i.jsx("div",{className:`flex-shrink-0 w-8 h-8 flex items-center justify-center transition-transform duration-200 ${a?"rotate-180":""}`,children:i.jsx("span",{className:"text-pink-600 text-2xl",children:"▼"})})]}),a&&i.jsx("div",{className:"px-6 pb-6 animate-in slide-in-from-top-2 duration-300",children:i.jsx("div",{className:"pt-2 border-t border-gray-100",children:i.jsx("p",{className:"text-gray-600 leading-relaxed",children:t.answer})})})]},t.id)})})}),(0,i.jsxs)("div",{className:"text-center mt-12",children:[i.jsx("p",{className:"text-gray-600 mb-4",children:"Still have questions?"}),i.jsx("button",{className:"text-pink-600 font-semibold hover:text-pink-700 transition-colors duration-200",children:"Let us help you →"})]})]})})}},21749:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var i=s(10326),r=s(46226),n=s(64892);function a(){return i.jsx("section",{id:"ready-start",className:"py-20 bg-white",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)(n.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[i.jsx("h2",{className:"text-4xl md:text-5xl font-bold mb-6",style:{background:"linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},children:"Ready to Get Started?"}),i.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto mb-12",children:"Join thousands of satisfied travelers who trust our eSIM solutions for their global connectivity needs. Experience the freedom of instant connectivity."})]}),i.jsx(n.E.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"relative max-w-6xl mx-auto",children:(0,i.jsxs)("div",{className:"relative w-full h-[500px] md:h-[600px] flex items-center justify-center",children:[i.jsx(r.default,{src:"/worldLow 1.svg",alt:"World map",fill:!0,className:"object-contain scale-110"}),(0,i.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-end pb-8 gap-4 z-10",children:[(0,i.jsxs)(n.E.button,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"bg-pink-500 hover:bg-pink-600 text-white font-medium rounded-full shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-64 h-12 justify-center",children:[i.jsx("span",{className:"w-5 h-5 flex items-center justify-center",children:i.jsx(r.default,{src:"/gt1.svg",alt:"Browse plans",width:20,height:20,className:"object-contain"})}),i.jsx("span",{children:"Browse Yolloo Plans"})]}),(0,i.jsxs)(n.E.button,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"bg-white hover:bg-gray-50 text-pink-500 font-medium rounded-full border border-pink-200 shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-64 h-12 justify-center",children:[i.jsx("span",{className:"w-5 h-5 flex items-center justify-center",children:i.jsx(r.default,{src:"/gt2.svg",alt:"Contact sales",width:20,height:20,className:"object-contain"})}),i.jsx("span",{children:"Contact Sales"})]})]})]})})]})})}},93318:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var i=s(10326),r=s(77626),n=s.n(r),a=s(17577),o=s(46226),l=s(64892);let d=[{id:1,name:"Sarah Johnson",rating:5,review:"Amazing service! The eSIM worked perfectly during my trip to Europe. No more worrying about roaming charges or finding local SIM cards.",platform:"android",country:"United States"},{id:2,name:"Mike Chen",rating:5,review:"Best travel companion ever! Used it across 5 countries in Asia and the connection was always reliable. Highly recommend!",platform:"ios",country:"Canada"},{id:3,name:"Emma Wilson",rating:4,review:"Great value for money. The setup was easy and customer support was very helpful when I had questions.",platform:"android",country:"Australia"},{id:4,name:"David Rodriguez",rating:5,review:"Seamless experience from purchase to activation. The data speeds were excellent throughout my business trip.",platform:"ios",country:"Spain"},{id:5,name:"Lisa Park",rating:5,review:"Love how easy it is to switch between different country plans. Perfect for frequent travelers like me!",platform:"android",country:"South Korea"},{id:6,name:"James Thompson",rating:4,review:"Reliable connection and fair pricing. The app interface is user-friendly and makes managing data plans simple.",platform:"ios",country:"United Kingdom"}];function c(){let e=(0,a.useRef)(null);(0,a.useRef)();let t=(0,a.useRef)(!1),s=e=>Array.from({length:5},(t,s)=>i.jsx("span",{className:`text-lg ${s<e?"text-yellow-400":"text-gray-300"}`,children:"★"},s));return d.reduce((e,t)=>e+t.rating,0),d.length,(0,i.jsxs)("section",{id:"user-react",className:"jsx-33dd2d3238049260 py-20 bg-gray-50",children:[(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 container mx-auto px-4",children:[(0,i.jsxs)(l.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[i.jsx("h2",{style:{background:"linear-gradient(90deg, #B82E4E 0%, #F799A6 50%, #B82E4E 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent",backgroundClip:"text"},className:"jsx-33dd2d3238049260 text-4xl font-bold mb-4",children:"Hear from Users Around the World"}),i.jsx("p",{className:"jsx-33dd2d3238049260 text-lg text-gray-600 mb-8",children:"Start your journey with confidence—just like they did"}),i.jsx("div",{className:"jsx-33dd2d3238049260 flex justify-center mb-8",children:i.jsx("div",{className:"jsx-33dd2d3238049260 w-32 h-32 relative",children:i.jsx(o.default,{src:"/Frame 196.svg",alt:"User character",fill:!0,className:"object-contain"})})}),(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 flex items-center justify-center gap-4 mb-8",children:[(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 flex items-center gap-2",children:[i.jsx("span",{className:"jsx-33dd2d3238049260 text-3xl font-bold text-[#B82E4E]",children:"4.9/5"}),i.jsx("span",{className:"jsx-33dd2d3238049260 text-2xl text-yellow-400",children:"★"})]}),i.jsx("div",{className:"jsx-33dd2d3238049260 text-gray-600",children:"Real reviews from travelers in over 190 countries"})]})]}),(0,i.jsxs)(l.E.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"relative overflow-hidden",onMouseEnter:()=>{t.current=!0},onMouseLeave:()=>{t.current=!1},children:[i.jsx("div",{className:"jsx-33dd2d3238049260 absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-gray-50 to-transparent z-10 pointer-events-none"}),i.jsx("div",{className:"jsx-33dd2d3238049260 absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-gray-50 to-transparent z-10 pointer-events-none"}),i.jsx("div",{ref:e,className:"jsx-33dd2d3238049260 flex gap-4",children:[void 0,void 0].map((e,t)=>d.map(e=>(0,i.jsxs)("div",{style:{width:"280px"},className:"jsx-33dd2d3238049260 bg-white rounded-xl p-4 shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-300 flex-shrink-0",children:[(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 flex items-center gap-3 mb-3",children:[i.jsx("div",{className:"jsx-33dd2d3238049260 w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center",children:i.jsx("span",{className:"jsx-33dd2d3238049260 text-sm font-semibold text-gray-600",children:e.name.charAt(0)})}),(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 flex-1 min-w-0",children:[i.jsx("h4",{className:"jsx-33dd2d3238049260 font-semibold text-gray-900 text-sm truncate",children:e.name}),i.jsx("div",{className:"jsx-33dd2d3238049260 flex items-center gap-1",children:s(e.rating)})]})]}),i.jsx("p",{className:"jsx-33dd2d3238049260 text-gray-600 leading-relaxed mb-3 text-xs line-clamp-3",children:e.review}),(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 flex items-center justify-between",children:[i.jsx("div",{className:"jsx-33dd2d3238049260 flex items-center gap-2",children:i.jsx("div",{className:`jsx-33dd2d3238049260 px-2 py-1 rounded-full text-xs font-medium ${"android"===e.platform?"bg-green-100 text-green-700":"bg-gray-100 text-gray-700"}`,children:(0,i.jsxs)("div",{className:"jsx-33dd2d3238049260 flex items-center gap-1",children:[i.jsx(o.default,{src:"android"===e.platform?"/android .svg":"/apple.svg",alt:e.platform,width:10,height:10,className:"object-contain"}),i.jsx("span",{className:"jsx-33dd2d3238049260 text-xs",children:"android"===e.platform?"Android":"iOS"})]})})}),i.jsx("span",{className:"jsx-33dd2d3238049260 text-xs text-gray-500 truncate max-w-[80px]",children:e.country})]})]},`set-${t}-${e.id}`)))})]})]}),i.jsx(n(),{id:"33dd2d3238049260",children:".line-clamp-3.jsx-33dd2d3238049260{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}"})]})}},8472:()=>{},51596:(e,t,s)=>{"use strict";s(8472);var i=s(17577),r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),n="undefined"!=typeof process&&process.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,s=t.name,i=void 0===s?"stylesheet":s,r=t.optimizeForSpeed,o=void 0===r?n:r;l(a(i),"`name` must be a string"),this._name=i,this._deletedRulePlaceholder="#"+i+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){return l(a(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},t.replaceRule=function(e,t){this._optimizeForSpeed;var s=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(i){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}return e},t.deleteRule=function(e){this._serverSheet.deleteRule(e)},t.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},t.cssRules=function(){return this._serverSheet.cssRules},t.makeStyleTag=function(e,t,s){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var i=document.createElement("style");this._nonce&&i.setAttribute("nonce",this._nonce),i.type="text/css",i.setAttribute("data-"+e,""),t&&i.appendChild(document.createTextNode(t));var r=document.head||document.getElementsByTagName("head")[0];return s?r.insertBefore(i,s):r.appendChild(i),i},function(e,t){for(var s=0;s<t.length;s++){var i=t[s];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},c={};function u(e,t){if(!t)return"jsx-"+e;var s=String(t),i=e+s;return c[i]||(c[i]="jsx-"+d(e+"-"+s)),c[i]}function h(e,t){var s=e+(t=t.replace(/\/style/gi,"\\/style"));return c[s]||(c[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[s]}var m=i.createContext(null);m.displayName="StyleSheetContext",r.default.useInsertionEffect||r.default.useLayoutEffect;var p=void 0;function f(e){var t=p||i.useContext(m);return t&&t.add(e),null}f.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=f},77626:(e,t,s)=>{"use strict";e.exports=s(51596).style},78913:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>B});var i=s(19510),r=s(68570);let n=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-hero.tsx`),{__esModule:a,$$typeof:o}=n;n.default;let l=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-hero.tsx#default`),d=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-pricing.tsx`),{__esModule:c,$$typeof:u}=d;d.default;let h=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-pricing.tsx#default`),m=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-compare-plans.tsx`),{__esModule:p,$$typeof:f}=m;m.default;let x=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-compare-plans.tsx#default`),g=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-choose-us.tsx`),{__esModule:y,$$typeof:v}=g;g.default;let b=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-choose-us.tsx#default`),j=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-user-react.tsx`),{__esModule:w,$$typeof:P}=j;j.default;let N=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-user-react.tsx#default`),S=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-get-start.tsx`),{__esModule:A,$$typeof:k}=S;S.default;let E=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-get-start.tsx#default`),T=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-qa.tsx`),{__esModule:C,$$typeof:M}=T;T.default;let V=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-qa.tsx#default`),D=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-ready-start.tsx`),{__esModule:F,$$typeof:R}=D;D.default;let L=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\new-ready-start.tsx#default`);async function B(){return(0,i.jsxs)("div",{className:"flex min-h-screen flex-col",children:[i.jsx(l,{}),i.jsx(h,{}),i.jsx(x,{}),i.jsx(b,{}),i.jsx(N,{}),i.jsx(E,{}),i.jsx(V,{}),i.jsx(L,{})]})}},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var i=s(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,i.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},64892:(e,t,s)=>{"use strict";s.d(t,{E:()=>r_});var i=s(17577);let r=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),n=(0,i.createContext)({}),a=(0,i.createContext)(null),o="undefined"!=typeof document,l=o?i.useLayoutEffect:i.useEffect,d=(0,i.createContext)({strict:!1});function c(e){return"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function u(e){return"string"==typeof e||Array.isArray(e)}function h(e){return"object"==typeof e&&"function"==typeof e.start}let m=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],p=["initial",...m];function f(e){return h(e.animate)||p.some(t=>u(e[t]))}function x(e){return!!(f(e)||e.variants)}function g(e){return Array.isArray(e)?e.join(" "):e}let y={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},v={};for(let e in y)v[e]={isEnabled:t=>y[e].some(e=>!!t[e])};let b=(0,i.createContext)({}),j=(0,i.createContext)({}),w=Symbol.for("motionComponentSymbol"),P=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function N(e){if("string"!=typeof e||e.includes("-"));else if(P.indexOf(e)>-1||/[A-Z]/.test(e))return!0;return!1}let S={},A=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],k=new Set(A);function E(e,{layout:t,layoutId:s}){return k.has(e)||e.startsWith("origin")||(t||void 0!==s)&&(!!S[e]||"opacity"===e)}let T=e=>!!(e&&e.getVelocity),C={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},M=A.length,V=e=>t=>"string"==typeof t&&t.startsWith(e),D=V("--"),F=V("var(--"),R=(e,t)=>t&&"number"==typeof e?t.transform(e):e,L=(e,t,s)=>Math.min(Math.max(s,e),t),B={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},I={...B,transform:e=>L(0,1,e)},O={...B,default:1},z=e=>Math.round(1e5*e)/1e5,$=/(-)?([\d]*\.?[\d])+/g,_=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,U=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function W(e){return"string"==typeof e}let Y=e=>({test:t=>W(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),q=Y("deg"),G=Y("%"),H=Y("px"),X=Y("vh"),Z=Y("vw"),K={...G,parse:e=>G.parse(e)/100,transform:e=>G.transform(100*e)},Q={...B,transform:Math.round},J={borderWidth:H,borderTopWidth:H,borderRightWidth:H,borderBottomWidth:H,borderLeftWidth:H,borderRadius:H,radius:H,borderTopLeftRadius:H,borderTopRightRadius:H,borderBottomRightRadius:H,borderBottomLeftRadius:H,width:H,maxWidth:H,height:H,maxHeight:H,size:H,top:H,right:H,bottom:H,left:H,padding:H,paddingTop:H,paddingRight:H,paddingBottom:H,paddingLeft:H,margin:H,marginTop:H,marginRight:H,marginBottom:H,marginLeft:H,rotate:q,rotateX:q,rotateY:q,rotateZ:q,scale:O,scaleX:O,scaleY:O,scaleZ:O,skew:q,skewX:q,skewY:q,distance:H,translateX:H,translateY:H,translateZ:H,x:H,y:H,z:H,perspective:H,transformPerspective:H,opacity:I,originX:K,originY:K,originZ:H,zIndex:Q,fillOpacity:I,strokeOpacity:I,numOctaves:Q};function ee(e,t,s,i){let{style:r,vars:n,transform:a,transformOrigin:o}=e,l=!1,d=!1,c=!0;for(let e in t){let s=t[e];if(D(e)){n[e]=s;continue}let i=J[e],u=R(s,i);if(k.has(e)){if(l=!0,a[e]=u,!c)continue;s!==(i.default||0)&&(c=!1)}else e.startsWith("origin")?(d=!0,o[e]=u):r[e]=u}if(!t.transform&&(l||i?r.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:s=!0},i,r){let n="";for(let t=0;t<M;t++){let s=A[t];if(void 0!==e[s]){let t=C[s]||s;n+=`${t}(${e[s]}) `}}return t&&!e.z&&(n+="translateZ(0)"),n=n.trim(),r?n=r(e,i?"":n):s&&i&&(n="none"),n}(e.transform,s,c,i):r.transform&&(r.transform="none")),d){let{originX:e="50%",originY:t="50%",originZ:s=0}=o;r.transformOrigin=`${e} ${t} ${s}`}}let et=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function es(e,t,s){for(let i in t)T(t[i])||E(i,s)||(e[i]=t[i])}let ei=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","ignoreStrict","viewport"]);function er(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||ei.has(e)}let en=e=>!er(e);try{!function(e){e&&(en=t=>t.startsWith("on")?!er(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}function ea(e,t,s){return"string"==typeof e?e:H.transform(t+s*e)}let eo={offset:"stroke-dashoffset",array:"stroke-dasharray"},el={offset:"strokeDashoffset",array:"strokeDasharray"};function ed(e,{attrX:t,attrY:s,attrScale:i,originX:r,originY:n,pathLength:a,pathSpacing:o=1,pathOffset:l=0,...d},c,u,h){if(ee(e,d,c,h),u){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:m,style:p,dimensions:f}=e;m.transform&&(f&&(p.transform=m.transform),delete m.transform),f&&(void 0!==r||void 0!==n||p.transform)&&(p.transformOrigin=function(e,t,s){let i=ea(t,e.x,e.width),r=ea(s,e.y,e.height);return`${i} ${r}`}(f,void 0!==r?r:.5,void 0!==n?n:.5)),void 0!==t&&(m.x=t),void 0!==s&&(m.y=s),void 0!==i&&(m.scale=i),void 0!==a&&function(e,t,s=1,i=0,r=!0){e.pathLength=1;let n=r?eo:el;e[n.offset]=H.transform(-i);let a=H.transform(t),o=H.transform(s);e[n.array]=`${a} ${o}`}(m,a,o,l,!1)}let ec=()=>({...et(),attrs:{}}),eu=e=>"string"==typeof e&&"svg"===e.toLowerCase(),eh=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function em(e,{style:t,vars:s},i,r){for(let n in Object.assign(e.style,t,r&&r.getProjectionStyles(i)),s)e.style.setProperty(n,s[n])}let ep=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ef(e,t,s,i){for(let s in em(e,t,void 0,i),t.attrs)e.setAttribute(ep.has(s)?s:eh(s),t.attrs[s])}function ex(e,t){let{style:s}=e,i={};for(let r in s)(T(s[r])||t.style&&T(t.style[r])||E(r,e))&&(i[r]=s[r]);return i}function eg(e,t){let s=ex(e,t);for(let i in e)(T(e[i])||T(t[i]))&&(s[-1!==A.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return s}function ey(e,t,s,i={},r={}){return"function"==typeof t&&(t=t(void 0!==s?s:e.custom,i,r)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==s?s:e.custom,i,r)),t}let ev=e=>Array.isArray(e),eb=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),ej=e=>ev(e)?e[e.length-1]||0:e;function ew(e){let t=T(e)?e.get():e;return eb(t)?t.toValue():t}let eP=e=>(t,s)=>{let r=(0,i.useContext)(n),o=(0,i.useContext)(a),l=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:s},i,r,n){let a={latestValues:function(e,t,s,i){let r={},n=i(e,{});for(let e in n)r[e]=ew(n[e]);let{initial:a,animate:o}=e,l=f(e),d=x(e);t&&d&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===o&&(o=t.animate));let c=!!s&&!1===s.initial,u=(c=c||!1===a)?o:a;return u&&"boolean"!=typeof u&&!h(u)&&(Array.isArray(u)?u:[u]).forEach(t=>{let s=ey(e,t);if(!s)return;let{transitionEnd:i,transition:n,...a}=s;for(let e in a){let t=a[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(r[e]=t)}for(let e in i)r[e]=i[e]}),r}(i,r,n,e),renderState:t()};return s&&(a.mount=e=>s(i,e,a)),a})(e,t,r,o);return s?l():function(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}(l)},eN=e=>e;class eS{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){let t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}let eA=["prepare","read","update","preRender","render","postRender"],{schedule:ek,cancel:eE,state:eT,steps:eC}=function(e,t){let s=!1,i=!0,r={delta:0,timestamp:0,isProcessing:!1},n=eA.reduce((e,t)=>(e[t]=function(e){let t=new eS,s=new eS,i=0,r=!1,n=!1,a=new WeakSet,o={schedule:(e,n=!1,o=!1)=>{let l=o&&r,d=l?t:s;return n&&a.add(e),d.add(e)&&l&&r&&(i=t.order.length),e},cancel:e=>{s.remove(e),a.delete(e)},process:l=>{if(r){n=!0;return}if(r=!0,[t,s]=[s,t],s.clear(),i=t.order.length)for(let s=0;s<i;s++){let i=t.order[s];i(l),a.has(i)&&(o.schedule(i),e())}r=!1,n&&(n=!1,o.process(l))}};return o}(()=>s=!0),e),{}),a=e=>n[e].process(r),o=()=>{let n=performance.now();s=!1,r.delta=i?1e3/60:Math.max(Math.min(n-r.timestamp,40),1),r.timestamp=n,r.isProcessing=!0,eA.forEach(a),r.isProcessing=!1,s&&t&&(i=!1,e(o))},l=()=>{s=!0,i=!0,r.isProcessing||e(o)};return{schedule:eA.reduce((e,t)=>{let i=n[t];return e[t]=(e,t=!1,r=!1)=>(s||l(),i.schedule(e,t,r)),e},{}),cancel:e=>eA.forEach(t=>n[t].cancel(e)),state:r,steps:n}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:eN,!0),eM={useVisualState:eP({scrapeMotionValuesFromProps:eg,createRenderState:ec,onMount:(e,t,{renderState:s,latestValues:i})=>{ek.read(()=>{try{s.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){s.dimensions={x:0,y:0,width:0,height:0}}}),ek.render(()=>{ed(s,i,{enableHardwareAcceleration:!1},eu(t.tagName),e.transformTemplate),ef(t,s)})}})},eV={useVisualState:eP({scrapeMotionValuesFromProps:ex,createRenderState:et})};function eD(e,t,s,i={passive:!0}){return e.addEventListener(t,s,i),()=>e.removeEventListener(t,s)}let eF=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eR(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}let eL=e=>t=>eF(t)&&e(t,eR(t));function eB(e,t,s,i){return eD(e,t,eL(s),i)}let eI=(e,t)=>s=>t(e(s)),eO=(...e)=>e.reduce(eI);function ez(e){let t=null;return()=>null===t&&(t=e,()=>{t=null})}let e$=ez("dragHorizontal"),e_=ez("dragVertical");function eU(e){let t=!1;if("y"===e)t=e_();else if("x"===e)t=e$();else{let e=e$(),s=e_();e&&s?t=()=>{e(),s()}:(e&&e(),s&&s())}return t}function eW(){let e=eU(!0);return!e||(e(),!1)}class eY{constructor(e){this.isMounted=!1,this.node=e}update(){}}function eq(e,t){let s="onHover"+(t?"Start":"End");return eB(e.current,"pointer"+(t?"enter":"leave"),(i,r)=>{if("touch"===i.type||eW())return;let n=e.getProps();e.animationState&&n.whileHover&&e.animationState.setActive("whileHover",t),n[s]&&ek.update(()=>n[s](i,r))},{passive:!e.getProps()[s]})}class eG extends eY{mount(){this.unmount=eO(eq(this.node,!0),eq(this.node,!1))}unmount(){}}class eH extends eY{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eO(eD(this.node.current,"focus",()=>this.onFocus()),eD(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let eX=(e,t)=>!!t&&(e===t||eX(e,t.parentElement));function eZ(e,t){if(!t)return;let s=new PointerEvent("pointer"+e);t(s,eR(s))}class eK extends eY{constructor(){super(...arguments),this.removeStartListeners=eN,this.removeEndListeners=eN,this.removeAccessibleListeners=eN,this.startPointerPress=(e,t)=>{if(this.removeEndListeners(),this.isPressing)return;let s=this.node.getProps(),i=eB(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;let{onTap:s,onTapCancel:i}=this.node.getProps();ek.update(()=>{eX(this.node.current,e.target)?s&&s(e,t):i&&i(e,t)})},{passive:!(s.onTap||s.onPointerUp)}),r=eB(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(s.onTapCancel||s.onPointerCancel)});this.removeEndListeners=eO(i,r),this.startPress(e,t)},this.startAccessiblePress=()=>{let e=eD(this.node.current,"keydown",e=>{"Enter"!==e.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=eD(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&eZ("up",(e,t)=>{let{onTap:s}=this.node.getProps();s&&ek.update(()=>s(e,t))})}),eZ("down",(e,t)=>{this.startPress(e,t)}))}),t=eD(this.node.current,"blur",()=>{this.isPressing&&eZ("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=eO(e,t)}}startPress(e,t){this.isPressing=!0;let{onTapStart:s,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),s&&ek.update(()=>s(e,t))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!eW()}cancelPress(e,t){if(!this.checkPressEnd())return;let{onTapCancel:s}=this.node.getProps();s&&ek.update(()=>s(e,t))}mount(){let e=this.node.getProps(),t=eB(this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),s=eD(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=eO(t,s)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let eQ=new WeakMap,eJ=new WeakMap,e0=e=>{let t=eQ.get(e.target);t&&t(e)},e1=e=>{e.forEach(e0)},e5={some:0,all:1};class e2 extends eY{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:s,amount:i="some",once:r}=e,n={root:t?t.current:void 0,rootMargin:s,threshold:"number"==typeof i?i:e5[i]};return function(e,t,s){let i=function({root:e,...t}){let s=e||document;eJ.has(s)||eJ.set(s,{});let i=eJ.get(s),r=JSON.stringify(t);return i[r]||(i[r]=new IntersectionObserver(e1,{root:e,...t})),i[r]}(t);return eQ.set(e,s),i.observe(e),()=>{eQ.delete(e),i.unobserve(e)}}(this.node.current,n,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:s,onViewportLeave:i}=this.node.getProps(),n=t?s:i;n&&n(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return s=>e[s]!==t[s]}(e,t))&&this.startObserver()}unmount(){}}function e3(e,t){if(!Array.isArray(t))return!1;let s=t.length;if(s!==e.length)return!1;for(let i=0;i<s;i++)if(t[i]!==e[i])return!1;return!0}function e6(e,t,s){let i=e.getProps();return ey(i,t,void 0!==s?s:i.custom,function(e){let t={};return e.values.forEach((e,s)=>t[s]=e.get()),t}(e),function(e){let t={};return e.values.forEach((e,s)=>t[s]=e.getVelocity()),t}(e))}let e4="data-"+eh("framerAppearId"),e9=e=>1e3*e,e7=e=>e/1e3,e8={current:!1},te=e=>Array.isArray(e)&&"number"==typeof e[0],tt=([e,t,s,i])=>`cubic-bezier(${e}, ${t}, ${s}, ${i})`,ts={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tt([0,.65,.55,1]),circOut:tt([.55,0,1,.45]),backIn:tt([.31,.01,.66,-.59]),backOut:tt([.33,1.53,.69,.99])},ti=(e,t,s)=>(((1-3*s+3*t)*e+(3*s-6*t))*e+3*t)*e;function tr(e,t,s,i){if(e===t&&s===i)return eN;let r=t=>(function(e,t,s,i,r){let n,a;let o=0;do(n=ti(a=t+(s-t)/2,i,r)-e)>0?s=a:t=a;while(Math.abs(n)>1e-7&&++o<12);return a})(t,0,1,e,s);return e=>0===e||1===e?e:ti(r(e),t,i)}let tn=tr(.42,0,1,1),ta=tr(0,0,.58,1),to=tr(.42,0,.58,1),tl=e=>Array.isArray(e)&&"number"!=typeof e[0],td=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,tc=e=>t=>1-e(1-t),tu=e=>1-Math.sin(Math.acos(e)),th=tc(tu),tm=td(th),tp=tr(.33,1.53,.69,.99),tf=tc(tp),tx=td(tf),tg={linear:eN,easeIn:tn,easeInOut:to,easeOut:ta,circIn:tu,circInOut:tm,circOut:th,backIn:tf,backInOut:tx,backOut:tp,anticipate:e=>(e*=2)<1?.5*tf(e):.5*(2-Math.pow(2,-10*(e-1)))},ty=e=>{if(Array.isArray(e)){eN(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,s,i,r]=e;return tr(t,s,i,r)}return"string"==typeof e?(eN(void 0!==tg[e],`Invalid easing type '${e}'`),tg[e]):e},tv=(e,t)=>s=>!!(W(s)&&U.test(s)&&s.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(s,t)),tb=(e,t,s)=>i=>{if(!W(i))return i;let[r,n,a,o]=i.match($);return{[e]:parseFloat(r),[t]:parseFloat(n),[s]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},tj=e=>L(0,255,e),tw={...B,transform:e=>Math.round(tj(e))},tP={test:tv("rgb","red"),parse:tb("red","green","blue"),transform:({red:e,green:t,blue:s,alpha:i=1})=>"rgba("+tw.transform(e)+", "+tw.transform(t)+", "+tw.transform(s)+", "+z(I.transform(i))+")"},tN={test:tv("#"),parse:function(e){let t="",s="",i="",r="";return e.length>5?(t=e.substring(1,3),s=e.substring(3,5),i=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),s=e.substring(2,3),i=e.substring(3,4),r=e.substring(4,5),t+=t,s+=s,i+=i,r+=r),{red:parseInt(t,16),green:parseInt(s,16),blue:parseInt(i,16),alpha:r?parseInt(r,16)/255:1}},transform:tP.transform},tS={test:tv("hsl","hue"),parse:tb("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:s,alpha:i=1})=>"hsla("+Math.round(e)+", "+G.transform(z(t))+", "+G.transform(z(s))+", "+z(I.transform(i))+")"},tA={test:e=>tP.test(e)||tN.test(e)||tS.test(e),parse:e=>tP.test(e)?tP.parse(e):tS.test(e)?tS.parse(e):tN.parse(e),transform:e=>W(e)?e:e.hasOwnProperty("red")?tP.transform(e):tS.transform(e)},tk=(e,t,s)=>-s*e+s*t+e;function tE(e,t,s){return(s<0&&(s+=1),s>1&&(s-=1),s<1/6)?e+(t-e)*6*s:s<.5?t:s<2/3?e+(t-e)*(2/3-s)*6:e}let tT=(e,t,s)=>{let i=e*e;return Math.sqrt(Math.max(0,s*(t*t-i)+i))},tC=[tN,tP,tS],tM=e=>tC.find(t=>t.test(e));function tV(e){let t=tM(e);eN(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`);let s=t.parse(e);return t===tS&&(s=function({hue:e,saturation:t,lightness:s,alpha:i}){e/=360,s/=100;let r=0,n=0,a=0;if(t/=100){let i=s<.5?s*(1+t):s+t-s*t,o=2*s-i;r=tE(o,i,e+1/3),n=tE(o,i,e),a=tE(o,i,e-1/3)}else r=n=a=s;return{red:Math.round(255*r),green:Math.round(255*n),blue:Math.round(255*a),alpha:i}}(s)),s}let tD=(e,t)=>{let s=tV(e),i=tV(t),r={...s};return e=>(r.red=tT(s.red,i.red,e),r.green=tT(s.green,i.green,e),r.blue=tT(s.blue,i.blue,e),r.alpha=tk(s.alpha,i.alpha,e),tP.transform(r))},tF={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:eN},tR={regex:_,countKey:"Colors",token:"${c}",parse:tA.parse},tL={regex:$,countKey:"Numbers",token:"${n}",parse:B.parse};function tB(e,{regex:t,countKey:s,token:i,parse:r}){let n=e.tokenised.match(t);n&&(e["num"+s]=n.length,e.tokenised=e.tokenised.replace(t,i),e.values.push(...n.map(r)))}function tI(e){let t=e.toString(),s={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return s.value.includes("var(--")&&tB(s,tF),tB(s,tR),tB(s,tL),s}function tO(e){return tI(e).values}function tz(e){let{values:t,numColors:s,numVars:i,tokenised:r}=tI(e),n=t.length;return e=>{let t=r;for(let r=0;r<n;r++)t=r<i?t.replace(tF.token,e[r]):r<i+s?t.replace(tR.token,tA.transform(e[r])):t.replace(tL.token,z(e[r]));return t}}let t$=e=>"number"==typeof e?0:e,t_={test:function(e){var t,s;return isNaN(e)&&W(e)&&((null===(t=e.match($))||void 0===t?void 0:t.length)||0)+((null===(s=e.match(_))||void 0===s?void 0:s.length)||0)>0},parse:tO,createTransformer:tz,getAnimatableNone:function(e){let t=tO(e);return tz(e)(t.map(t$))}},tU=(e,t)=>s=>`${s>0?t:e}`;function tW(e,t){return"number"==typeof e?s=>tk(e,t,s):tA.test(e)?tD(e,t):e.startsWith("var(")?tU(e,t):tG(e,t)}let tY=(e,t)=>{let s=[...e],i=s.length,r=e.map((e,s)=>tW(e,t[s]));return e=>{for(let t=0;t<i;t++)s[t]=r[t](e);return s}},tq=(e,t)=>{let s={...e,...t},i={};for(let r in s)void 0!==e[r]&&void 0!==t[r]&&(i[r]=tW(e[r],t[r]));return e=>{for(let t in i)s[t]=i[t](e);return s}},tG=(e,t)=>{let s=t_.createTransformer(t),i=tI(e),r=tI(t);return i.numVars===r.numVars&&i.numColors===r.numColors&&i.numNumbers>=r.numNumbers?eO(tY(i.values,r.values),s):(eN(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tU(e,t))},tH=(e,t,s)=>{let i=t-e;return 0===i?1:(s-e)/i},tX=(e,t)=>s=>tk(e,t,s);function tZ(e,t,{clamp:s=!0,ease:i,mixer:r}={}){let n=e.length;if(eN(n===t.length,"Both input and output ranges must be the same length"),1===n)return()=>t[0];e[0]>e[n-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,s){let i=[],r=s||function(e){if("number"==typeof e);else if("string"==typeof e)return tA.test(e)?tD:tG;else if(Array.isArray(e))return tY;else if("object"==typeof e)return tq;return tX}(e[0]),n=e.length-1;for(let s=0;s<n;s++){let n=r(e[s],e[s+1]);t&&(n=eO(Array.isArray(t)?t[s]||eN:t,n)),i.push(n)}return i}(t,i,r),o=a.length,l=t=>{let s=0;if(o>1)for(;s<e.length-2&&!(t<e[s+1]);s++);let i=tH(e[s],e[s+1],t);return a[s](i)};return s?t=>l(L(e[0],e[n-1],t)):l}function tK({duration:e=300,keyframes:t,times:s,ease:i="easeInOut"}){let r=tl(i)?i.map(ty):ty(i),n={done:!1,value:t[0]},a=tZ((s&&s.length===t.length?s:function(e){let t=[0];return function(e,t){let s=e[e.length-1];for(let i=1;i<=t;i++){let r=tH(0,t,i);e.push(tk(s,1,r))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(r)?r:t.map(()=>r||to).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(n.value=a(t),n.done=t>=e,n)}}function tQ(e,t,s){var i,r;let n=Math.max(t-5,0);return i=s-e(n),(r=t-n)?1e3/r*i:0}function tJ(e,t){return e*Math.sqrt(1-t*t)}let t0=["duration","bounce"],t1=["stiffness","damping","mass"];function t5(e,t){return t.some(t=>void 0!==e[t])}function t2({keyframes:e,restDelta:t,restSpeed:s,...i}){let r;let n=e[0],a=e[e.length-1],o={done:!1,value:n},{stiffness:l,damping:d,mass:c,velocity:u,duration:h,isResolvedFromDuration:m}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!t5(e,t1)&&t5(e,t0)){let s=function({duration:e=800,bounce:t=.25,velocity:s=0,mass:i=1}){let r,n;eN(e<=e9(10),"Spring duration must be 10 seconds or less");let a=1-t;a=L(.05,1,a),e=L(.01,10,e7(e)),a<1?(r=t=>{let i=t*a,r=i*e;return .001-(i-s)/tJ(t,a)*Math.exp(-r)},n=t=>{let i=t*a*e,n=Math.pow(a,2)*Math.pow(t,2)*e,o=tJ(Math.pow(t,2),a);return(i*s+s-n)*Math.exp(-i)*(-r(t)+.001>0?-1:1)/o}):(r=t=>-.001+Math.exp(-t*e)*((t-s)*e+1),n=t=>e*e*(s-t)*Math.exp(-t*e));let o=function(e,t,s){let i=s;for(let s=1;s<12;s++)i-=e(i)/t(i);return i}(r,n,5/e);if(e=e9(e),isNaN(o))return{stiffness:100,damping:10,duration:e};{let t=Math.pow(o,2)*i;return{stiffness:t,damping:2*a*Math.sqrt(i*t),duration:e}}}(e);(t={...t,...s,velocity:0,mass:1}).isResolvedFromDuration=!0}return t}(i),p=u?-e7(u):0,f=d/(2*Math.sqrt(l*c)),x=a-n,g=e7(Math.sqrt(l/c)),y=5>Math.abs(x);if(s||(s=y?.01:2),t||(t=y?.005:.5),f<1){let e=tJ(g,f);r=t=>a-Math.exp(-f*g*t)*((p+f*g*x)/e*Math.sin(e*t)+x*Math.cos(e*t))}else if(1===f)r=e=>a-Math.exp(-g*e)*(x+(p+g*x)*e);else{let e=g*Math.sqrt(f*f-1);r=t=>{let s=Math.exp(-f*g*t),i=Math.min(e*t,300);return a-s*((p+f*g*x)*Math.sinh(i)+e*x*Math.cosh(i))/e}}return{calculatedDuration:m&&h||null,next:e=>{let i=r(e);if(m)o.done=e>=h;else{let n=p;0!==e&&(n=f<1?tQ(r,e,i):0);let l=Math.abs(n)<=s,d=Math.abs(a-i)<=t;o.done=l&&d}return o.value=o.done?a:i,o}}}function t3({keyframes:e,velocity:t=0,power:s=.8,timeConstant:i=325,bounceDamping:r=10,bounceStiffness:n=500,modifyTarget:a,min:o,max:l,restDelta:d=.5,restSpeed:c}){let u,h;let m=e[0],p={done:!1,value:m},f=e=>void 0!==o&&e<o||void 0!==l&&e>l,x=e=>void 0===o?l:void 0===l?o:Math.abs(o-e)<Math.abs(l-e)?o:l,g=s*t,y=m+g,v=void 0===a?y:a(y);v!==y&&(g=v-m);let b=e=>-g*Math.exp(-e/i),j=e=>v+b(e),w=e=>{let t=b(e),s=j(e);p.done=Math.abs(t)<=d,p.value=p.done?v:s},P=e=>{f(p.value)&&(u=e,h=t2({keyframes:[p.value,x(p.value)],velocity:tQ(j,e,p.value),damping:r,stiffness:n,restDelta:d,restSpeed:c}))};return P(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==u||(t=!0,w(e),P(e)),void 0!==u&&e>u)?h.next(e-u):(t||w(e),p)}}}let t6=e=>{let t=({timestamp:t})=>e(t);return{start:()=>ek.update(t,!0),stop:()=>eE(t),now:()=>eT.isProcessing?eT.timestamp:performance.now()}};function t4(e){let t=0,s=e.next(t);for(;!s.done&&t<2e4;)t+=50,s=e.next(t);return t>=2e4?1/0:t}let t9={decay:t3,inertia:t3,tween:tK,keyframes:tK,spring:t2};function t7({autoplay:e=!0,delay:t=0,driver:s=t6,keyframes:i,type:r="keyframes",repeat:n=0,repeatDelay:a=0,repeatType:o="loop",onPlay:l,onStop:d,onComplete:c,onUpdate:u,...h}){let m,p,f,x,g,y=1,v=!1,b=()=>{p=new Promise(e=>{m=e})};b();let j=t9[r]||tK;j!==tK&&"number"!=typeof i[0]&&(x=tZ([0,100],i,{clamp:!1}),i=[0,100]);let w=j({...h,keyframes:i});"mirror"===o&&(g=j({...h,keyframes:[...i].reverse(),velocity:-(h.velocity||0)}));let P="idle",N=null,S=null,A=null;null===w.calculatedDuration&&n&&(w.calculatedDuration=t4(w));let{calculatedDuration:k}=w,E=1/0,T=1/0;null!==k&&(T=(E=k+a)*(n+1)-a);let C=0,M=e=>{if(null===S)return;y>0&&(S=Math.min(S,e)),y<0&&(S=Math.min(e-T/y,S));let s=(C=null!==N?N:Math.round(e-S)*y)-t*(y>=0?1:-1),r=y>=0?s<0:s>T;C=Math.max(s,0),"finished"===P&&null===N&&(C=T);let l=C,d=w;if(n){let e=C/E,t=Math.floor(e),s=e%1;!s&&e>=1&&(s=1),1===s&&t--;let i=!!((t=Math.min(t,n+1))%2);i&&("reverse"===o?(s=1-s,a&&(s-=a/E)):"mirror"===o&&(d=g));let r=L(0,1,s);C>T&&(r="reverse"===o&&i?1:0),l=r*E}let c=r?{done:!1,value:i[0]}:d.next(l);x&&(c.value=x(c.value));let{done:h}=c;r||null===k||(h=y>=0?C>=T:C<=0);let m=null===N&&("finished"===P||"running"===P&&h);return u&&u(c.value),m&&F(),c},V=()=>{f&&f.stop(),f=void 0},D=()=>{P="idle",V(),m(),b(),S=A=null},F=()=>{P="finished",c&&c(),V(),m()},R=()=>{if(v)return;f||(f=s(M));let e=f.now();l&&l(),null!==N?S=e-N:S&&"finished"!==P||(S=e),"finished"===P&&b(),A=S,N=null,P="running",f.start()};e&&R();let B={then:(e,t)=>p.then(e,t),get time(){return e7(C)},set time(newTime){C=newTime=e9(newTime),null===N&&f&&0!==y?S=f.now()-newTime/y:N=newTime},get duration(){return e7(null===w.calculatedDuration?t4(w):w.calculatedDuration)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!f)return;y=newSpeed,B.time=e7(C)},get state(){return P},play:R,pause:()=>{P="paused",N=C},stop:()=>{v=!0,"idle"!==P&&(P="idle",d&&d(),D())},cancel:()=>{null!==A&&M(A),D()},complete:()=>{P="finished"},sample:e=>(S=0,M(e))};return B}let t8=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),se=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),st=(e,t)=>"spring"===t.type||"backgroundColor"===e||!function e(t){return!!(!t||"string"==typeof t&&ts[t]||te(t)||Array.isArray(t)&&t.every(e))}(t.ease),ss={type:"spring",stiffness:500,damping:25,restSpeed:10},si=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),sr={type:"keyframes",duration:.8},sn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},sa=(e,{keyframes:t})=>t.length>2?sr:k.has(e)?e.startsWith("scale")?si(t[1]):ss:sn,so=(e,t)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t_.test(t)||"0"===t)&&!t.startsWith("url(")),sl=new Set(["brightness","contrast","saturate","opacity"]);function sd(e){let[t,s]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[i]=s.match($)||[];if(!i)return e;let r=s.replace(i,""),n=sl.has(t)?1:0;return i!==s&&(n*=100),t+"("+n+r+")"}let sc=/([a-z-]*)\(.*?\)/g,su={...t_,getAnimatableNone:e=>{let t=e.match(sc);return t?t.map(sd).join(" "):e}},sh={...J,color:tA,backgroundColor:tA,outlineColor:tA,fill:tA,stroke:tA,borderColor:tA,borderTopColor:tA,borderRightColor:tA,borderBottomColor:tA,borderLeftColor:tA,filter:su,WebkitFilter:su},sm=e=>sh[e];function sp(e,t){let s=sm(e);return s!==su&&(s=t_),s.getAnimatableNone?s.getAnimatableNone(t):void 0}let sf=e=>/^0[^.\s]+$/.test(e);function sx(e,t){return e[t]||e.default||e}let sg=(e,t,s,i={})=>r=>{let n=sx(i,e)||{},a=n.delay||i.delay||0,{elapsed:o=0}=i;o-=e9(a);let l=function(e,t,s,i){let r,n;let a=so(t,s);r=Array.isArray(s)?[...s]:[null,s];let o=void 0!==i.from?i.from:e.get(),l=[];for(let e=0;e<r.length;e++){var d;null===r[e]&&(r[e]=0===e?o:r[e-1]),("number"==typeof(d=r[e])?0===d:null!==d?"none"===d||"0"===d||sf(d):void 0)&&l.push(e),"string"==typeof r[e]&&"none"!==r[e]&&"0"!==r[e]&&(n=r[e])}if(a&&l.length&&n)for(let e=0;e<l.length;e++)r[l[e]]=sp(t,n);return r}(t,e,s,n),d=l[0],c=l[l.length-1],u=so(e,d),h=so(e,c);eN(u===h,`You are trying to animate ${e} from "${d}" to "${c}". ${d} is not an animatable value - to enable this animation set ${d} to a value animatable to ${c} via the \`style\` property.`);let m={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...n,delay:-o,onUpdate:e=>{t.set(e),n.onUpdate&&n.onUpdate(e)},onComplete:()=>{r(),n.onComplete&&n.onComplete()}};if(!function({when:e,delay:t,delayChildren:s,staggerChildren:i,staggerDirection:r,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:d,...c}){return!!Object.keys(c).length}(n)&&(m={...m,...sa(e,m)}),m.duration&&(m.duration=e9(m.duration)),m.repeatDelay&&(m.repeatDelay=e9(m.repeatDelay)),!u||!h||e8.current||!1===n.type)return function({keyframes:e,delay:t,onUpdate:s,onComplete:i}){let r=()=>(s&&s(e[e.length-1]),i&&i(),{time:0,speed:1,duration:0,play:eN,pause:eN,stop:eN,then:e=>(e(),Promise.resolve()),cancel:eN,complete:eN});return t?t7({keyframes:[0,1],duration:0,delay:t,onComplete:r}):r()}(e8.current?{...m,delay:0}:m);if(t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){let s=function(e,t,{onUpdate:s,onComplete:i,...r}){let n,a;if(!(t8()&&se.has(t)&&!r.repeatDelay&&"mirror"!==r.repeatType&&0!==r.damping&&"inertia"!==r.type))return!1;let o=!1,l=()=>{a=new Promise(e=>{n=e})};l();let{keyframes:d,duration:c=300,ease:u,times:h}=r;if(st(t,r)){let e=t7({...r,repeat:0,delay:0}),t={done:!1,value:d[0]},s=[],i=0;for(;!t.done&&i<2e4;)t=e.sample(i),s.push(t.value),i+=10;h=void 0,d=s,c=i-10,u="linear"}let m=function(e,t,s,{delay:i=0,duration:r,repeat:n=0,repeatType:a="loop",ease:o,times:l}={}){let d={[t]:s};l&&(d.offset=l);let c=function e(t){if(t)return te(t)?tt(t):Array.isArray(t)?t.map(e):ts[t]}(o);return Array.isArray(c)&&(d.easing=c),e.animate(d,{delay:i,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:n+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,d,{...r,duration:c,ease:u,times:h});r.syncStart&&(m.startTime=eT.isProcessing?eT.timestamp:document.timeline?document.timeline.currentTime:performance.now());let p=()=>m.cancel(),f=()=>{ek.update(p),n(),l()};return m.onfinish=()=>{e.set(function(e,{repeat:t,repeatType:s="loop"}){let i=t&&"loop"!==s&&t%2==1?0:e.length-1;return e[i]}(d,r)),i&&i(),f()},{then:(e,t)=>a.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,eN),get time(){return e7(m.currentTime||0)},set time(newTime){m.currentTime=e9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return e7(c)},play:()=>{o||(m.play(),eE(p))},pause:()=>m.pause(),stop:()=>{if(o=!0,"idle"===m.playState)return;let{currentTime:t}=m;if(t){let s=t7({...r,autoplay:!1});e.setWithVelocity(s.sample(t-10).value,s.sample(t).value,10)}f()},complete:()=>m.finish(),cancel:f}}(t,e,m);if(s)return s}return t7(m)};function sy(e){return!!(T(e)&&e.add)}let sv=e=>/^\-?\d*\.?\d+$/.test(e);function sb(e,t){-1===e.indexOf(t)&&e.push(t)}function sj(e,t){let s=e.indexOf(t);s>-1&&e.splice(s,1)}class sw{constructor(){this.subscriptions=[]}add(e){return sb(this.subscriptions,e),()=>sj(this.subscriptions,e)}notify(e,t,s){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](e,t,s);else for(let r=0;r<i;r++){let i=this.subscriptions[r];i&&i(e,t,s)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let sP=e=>!isNaN(parseFloat(e)),sN={current:void 0};class sS{constructor(e,t={}){this.version="10.16.4",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;let{delta:s,timestamp:i}=eT;this.lastUpdated!==i&&(this.timeDelta=s,this.lastUpdated=i,ek.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>ek.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=sP(this.current),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new sw);let s=this.events[e].add(t);return"change"===e?()=>{s(),ek.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,s){this.set(t),this.prev=e,this.timeDelta=s}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return sN.current&&sN.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e,t;return this.canTrackVelocity?(e=parseFloat(this.current)-parseFloat(this.prev),(t=this.timeDelta)?1e3/t*e:0):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function sA(e,t){return new sS(e,t)}let sk=e=>t=>t.test(e),sE=[B,H,G,q,Z,X,{test:e=>"auto"===e,parse:e=>e}],sT=e=>sE.find(sk(e)),sC=[...sE,tA,t_],sM=e=>sC.find(sk(e));function sV(e,t,{delay:s=0,transitionOverride:i,type:r}={}){let{transition:n=e.getDefaultTransition(),transitionEnd:a,...o}=e.makeTargetAnimatable(t),l=e.getValue("willChange");i&&(n=i);let d=[],c=r&&e.animationState&&e.animationState.getState()[r];for(let t in o){let i=e.getValue(t),r=o[t];if(!i||void 0===r||c&&function({protectedKeys:e,needsAnimating:t},s){let i=e.hasOwnProperty(s)&&!0!==t[s];return t[s]=!1,i}(c,t))continue;let a={delay:s,elapsed:0,...n};if(window.HandoffAppearAnimations&&!i.hasAnimated){let s=e.getProps()[e4];s&&(a.elapsed=window.HandoffAppearAnimations(s,t,i,ek),a.syncStart=!0)}i.start(sg(t,i,r,e.shouldReduceMotion&&k.has(t)?{type:!1}:a));let u=i.animation;sy(l)&&(l.add(t),u.then(()=>l.remove(t))),d.push(u)}return a&&Promise.all(d).then(()=>{a&&function(e,t){let s=e6(e,t),{transitionEnd:i={},transition:r={},...n}=s?e.makeTargetAnimatable(s,!1):{};for(let t in n={...n,...i}){let s=ej(n[t]);e.hasValue(t)?e.getValue(t).set(s):e.addValue(t,sA(s))}}(e,a)}),d}function sD(e,t,s={}){let i=e6(e,t,s.custom),{transition:r=e.getDefaultTransition()||{}}=i||{};s.transitionOverride&&(r=s.transitionOverride);let n=i?()=>Promise.all(sV(e,i,s)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(i=0)=>{let{delayChildren:n=0,staggerChildren:a,staggerDirection:o}=r;return function(e,t,s=0,i=0,r=1,n){let a=[],o=(e.variantChildren.size-1)*i,l=1===r?(e=0)=>e*i:(e=0)=>o-e*i;return Array.from(e.variantChildren).sort(sF).forEach((e,i)=>{e.notify("AnimationStart",t),a.push(sD(e,t,{...n,delay:s+l(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,n+i,a,o,s)}:()=>Promise.resolve(),{when:o}=r;if(!o)return Promise.all([n(),a(s.delay)]);{let[e,t]="beforeChildren"===o?[n,a]:[a,n];return e().then(()=>t())}}function sF(e,t){return e.sortNodePosition(t)}let sR=[...m].reverse(),sL=m.length;function sB(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class sI extends eY{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:s})=>(function(e,t,s={}){let i;if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>sD(e,t,s)));else if("string"==typeof t)i=sD(e,t,s);else{let r="function"==typeof t?e6(e,t,s.custom):t;i=Promise.all(sV(e,r,s))}return i.then(()=>e.notify("AnimationComplete",t))})(e,t,s))),s={animate:sB(!0),whileInView:sB(),whileHover:sB(),whileTap:sB(),whileDrag:sB(),whileFocus:sB(),exit:sB()},i=!0,r=(t,s)=>{let i=e6(e,s);if(i){let{transition:e,transitionEnd:s,...r}=i;t={...t,...r,...s}}return t};function n(n,a){let o=e.getProps(),l=e.getVariantContext(!0)||{},d=[],c=new Set,m={},p=1/0;for(let t=0;t<sL;t++){var f;let x=sR[t],g=s[x],y=void 0!==o[x]?o[x]:l[x],v=u(y),b=x===a?g.isActive:null;!1===b&&(p=t);let j=y===l[x]&&y!==o[x]&&v;if(j&&i&&e.manuallyAnimateOnMount&&(j=!1),g.protectedKeys={...m},!g.isActive&&null===b||!y&&!g.prevProp||h(y)||"boolean"==typeof y)continue;let w=(f=g.prevProp,"string"==typeof y?y!==f:!!Array.isArray(y)&&!e3(y,f)),P=w||x===a&&g.isActive&&!j&&v||t>p&&v,N=Array.isArray(y)?y:[y],S=N.reduce(r,{});!1===b&&(S={});let{prevResolvedValues:A={}}=g,k={...A,...S},E=e=>{P=!0,c.delete(e),g.needsAnimating[e]=!0};for(let e in k){let t=S[e],s=A[e];m.hasOwnProperty(e)||(t!==s?ev(t)&&ev(s)?!e3(t,s)||w?E(e):g.protectedKeys[e]=!0:void 0!==t?E(e):c.add(e):void 0!==t&&c.has(e)?E(e):g.protectedKeys[e]=!0)}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(m={...m,...S}),i&&e.blockInitialAnimation&&(P=!1),P&&!j&&d.push(...N.map(e=>({animation:e,options:{type:x,...n}})))}if(c.size){let t={};c.forEach(s=>{let i=e.getBaseTarget(s);void 0!==i&&(t[s]=i)}),d.push({animation:t})}let x=!!d.length;return i&&!1===o.initial&&!e.manuallyAnimateOnMount&&(x=!1),i=!1,x?t(d):Promise.resolve()}return{animateChanges:n,setActive:function(t,i,r){var a;if(s[t].isActive===i)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach(e=>{var s;return null===(s=e.animationState)||void 0===s?void 0:s.setActive(t,i)}),s[t].isActive=i;let o=n(r,t);for(let e in s)s[e].protectedKeys={};return o},setAnimateFunction:function(s){t=s(e)},getState:()=>s}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),h(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}let sO=0;class sz extends eY{constructor(){super(...arguments),this.id=sO++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t,custom:s}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e,{custom:null!=s?s:this.node.getProps().custom});t&&!e&&r.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}let s$=(e,t)=>Math.abs(e-t);class s_{constructor(e,t,{transformPagePoint:s}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=sY(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,s=function(e,t){return Math.sqrt(s$(e.x,t.x)**2+s$(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!s)return;let{point:i}=e,{timestamp:r}=eT;this.history.push({...i,timestamp:r});let{onStart:n,onMove:a}=this.handlers;t||(n&&n(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=sU(t,this.transformPagePoint),ek.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{if(this.end(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let{onEnd:s,onSessionEnd:i}=this.handlers,r=sY("pointercancel"===e.type?this.lastMoveEventInfo:sU(t,this.transformPagePoint),this.history);this.startEvent&&s&&s(e,r),i&&i(e,r)},!eF(e))return;this.handlers=t,this.transformPagePoint=s;let i=sU(eR(e),this.transformPagePoint),{point:r}=i,{timestamp:n}=eT;this.history=[{...r,timestamp:n}];let{onSessionStart:a}=t;a&&a(e,sY(i,this.history)),this.removeListeners=eO(eB(window,"pointermove",this.handlePointerMove),eB(window,"pointerup",this.handlePointerUp),eB(window,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),eE(this.updatePoint)}}function sU(e,t){return t?{point:t(e.point)}:e}function sW(e,t){return{x:e.x-t.x,y:e.y-t.y}}function sY({point:e},t){return{point:e,delta:sW(e,sq(t)),offset:sW(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let s=e.length-1,i=null,r=sq(e);for(;s>=0&&(i=e[s],!(r.timestamp-i.timestamp>e9(.1)));)s--;if(!i)return{x:0,y:0};let n=e7(r.timestamp-i.timestamp);if(0===n)return{x:0,y:0};let a={x:(r.x-i.x)/n,y:(r.y-i.y)/n};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function sq(e){return e[e.length-1]}function sG(e){return e.max-e.min}function sH(e,t=0,s=.01){return Math.abs(e-t)<=s}function sX(e,t,s,i=.5){e.origin=i,e.originPoint=tk(t.min,t.max,e.origin),e.scale=sG(s)/sG(t),(sH(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=tk(s.min,s.max,e.origin)-e.originPoint,(sH(e.translate)||isNaN(e.translate))&&(e.translate=0)}function sZ(e,t,s,i){sX(e.x,t.x,s.x,i?i.originX:void 0),sX(e.y,t.y,s.y,i?i.originY:void 0)}function sK(e,t,s){e.min=s.min+t.min,e.max=e.min+sG(t)}function sQ(e,t,s){e.min=t.min-s.min,e.max=e.min+sG(t)}function sJ(e,t,s){sQ(e.x,t.x,s.x),sQ(e.y,t.y,s.y)}function s0(e,t,s){return{min:void 0!==t?e.min+t:void 0,max:void 0!==s?e.max+s-(e.max-e.min):void 0}}function s1(e,t){let s=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&([s,i]=[i,s]),{min:s,max:i}}function s5(e,t,s){return{min:s2(e,t),max:s2(e,s)}}function s2(e,t){return"number"==typeof e?e:e[t]||0}let s3=()=>({translate:0,scale:1,origin:0,originPoint:0}),s6=()=>({x:s3(),y:s3()}),s4=()=>({min:0,max:0}),s9=()=>({x:s4(),y:s4()});function s7(e){return[e("x"),e("y")]}function s8({top:e,left:t,right:s,bottom:i}){return{x:{min:t,max:s},y:{min:e,max:i}}}function ie(e){return void 0===e||1===e}function it({scale:e,scaleX:t,scaleY:s}){return!ie(e)||!ie(t)||!ie(s)}function is(e){return it(e)||ii(e)||e.z||e.rotate||e.rotateX||e.rotateY}function ii(e){var t,s;return(t=e.x)&&"0%"!==t||(s=e.y)&&"0%"!==s}function ir(e,t,s,i,r){return void 0!==r&&(e=i+r*(e-i)),i+s*(e-i)+t}function ia(e,t=0,s=1,i,r){e.min=ir(e.min,t,s,i,r),e.max=ir(e.max,t,s,i,r)}function io(e,{x:t,y:s}){ia(e.x,t.translate,t.scale,t.originPoint),ia(e.y,s.translate,s.scale,s.originPoint)}function il(e){return Number.isInteger(e)?e:e>1.0000000000001||e<.999999999999?e:1}function id(e,t){e.min=e.min+t,e.max=e.max+t}function ic(e,t,[s,i,r]){let n=void 0!==t[r]?t[r]:.5,a=tk(e.min,e.max,n);ia(e,t[s],t[i],a,t.scale)}let iu=["x","scaleX","originX"],ih=["y","scaleY","originY"];function im(e,t){ic(e.x,t,iu),ic(e.y,t,ih)}function ip(e,t){return s8(function(e,t){if(!t)return e;let s=t({x:e.left,y:e.top}),i=t({x:e.right,y:e.bottom});return{top:s.y,left:s.x,bottom:i.y,right:i.x}}(e.getBoundingClientRect(),t))}let ix=new WeakMap;class ig{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=s9(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:s}=this.visualElement;s&&!1===s.isPresent||(this.panSession=new s_(e,{onSessionStart:e=>{this.stopAnimation(),t&&this.snapToCursor(eR(e,"page").point)},onStart:(e,t)=>{let{drag:s,dragPropagation:i,onDragStart:r}=this.getProps();if(s&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=eU(s),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),s7(e=>{let t=this.getAxisMotionValue(e).get()||0;if(G.test(t)){let{projection:s}=this.visualElement;if(s&&s.layout){let i=s.layout.layoutBox[e];if(i){let e=sG(i);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),r&&ek.update(()=>r(e,t),!1,!0);let{animationState:n}=this.visualElement;n&&n.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:s,dragDirectionLock:i,onDirectionLock:r,onDrag:n}=this.getProps();if(!s&&!this.openGlobalLock)return;let{offset:a}=t;if(i&&null===this.currentDirection){this.currentDirection=function(e,t=10){let s=null;return Math.abs(e.y)>t?s="y":Math.abs(e.x)>t&&(s="x"),s}(a),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),n&&n(e,t)},onSessionEnd:(e,t)=>this.stop(e,t)},{transformPagePoint:this.visualElement.getTransformPagePoint()}))}stop(e,t){let s=this.isDragging;if(this.cancel(),!s)return;let{velocity:i}=t;this.startAnimation(i);let{onDragEnd:r}=this.getProps();r&&ek.update(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:s}=this.getProps();!s&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,s){let{drag:i}=this.getProps();if(!s||!iy(e,i,this.currentDirection))return;let r=this.getAxisMotionValue(e),n=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(n=function(e,{min:t,max:s},i){return void 0!==t&&e<t?e=i?tk(t,e,i.min):Math.max(e,t):void 0!==s&&e>s&&(e=i?tk(s,e,i.max):Math.min(e,s)),e}(n,this.constraints[e],this.elastic[e])),r.set(n)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),{layout:s}=this.visualElement.projection||{},i=this.constraints;e&&c(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(e,{top:t,left:s,bottom:i,right:r}){return{x:s0(e.x,s,r),y:s0(e.y,t,i)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:s5(e,"left","right"),y:s5(e,"top","bottom")}}(t),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&s7(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let s={};return void 0!==t.min&&(s.min=t.min-e.min),void 0!==t.max&&(s.max=t.max-e.min),s}(s.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:s}=this.getProps();if(!t||!c(t))return!1;let i=t.current;eN(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let n=function(e,t,s){let i=ip(e,s),{scroll:r}=t;return r&&(id(i.x,r.offset.x),id(i.y,r.offset.y)),i}(i,r.root,this.visualElement.getTransformPagePoint()),a={x:s1((e=r.layout.layoutBox).x,n.x),y:s1(e.y,n.y)};if(s){let e=s(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=s8(e))}return a}startAnimation(e){let{drag:t,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:n,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(s7(a=>{if(!iy(a,t,this.currentDirection))return;let l=o&&o[a]||{};n&&(l={min:0,max:0});let d={type:"inertia",velocity:s?e[a]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(a,d)})).then(a)}startAxisValueAnimation(e,t){let s=this.getAxisMotionValue(e);return s.start(sg(e,s,0,t))}stopAnimation(){s7(e=>this.getAxisMotionValue(e).stop())}getAxisMotionValue(e){let t="_drag"+e.toUpperCase(),s=this.visualElement.getProps();return s[t]||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){s7(t=>{let{drag:s}=this.getProps();if(!iy(t,s,this.currentDirection))return;let{projection:i}=this.visualElement,r=this.getAxisMotionValue(t);if(i&&i.layout){let{min:s,max:n}=i.layout.layoutBox[t];r.set(e[t]-tk(s,n,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:s}=this.visualElement;if(!c(t)||!s||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};s7(e=>{let t=this.getAxisMotionValue(e);if(t){let s=t.get();i[e]=function(e,t){let s=.5,i=sG(e),r=sG(t);return r>i?s=tH(t.min,t.max-i,e.min):i>r&&(s=tH(e.min,e.max-r,t.min)),L(0,1,s)}({min:s,max:s},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),s7(t=>{if(!iy(t,e,null))return;let s=this.getAxisMotionValue(t),{min:r,max:n}=this.constraints[t];s.set(tk(r,n,i[t]))})}addListeners(){if(!this.visualElement.current)return;ix.set(this.visualElement,this);let e=eB(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:s=!0}=this.getProps();t&&s&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();c(e)&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",t);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),t();let r=eD(window,"resize",()=>this.scalePositionWithinConstraints()),n=s.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(s7(t=>{let s=this.getAxisMotionValue(t);s&&(this.originPoint[t]+=e[t].translate,s.set(s.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),i(),n&&n()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:n=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:n,dragMomentum:a}}}function iy(e,t,s){return(!0===t||t===e)&&(null===s||s===e)}class iv extends eY{constructor(e){super(e),this.removeGroupControls=eN,this.removeListeners=eN,this.controls=new ig(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eN}unmount(){this.removeGroupControls(),this.removeListeners()}}let ib=e=>(t,s)=>{e&&ek.update(()=>e(t,s))};class ij extends eY{constructor(){super(...arguments),this.removePointerDownListener=eN}onPointerDown(e){this.session=new s_(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:ib(e),onStart:ib(t),onMove:s,onEnd:(e,t)=>{delete this.session,i&&ek.update(()=>i(e,t))}}}mount(){this.removePointerDownListener=eB(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iw={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iP(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let iN={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!H.test(e))return e;e=parseFloat(e)}let s=iP(e,t.target.x),i=iP(e,t.target.y);return`${s}% ${i}%`}};class iS extends i.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;Object.assign(S,ik),r&&(t.group&&t.group.add(r),s&&s.register&&i&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),iw.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:s,drag:i,isPresent:r}=this.props,n=s.projection;return n&&(n.isPresent=r,i||e.layoutDependency!==t||void 0===t?n.willUpdate():this.safeToRemove(),e.isPresent===r||(r?n.promote():n.relegate()||ek.postRender(()=>{let e=n.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function iA(e){let[t,s]=function(){let e=(0,i.useContext)(a);if(null===e)return[!0,null];let{isPresent:t,onExitComplete:s,register:r}=e,n=(0,i.useId)();return(0,i.useEffect)(()=>r(n),[]),!t&&s?[!1,()=>s&&s(n)]:[!0]}(),r=(0,i.useContext)(b);return i.createElement(iS,{...e,layoutGroup:r,switchLayoutGroup:(0,i.useContext)(j),isPresent:t,safeToRemove:s})}let ik={borderRadius:{...iN,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iN,borderTopRightRadius:iN,borderBottomLeftRadius:iN,borderBottomRightRadius:iN,boxShadow:{correct:(e,{treeScale:t,projectionDelta:s})=>{let i=t_.parse(e);if(i.length>5)return e;let r=t_.createTransformer(e),n="number"!=typeof i[0]?1:0,a=s.x.scale*t.x,o=s.y.scale*t.y;i[0+n]/=a,i[1+n]/=o;let l=tk(a,o,.5);return"number"==typeof i[2+n]&&(i[2+n]/=l),"number"==typeof i[3+n]&&(i[3+n]/=l),r(i)}}},iE=["TopLeft","TopRight","BottomLeft","BottomRight"],iT=iE.length,iC=e=>"string"==typeof e?parseFloat(e):e,iM=e=>"number"==typeof e||H.test(e);function iV(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let iD=iR(0,.5,th),iF=iR(.5,.95,eN);function iR(e,t,s){return i=>i<e?0:i>t?1:s(tH(e,t,i))}function iL(e,t){e.min=t.min,e.max=t.max}function iB(e,t){iL(e.x,t.x),iL(e.y,t.y)}function iI(e,t,s,i,r){return e-=t,e=i+1/s*(e-i),void 0!==r&&(e=i+1/r*(e-i)),e}function iO(e,t,[s,i,r],n,a){!function(e,t=0,s=1,i=.5,r,n=e,a=e){if(G.test(t)&&(t=parseFloat(t),t=tk(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let o=tk(n.min,n.max,i);e===n&&(o-=t),e.min=iI(e.min,t,s,o,r),e.max=iI(e.max,t,s,o,r)}(e,t[s],t[i],t[r],t.scale,n,a)}let iz=["x","scaleX","originX"],i$=["y","scaleY","originY"];function i_(e,t,s,i){iO(e.x,t,iz,s?s.x:void 0,i?i.x:void 0),iO(e.y,t,i$,s?s.y:void 0,i?i.y:void 0)}function iU(e){return 0===e.translate&&1===e.scale}function iW(e){return iU(e.x)&&iU(e.y)}function iY(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function iq(e){return sG(e.x)/sG(e.y)}class iG{constructor(){this.members=[]}add(e){sb(this.members,e),e.scheduleRender()}remove(e){if(sj(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let s=this.members.findIndex(t=>e===t);if(0===s)return!1;for(let e=s;e>=0;e--){let s=this.members[e];if(!1!==s.isPresent){t=s;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,t&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:i}=e.options;!1===i&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:s}=e;t.onExitComplete&&t.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iH(e,t,s){let i="",r=e.x.translate/t.x,n=e.y.translate/t.y;if((r||n)&&(i=`translate3d(${r}px, ${n}px, 0) `),(1!==t.x||1!==t.y)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),s){let{rotate:e,rotateX:t,rotateY:r}=s;e&&(i+=`rotate(${e}deg) `),t&&(i+=`rotateX(${t}deg) `),r&&(i+=`rotateY(${r}deg) `)}let a=e.x.scale*t.x,o=e.y.scale*t.y;return(1!==a||1!==o)&&(i+=`scale(${a}, ${o})`),i||"none"}let iX=(e,t)=>e.depth-t.depth;class iZ{constructor(){this.children=[],this.isDirty=!1}add(e){sb(this.children,e),this.isDirty=!0}remove(e){sj(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(iX),this.isDirty=!1,this.children.forEach(e)}}let iK=["","X","Y","Z"],iQ=0,iJ={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function i0({attachResizeListener:e,defaultParent:t,measureScroll:s,checkIsScrollRoot:i,resetTransform:r}){return class{constructor(e={},s=null==t?void 0:t()){this.id=iQ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{iJ.totalNodes=iJ.resolvedTargetDeltas=iJ.recalculatedProjection=0,this.nodes.forEach(i2),this.nodes.forEach(re),this.nodes.forEach(rt),this.nodes.forEach(i3),window.MotionDebug&&window.MotionDebug.record(iJ)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new iZ)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new sw),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let s=this.eventHandlers.get(e);s&&s.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,s=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),s&&(r||i)&&(this.isLayoutDirty=!0),e){let s;let i=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,s&&s(),s=function(e,t){let s=performance.now(),i=({timestamp:t})=>{let r=t-s;r>=250&&(eE(i),e(r-250))};return ek.read(i,!0),()=>eE(i)}(i,0),iw.hasAnimatedSinceResize&&(iw.hasAnimatedSinceResize=!1,this.nodes.forEach(i8))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:s,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||ro,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!iY(this.targetLayout,i)||s,d=!t&&s;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||d||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,d);let t={...sx(r,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||i8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,eE(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rs),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:s}=this.options;if(void 0===t&&!s)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i4);return}this.isUpdating||this.nodes.forEach(i9),this.isUpdating=!1,this.nodes.forEach(i7),this.nodes.forEach(i1),this.nodes.forEach(i5),this.clearAllSnapshots();let e=performance.now();eT.delta=L(0,1e3/60,e-eT.timestamp),eT.timestamp=e,eT.isProcessing=!0,eC.update.process(eT),eC.preRender.process(eT),eC.render.process(eT),eT.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(i6),this.sharedNodes.forEach(ri)}scheduleUpdateProjection(){ek.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){ek.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=s9(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:i(this.instance),offset:s(this.instance)})}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!iW(this.projectionDelta),s=this.getTransformTemplate(),i=s?s(this.latestValues,""):void 0,n=i!==this.prevTransformTemplateValue;e&&(t||is(this.latestValues)||n)&&(r(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let s=this.measurePageBox(),i=this.removeElementScroll(s);return e&&(i=this.removeTransform(i)),rc((t=i).x),rc(t.y),{animationId:this.root.animationId,measuredBox:s,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return s9();let t=e.measureViewportBox(),{scroll:s}=this.root;return s&&(id(t.x,s.offset.x),id(t.y,s.offset.y)),t}removeElementScroll(e){let t=s9();iB(t,e);for(let s=0;s<this.path.length;s++){let i=this.path[s],{scroll:r,options:n}=i;if(i!==this.root&&r&&n.layoutScroll){if(r.isRoot){iB(t,e);let{scroll:s}=this.root;s&&(id(t.x,-s.offset.x),id(t.y,-s.offset.y))}id(t.x,r.offset.x),id(t.y,r.offset.y)}}return t}applyTransform(e,t=!1){let s=s9();iB(s,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];!t&&i.options.layoutScroll&&i.scroll&&i!==i.root&&im(s,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),is(i.latestValues)&&im(s,i.latestValues)}return is(this.latestValues)&&im(s,this.latestValues),s}removeTransform(e){let t=s9();iB(t,e);for(let e=0;e<this.path.length;e++){let s=this.path[e];if(!s.instance||!is(s.latestValues))continue;it(s.latestValues)&&s.updateSnapshot();let i=s9();iB(i,s.measurePageBox()),i_(t,s.latestValues,s.snapshot?s.snapshot.layoutBox:void 0,i)}return is(this.latestValues)&&i_(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eT.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,s,i,r;let n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);let a=!!this.resumingFrom||this!==n;if(!(e||a&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:o,layoutId:l}=this.options;if(this.layout&&(o||l)){if(this.resolvedRelativeTargetAt=eT.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=s9(),this.relativeTargetOrigin=s9(),sJ(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),iB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=s9(),this.targetWithTransforms=s9()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),s=this.target,i=this.relativeTarget,r=this.relativeParent.target,sK(s.x,i.x,r.x),sK(s.y,i.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iB(this.target,this.layout.layoutBox),io(this.target,this.targetDelta)):iB(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=s9(),this.relativeTargetOrigin=s9(),sJ(this.relativeTargetOrigin,this.target,e.target),iB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}iJ.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||it(this.parent.latestValues)||ii(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),s=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(i=!1),s&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===eT.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;iB(this.layoutCorrected,this.layout.layoutBox);let a=this.treeScale.x,o=this.treeScale.y;(function(e,t,s,i=!1){let r,n;let a=s.length;if(a){t.x=t.y=1;for(let o=0;o<a;o++){n=(r=s[o]).projectionDelta;let a=r.instance;(!a||!a.style||"contents"!==a.style.display)&&(i&&r.options.layoutScroll&&r.scroll&&r!==r.root&&im(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),n&&(t.x*=n.x.scale,t.y*=n.y.scale,io(e,n)),i&&is(r.latestValues)&&im(e,r.latestValues))}t.x=il(t.x),t.y=il(t.y)}})(this.layoutCorrected,this.treeScale,this.path,s),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox);let{target:l}=t;if(!l){this.projectionTransform&&(this.projectionDelta=s6(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=s6(),this.projectionDeltaWithTransform=s6());let d=this.projectionTransform;sZ(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=iH(this.projectionDelta,this.treeScale),(this.projectionTransform!==d||this.treeScale.x!==a||this.treeScale.y!==o)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),iJ.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){let s;let i=this.snapshot,r=i?i.latestValues:{},n={...this.latestValues},a=s6();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let o=s9(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),d=this.getStack(),c=!d||d.members.length<=1,u=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(ra));this.animationProgress=0,this.mixTargetDelta=t=>{let i=t/1e3;if(rr(a.x,e.x,i),rr(a.y,e.y,i),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var d,h,m,p;sJ(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,rn(m.x,p.x,o.x,i),rn(m.y,p.y,o.y,i),s&&(d=this.relativeTarget,h=s,d.x.min===h.x.min&&d.x.max===h.x.max&&d.y.min===h.y.min&&d.y.max===h.y.max)&&(this.isProjectionDirty=!1),s||(s=s9()),iB(s,this.relativeTarget)}l&&(this.animationValues=n,function(e,t,s,i,r,n){r?(e.opacity=tk(0,void 0!==s.opacity?s.opacity:1,iD(i)),e.opacityExit=tk(void 0!==t.opacity?t.opacity:1,0,iF(i))):n&&(e.opacity=tk(void 0!==t.opacity?t.opacity:1,void 0!==s.opacity?s.opacity:1,i));for(let r=0;r<iT;r++){let n=`border${iE[r]}Radius`,a=iV(t,n),o=iV(s,n);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||iM(a)===iM(o)?(e[n]=Math.max(tk(iC(a),iC(o),i),0),(G.test(o)||G.test(a))&&(e[n]+="%")):e[n]=o)}(t.rotate||s.rotate)&&(e.rotate=tk(t.rotate||0,s.rotate||0,i))}(n,r,this.latestValues,i,u,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(eE(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ek.update(()=>{iw.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,s){let i=T(0)?0:sA(0);return i.start(sg("",i,1e3,s)),i.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:s,layout:i,latestValues:r}=e;if(t&&s&&i){if(this!==e&&this.layout&&i&&ru(this.options.animationType,this.layout.layoutBox,i.layoutBox)){s=this.target||s9();let t=sG(this.layout.layoutBox.x);s.x.min=e.target.x.min,s.x.max=s.x.min+t;let i=sG(this.layout.layoutBox.y);s.y.min=e.target.y.min,s.y.max=s.y.min+i}iB(t,s),im(t,r),sZ(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new iG),this.sharedNodes.get(e).add(t);let s=t.options.initialPromotionConfig;t.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:s}={}){let i=this.getStack();i&&i.promote(this,s),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:s}=e;if((s.rotate||s.rotateX||s.rotateY||s.rotateZ)&&(t=!0),!t)return;let i={};for(let t=0;t<iK.length;t++){let r="rotate"+iK[t];s[r]&&(i[r]=s[r],e.setStaticValue(r,0))}for(let t in e.render(),i)e.setStaticValue(t,i[t]);e.scheduleRender()}getProjectionStyles(e={}){var t,s;let i={};if(!this.instance||this.isSVG)return i;if(!this.isVisible)return{visibility:"hidden"};i.visibility="";let r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=ew(e.pointerEvents)||"",i.transform=r?r(this.latestValues,""):"none",i;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=ew(e.pointerEvents)||""),this.hasProjected&&!is(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let a=n.animationValues||n.latestValues;this.applyTransformsToTarget(),i.transform=iH(this.projectionDeltaWithTransform,this.treeScale,a),r&&(i.transform=r(a,i.transform));let{x:o,y:l}=this.projectionDelta;for(let e in i.transformOrigin=`${100*o.origin}% ${100*l.origin}% 0`,n.animationValues?i.opacity=n===this?null!==(s=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==s?s:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:i.opacity=n===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0,S){if(void 0===a[e])continue;let{correct:t,applyTo:s}=S[e],r="none"===i.transform?a[e]:t(a[e],n);if(s){let e=s.length;for(let t=0;t<e;t++)i[s[t]]=r}else i[e]=r}return this.options.layoutId&&(i.pointerEvents=n===this?ew(e.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(i4),this.root.sharedNodes.clear()}}}function i1(e){e.updateLayout()}function i5(e){var t;let s=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&s&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:i}=e.layout,{animationType:r}=e.options,n=s.source!==e.layout.source;"size"===r?s7(e=>{let i=n?s.measuredBox[e]:s.layoutBox[e],r=sG(i);i.min=t[e].min,i.max=i.min+r}):ru(r,s.layoutBox,t)&&s7(i=>{let r=n?s.measuredBox[i]:s.layoutBox[i],a=sG(t[i]);r.max=r.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[i].max=e.relativeTarget[i].min+a)});let a=s6();sZ(a,t,s.layoutBox);let o=s6();n?sZ(o,e.applyTransform(i,!0),s.measuredBox):sZ(o,t,s.layoutBox);let l=!iW(a),d=!1;if(!e.resumeFrom){let i=e.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:r,layout:n}=i;if(r&&n){let a=s9();sJ(a,s.layoutBox,r.layoutBox);let o=s9();sJ(o,t,n.layoutBox),iY(a,o)||(d=!0),i.options.layoutRoot&&(e.relativeTarget=o,e.relativeTargetOrigin=a,e.relativeParent=i)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:s,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:d})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function i2(e){iJ.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function i3(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function i6(e){e.clearSnapshot()}function i4(e){e.clearMeasurements()}function i9(e){e.isLayoutDirty=!1}function i7(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function i8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function re(e){e.resolveTargetDelta()}function rt(e){e.calcProjection()}function rs(e){e.resetRotation()}function ri(e){e.removeLeadSnapshot()}function rr(e,t,s){e.translate=tk(t.translate,0,s),e.scale=tk(t.scale,1,s),e.origin=t.origin,e.originPoint=t.originPoint}function rn(e,t,s,i){e.min=tk(t.min,s.min,i),e.max=tk(t.max,s.max,i)}function ra(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let ro={duration:.45,ease:[.4,0,.1,1]},rl=e=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(e),rd=rl("applewebkit/")&&!rl("chrome/")?Math.round:eN;function rc(e){e.min=rd(e.min),e.max=rd(e.max)}function ru(e,t,s){return"position"===e||"preserve-aspect"===e&&!sH(iq(t),iq(s),.2)}let rh=i0({attachResizeListener:(e,t)=>eD(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rm={current:void 0},rp=i0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rm.current){let e=new rh({});e.mount(window),e.setOptions({layoutScroll:!0}),rm.current=e}return rm.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position}),rf=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function rx(e,t,s=1){eN(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[i,r]=function(e){let t=rf.exec(e);if(!t)return[,];let[,s,i]=t;return[s,i]}(e);if(!i)return;let n=window.getComputedStyle(t).getPropertyValue(i);if(n){let e=n.trim();return sv(e)?parseFloat(e):e}return F(r)?rx(r,t,s+1):r}let rg=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ry=e=>rg.has(e),rv=e=>Object.keys(e).some(ry),rb=e=>e===B||e===H,rj=(e,t)=>parseFloat(e.split(", ")[t]),rw=(e,t)=>(s,{transform:i})=>{if("none"===i||!i)return 0;let r=i.match(/^matrix3d\((.+)\)$/);if(r)return rj(r[1],t);{let t=i.match(/^matrix\((.+)\)$/);return t?rj(t[1],e):0}},rP=new Set(["x","y","z"]),rN=A.filter(e=>!rP.has(e)),rS={width:({x:e},{paddingLeft:t="0",paddingRight:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),height:({y:e},{paddingTop:t="0",paddingBottom:s="0"})=>e.max-e.min-parseFloat(t)-parseFloat(s),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:rw(4,13),y:rw(5,14)};rS.translateX=rS.x,rS.translateY=rS.y;let rA=(e,t,s)=>{let i=t.measureViewportBox(),r=getComputedStyle(t.current),{display:n}=r,a={};"none"===n&&t.setStaticValue("display",e.display||"block"),s.forEach(e=>{a[e]=rS[e](i,r)}),t.render();let o=t.measureViewportBox();return s.forEach(s=>{let i=t.getValue(s);i&&i.jump(a[s]),e[s]=rS[s](o,r)}),e},rk=(e,t,s={},i={})=>{t={...t},i={...i};let r=Object.keys(t).filter(ry),n=[],a=!1,l=[];if(r.forEach(r=>{let o;let d=e.getValue(r);if(!e.hasValue(r))return;let c=s[r],u=sT(c),h=t[r];if(ev(h)){let e=h.length,t=null===h[0]?1:0;u=sT(c=h[t]);for(let s=t;s<e&&null!==h[s];s++)o?eN(sT(h[s])===o,"All keyframes must be of the same type"):eN((o=sT(h[s]))===u||rb(u)&&rb(o),"Keyframes must be of the same dimension as the current value")}else o=sT(h);if(u!==o){if(rb(u)&&rb(o)){let e=d.get();"string"==typeof e&&d.set(parseFloat(e)),"string"==typeof h?t[r]=parseFloat(h):Array.isArray(h)&&o===H&&(t[r]=h.map(parseFloat))}else(null==u?void 0:u.transform)&&(null==o?void 0:o.transform)&&(0===c||0===h)?0===c?d.set(o.transform(c)):t[r]=u.transform(h):(a||(n=function(e){let t=[];return rN.forEach(s=>{let i=e.getValue(s);void 0!==i&&(t.push([s,i.get()]),i.set(s.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),a=!0),l.push(r),i[r]=void 0!==i[r]?i[r]:t[r],d.jump(h))}}),!l.length)return{target:t,transitionEnd:i};{let s=l.indexOf("height")>=0?window.pageYOffset:null,r=rA(t,e,l);return n.length&&n.forEach(([t,s])=>{e.getValue(t).set(s)}),e.render(),o&&null!==s&&window.scrollTo({top:s}),{target:r,transitionEnd:i}}},rE=(e,t,s,i)=>{let r=function(e,{...t},s){let i=e.current;if(!(i instanceof Element))return{target:t,transitionEnd:s};for(let r in s&&(s={...s}),e.values.forEach(e=>{let t=e.get();if(!F(t))return;let s=rx(t,i);s&&e.set(s)}),t){let e=t[r];if(!F(e))continue;let n=rx(e,i);n&&(t[r]=n,s||(s={}),void 0===s[r]&&(s[r]=e))}return{target:t,transitionEnd:s}}(e,t,i);return function(e,t,s,i){return rv(t)?rk(e,t,s,i):{target:t,transitionEnd:i}}(e,t=r.target,s,i=r.transitionEnd)},rT={current:null},rC={current:!1},rM=new WeakMap,rV=Object.keys(v),rD=rV.length,rF=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],rR=p.length;class rL{constructor({parent:e,props:t,presenceContext:s,reducedMotionConfig:i,visualState:r},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>ek.render(this.render,!1,!0);let{latestValues:a,renderState:o}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=o,this.parent=e,this.props=t,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=n,this.isControllingVariants=f(t),this.isVariantNode=x(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:l,...d}=this.scrapeMotionValuesFromProps(t,{});for(let e in d){let t=d[e];void 0!==a[e]&&T(t)&&(t.set(a[e],!1),sy(l)&&l.add(e))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,rM.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),rC.current||function(){if(rC.current=!0,o){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>rT.current=e.matches;e.addListener(t),t()}else rT.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rT.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in rM.delete(this.current),this.projection&&this.projection.unmount(),eE(this.notifyUpdate),eE(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){let s=k.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&ek.update(this.notifyUpdate,!1,!0),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{i(),r()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},s,i,r){let n,a;for(let e=0;e<rD;e++){let s=rV[e],{isEnabled:i,Feature:r,ProjectionNode:o,MeasureLayout:l}=v[s];o&&(n=o),i(t)&&(!this.features[s]&&r&&(this.features[s]=new r(this)),l&&(a=l))}if(!this.projection&&n){this.projection=new n(this.latestValues,this.parent&&this.parent.projection);let{layoutId:e,layout:s,drag:i,dragConstraints:a,layoutScroll:o,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:s,alwaysMeasureLayout:!!i||a&&c(a),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,layoutScroll:o,layoutRoot:l})}return a}updateFeatures(){for(let e in this.features){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):s9()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<rF.length;t++){let s=rF[t];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);let i=e["on"+s];i&&(this.propEventSubscriptions[s]=this.on(s,i))}this.prevMotionValues=function(e,t,s){let{willChange:i}=t;for(let r in t){let n=t[r],a=s[r];if(T(n))e.addValue(r,n),sy(i)&&i.add(r);else if(T(a))e.addValue(r,sA(n,{owner:e})),sy(i)&&i.remove(r);else if(a!==n){if(e.hasValue(r)){let t=e.getValue(r);t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,sA(void 0!==t?t:n,{owner:e}))}}}for(let i in s)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}let t={};for(let e=0;e<rR;e++){let s=p[e],i=this.props[s];(u(i)||!1===i)&&(t[s]=i)}return t}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return void 0===s&&void 0!==t&&(s=sA(t,{owner:this}),this.addValue(e,s)),s}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let{initial:s}=this.props,i="string"==typeof s||"object"==typeof s?null===(t=ey(this.props,s))||void 0===t?void 0:t[e]:void 0;if(s&&void 0!==i)return i;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||T(r)?void 0!==this.initialValues[e]&&void 0===i?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new sw),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class rB extends rL{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:s}){delete t[e],delete s[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...s},{transformValues:i},r){let n=function(e,t,s){let i={};for(let r in e){let e=function(e,t){if(t)return(t[e]||t.default||t).from}(r,t);if(void 0!==e)i[r]=e;else{let e=s.getValue(r);e&&(i[r]=e.get())}}return i}(s,e||{},this);if(i&&(t&&(t=i(t)),s&&(s=i(s)),n&&(n=i(n))),r){!function(e,t,s){var i,r;let n=Object.keys(t).filter(t=>!e.hasValue(t)),a=n.length;if(a)for(let o=0;o<a;o++){let a=n[o],l=t[a],d=null;Array.isArray(l)&&(d=l[0]),null===d&&(d=null!==(r=null!==(i=s[a])&&void 0!==i?i:e.readValue(a))&&void 0!==r?r:t[a]),null!=d&&("string"==typeof d&&(sv(d)||sf(d))?d=parseFloat(d):!sM(d)&&t_.test(l)&&(d=sp(a,l)),e.addValue(a,sA(d,{owner:e})),void 0===s[a]&&(s[a]=d),null!==d&&e.setBaseTarget(a,d))}}(this,s,n);let e=rE(this,s,n,t);t=e.transitionEnd,s=e.target}return{transition:e,transitionEnd:t,...s}}}class rI extends rB{readValueFromInstance(e,t){if(k.has(t)){let e=sm(t);return e&&e.default||0}{let s=window.getComputedStyle(e),i=(D(t)?s.getPropertyValue(t):s[t])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:t}){return ip(e,t)}build(e,t,s,i){ee(e,t,s,i.transformTemplate)}scrapeMotionValuesFromProps(e,t){return ex(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;T(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,s,i){em(e,t,s,i)}}class rO extends rB{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(k.has(t)){let e=sm(t);return e&&e.default||0}return t=ep.has(t)?t:eh(t),e.getAttribute(t)}measureInstanceViewportBox(){return s9()}scrapeMotionValuesFromProps(e,t){return eg(e,t)}build(e,t,s,i){ed(e,t,s,this.isSVGTag,i.transformTemplate)}renderInstance(e,t,s,i){ef(e,t,s,i)}mount(e){this.isSVGTag=eu(e.tagName),super.mount(e)}}let rz=(e,t)=>N(e)?new rO(t,{enableHardwareAcceleration:!1}):new rI(t,{enableHardwareAcceleration:!0}),r$={animation:{Feature:sI},exit:{Feature:sz},inView:{Feature:e2},tap:{Feature:eK},focus:{Feature:eH},hover:{Feature:eG},pan:{Feature:ij},drag:{Feature:iv,ProjectionNode:rp,MeasureLayout:iA},layout:{ProjectionNode:rp,MeasureLayout:iA}},r_=function(e){function t(t,s={}){return function({preloadedFeatures:e,createVisualElement:t,useRender:s,useVisualState:h,Component:m}){e&&function(e){for(let t in e)v[t]={...v[t],...e[t]}}(e);let p=(0,i.forwardRef)(function(p,x){var y;let v;let w={...(0,i.useContext)(r),...p,layoutId:function({layoutId:e}){let t=(0,i.useContext)(b).id;return t&&void 0!==e?t+"-"+e:e}(p)},{isStatic:P}=w,N=function(e){let{initial:t,animate:s}=function(e,t){if(f(e)){let{initial:t,animate:s}=e;return{initial:!1===t||u(t)?t:void 0,animate:u(s)?s:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(n));return(0,i.useMemo)(()=>({initial:t,animate:s}),[g(t),g(s)])}(p),S=h(p,P);if(!P&&o){N.visualElement=function(e,t,s,o){let{visualElement:c}=(0,i.useContext)(n),u=(0,i.useContext)(d),h=(0,i.useContext)(a),m=(0,i.useContext)(r).reducedMotion,p=(0,i.useRef)();o=o||u.renderer,!p.current&&o&&(p.current=o(e,{visualState:t,parent:c,props:s,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:m}));let f=p.current;(0,i.useInsertionEffect)(()=>{f&&f.update(s,h)});let x=(0,i.useRef)(!!window.HandoffAppearAnimations);return l(()=>{f&&(f.render(),x.current&&f.animationState&&f.animationState.animateChanges())}),(0,i.useEffect)(()=>{f&&(f.updateFeatures(),!x.current&&f.animationState&&f.animationState.animateChanges(),window.HandoffAppearAnimations=void 0,x.current=!1)}),f}(m,S,w,t);let s=(0,i.useContext)(j),o=(0,i.useContext)(d).strict;N.visualElement&&(v=N.visualElement.loadFeatures(w,o,e,s))}return i.createElement(n.Provider,{value:N},v&&N.visualElement?i.createElement(v,{visualElement:N.visualElement,...w}):null,s(m,p,(y=N.visualElement,(0,i.useCallback)(e=>{e&&S.mount&&S.mount(e),y&&(e?y.mount(e):y.unmount()),x&&("function"==typeof x?x(e):c(x)&&(x.current=e))},[y])),S,P,N.visualElement))});return p[w]=m,p}(e(t,s))}if("undefined"==typeof Proxy)return t;let s=new Map;return new Proxy(t,{get:(e,i)=>(s.has(i)||s.set(i,t(i)),s.get(i))})}((e,t)=>(function(e,{forwardMotionProps:t=!1},s,r){return{...N(e)?eM:eV,preloadedFeatures:s,useRender:function(e=!1){return(t,s,r,{latestValues:n},a)=>{let o=(N(t)?function(e,t,s,r){let n=(0,i.useMemo)(()=>{let s=ec();return ed(s,t,{enableHardwareAcceleration:!1},eu(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){let t={};es(t,e.style,e),n.style={...t,...n.style}}return n}:function(e,t,s){let r={},n=function(e,t,s){let r=e.style||{},n={};return es(n,r,e),Object.assign(n,function({transformTemplate:e},t,s){return(0,i.useMemo)(()=>{let i=et();return ee(i,t,{enableHardwareAcceleration:!s},e),Object.assign({},i.vars,i.style)},[t])}(e,t,s)),e.transformValues?e.transformValues(n):n}(e,t,s);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(s,n,a,t),l={...function(e,t,s){let i={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(en(r)||!0===s&&er(r)||!t&&!er(r)||e.draggable&&r.startsWith("onDrag"))&&(i[r]=e[r]);return i}(s,"string"==typeof t,e),...o,ref:r},{children:d}=s,c=(0,i.useMemo)(()=>T(d)?d.get():d,[d]);return(0,i.createElement)(t,{...l,children:c})}}(t),createVisualElement:r,Component:e}})(e,t,r$,rz))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>s(55238));module.exports=i})();