import { Controller, Get, Post, Body, Query, Param, UseGuards } from '@nestjs/common';
import { DataBoostersService } from './data-boosters.service';
import { DataBoostersQueryDto, DataBoosterOrderDto } from './dto/data-boosters-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('data-boosters')
export class DataBoostersController {
  constructor(private readonly dataBoostersService: DataBoostersService) {}

  @Public()
  @Get()
  getDataBoosters(
    @Query() query: DataBoostersQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.dataBoostersService.getDataBoosters(query, ctx);
  }

  @Public()
  @Get(':boosterId')
  getBoosterById(
    @Param('boosterId') boosterId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.dataBoostersService.getBoosterById(boosterId, ctx);
  }

  @Post('order')
  createBoosterOrder(
    @Body() orderData: DataBoosterOrderDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.dataBoostersService.createBoosterOrder(orderData, ctx);
  }

  @Get('history/user')
  getBoosterHistory(
    @CurrentUser('id') userId: string,
    @Query() query: DataBoostersQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.dataBoostersService.getBoosterHistory(userId, query, ctx);
  }
}
