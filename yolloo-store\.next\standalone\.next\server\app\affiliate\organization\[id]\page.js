(()=>{var e={};e.id=6692,e.ids=[6692],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},97667:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d}),a(14382),a(14417),a(89090),a(26083),a(35866);var t=a(23191),r=a(88716),i=a(37922),n=a.n(i),l=a(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(s,o);let d=["",{children:["affiliate",{children:["organization",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,14382)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,14417)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\[id]\\page.tsx"],m="/affiliate/organization/[id]/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/affiliate/organization/[id]/page",pathname:"/affiliate/organization/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33392:(e,s,a)=>{Promise.resolve().then(a.bind(a,75024))},35303:()=>{},75024:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var t=a(10326),r=a(17577),i=a(35047),n=a(77506),l=a(11890),o=a(79635),d=a(16671),c=a(80239),m=a(28916),x=a(90772),h=a(33071);a(85999);var u=a(77863),p=a(28758),f=a(68483),j=a(90434);function g({params:e}){let s=(0,i.useRouter)(),[a,g]=(0,r.useState)(null),[N,v]=(0,r.useState)(!0),[b,y]=(0,r.useState)(!1),[w,z]=(0,r.useState)(!1),[C,Z]=(0,r.useState)(null);return N?t.jsx("div",{className:"flex justify-center items-center h-64",children:t.jsx(n.Z,{className:"h-8 w-8 animate-spin text-primary"})}):a&&b?(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[t.jsx(x.Button,{variant:"outline",size:"sm",asChild:!0,children:(0,t.jsxs)(j.default,{href:"/affiliate",children:[t.jsx(l.Z,{className:"mr-2 h-4 w-4"}),"Back to Affiliate Dashboard"]})}),w&&t.jsx("div",{className:"flex gap-2",children:t.jsx(x.Button,{size:"sm",onClick:()=>{s.push(`/affiliate/organization/${e.id}/edit`)},children:"Edit Organization"})})]}),t.jsx("div",{className:"grid gap-6",children:(0,t.jsxs)(h.Zb,{children:[t.jsx(h.Ol,{children:t.jsx("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[a?.logo?(0,t.jsxs)(p.qE,{className:"h-12 w-12",children:[t.jsx(p.F$,{src:a.logo,alt:a.name}),t.jsx(p.Q5,{children:a?.name.substring(0,2).toUpperCase()})]}):t.jsx(p.qE,{className:"h-12 w-12",children:t.jsx(p.Q5,{children:a?.name.substring(0,2).toUpperCase()})}),(0,t.jsxs)("div",{children:[t.jsx(h.ll,{className:"text-xl",children:a?.name}),(0,t.jsxs)(h.SZ,{children:["Organization Code: ",a?.code]})]})]})})}),t.jsx(h.aY,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[a?.description&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-2",children:"About"}),t.jsx("p",{children:a.description})]}),t.jsx(f.Separator,{})]}),w&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-3",children:"Management"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(h.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>{s.push(`/affiliate/organization/${e.id}/members`)},children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Manage Members"})}),t.jsx(h.aY,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(o.Z,{className:"h-4 w-4 mr-2 text-muted-foreground"}),t.jsx("span",{children:"View and manage members"})]})})]}),(0,t.jsxs)(h.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>{s.push(`/affiliate/organization/${e.id}/withdrawals`)},children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Withdrawals"})}),t.jsx(h.aY,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(d.Z,{className:"h-4 w-4 mr-2 text-muted-foreground"}),t.jsx("span",{children:"Process withdrawals"})]})})]}),(0,t.jsxs)(h.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>{s.push(`/affiliate/organization/${e.id}/analytics`)},children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Analytics"})}),t.jsx(h.aY,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2 text-muted-foreground"}),t.jsx("span",{children:"View performance data"})]})})]})]})]}),t.jsx(f.Separator,{})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-3",children:"Organization Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(h.Zb,{children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Members"})}),t.jsx(h.aY,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(o.Z,{className:"h-4 w-4 mr-2 text-muted-foreground"}),t.jsx("span",{className:"text-2xl font-bold",children:a?._count.members})]})})]}),(0,t.jsxs)(h.Zb,{children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Commission Rate"})}),t.jsx(h.aY,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-2xl font-bold",children:[(a?.commissionRate*100).toFixed(0),"%"]})]})})]}),(0,t.jsxs)(h.Zb,{children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Discount Rate"})}),(0,t.jsxs)(h.aY,{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(m.Z,{className:"h-4 w-4 mr-2 text-muted-foreground"}),(0,t.jsxs)("span",{className:"text-2xl font-bold",children:[(a?.discountRate*100).toFixed(0),"%"]})]}),t.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Discount for customers using organization referral code."})]})]})]}),t.jsx("div",{className:"grid grid-cols-1 mt-4",children:(0,t.jsxs)(h.Zb,{children:[t.jsx(h.Ol,{className:"pb-2",children:t.jsx(h.ll,{className:"text-sm font-medium",children:"Created"})}),t.jsx(h.aY,{children:t.jsx("div",{className:"text-2xl font-bold",children:u.CN.relative(a?.createdAt)})})]})})]}),t.jsx(f.Separator,{}),a?.administrators&&a.administrators.length>0&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-3",children:"Organization Administrators"}),t.jsx("div",{className:"space-y-4",children:a.administrators.map(e=>{let s=C&&e.email===C;return(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)(p.qE,{className:"h-8 w-8",children:[t.jsx(p.F$,{src:e.image,alt:e.name}),t.jsx(p.Q5,{children:e.name.substring(0,2).toUpperCase()})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"font-medium flex items-center",children:[e.name,s&&t.jsx("span",{className:"ml-2 bg-primary/10 text-primary text-xs px-2 py-0.5 rounded-full",children:"You"})]}),t.jsx("p",{className:"text-sm text-muted-foreground",children:e.email})]})]},e.id)})})]}),t.jsx(f.Separator,{})]}),!w&&(0,t.jsxs)("div",{className:"rounded-lg bg-muted p-4",children:[t.jsx("h3",{className:"font-medium mb-2",children:"About Your Membership"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"You are a member of this organization. Your commission rate is applied directly to the product price. The organization earns the difference between their commission rate and your commission rate. For example, if the organization's rate is 30% and your rate is 20%, on a $100 order, you earn $20 and the organization keeps $10."})]})]})})]})})]}):null}},33071:(e,s,a)=>{"use strict";a.d(s,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>m,ll:()=>o});var t=a(10326),r=a(17577),i=a(77863);let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));n.displayName="Card";let l=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},68483:(e,s,a)=>{"use strict";a.d(s,{Separator:()=>c});var t=a(10326),r=a(17577),i=a(45226),n="horizontal",l=["horizontal","vertical"],o=r.forwardRef((e,s)=>{let{decorative:a,orientation:r=n,...o}=e,d=l.includes(r)?r:n;return(0,t.jsx)(i.WV.div,{"data-orientation":d,...a?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...o,ref:s})});o.displayName="Separator";var d=a(77863);let c=r.forwardRef(({className:e,orientation:s="horizontal",decorative:a=!0,...r},i)=>t.jsx(o,{ref:i,decorative:a,orientation:s,className:(0,d.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",e),...r}));c.displayName=o.displayName},80239:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62881).Z)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},16671:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(62881).Z)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},14417:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r,metadata:()=>t});let t={title:"Affiliate Program | Yolloo Store",description:"Manage your affiliate program"};function r({children:e}){return e}},14382:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var t=a(68570);let r=(0,t.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\[id]\page.tsx`),{__esModule:i,$$typeof:n}=r;r.default;let l=(0,t.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\[id]\page.tsx#default`)},57481:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var t=a(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>a(97667));module.exports=t})();