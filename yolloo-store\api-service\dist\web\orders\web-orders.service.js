"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebOrdersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let WebOrdersService = class WebOrdersService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getOrders(userId, query) {
        const { page = 1, limit = 10, status = 'all', sort = 'newest', search = '' } = query;
        const skip = (page - 1) * limit;
        const whereCondition = {
            userId,
        };
        if (status !== 'all') {
            whereCondition.status = status;
        }
        if (search) {
            whereCondition.OR = [
                { id: { contains: search, mode: 'insensitive' } },
                { items: { some: { productCode: { contains: search, mode: 'insensitive' } } } },
            ];
        }
        let orderBy = { createdAt: 'desc' };
        if (sort === 'oldest') {
            orderBy = { createdAt: 'asc' };
        }
        else if (sort === 'amount_high') {
            orderBy = { total: 'desc' };
        }
        else if (sort === 'amount_low') {
            orderBy = { total: 'asc' };
        }
        const [orders, total] = await Promise.all([
            this.prisma.order.findMany({
                where: whereCondition,
                skip,
                take: parseInt(limit),
                include: {
                    items: true,
                    payment: true,
                    shippingAddress: true,
                    odooStatuses: true,
                },
                orderBy,
            }),
            this.prisma.order.count({
                where: whereCondition,
            }),
        ]);
        return {
            orders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getOrderById(orderId) {
        return await this.prisma.order.findUnique({
            where: { id: orderId },
            include: {
                items: true,
                payment: true,
                shippingAddress: true,
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
                odooStatuses: true,
            },
        });
    }
    async createOrder(userId, createOrderDto) {
        const { items, shippingAddress, billingAddress, paymentMethod, total, referralCode, } = createOrderDto;
        const order = await this.prisma.order.create({
            data: {
                userId,
                total,
                status: 'PENDING',
                referralCode,
                shippingAddressSnapshot: shippingAddress,
                items: {
                    create: items.map((item) => ({
                        productCode: item.productCode,
                        variantCode: item.variantCode,
                        variantText: item.variantText,
                        quantity: item.quantity,
                        price: item.price,
                        uid: item.uid,
                        lpaString: item.lpaString,
                    })),
                },
            },
            include: {
                items: true,
            },
        });
        return order;
    }
    async updateOrder(orderId, updateOrderDto) {
        return await this.prisma.order.update({
            where: { id: orderId },
            data: updateOrderDto,
            include: {
                items: true,
                payment: true,
                odooStatuses: true,
            },
        });
    }
    async getAvailableEsims(query) {
        const { productId, country, limit = 10 } = query;
        const whereCondition = {
            status: 'Inactive',
        };
        if (productId) {
            whereCondition.productId = productId;
        }
        const esims = await this.prisma.esim.findMany({
            where: whereCondition,
            take: parseInt(limit),
            include: {
                product: {
                    select: {
                        id: true,
                        name: true,
                        country: true,
                        countryCode: true,
                    },
                },
                yollooCard: {
                    select: {
                        id: true,
                        number: true,
                        type: true,
                    },
                },
            },
        });
        return {
            esims,
            total: esims.length,
        };
    }
};
WebOrdersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WebOrdersService);
exports.WebOrdersService = WebOrdersService;
//# sourceMappingURL=web-orders.service.js.map