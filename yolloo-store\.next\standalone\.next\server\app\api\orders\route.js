"use strict";(()=>{var e={};e.id=9146,e.ids=[9146],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},68659:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>R,patchFetch:()=>O,requestAsyncStorage:()=>_,routeModule:()=>g,serverHooks:()=>x,staticGenerationAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{GET:()=>w,POST:()=>h,dynamic:()=>m,fetchCache:()=>f,revalidate:()=>y});var n=t(49303),o=t(88716),i=t(60670),s=t(87070),l=t(75571),d=t(72331),u=t(90455),c=t(53524),p=t(71615);let m="force-dynamic",f="force-no-store",y=0;async function w(e){let r=await (0,l.getServerSession)(u.L);if(!r)return new s.NextResponse("Unauthorized",{status:401});let{searchParams:t}=new URL(e.url),a=Number(t.get("page"))||1,n=t.get("status")||"all",o=t.get("sort")||"newest",i=t.get("search")||"",p="all"===n||Object.values(c.OrderStatus).includes(n);if("all"!==n&&!p)return new s.NextResponse("Invalid status parameter",{status:400});let m={userId:r.user.id,..."all"!==n&&{status:n},...i&&{OR:[{id:{contains:i}},{items:{some:{OR:[{variantText:{contains:i,mode:"insensitive"}}]}}}]}};try{let[e,t,n]=await Promise.all([d._.order.findMany({where:m,include:{items:{select:{id:!0,quantity:!0,price:!0,productCode:!0,variantCode:!0,variantText:!0}},payment:!0},orderBy:{createdAt:"oldest"===o?"asc":"desc"},skip:(a-1)*10,take:10}),d._.order.count({where:m}),d._.order.groupBy({by:["status"],where:{userId:r.user.id},_count:!0})]),i=await d._.order.count({where:{userId:r.user.id}});return s.NextResponse.json({orders:e,total:t,totalOrders:i,statusCounts:n,totalPages:Math.ceil(t/10)})}catch(e){return console.error("[ORDERS_GET]",e),new s.NextResponse("Internal error",{status:500})}}async function h(e){try{let r;let t=await (0,l.getServerSession)(u.L);if(!t)return new s.NextResponse("Unauthorized",{status:401});let a=(0,p.cookies)(),n=a.get("referralCode")?.value;console.log(`[ORDER_POST] Referral code from cookie: ${n||"none"}`);try{r=await e.json(),console.log("[ORDER_POST] Request body:",JSON.stringify(r))}catch(e){return console.error("[ORDER_POST] Error parsing request body:",e),new s.NextResponse("Invalid request body",{status:400})}let{addressId:o,items:i}=r;if(!o)return new s.NextResponse("Address ID is required",{status:400});if(!i||!i.length)return new s.NextResponse("Items are required",{status:400});let c=i.reduce((e,r)=>e+r.price*r.quantity,0),m=await d._.address.findUnique({where:{id:o}});m||(console.log(`Address not found with ID: ${o}, attempting to find system default address`),m=await d._.address.findFirst({where:{userId:null,name:"System Default Address"}}));let f=[...new Set(i.map(e=>e.productId))],y=[...new Set(i.map(e=>e.variantId).filter(Boolean))],w=await d._.product.findMany({where:{id:{in:f}}}),h=await d._.productVariant.findMany({where:{id:{in:y}}}),g=new Map(w.map(e=>[e.id,e])),_=new Map(h.map(e=>[e.id,e])),v=await d._.order.create({data:{userId:t.user.id,addressId:m.id,total:c,...n&&{referralCode:n},shippingAddressSnapshot:{name:m.name,phone:m.phone,address1:m.address1,address2:m.address2,city:m.city,state:m.state,postalCode:m.postalCode,country:m.country},items:{create:i.map(e=>{let r=g.get(e.productId),t=e.variantId?_.get(e.variantId):null,a=r?.name||"";return t?.duration&&t?.durationType&&(a+=` ${t.duration} ${t.durationType}`),{productCode:r?.sku,variantCode:t?.variantCode,variantText:a,quantity:e.quantity,price:parseFloat(e.price),uid:e.uid,...e.lpaString&&{lpaString:e.lpaString}}})}},include:{items:!0}});for(let e of(await d._.odooOrderStatus.create({data:{orderId:v.id,variantCode:"default",status:"pending",description:"Order created, waiting for payment",isDigital:!1,deliveredQty:0,lastCheckedAt:new Date}}),i))try{await d._.product.update({where:{id:e.productId},data:{stock:{decrement:e.quantity}}}),console.log(`[ORDER_POST] Decreased stock for product ${e.productId} by ${e.quantity}`)}catch(r){console.error(`[ORDER_POST] Failed to update stock for product ${e.productId}:`,r)}return s.NextResponse.json(v)}catch(e){return console.error("[ORDER_POST]",e),new s.NextResponse("Internal error",{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\orders\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:_,staticGenerationAsyncStorage:v,serverHooks:x}=g,R="/api/orders/route";function O(){return(0,i.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:v})}},90455:(e,r,t)=>{t.d(r,{L:()=>u});var a=t(7585),n=t(72331),o=t(77234),i=t(53797),s=t(42023),l=t.n(s),d=t(93475);let u={adapter:{...(0,a.N)(n._),getUser:async e=>{let r=await n._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await n._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await n._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,i.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await n._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,i.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,d.Ak)(e.email);if(!r||r!==e.code)return null;await (0,d.qc)(e.email);let t=await n._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await n._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await n._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:o}){try{if(t&&t.id){let r=o?.headers||new Headers,i=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",d="unknown";a?d=a.code&&!a.password?"email_code":"password":e&&(d=e.provider),await n._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:i||null,loginMethod:d,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,n=new URL(a).searchParams.get("callbackUrl");if(n){let e=decodeURIComponent(n);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let o=await n._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return o?{id:o.id,name:o.name,email:o.email,picture:o.image,role:o.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>n});var a=t(53524);let n=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>d,yz:()=>u});var a=t(62197),n=t.n(a);let o=null;function i(){if(!o){let e=process.env.REDIS_URL||"redis://localhost:6379";(o=new(n())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),o.on("connect",()=>{console.log("Successfully connected to Redis")})}return o}async function s(e,r,t=300){try{let a=i(),n=`verification_code:${e}`;return await a.setex(n,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=i(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function d(e){try{let r=i(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function u(e,r,t){try{let a=i(),n=`rate_limit:${e}`,o=await a.get(n),s=o?parseInt(o):0;if(s>=r)return!1;return 0===s?await a.setex(n,t,"1"):await a.incr(n),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=n?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(a,o,s):a[o]=e[o]}return a.default=e,t&&t.set(e,a),a}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>t(68659));module.exports=a})();