"use strict";(()=>{var e={};e.id=5212,e.ids=[5212],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},89835:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>v,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>m,staticGenerationAsyncStorage:()=>_});var o={};r.r(o),r.d(o,{POST:()=>h,dynamic:()=>d,fetchCache:()=>l,revalidate:()=>c});var a=r(49303),s=r(88716),n=r(60670),i=r(87070),u=r(6570);let d="force-dynamic",l="force-no-store",c=0,p=(0,u.T)({address:process.env.ODOO_ADDRESS||"",channelId:process.env.ODOO_CHANNEL_ID||"",channelLanguage:process.env.ODOO_CHANNEL_LANGUAGE||"zh_CN",authSecret:process.env.ODOO_AUTH_SECRET||"",signMethod:process.env.ODOO_SIGN_METHOD||"MD5"});async function h(e){try{let t=await e.text();console.log("Received Odoo webhook raw body:",t);let r=e.headers.get("X-Sign-Value");if(!p.verifySignature(t,r))return console.error("Invalid signature"),i.NextResponse.json({code:403,msg:"Invalid signature",data:null},{status:403});let o=JSON.parse(t);console.log("Parsed Odoo webhook data:",o);let a=o.action,s=o.data;switch(a){case"accept_product_info_from_internal":await p.handleProductPush(s);break;case"accept_order_status_from_internal":await p.handleOrderStatusPush(s);break;default:return console.warn("Unknown action:",a),i.NextResponse.json({code:400,msg:"Unknown action",data:null},{status:400})}return i.NextResponse.json({code:200,msg:"Success",data:null})}catch(e){return console.error("Error processing Odoo webhook:",e),i.NextResponse.json({code:500,msg:"Internal server error",data:null},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/odoo/webhook/route",pathname:"/api/odoo/webhook",filename:"route",bundlePath:"app/api/odoo/webhook/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\odoo\\webhook\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:_,serverHooks:m}=g,y="/api/odoo/webhook/route";function v(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:_})}},89880:(e,t,r)=>{r.d(t,{N_:()=>u,Pm:()=>a,eM:()=>n,gA:()=>s,rf:()=>i,tK:()=>o});let o={address:process.env.ODOO_ADDRESS||"",channelId:process.env.ODOO_CHANNEL_ID||"",channelLanguage:process.env.ODOO_CHANNEL_LANGUAGE||"en_US",authSecret:process.env.ODOO_AUTH_SECRET||"",signMethod:process.env.ODOO_SIGN_METHOD||"md5"},a=["esim","data","effective_date","external_data","other","esim-card"],s={defaultStart:0,defaultLength:500,maxLength:5e3},n={ACTIVE:"ACTIVE",INACTIVE:"INACTIVE"},i={defaultCategory:"default",defaultDescription:e=>`Category for ${e} products`},u={currency:"USD",stock:999}},6570:(e,t,r)=>{r.d(t,{T:()=>u,z:()=>i});var o=r(29712),a=r(6113),s=r(89880),n=r(72331);class i{constructor(e){this.config=e,this.client=o.Z.create({baseURL:"",headers:{"Content-Type":"application/json","X-Channel-Id":e.channelId,"X-Channel-Language":e.channelLanguage},timeout:3e5})}generateSignature(e){let t=`${this.config.authSecret}${e}${this.config.channelId}`.replace(/\s/g,"");return(0,a.createHash)("md5").update(t,"utf8").digest("hex").toLowerCase()}verifySignature(e,t){return!!t&&this.generateSignature(e)===t.toLowerCase()}async request(e,t,r){let a=JSON.stringify(r||{},null,0);console.log("[Odoo] req Body: ",a);let s=this.generateSignature(a),n=`${this.config.address}${t}`,i={Accept:"application/json","Content-Type":"application/json","X-Channel-Id":this.config.channelId,"X-Channel-Language":this.config.channelLanguage,"X-sign-Method":this.config.signMethod.toLowerCase(),"X-Sign-Value":s};try{return(await this.client.request({method:e,url:n,data:a,headers:i})).data}catch(e){if(o.Z.isAxiosError(e))throw console.error("Odoo API Error:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,config:{url:e.config?.url,method:e.config?.method,headers:e.config?.headers,data:e.config?.data}}),Error(`Odoo API Error: ${e.response?.data?.error||e.message}`);throw console.error("Unexpected error:",e),e}}async pullProducts(e="esim"){let t=[],r=s.gA.defaultStart,o=!0,a=null,n=0;for(console.log(`[Odoo] Fetching products=${e}`);o&&r<s.gA.maxLength;){n++;let i={product_type:e,start:r,length:s.gA.defaultLength},u=await this.request("POST","/openapi/v3/get_proudct_list",i);if(a=u,"success"!==u.status&&"200"!==u.status&&(!u.result||"success"!==u.result.status&&"ok"!==u.result.status&&"操作成功"!==u.result.message))throw Error(`Failed to fetch products: ${u.result?.message||"Unknown error"}`);if(!u.result?.data||!Array.isArray(u.result.data))throw Error("No product data returned from API");let d=u.result.data;t=[...t,...d],d.length<s.gA.defaultLength&&(o=!1),r+=s.gA.defaultLength}if(console.log(`[Odoo] Fetched ${t.length} products of type=${e} in ${n} batches`),!a)throw Error("No response received from Odoo API");return{...a,result:{...a.result,data:t}}}async getProductPrices(e){let t={product_code:e?.product_code,start:e?.start||s.gA.defaultStart,length:e?.length||s.gA.defaultLength};return this.request("POST","/openapi/v3/get_proudct_price",t)}async createOrder(e){return this.request("POST","/openapi/v3/create_purchase_order",e)}async queryOrderStatus(e){let t={customer_order_ref:[e]};return console.log("Sending request to Odoo:",{url:"/openapi/v3/get_sale_order_status",data:t}),this.request("POST","/openapi/v3/get_sale_order_status",t)}async queryOrderStatusMultiple(e){let t={customer_order_ref:e};return console.log("Sending request to Odoo for multiple orders:",{url:"/openapi/v3/get_sale_order_status",data:t}),this.request("POST","/openapi/v3/get_sale_order_status",t)}async getInvoiceOrders(e){return this.request("POST","/openapi/v3/get_invoice_order_list",e)}async handleProductPush(e){console.log("Processing product push:",e);try{for(let t of e){let{product_code:e,name:r,description_sale:o,website_description:a,off_shelve:s,variants:i}=t,u=await n._.product.findFirst({where:{specifications:{path:["odooProductCode"],equals:e}}});if(u&&(await n._.product.update({where:{id:u.id},data:{name:r,description:o,websiteDescription:a,off_shelve:s??!1,...i?.[0]?.supply_price&&{price:i[0].supply_price}}}),i&&i.length>0))for(let e of i){if(e.variant_attributes?.some(e=>"card"===e.code)){console.log(`Skipping variant with card attribute: ${e.variant_code}`);continue}let t=e.variant_attributes?.reduce((e,t)=>(e[t.code]=t.value,e),{})||{},r=await n._.productVariant.findFirst({where:{AND:[{productId:u.id},{variantCode:e.variant_code}]}});r?await n._.productVariant.update({where:{id:r.id},data:{price:e.supply_price,currency:e.currency,attributes:t}}):await n._.productVariant.create({data:{price:e.supply_price,currency:e.currency,variantCode:e.variant_code,attributes:t,productId:u.id}})}}}catch(e){throw console.error("Error processing product push:",e),e}}async handleOrderStatusPush(e){console.log("Processing order status push:",e);try{for(let t of e){let{customer_order_ref:e,order_lines:r}=t;if(!r||0===r.length)continue;let o=r[0],a=[],s=null;o.data&&(Array.isArray(o.data)?(a=o.data.filter(e=>e&&e.uid).map(e=>e.uid)).length>0&&(s=a.join(",")):"object"==typeof o.data&&o.data.uid&&(s=o.data.uid)),console.log(`[ORDER_STATUS_PUSH] Extracted UIDs for order ${e}:`,a);let i=o.variant_code||"default",u=s?s.replace(/[^0-9,]/g,""):"";console.log(`[ORDER_STATUS_PUSH] Using variantCode: ${i}, formatted UID: ${u}`);let d=await n._.odooOrderStatus.findFirst({where:{orderId:e,variantCode:i,uid:u||null}});if(d?await n._.odooOrderStatus.update({where:{id:d.id},data:{status:o.status,description:o.description,productName:o.product_name,isDigital:o.is_digital||!1,deliveredQty:o.delivered_qty||0,trackingNumber:o.tracking_number,planState:o.data?.[0]?.plan_state,lastCheckedAt:new Date}}):await n._.odooOrderStatus.create({data:{orderId:e,variantCode:i,status:o.status,description:o.description,productName:o.product_name,isDigital:o.is_digital||!1,deliveredQty:o.delivered_qty||0,trackingNumber:o.tracking_number,planState:o.data?.[0]?.plan_state,uid:u||null,lastCheckedAt:new Date}}),"delivered"===o.status){await n._.order.update({where:{id:e},data:{status:"DELIVERED"}});let t=await n._.affiliateReferral.findFirst({where:{orderId:e,status:"PENDING"},include:{organizationCommission:!0}});t&&(console.log(`Updating affiliate referral status for order: ${e}`),await n._.affiliateReferral.update({where:{id:t.id},data:{status:"APPROVED"}}),t.organizationCommission&&(console.log(`Updating organization commission status for order: ${e}`),await n._.organizationCommission.update({where:{id:t.organizationCommissionId},data:{status:"APPROVED"}})))}}}catch(e){throw console.error("Error processing order status push:",e),e}}async getOrderQRCode(e){let t={customer_order_ref:Array.isArray(e)?e.join(","):e};return this.request("POST","/openapi/v3/get_sale_order_esim_qrcode",t)}}let u=e=>new i(e)},72331:(e,t,r)=>{r.d(t,{_:()=>a});var o=r(53524);let a=global.prisma||new o.PrismaClient({log:["error"]})},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,s={};function n(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),o=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?o:`${o}; ${r.join("; ")}`}function i(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[o,a],...s]=i(e),{domain:n,expires:u,httponly:c,maxage:p,path:h,samesite:g,secure:f,partitioned:_,priority:m}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(a),domain:n,...u&&{expires:new Date(u)},...c&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...g&&{sameSite:d.includes(t=(t=g).toLowerCase())?t:void 0},...f&&{secure:!0},...m&&{priority:l.includes(r=(r=m).toLowerCase())?r:void 0},..._&&{partitioned:!0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>p,parseCookie:()=>i,parseSetCookie:()=>u,stringifyCookie:()=>n}),e.exports=((e,s,n,i)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let n of o(s))a.call(e,n)||void 0===n||t(e,n,{get:()=>s[n],enumerable:!(i=r(s,n))||i.enumerable});return e})(t({},"__esModule",{value:!0}),s);var d=["strict","lax","none"],l=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of i(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>n(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>n(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let a=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,o,a,s,n=[],i=0;function u(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,s=!1;u();)if(","===(r=e.charAt(i))){for(o=i,i+=1,u(),a=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(s=!0,i=a,n.push(e.substring(t,o)),t=i):i=o+1}else i+=1;(!s||i>=e.length)&&n.push(e.substring(t,e.length))}return n}(a)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=n(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(n).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,9092,5972,9712],()=>r(89835));module.exports=o})();