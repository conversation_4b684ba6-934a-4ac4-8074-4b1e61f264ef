"use strict";(()=>{var e={};e.id=3047,e.ids=[3047],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},67136:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{POST:()=>f,dynamic:()=>u,fetchCache:()=>p,revalidate:()=>d});var n=r(49303),o=r(88716),s=r(60670),a=r(87070),l=r(72331);let u="force-dynamic",p="force-no-store",d=0;async function f(e){try{let{referralCode:t,source:r,path:i,userAgent:n,referrer:o}=await e.json();if(!t)return a.NextResponse.json({error:"Referral code is required"},{status:400});let s=await l._.affiliateProfile.findUnique({where:{code:t}});if(!s)return a.NextResponse.json({error:"Affiliate not found"},{status:404});let u=await l._.affiliateVisit.create({data:{affiliateId:s.id,organizationId:s.organizationId||void 0,source:r||"direct",path:i||"/",userAgent:n||"",referrer:o||"",convertedToOrder:!1}});return a.NextResponse.json({success:!0,visit:u})}catch(e){return console.error("[AFFILIATE_VISIT]",e),a.NextResponse.json({error:"Failed to record visit"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/affiliate/visit/route",pathname:"/api/affiliate/visit",filename:"route",bundlePath:"app/api/affiliate/visit/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\visit\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:g}=c,y="/api/affiliate/visit/route";function v(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},72331:(e,t,r)=>{r.d(t,{_:()=>n});var i=r(53524);let n=global.prisma||new i.PrismaClient({log:["error"]})},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,o={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,n],...o]=a(e),{domain:s,expires:l,httponly:d,maxage:f,path:c,samesite:h,secure:m,partitioned:g,priority:y}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(n),domain:s,...l&&{expires:new Date(l)},...d&&{httpOnly:!0},..."string"==typeof f&&{maxAge:Number(f)},path:c,...h&&{sameSite:u.includes(t=(t=h).toLowerCase())?t:void 0},...m&&{secure:!0},...y&&{priority:p.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(o,{RequestCookies:()=>d,ResponseCookies:()=>f,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,o,s,a)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let s of i(o))n.call(e,s)||void 0===s||t(e,s,{get:()=>o[s],enumerable:!(a=r(o,s))||a.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u=["strict","lax","none"],p=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let n=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,i,n,o,s=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,o=!1;l();)if(","===(r=e.charAt(a))){for(i=a,a+=1,l(),n=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(o=!0,a=n,s.push(e.substring(t,i)),t=a):a=i+1}else a+=1;(!o||a>=e.length)&&s.push(e.substring(t,e.length))}return s}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return i.RequestCookies},ResponseCookies:function(){return i.ResponseCookies}});let i=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,5972],()=>r(67136));module.exports=i})();