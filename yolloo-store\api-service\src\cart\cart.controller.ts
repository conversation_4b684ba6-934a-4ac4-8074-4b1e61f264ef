import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
  UnauthorizedException,
} from '@nestjs/common';
import { CartService } from './cart.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { AddCartItemDto } from './dto/add-cart-item.dto';
import { UpdateCartItemDto } from './dto/update-cart-item.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('cart')
@UseGuards(JwtAuthGuard)
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @Get()
  getCart(@CurrentUser() user: any) {
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.cartService.getCart(user.id);
  }

  @Post('items')
  addCartItem(
    @CurrentUser() user: any,
    @Body() addCartItemDto: AddCartItemDto,
  ) {
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.cartService.addCartItem(user.id, addCartItemDto);
  }

  @Put('items/:itemId')
  updateCartItem(
    @CurrentUser() user: any,
    @Param('itemId') itemId: string,
    @Body() updateCartItemDto: UpdateCartItemDto,
  ) {
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.cartService.updateCartItem(user.id, itemId, updateCartItemDto);
  }

  @Delete('items/:itemId')
  removeCartItem(
    @CurrentUser() user: any,
    @Param('itemId') itemId: string,
  ) {
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.cartService.removeCartItem(user.id, itemId);
  }
}
