import { PrismaService } from '../../prisma.service';
export declare class WebAuthService {
    private prisma;
    constructor(prisma: PrismaService);
    signup(name: string, email: string, password: string): Promise<{
        message: string;
        user: {
            id: string;
            name: string | null;
            email: string | null;
            role: import(".prisma/client").UserRole;
            createdAt: Date;
        };
    }>;
    forgotPassword(email: string): Promise<{
        message: string;
    }>;
    resetPassword(token: string, newPassword: string): Promise<{
        message: string;
    }>;
    sendVerificationCode(email: string, type: string): Promise<{
        message: string;
        email: string;
        type: string;
    }>;
    verifyCode(email: string, code: string, type: string): Promise<{
        message: string;
        email: string;
        type: string;
    }>;
    changePassword(userId: string, currentPassword: string, newPassword: string): Promise<{
        message: string;
    }>;
}
