import { Module } from '@nestjs/common';
import { WebAuthController } from './web-auth.controller';
import { WebAuthService } from './web-auth.service';
import { PrismaService } from '../../prisma.service';
import { AuthModule } from '../../auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [WebAuthController],
  providers: [WebAuthService, PrismaService],
  exports: [WebAuthService],
})
export class WebAuthModule {}
