import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { PrismaService } from '../prisma.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { SocialAuthModule } from '../social-auth/social-auth.module';
import { Reflector } from '@nestjs/core';

@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET') || 'your-secret-key',
        signOptions: { expiresIn: '7d' },
      }),
    }),
    SocialAuthModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    PrismaService,
    {
      provide: JwtAuthGuard,
      inject: [Reflector, JwtService, ConfigService],
      useFactory: (reflector: Reflector, jwtService: JwtService, configService: ConfigService) => {
        return new JwtAuthGuard(reflector, jwtService, configService);
      },
    }
  ],
  exports: [AuthService, JwtAuthGuard, JwtModule],
})
export class AuthModule {}
