/// <reference types="cookie-parser" />
import { Request, Response } from 'express';
import { WebAdminService } from './web-admin.service';
export declare class WebAdminController {
    private readonly webAdminService;
    constructor(webAdminService: WebAdminService);
    private checkAdminPermission;
    getDashboard(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getUsers(query: any, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getUser(userId: string, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    createUser(createUserDto: any, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    updateUser(userId: string, updateUserDto: any, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    deleteUser(userId: string, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getOrders(query: any, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getProducts(query: any, req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
}
