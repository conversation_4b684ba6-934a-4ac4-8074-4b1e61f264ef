import { PrismaService } from '../../prisma.service';
export declare class WebOrdersService {
    private prisma;
    constructor(prisma: PrismaService);
    getOrders(userId: string, query: any): Promise<{
        orders: ({
            items: (import("@prisma/client/runtime").GetResult<{
                id: string;
                orderId: string;
                productId: string | null;
                productCode: string | null;
                variantCode: string | null;
                variantText: string | null;
                quantity: number;
                price: number;
                uid: string | null;
                lpaString: string | null;
            }, unknown> & {})[];
            payment: (import("@prisma/client/runtime").GetResult<{
                id: string;
                amount: number;
                currency: string;
                status: import(".prisma/client").PaymentStatus;
                provider: string;
                paymentMethod: string;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {}) | null;
            shippingAddress: (import("@prisma/client/runtime").GetResult<{
                id: string;
                userId: string | null;
                type: import(".prisma/client").AddressType;
                name: string;
                phone: string;
                address1: string;
                address2: string | null;
                city: string;
                state: string;
                postalCode: string;
                country: string;
                isDefault: boolean;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {}) | null;
            odooStatuses: (import("@prisma/client/runtime").GetResult<{
                id: string;
                orderId: string;
                variantCode: string;
                odooOrderRef: string | null;
                status: string;
                description: string | null;
                productName: string | null;
                isDigital: boolean;
                deliveredQty: number;
                trackingNumber: string | null;
                planState: string | null;
                uid: string | null;
                lastCheckedAt: Date;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {})[];
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            total: number;
            status: import(".prisma/client").OrderStatus;
            addressId: string | null;
            shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
            paymentId: string | null;
            createdAt: Date;
            updatedAt: Date;
            referralCode: string | null;
        }, unknown> & {})[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
    }>;
    getOrderById(orderId: string): Promise<({
        items: (import("@prisma/client/runtime").GetResult<{
            id: string;
            orderId: string;
            productId: string | null;
            productCode: string | null;
            variantCode: string | null;
            variantText: string | null;
            quantity: number;
            price: number;
            uid: string | null;
            lpaString: string | null;
        }, unknown> & {})[];
        payment: (import("@prisma/client/runtime").GetResult<{
            id: string;
            amount: number;
            currency: string;
            status: import(".prisma/client").PaymentStatus;
            provider: string;
            paymentMethod: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {}) | null;
        shippingAddress: (import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string | null;
            type: import(".prisma/client").AddressType;
            name: string;
            phone: string;
            address1: string;
            address2: string | null;
            city: string;
            state: string;
            postalCode: string;
            country: string;
            isDefault: boolean;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {}) | null;
        user: {
            id: string;
            name: string | null;
            email: string | null;
        };
        odooStatuses: (import("@prisma/client/runtime").GetResult<{
            id: string;
            orderId: string;
            variantCode: string;
            odooOrderRef: string | null;
            status: string;
            description: string | null;
            productName: string | null;
            isDigital: boolean;
            deliveredQty: number;
            trackingNumber: string | null;
            planState: string | null;
            uid: string | null;
            lastCheckedAt: Date;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        total: number;
        status: import(".prisma/client").OrderStatus;
        addressId: string | null;
        shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
        paymentId: string | null;
        createdAt: Date;
        updatedAt: Date;
        referralCode: string | null;
    }, unknown> & {}) | null>;
    createOrder(userId: string, createOrderDto: any): Promise<{
        items: (import("@prisma/client/runtime").GetResult<{
            id: string;
            orderId: string;
            productId: string | null;
            productCode: string | null;
            variantCode: string | null;
            variantText: string | null;
            quantity: number;
            price: number;
            uid: string | null;
            lpaString: string | null;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        total: number;
        status: import(".prisma/client").OrderStatus;
        addressId: string | null;
        shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
        paymentId: string | null;
        createdAt: Date;
        updatedAt: Date;
        referralCode: string | null;
    }, unknown> & {}>;
    updateOrder(orderId: string, updateOrderDto: any): Promise<{
        items: (import("@prisma/client/runtime").GetResult<{
            id: string;
            orderId: string;
            productId: string | null;
            productCode: string | null;
            variantCode: string | null;
            variantText: string | null;
            quantity: number;
            price: number;
            uid: string | null;
            lpaString: string | null;
        }, unknown> & {})[];
        payment: (import("@prisma/client/runtime").GetResult<{
            id: string;
            amount: number;
            currency: string;
            status: import(".prisma/client").PaymentStatus;
            provider: string;
            paymentMethod: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {}) | null;
        odooStatuses: (import("@prisma/client/runtime").GetResult<{
            id: string;
            orderId: string;
            variantCode: string;
            odooOrderRef: string | null;
            status: string;
            description: string | null;
            productName: string | null;
            isDigital: boolean;
            deliveredQty: number;
            trackingNumber: string | null;
            planState: string | null;
            uid: string | null;
            lastCheckedAt: Date;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        total: number;
        status: import(".prisma/client").OrderStatus;
        addressId: string | null;
        shippingAddressSnapshot: import(".prisma/client").Prisma.JsonValue;
        paymentId: string | null;
        createdAt: Date;
        updatedAt: Date;
        referralCode: string | null;
    }, unknown> & {}>;
    getAvailableEsims(query: any): Promise<{
        esims: ({
            product: {
                id: string;
                name: string;
                country: string | null;
                countryCode: string | null;
            };
            yollooCard: {
                id: string;
                number: string;
                type: string;
            };
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            iccid: string;
            status: string;
            activationDate: Date | null;
            expiryDate: Date | null;
            yollooCardId: string;
            productId: string;
            profileId: string | null;
            planId: string | null;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        total: number;
    }>;
}
