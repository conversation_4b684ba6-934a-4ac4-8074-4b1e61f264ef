import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { SocialAuthService } from './social-auth.service';
import { PrismaService } from '../prisma.service';

@Module({
  imports: [ConfigModule, HttpModule],
  providers: [SocialAuthService, PrismaService],
  exports: [SocialAuthService],
})
export class SocialAuthModule {}
