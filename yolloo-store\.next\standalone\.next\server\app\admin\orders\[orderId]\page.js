(()=>{var e={};e.id=8486,e.ids=[8486],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},79460:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l}),s(8689),s(94148),s(85460),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),n=s(37922),o=s.n(n),d=s(95231),i={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);s.d(t,i);let l=["",{children:["admin",{children:["orders",{children:["[orderId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8689)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\orders\\[orderId]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94148)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\orders\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\orders\\[orderId]\\page.tsx"],u="/admin/orders/[orderId]/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/orders/[orderId]/page",pathname:"/admin/orders/[orderId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},28659:(e,t,s)=>{Promise.resolve().then(s.bind(s,17405))},6671:(e,t,s)=>{Promise.resolve().then(s.bind(s,75784)),Promise.resolve().then(s.bind(s,60076)),Promise.resolve().then(s.bind(s,53124)),Promise.resolve().then(s.bind(s,77715)),Promise.resolve().then(s.bind(s,23814)),Promise.resolve().then(s.bind(s,62737)),Promise.resolve().then(s.bind(s,90772)),Promise.resolve().then(s.t.bind(s,79404,23))},21379:(e,t,s)=>{"use strict";s.d(t,{L:()=>a,y:()=>n});var r=s(17577);let a=(0,r.createContext)(null);function n(){let e=(0,r.useContext)(a);if(null===e)throw Error("useOrders must be used within an OrdersProvider");return e}},17405:(e,t,s)=>{"use strict";s.d(t,{OrdersProvider:()=>n});var r=s(10326);s(17577);var a=s(21379);function n({children:e,orders:t}){return r.jsx(a.L.Provider,{value:t,children:e})}},75784:(e,t,s)=>{"use strict";s.d(t,{AutoUpdateOdooStatus:()=>n});var r=s(17577),a=s(35047);function n({orderId:e}){(0,a.useRouter)();let[t,s]=(0,r.useState)(!0);return null}s(85999)},60076:(e,t,s)=>{"use strict";s.d(t,{OdooStatusList:()=>h});var r=s(10326),a=s(33071),n=s(567),o=s(77863),d=s(17577),i=s(35047),l=s(90772),c=s(57372),u=s(85999);function m({orderId:e}){let t=(0,i.useRouter)(),[s,a]=(0,d.useState)(!1);async function n(){a(!0);try{let s=await fetch(`/api/admin/orders/${e}/update-status`,{method:"POST",headers:{"Content-Type":"application/json"}}),r=await s.json();if(!s.ok)throw Error(r.message||"Failed to update order status");let a=r.odooStatuses?.length||0;u.A.success("Odoo status updated successfully",{description:`Updated ${a} status records. Order information has been refreshed with the latest status from Odoo.`}),t.refresh()}catch(e){console.error(e),u.A.error(`Failed to update order status: ${e instanceof Error?e.message:"Unknown error"}`,{duration:5e3})}finally{a(!1)}}return r.jsx(l.Button,{onClick:n,disabled:s,variant:"outline",size:"sm",children:s?(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.P.refresh,{className:"mr-2 h-4 w-4"}),"Refresh Status"]})})}function p({orderId:e,variant:t="secondary"}){let s=(0,i.useRouter)(),[a,n]=(0,d.useState)(!1);async function o(){n(!0);try{let t=await fetch(`/api/admin/orders/${e}/odoo`,{method:"POST",headers:{"Content-Type":"application/json"}}),r=await t.json();if(!t.ok)throw Error(r.message||"Failed to create Odoo order");let a=r.serviceType||"Standard Odoo Service";r.odooResponse&&"error"===r.odooResponse.status?u.A.error(`Odoo returned error: ${r.odooResponse.message}`,{duration:5e3,description:`Using ${a}. Order information has been updated with error details`}):u.A.success("Odoo order created successfully",{description:`Using ${a}. ${r.odooResponse?.message||"Order has been synced with Odoo"}`}),s.refresh()}catch(e){console.error(e),u.A.error(`Failed to create Odoo order: ${e instanceof Error?e.message:"Unknown error"}`,{duration:5e3})}finally{n(!1)}}return r.jsx(l.Button,{onClick:o,disabled:a,variant:t,children:a?(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.P.refresh,{className:"mr-2 h-4 w-4"}),"Create Odoo Order"]})})}let x={processing:{variant:"default"},delivered:{variant:"default"},shipped:{variant:"secondary"},error:{variant:"destructive"},pending:{variant:"secondary"},default:{variant:"secondary"}};function h({orderId:e,orderItems:t,odooStatuses:s}){let d=s.filter(e=>"default"!==e.variantCode),i=(e,s)=>t.find(t=>t.variantCode===e&&(null===s||t.uid===s)),l=e=>{let t=i(e.variantCode,e.uid);return t?.variantText?t.variantText:e.productName||e.variantCode},c=t.map((e,t)=>{let s=d.find(t=>t.variantCode===e.variantCode&&(null===t.uid||t.uid===e.uid));return s?{...s,itemIndex:t}:null}).filter(Boolean);return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("h3",{className:"text-lg font-semibold",children:"Odoo Order Status"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(m,{orderId:e}),r.jsx(p,{orderId:e})]})]}),0===c.length?r.jsx("div",{className:"text-center py-6 text-muted-foreground",children:r.jsx("p",{children:'No Odoo order status available. Click "Create Odoo Order" to create one.'})}):r.jsx("div",{className:"space-y-4",children:c.map(e=>{let s=x[e.status.toLowerCase()]||x.default,d=l(e);return(0,r.jsxs)(a.Zb,{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:d}),c.length>1&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Item ",e.itemIndex+1," of ",t.length]})]}),r.jsx(n.C,{variant:s.variant,children:e.status})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Description: "}),r.jsx("span",{children:e.description||"-"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Odoo Order Ref: "}),r.jsx("span",{className:"font-mono",children:e.odooOrderRef||"-"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Tracking Number: "}),r.jsx("span",{className:"font-mono",children:e.trackingNumber||"-"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Plan State: "}),r.jsx("span",{children:e.planState||"-"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"UID: "}),r.jsx("span",{className:"font-mono",children:e.uid||"-"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Last Updated: "}),r.jsx("span",{children:o.CN.withTimezone(e.lastCheckedAt)})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Variant Code: "}),r.jsx("span",{className:"font-mono text-xs",children:e.variantCode||"-"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Digital: "}),r.jsx("span",{children:e.isDigital?"Yes":"No"})]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium text-muted-foreground",children:"Delivered Qty: "}),r.jsx("span",{children:e.deliveredQty||0})]})]})]},e.id)})})]})}},53124:(e,t,s)=>{"use strict";s.d(t,{OrderStatusSelect:()=>c});var r=s(10326),a=s(17577),n=s(35047),o=s(57372),d=s(34474),i=s(85999);let l={PENDING:{label:"Pending",variant:"warning"},PROCESSING:{label:"Processing",variant:"secondary"},SHIPPED:{label:"Shipped",variant:"info"},DELIVERED:{label:"Delivered",variant:"success"},CANCELLED:{label:"Cancelled",variant:"destructive"},PAID:{label:"Paid",variant:"default"}};function c({orderId:e,currentStatus:t}){let s=(0,n.useRouter)(),[c,u]=(0,a.useState)(!1);async function m(r){if(r!==t){u(!0);try{if(!(await fetch(`/api/admin/orders/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:r})})).ok)throw Error("Failed to update order status");i.A.success("Order status updated successfully"),s.refresh()}catch(e){console.error(e),i.A.error("Something went wrong")}finally{u(!1)}}}return r.jsx("div",{className:"flex items-center gap-2",children:(0,r.jsxs)(d.Ph,{value:t,onValueChange:m,disabled:c,children:[r.jsx(d.i4,{className:"w-[180px]",children:r.jsx(d.ki,{children:c?r.jsx(o.P.spinner,{className:"h-4 w-4 animate-spin"}):l[t]?.label||t})}),r.jsx(d.Bw,{children:Object.entries(l).map(([e,{label:t}])=>r.jsx(d.Ql,{value:e,children:t},e))})]})})}},77715:(e,t,s)=>{"use strict";s.d(t,{ShipOrderButton:()=>u});var r=s(10326),a=s(17577),n=s(35047),o=s(90772),d=s(54432),i=s(57372),l=s(62288),c=s(85999);function u({orderId:e,currentStatus:t}){let s=(0,n.useRouter)(),[u,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!1),[h,f]=(0,a.useState)("");async function j(){if(!h.trim()){c.A.error("Please enter a tracking number");return}m(!0);try{if(!(await fetch(`/api/admin/orders/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"SHIPPED",trackingNumber:h.trim()})})).ok)throw Error("Failed to update order status");c.A.success("Order marked as shipped"),x(!1),s.refresh()}catch(e){console.error(e),c.A.error("Something went wrong")}finally{m(!1)}}return"PAID"!==t?null:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(o.Button,{variant:"outline",size:"sm",onClick:()=>x(!0),children:[r.jsx(i.P.truck,{className:"mr-2 h-4 w-4"}),"Ship Order"]}),r.jsx(l.Vq,{open:p,onOpenChange:x,children:(0,r.jsxs)(l.cZ,{children:[(0,r.jsxs)(l.fK,{children:[r.jsx(l.$N,{children:"Ship Order"}),r.jsx(l.Be,{children:"Enter the tracking number to mark this order as shipped."})]}),r.jsx("div",{className:"space-y-4 py-4",children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("label",{htmlFor:"tracking-number",className:"text-sm font-medium",children:"Tracking Number"}),r.jsx(d.I,{id:"tracking-number",placeholder:"Enter tracking number",value:h,onChange:e=>f(e.target.value)})]})}),(0,r.jsxs)(l.cN,{children:[r.jsx(o.Button,{variant:"outline",onClick:()=>x(!1),disabled:u,children:"Cancel"}),(0,r.jsxs)(o.Button,{onClick:j,disabled:u,children:[u&&r.jsx(i.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Confirm Shipment"]})]})]})})]})}},23814:(e,t,s)=>{"use strict";s.d(t,{QRCodeDisplay:()=>p});var r=s(10326),a=s(17577),n=s(33071),o=s(90772),d=s(57372),i=s(92447),l=s(32933),c=s(43810),u=s(34789),m=s(31270);function p({orderId:e,orderRef:t,status:s,isQrCodeProduct:p}){let[x,h]=(0,a.useState)([]),[f,j]=(0,a.useState)(!1),[g,y]=(0,a.useState)(null),[v,N]=(0,a.useState)(null),[b,w]=(0,a.useState)({}),{toast:C}=(0,u.pm)(),S=p&&("delivered"===s||"processing"===s),P=e=>e&&""!==e.trim()&&e.startsWith("LPA:"),A=async(e,t)=>{try{let s=await m.toDataURL(e,{width:200,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});w(e=>({...e,[t]:s}))}catch(e){console.error("Error generating QR code:",e)}},k=async()=>{if(S){j(!0),y(null);try{let e=await fetch("/api/odoo/orders/qrcode",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({customer_order_ref:t})});if(!e.ok)throw Error("Failed to fetch QR codes");let s=await e.json();if(s?.result?.data?.[0]?.qrcode){let e=s.result.data[0].qrcode;h(e),e.forEach((e,t)=>{P(e.qrCodeContent)&&A(e.qrCodeContent,t)})}else y("No QR codes available yet")}catch(e){console.error("Error fetching QR codes:",e),y("Failed to load QR codes")}finally{j(!1)}}},R=async(e,t)=>{try{await navigator.clipboard.writeText(e),N(t),C({title:"Copied!",description:"LPA string copied to clipboard"}),setTimeout(()=>N(null),2e3)}catch(e){C({title:"Failed to copy",description:"Please copy the text manually",variant:"destructive"})}},O=e=>{if(b[e]){let t=document.createElement("a");t.href=b[e],t.download=`esim-qrcode-${e+1}.png`,document.body.appendChild(t),t.click(),document.body.removeChild(t)}};return S?r.jsx(n.Zb,{children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(i.Z,{className:"h-5 w-5 text-blue-600"}),r.jsx("h2",{className:"font-semibold",children:"eSIM QR Codes"})]}),(0,r.jsxs)(o.Button,{variant:"outline",size:"sm",onClick:k,disabled:f,children:[f?r.jsx(d.P.spinner,{className:"h-4 w-4 animate-spin"}):r.jsx(d.P.refresh,{className:"h-4 w-4"}),"Refresh"]})]}),f&&0===x.length&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-8",children:[r.jsx(d.P.spinner,{className:"h-6 w-6 animate-spin mr-2"}),r.jsx("span",{children:"Loading QR codes..."})]}),g&&(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx("p",{className:"text-muted-foreground",children:g}),r.jsx(o.Button,{variant:"outline",size:"sm",onClick:k,className:"mt-2",children:"Try Again"})]}),x.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Your eSIM activation codes are ready. Use it to activate your eSIM."}),x.map((e,t)=>{let s=P(e.qrCodeContent),a=b[t];return(0,r.jsxs)("div",{className:"border rounded-lg p-4 space-y-4",children:[s&&a&&r.jsx("div",{className:"flex justify-center",children:r.jsx("div",{className:"bg-white p-4 rounded-lg border",children:r.jsx("img",{src:a,alt:`eSIM QR Code ${t+1}`,className:"w-48 h-48"})})}),(0,r.jsxs)("div",{className:"flex justify-center gap-2 flex-wrap",children:[(0,r.jsxs)(o.Button,{variant:"outline",size:"sm",onClick:()=>R(e.qrCodeContent,t),className:"flex items-center gap-1",children:[v===t?r.jsx(l.Z,{className:"h-4 w-4 text-green-600"}):r.jsx(c.Z,{className:"h-4 w-4"}),v===t?"Copied!":"Copy LPA"]}),s&&a&&(0,r.jsxs)(o.Button,{variant:"outline",size:"sm",onClick:()=>O(t),children:[r.jsx(i.Z,{className:"h-4 w-4 mr-1"}),"Download QR"]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded p-3",children:[r.jsx("p",{className:"text-xs text-muted-foreground mb-1",children:"LPA String:"}),r.jsx("p",{className:"font-mono text-sm break-all select-all",children:e.qrCodeContent})]}),r.jsx("div",{className:"text-xs text-muted-foreground",children:r.jsx("p",{children:"\uD83D\uDCF1 To activate: Go to Settings → Cellular → Add eSIM → Use QR Code"})}),!s&&r.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded p-3",children:r.jsx("p",{className:"text-sm text-yellow-800",children:"⚠️ Invalid LPA format. QR code cannot be generated."})})]},t)})]})]})}):null}},62737:(e,t,s)=>{"use strict";s.d(t,{ProductLink:()=>o});var r=s(10326),a=s(17577),n=s(90434);function o({productCode:e,children:t}){let[s,o]=(0,a.useState)(null),[d,i]=(0,a.useState)(!0);return e&&!d&&s?r.jsx(n.default,{href:`/products/${s}`,target:"_blank",className:"font-medium hover:text-primary hover:underline",children:t}):r.jsx("span",{className:"font-medium",children:t})}},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>d});var r=s(10326);s(17577);var a=s(79360),n=s(77863);let o=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,...s}){return r.jsx("div",{className:(0,n.cn)(o({variant:t}),e),...s})}},33071:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>d,SZ:()=>l,Zb:()=>o,aY:()=>c,eW:()=>u,ll:()=>i});var r=s(10326),a=s(17577),n=s(77863);let o=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));o.displayName="Card";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));d.displayName="CardHeader";let i=a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t}));i.displayName="CardTitle";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},62288:(e,t,s)=>{"use strict";s.d(t,{$N:()=>h,Be:()=>f,Vq:()=>i,cN:()=>x,cZ:()=>m,fK:()=>p,hg:()=>l});var r=s(10326),a=s(17577),n=s(11123),o=s(94019),d=s(77863);let i=n.fC,l=n.xz,c=n.h_;n.x8;let u=a.forwardRef(({className:e,...t},s)=>r.jsx(n.aV,{ref:s,className:(0,d.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=n.aV.displayName;let m=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(c,{children:[r.jsx(u,{}),(0,r.jsxs)(n.VY,{ref:a,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,r.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(o.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=n.VY.displayName;let p=({className:e,...t})=>r.jsx("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let x=({className:e,...t})=>r.jsx("div",{className:(0,d.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});x.displayName="DialogFooter";let h=a.forwardRef(({className:e,...t},s)=>r.jsx(n.Dx,{ref:s,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=n.Dx.displayName;let f=a.forwardRef(({className:e,...t},s)=>r.jsx(n.dk,{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=n.dk.displayName},34474:(e,t,s)=>{"use strict";s.d(t,{Bw:()=>m,Ph:()=>l,Ql:()=>p,i4:()=>u,ki:()=>c});var r=s(10326),a=s(17577),n=s(18792),o=s(941),d=s(32933),i=s(77863);let l=n.fC;n.ZA;let c=n.B4,u=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(n.xz,{ref:a,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:[t,r.jsx(n.JO,{asChild:!0,children:r.jsx(o.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=n.xz.displayName;let m=a.forwardRef(({className:e,children:t,position:s="popper",...a},o)=>r.jsx(n.h_,{children:r.jsx(n.VY,{ref:o,className:(0,i.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:r.jsx(n.l_,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));m.displayName=n.VY.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(n.__,{ref:s,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.__.displayName;let p=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(n.ck,{ref:a,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(n.wU,{children:r.jsx(d.Z,{className:"h-4 w-4"})})}),r.jsx(n.eT,{children:t})]}));p.displayName=n.ck.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(n.Z0,{ref:s,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.Z0.displayName},8689:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>E});var r=s(19510),a=s(58585),n=s(72331),o=s(2946),d=s(46697),i=s(18307),l=s(68570);let c=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\ship-order-button.tsx`),{__esModule:u,$$typeof:m}=c;c.default;let p=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\ship-order-button.tsx#ShipOrderButton`),x=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\order-status-select.tsx`),{__esModule:h,$$typeof:f}=x;x.default;let j=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\order-status-select.tsx#OrderStatusSelect`),g=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\auto-update-odoo-status.tsx`),{__esModule:y,$$typeof:v}=g;g.default;let N=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\auto-update-odoo-status.tsx#AutoUpdateOdooStatus`),b=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\odoo-status-list.tsx`),{__esModule:w,$$typeof:C}=b;b.default;let S=(0,l.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\odoo-status-list.tsx#OdooStatusList`);var P=s(51170),A=s(88561),k=s(57371),R=s(50650);async function O(e){let t=await n._.order.findUnique({where:{id:e},select:{id:!0,userId:!0,total:!0,status:!0,addressId:!0,shippingAddressSnapshot:!0,paymentId:!0,createdAt:!0,updatedAt:!0,referralCode:!0,user:!0,items:{select:{id:!0,quantity:!0,price:!0,uid:!0,variantText:!0,productCode:!0,variantCode:!0}},shippingAddress:!0,payment:!0,odooStatuses:{select:{id:!0,orderId:!0,variantCode:!0,odooOrderRef:!0,status:!0,description:!0,productName:!0,isDigital:!0,deliveredQty:!0,trackingNumber:!0,planState:!0,uid:!0,lastCheckedAt:!0,createdAt:!0,updatedAt:!0}}}});t||(0,a.notFound)();let s=await Promise.all(t.items.map(async e=>{if(e.productCode){let t=await n._.product.findFirst({where:{sku:e.productCode},include:{category:!0}});return{...e,productId:t?.id||null,product:t}}return{...e,productId:null,product:null}})),r=t.odooStatuses.find(e=>"default"===e.variantCode)||null;return{...t,items:s,odooStatus:r}}async function E({params:e}){let t=await O(e.orderId);return(0,r.jsxs)("div",{className:"space-y-8",children:[r.jsx(N,{orderId:t.id}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(k.default,{href:"/admin/orders",children:(0,r.jsxs)(o.z,{variant:"ghost",size:"sm",children:[r.jsx(i.P.chevronLeft,{className:"h-4 w-4 mr-2"}),"Back to Orders"]})}),(0,r.jsxs)("h2",{className:"text-2xl font-bold tracking-tight",children:["Order #",t.id.slice(0,8)]})]}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Placed on ",R.CN.withTimezone(t.createdAt)]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx(j,{orderId:t.id,currentStatus:t.status}),r.jsx(p,{orderId:t.id,currentStatus:t.status})]})]}),(0,r.jsxs)("div",{className:"grid gap-8 md:grid-cols-2",children:[r.jsx(d.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Customer Information"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Name"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:t.user?.name||"Guest"})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Email"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:t.user?.email||"N/A"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Shipping Address"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-muted-foreground",children:[r.jsx("p",{children:t.shippingAddressSnapshot?.name||t.shippingAddress?.name}),r.jsx("p",{children:t.shippingAddressSnapshot?.address1||t.shippingAddress?.address1}),(t.shippingAddressSnapshot?.address2||t.shippingAddress?.address2)&&r.jsx("p",{children:t.shippingAddressSnapshot?.address2||t.shippingAddress?.address2}),(0,r.jsxs)("p",{children:[t.shippingAddressSnapshot?.city||t.shippingAddress?.city,", ",t.shippingAddressSnapshot?.state||t.shippingAddress?.state," ",t.shippingAddressSnapshot?.postalCode||t.shippingAddress?.postalCode]}),r.jsx("p",{children:t.shippingAddressSnapshot?.country||t.shippingAddress?.country}),(0,r.jsxs)("p",{children:["Phone: ",t.shippingAddressSnapshot?.phone||t.shippingAddress?.phone]})]})]}),r.jsx(S,{orderId:t.id,orderItems:t.items,odooStatuses:t.odooStatuses})]})}),r.jsx(d.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("h3",{className:"text-lg font-semibold",children:"Order Summary"}),(0,r.jsxs)("div",{className:"divide-y",children:[t.items.map(e=>(console.log(`[ADMIN_ORDER_DETAILS] Item ${e.id}: variantText=${e.variantText}`),(0,r.jsxs)("div",{className:"flex gap-4 py-4",children:[r.jsx("div",{className:"h-16 w-16 rounded-md bg-gray-200 flex items-center justify-center",children:r.jsx(i.P.package,{className:"h-8 w-8 text-gray-400"})}),r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:r.jsx(P.o,{productCode:e.productCode,children:e.variantText||"Product Deleted"})}),r.jsx("div",{className:"flex flex-col gap-1 mt-1",children:e.variantCode&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[r.jsx("span",{className:"font-medium",children:"SKU:"})," ",r.jsx("span",{className:"font-mono",children:e.variantCode})]})}),(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap gap-2",children:[(()=>{let t=function(e){if(!e)return null;let t=e.match(/(.+?)\s+(\d+)\s+(day|month)s?$/i);return t?{productName:t[1].trim(),duration:parseInt(t[2],10),durationType:t[3].toLowerCase()}:null}(e.variantText);return t?.duration&&t?.durationType?r.jsx("div",{className:"inline-flex items-center rounded-md bg-blue-50 border border-blue-200 px-3 py-1.5 text-sm",children:(0,r.jsxs)("span",{className:"font-mono text-blue-600",children:[t.duration," ",t.durationType]})}):null})(),e.uid&&(0,r.jsxs)("div",{className:"inline-flex items-center rounded-md bg-blue-50 border border-blue-200 px-3 py-1.5 text-sm",children:[r.jsx(i.P.creditCard,{className:"mr-2 h-4 w-4 text-blue-500"}),r.jsx("span",{className:"font-medium text-blue-700 mr-2",children:"UID:"}),r.jsx("span",{className:"font-mono text-blue-600",children:e.uid})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-medium",children:["$",(e.quantity*e.price).toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.quantity," \xd7 $",e.price.toFixed(2)]})]})]})})]},e.id))),(0,r.jsxs)("div",{className:"space-y-2 pt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["$",t.total.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"Shipping"}),r.jsx("span",{children:"Free"})]}),(0,r.jsxs)("div",{className:"flex justify-between font-medium text-lg",children:[r.jsx("span",{children:"Total"}),(0,r.jsxs)("span",{children:["$",t.total.toFixed(2)]})]})]})]}),t.payment&&(0,r.jsxs)("div",{className:"mt-6 pt-6 border-t",children:[r.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Payment Information"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Status"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:t.payment.status})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Method"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:t.payment.paymentMethod})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Amount"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["$",t.payment.amount.toFixed(2)," ",t.payment.currency]})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Payment Date"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:R.CN.withTimezone(t.payment.createdAt)})]})]})]})]})})]}),(()=>{let e=t.items.some(e=>e.product?.category?.name?.toLowerCase()==="qr_code");if(!e)return null;let s=t.odooStatuses.find(e=>{let s=t.items.find(t=>t.variantCode===e.variantCode&&(null===e.uid||e.uid===t.uid));return s?.product?.category?.name?.toLowerCase()==="qr_code"});if(!s)return null;let a=s.variantCode||"default",n=s.uid||"no-uid",o=`${t.id}-${a}:::${n}`;return r.jsx(A.x,{orderId:t.id,orderRef:o,status:s.status,isQrCodeProduct:e})})()]})}},94148:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(19510);s(71159);var a=s(72331),n=s(68570);let o=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\orders\orders-provider.tsx`),{__esModule:d,$$typeof:i}=o;o.default;let l=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\orders\orders-provider.tsx#OrdersProvider`);async function c(){return await a._.order.findMany({orderBy:{createdAt:"desc"},include:{items:{select:{id:!0,quantity:!0,price:!0,productCode:!0,variantCode:!0,variantText:!0}},shippingAddress:!0,user:!0}})}async function u({children:e}){let t=await c();return e?r.jsx(l,{orders:t,children:e}):null}},88561:(e,t,s)=>{"use strict";s.d(t,{x:()=>d});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\orders\qr-code-display.tsx`),{__esModule:n,$$typeof:o}=a;a.default;let d=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\orders\qr-code-display.tsx#QRCodeDisplay`)},51170:(e,t,s)=>{"use strict";s.d(t,{o:()=>d});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\product-link.tsx`),{__esModule:n,$$typeof:o}=a;a.default;let d=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\product-link.tsx#ProductLink`)},2946:(e,t,s)=>{"use strict";s.d(t,{z:()=>d});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\button.tsx`),{__esModule:n,$$typeof:o}=a;a.default;let d=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\button.tsx#Button`);(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\button.tsx#buttonVariants`)},46697:(e,t,s)=>{"use strict";s.d(t,{Zb:()=>o});var r=s(19510),a=s(71159),n=s(50650);let o=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));o.displayName="Card",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...t})).displayName="CardContent",a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,6499,4824,7123],()=>s(79460));module.exports=r})();