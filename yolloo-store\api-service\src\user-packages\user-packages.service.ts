import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { UserPackagesQueryDto, ActivatePackageDto, UsageStatsQueryDto, UsageHistoryQueryDto } from './dto/user-packages-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
import { DateFormatter, DateUtils } from '../common/utils';

@Injectable()
export class UserPackagesService {
  private readonly logger = new Logger(UserPackagesService.name);

  constructor(private prisma: PrismaService) {}

  async getUserPackages(userId: string, query: UserPackagesQueryDto, ctx: RequestContext) {
    console.log('Context in getUserPackages:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询用户的订单，这些订单代表用户购买的套餐
      const whereConditions: any = {
        userId: userId,
        status: {
          in: ['COMPLETED', 'PROCESSING', 'SHIPPED', 'DELIVERED'], // 已支付的订单
        },
      };

      // 查询订单总数
      const total = await this.prisma.order.count({
        where: whereConditions,
      });

      // 如果没有找到订单，返回空数据
      if (total === 0) {
        return {
          packages: [],
          pagination: {
            total: 0,
            page: query.page || 1,
            pageSize: query.pageSize || 10,
            hasMore: false,
          },
          context: {
            language: ctx.language,
            theme: ctx.theme,
            currency: ctx.currency,
          },
        };
      }

      // 查询订单列表
      const skip = (query.page! - 1) * query.pageSize!;
      const orders = await this.prisma.order.findMany({
        where: whereConditions,
        include: {
          items: true,
        },
        skip,
        take: query.pageSize!,
        orderBy: {
          createdAt: 'desc',
        },
      });

      // 获取所有产品代码，然后查询产品信息
      const productCodes = orders.flatMap(order =>
        order.items.map(item => item.productCode).filter(Boolean)
      );

      const products = await this.prisma.product.findMany({
        where: {
          sku: { in: productCodes as string[] },
        },
        select: {
          id: true,
          sku: true,
          name: true,
          description: true,
          images: true,
          dataSize: true,
          planType: true,
          specifications: true,
        },
      });

      // 创建产品映射
      const productMap = new Map(products.map(p => [p.sku, p]));

      // 将订单转换为用户套餐格式
      const packages = orders.flatMap(order =>
        order.items.map(item => this.formatOrderItemAsPackage(order, item, productMap, ctx, isZh))
      );

      // 应用筛选条件
      let filteredPackages = packages;

      if (query.status && query.status !== 'all') {
        filteredPackages = packages.filter(pkg => pkg.status === query.status);
      }

      if (query.packageType) {
        filteredPackages = filteredPackages.filter(pkg => pkg.packageType === query.packageType);
      }

      // 计算状态统计
      const statusCounts = {
        activating: packages.filter(p => p.status === 'activating').length,
        to_be_activated: packages.filter(p => p.status === 'to_be_activated').length,
        expired: packages.filter(p => p.status === 'expired').length,
        all: packages.length,
      };

      return {
        packages: filteredPackages,
        pagination: {
          total: filteredPackages.length,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: skip + filteredPackages.length < total,
        },
        statusCounts,
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching user packages:', error);
      return {
        packages: [],
        pagination: {
          total: 0,
          page: query.page || 1,
          pageSize: query.pageSize || 10,
          hasMore: false,
        },
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };
    }
  }




  async getPackageById(userId: string, packageId: string, ctx: RequestContext) {
    console.log('Context in getPackageById:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询特定的订单项目（packageId实际上是orderItemId）
      const orderItem = await this.prisma.orderItem.findFirst({
        where: {
          id: packageId,
          order: {
            userId: userId,
          },
        },
        include: {
          order: true,
        },
      });

      if (!orderItem) {
        return {
          error: 'Package not found',
          packageId: packageId,
        };
      }

      // 查询产品信息
      let product: any = null;
      if (orderItem.productCode) {
        product = await this.prisma.product.findFirst({
          where: { sku: orderItem.productCode },
          select: {
            id: true,
            sku: true,
            name: true,
            description: true,
            images: true,
            dataSize: true,
            planType: true,
            specifications: true,
          },
        });
      }

      // 格式化为详细的套餐数据
      const packageDetails = this.formatOrderItemAsDetailedPackage(orderItem, product, ctx, isZh);

      return packageDetails;

    } catch (error) {
      this.logger.error('Error fetching package details:', error);
      return {
        error: 'Failed to fetch package details',
        packageId: packageId,
      };
    }
  }



  async activatePackage(userId: string, activateData: ActivatePackageDto, ctx: RequestContext) {
    console.log('Context in activatePackage:', ctx);

    const isZh = ctx.language.startsWith('zh');

    // Mock activation process
    const activation = {
      id: `activation_${Date.now()}`,
      packageId: activateData.packageId,
      userId: userId,
      status: 'processing',
      statusText: isZh ? '激活处理中' : 'Activation in progress',
      activatedAt: new Date().toISOString(),
      estimatedCompletionTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes
    };

    return {
      activation,
      message: isZh ? '套餐激活请求已提交，预计5分钟内完成激活' : 'Package activation request submitted, expected to complete within 5 minutes',
    };
  }

  async getPackageUsageStats(userId: string, packageId: string, query: UsageStatsQueryDto, ctx: RequestContext) {
    console.log('Context in getPackageUsageStats:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      this.logger.log(`Fetching package usage stats for user: ${userId}, package: ${packageId}`);

      // 查找套餐使用记录
      const packageUsage = await this.prisma.packageUsage.findFirst({
        where: {
          userId: userId,
          packageId: packageId,
        },
        include: {
          user: true,
        },
      });

      if (!packageUsage) {
        // 如果没有找到使用记录，尝试从订单项查找
        const orderItem = await this.prisma.orderItem.findFirst({
          where: {
            id: packageId,
            order: {
              userId: userId,
            },
          },
          include: {
            order: true,
          },
        });

        if (!orderItem) {
          return {
            error: 'Package not found',
            packageId: packageId,
          };
        }

        // 创建套餐使用记录
        const product = orderItem.productCode ? await this.prisma.product.findFirst({
          where: { sku: orderItem.productCode },
        }) : null;

        const totalDataMB = product?.dataSize || 10240; // 默认10GB
        const usedDataMB = 0; // 初始使用量为0

        const newPackageUsage = await this.prisma.packageUsage.create({
          data: {
            userId: userId,
            orderId: orderItem.orderId,
            packageId: packageId,
            packageName: orderItem.variantText || 'Unknown Package',
            totalData: totalDataMB,
            usedData: usedDataMB,
            remainingData: totalDataMB - usedDataMB,
            validUntil: this.calculateValidUntil(orderItem.order.createdAt, product?.planType || undefined),
            status: 'active',
            lastUsedAt: new Date(),
          },
        });

        return this.formatUsageStats(newPackageUsage, ctx, isZh);
      }

      // 更新剩余流量
      const remainingData = packageUsage.totalData - packageUsage.usedData;
      if (remainingData !== packageUsage.remainingData) {
        await this.prisma.packageUsage.update({
          where: { id: packageUsage.id },
          data: { remainingData: remainingData },
        });
        packageUsage.remainingData = remainingData;
      }

      return this.formatUsageStats(packageUsage, ctx, isZh);

    } catch (error) {
      this.logger.error(`Error fetching package usage stats for ${packageId}:`, error);
      return {
        error: 'Failed to fetch usage stats',
        packageId: packageId,
      };
    }
  }

  private formatUsageStats(packageUsage: any, ctx: RequestContext, isZh: boolean) {
    const usagePercentage = Math.round((packageUsage.usedData / packageUsage.totalData) * 100);
    const remainingPercentage = 100 - usagePercentage;

    return {
      packageInfo: {
        id: packageUsage.packageId,
        name: packageUsage.packageName,
        description: isZh ? `${this.formatDataSize(packageUsage.totalData)}流量包` : `${this.formatDataSize(packageUsage.totalData)} Data Package`,
        validUntil: packageUsage.validUntil.toISOString().split('T')[0],
        status: packageUsage.status,
        statusText: isZh ? (packageUsage.status === 'active' ? '使用中' : '已过期') : (packageUsage.status === 'active' ? 'Active' : 'Expired'),
        canUpgrade: packageUsage.status === 'active' && remainingPercentage < 20,
        upgradeText: isZh ? '升级套餐' : 'Upgrade Package',
      },
      usageOverview: {
        totalData: this.formatDataSize(packageUsage.totalData),
        usedData: this.formatDataSize(packageUsage.usedData),
        remainingData: this.formatDataSize(packageUsage.remainingData),
        usagePercentage: usagePercentage,
        remainingPercentage: remainingPercentage,
        usedText: isZh ? '已使用流量' : 'Used Data',
        remainingText: isZh ? '剩余流量' : 'Remaining Data',
        progressColor: usagePercentage > 80 ? '#ef4444' : usagePercentage > 60 ? '#f59e0b' : '#10b981',
      },
      actions: [
        {
          id: 'add_data',
          text: isZh ? '增加流量' : 'Add Data',
          icon: 'plus',
          type: 'primary',
          action: 'add_data',
        },
        {
          id: 'data_alert',
          text: isZh ? '流量提醒' : 'Data Alert',
          icon: 'bell',
          type: 'secondary',
          action: 'set_alert',
        },
      ],
      context: {
        language: ctx.language,
        theme: ctx.theme,
        currency: ctx.currency,
      },
    };
  }



  async getPackageUsageHistory(userId: string, packageId: string, query: UsageHistoryQueryDto, ctx: RequestContext) {
    console.log('Context in getPackageUsageHistory:', ctx);

    const isZh = ctx.language.startsWith('zh');

    // Generate mock daily usage data for the chart (based on the image showing 2025/1/26)
    const generateDailyUsage = () => {
      const baseDate = new Date('2025-01-23');
      const dailyUsage: any[] = [];

      // Data for 7 days (23-29 January 2025)
      const usageData = [
        { day: 23, usage: 20 }, // 20MB
        { day: 24, usage: 100 }, // 100MB
        { day: 25, usage: 80 }, // 80MB
        { day: 26, usage: 100 }, // 100MB (highlighted day)
        { day: 27, usage: 0 }, // No usage
        { day: 28, usage: 0 }, // No usage
        { day: 29, usage: 0 }, // No usage
      ];

      usageData.forEach(data => {
        const date = new Date(baseDate);
        date.setDate(data.day);

        dailyUsage.push({
          date: DateFormatter.short(date), // YYYY-MM-DD format
          day: data.day,
          usage: data.usage,
          usageText: `${data.usage}MB`,
          isToday: data.day === 26, // Highlight January 26
          formattedDate: isZh ? `${data.day}日` : `${data.day}`,
        });
      });

      return dailyUsage;
    };

    const usageHistory = {
      period: query.period || 'daily',
      chartTitle: isZh ? '2025/1/26' : '2025/1/26',
      chartData: generateDailyUsage(),
      summary: {
        totalUsage: '300MB',
        averageDaily: '43MB',
        peakDay: isZh ? '1月24日' : 'Jan 24',
        peakUsage: '100MB',
      },
      chartConfig: {
        xAxisLabel: isZh ? '日期' : 'Date',
        yAxisLabel: isZh ? '使用量(MB)' : 'Usage (MB)',
        barColor: '#ef4444',
        highlightColor: '#dc2626',
        gridColor: '#f3f4f6',
      },
      pagination: {
        total: 7,
        page: query.page || 1,
        pageSize: query.pageSize || 10,
        hasMore: false,
      },
      context: {
        language: ctx.language,
        theme: ctx.theme,
        currency: ctx.currency,
      },
    };

    return usageHistory;
  }

  private formatOrderItemAsPackage(order: any, item: any, productMap: Map<string, any>, ctx: RequestContext, isZh: boolean) {
    const product = item.productCode ? productMap.get(item.productCode) : null;

    // 确定套餐状态
    const status = this.determinePackageStatus(order);
    const statusText = this.getStatusText(status, isZh);

    // 解析规格信息
    let packageType = 'domestic';
    let features: string[] = [];
    let dataSize = '10GB';
    let productName = item.variantText || 'Unknown Package';
    let productDescription = '';

    if (product) {
      try {
        const specs = typeof product.specifications === 'string'
          ? JSON.parse(product.specifications)
          : product.specifications;

        packageType = specs?.packageType || this.determinePackageType(product.name, product.description);
        features = specs?.features || [];
        dataSize = product.dataSize ? this.formatDataSize(product.dataSize) : '10GB';
        productName = product.name;
        productDescription = product.description;
      } catch (error) {
        this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
      }
    }

    // 默认特性
    if (features.length === 0) {
      features = [
        isZh ? '高速网络' : 'High-speed network',
        isZh ? '全天候支持' : '24/7 support',
        isZh ? '即时激活' : 'Instant activation'
      ];
    }

    // 模拟使用情况（在实际应用中，这应该来自使用统计）
    const usagePercentage = this.generateUsagePercentage(status);
    const usedData = this.calculateUsedData(dataSize, usagePercentage);

    // 确定有效期
    const validUntil = this.calculateValidUntil(order.createdAt, product?.planType);
    const activatedAt = order.createdAt;

    return {
      id: item.id,
      name: productName,
      description: productDescription,
      packageType: packageType,
      status: status,
      statusText: statusText,
      dataSize: dataSize,
      usedData: usedData,
      usagePercentage: usagePercentage,
      validUntil: validUntil,
      activatedAt: activatedAt.toISOString(),
      price: Number(item.price),
      currency: ctx.currency,
      features: features,
      imageUrl: product?.images && product.images.length > 0
        ? product.images[0]
        : 'https://example.com/default-package.jpg',
      progressColor: this.getProgressColor(status),
    };
  }

  private formatOrderItemAsDetailedPackage(orderItem: any, product: any, ctx: RequestContext, isZh: boolean) {
    const productMap = new Map();
    if (product && orderItem.productCode) {
      productMap.set(orderItem.productCode, product);
    }
    const basicPackage = this.formatOrderItemAsPackage(orderItem.order, orderItem, productMap, ctx, isZh);

    const remainingData = this.calculateRemainingData(basicPackage.dataSize, basicPackage.usagePercentage);

    return {
      ...basicPackage,
      remainingData: remainingData,
      detailedInfo: {
        activation: isZh ? '购买后立即激活' : 'Instant activation after purchase',
        coverage: isZh ? '全国覆盖' : 'Nationwide coverage',
        speed: isZh ? '5G网络下载速度最高1Gbps' : '5G network download speed up to 1Gbps',
        usage: isZh ? '适合日常使用、商务出行' : 'Perfect for daily use and business travel',
      },
      usageHistory: this.generateUsageHistory(basicPackage.dataSize, basicPackage.usagePercentage, isZh),
      restrictions: [
        isZh ? '仅限购买用户使用' : 'Valid only for purchasing user',
        isZh ? '不可转让或退款' : 'Non-transferable and non-refundable',
        isZh ? '按套餐条款使用' : 'Subject to package terms',
      ],
    };
  }

  private determinePackageStatus(order: any): string {
    const now = new Date();
    const orderDate = new Date(order.createdAt);
    const daysSinceOrder = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));

    if (order.status === 'PENDING' || order.status === 'PROCESSING') {
      return 'to_be_activated';
    } else if (order.status === 'COMPLETED' || order.status === 'DELIVERED') {
      if (daysSinceOrder < 1) {
        return 'activating';
      } else if (daysSinceOrder < 30) {
        return 'activating'; // 活跃状态
      } else {
        return 'expired';
      }
    }

    return 'to_be_activated';
  }

  private getStatusText(status: string, isZh: boolean): string {
    const statusMap: { [key: string]: { zh: string; en: string } } = {
      activating: { zh: '激活中', en: 'Activating' },
      to_be_activated: { zh: '待激活', en: 'To be activated' },
      expired: { zh: '已过期', en: 'Expired' },
    };

    return statusMap[status] ? (isZh ? statusMap[status].zh : statusMap[status].en) : status;
  }

  private getProgressColor(status: string): string {
    const colorMap: { [key: string]: string } = {
      activating: '#ef4444', // red
      to_be_activated: '#f59e0b', // amber
      expired: '#6b7280', // gray
    };

    return colorMap[status] || '#ef4444';
  }

  private determinePackageType(name: string, description: string): string {
    const text = (name + ' ' + description).toLowerCase();

    if (text.includes('漫游') || text.includes('roaming') || text.includes('international')) return 'roaming';
    if (text.includes('本地') || text.includes('local') || text.includes('city')) return 'local';

    return 'domestic'; // 默认为国内套餐
  }

  private generateUsagePercentage(status: string): number {
    if (status === 'expired') return Math.floor(Math.random() * 20) + 80; // 80-100%
    if (status === 'activating') return Math.floor(Math.random() * 60) + 20; // 20-80%
    return Math.floor(Math.random() * 30); // 0-30%
  }

  private calculateUsedData(totalData: string, usagePercentage: number): string {
    const totalMB = this.parseDataSize(totalData);
    const usedMB = Math.floor(totalMB * usagePercentage / 100);
    return this.formatDataSize(usedMB);
  }

  private calculateRemainingData(totalData: string, usagePercentage: number): string {
    const totalMB = this.parseDataSize(totalData);
    const remainingMB = Math.floor(totalMB * (100 - usagePercentage) / 100);
    return this.formatDataSize(remainingMB);
  }

  private calculateValidUntil(createdAt: Date, planType?: string): string {
    const created = new Date(createdAt);
    let validUntil: Date;

    if (planType === 'Daily') {
      validUntil = DateUtils.addDays(created, 1);
    } else if (planType === 'Weekly') {
      validUntil = DateUtils.addDays(created, 7);
    } else {
      validUntil = DateUtils.addDays(created, 30); // 默认30天
    }

    return validUntil.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
  }

  private generateUsageHistory(totalData: string, usagePercentage: number, isZh: boolean): Array<{date: string, usage: string, description: string}> {
    const history: Array<{date: string, usage: string, description: string}> = [];
    const days = Math.min(7, Math.floor(usagePercentage / 10)); // 根据使用百分比生成历史记录

    for (let i = 0; i < days; i++) {
      const date = DateUtils.addDays(new Date(), -i);
      const dailyUsage = Math.floor(Math.random() * 500) + 100; // 100-600MB

      history.push({
        date: date.toISOString().split('T')[0],
        usage: this.formatDataSize(dailyUsage),
        description: isZh ? '日常使用' : 'Daily usage'
      });
    }

    return history.reverse(); // 按时间正序排列
  }

  private parseDataSize(dataSize: string): number {
    const match = dataSize.match(/(\d+(?:\.\d+)?)\s*(MB|GB)/i);
    if (!match) return 1024; // 默认1GB

    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();

    return unit === 'GB' ? value * 1024 : value;
  }

  private formatDataSize(dataSize: number): string {
    if (dataSize >= 1024) {
      const sizeInGB = dataSize / 1024;
      return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
    } else {
      return `${Math.round(dataSize)}MB`;
    }
  }
}
