(()=>{var e={};e.id=2261,e.ids=[2261],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},87279:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),a(17709),a(89090),a(26083),a(35866);var r=a(23191),s=a(88716),o=a(37922),l=a.n(o),i=a(95231),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(t,n);let d=["",{children:["esims",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,17709)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\esims\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\esims\\page.tsx"],m="/esims/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/esims/page",pathname:"/esims",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89128:(e,t,a)=>{Promise.resolve().then(a.bind(a,28557))},28557:(e,t,a)=>{"use strict";a.d(t,{default:()=>j});var r=a(10326),s=a(17577),o=a(34474),l=a(62288),i=a(91148),n=a(33071),d=a(90772),c=a(567),m=a(57372),u=a(57671);function p({product:e}){let[t,a]=(0,s.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n.Zb,{className:"group p-4 flex flex-col gap-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-200 bg-gradient-to-br from-white via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 border-0",children:[(0,r.jsxs)("div",{className:"relative w-full aspect-video rounded-xl overflow-hidden mb-2 bg-gradient-to-tr from-blue-100 to-violet-100 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center",children:[e.images?.[0]?r.jsx("img",{src:e.images[0],alt:e.name,className:"object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"}):r.jsx(m.P.image,{className:"h-16 w-16 text-blue-200 dark:text-gray-700"}),e.stock<=10&&r.jsx("span",{className:"absolute top-2 right-2 bg-gradient-to-r from-pink-500 to-yellow-400 text-white text-xs px-2 py-1 rounded-full shadow",children:"Low Stock"})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col gap-1",children:[r.jsx("h3",{className:"font-semibold text-lg truncate",title:e.name,children:e.name}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 items-center mt-1",children:[e.country&&r.jsx(c.C,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800",children:e.country}),e.planType&&r.jsx(c.C,{variant:"outline",className:"text-xs bg-violet-50 text-violet-700 border-violet-200 dark:bg-violet-950 dark:text-violet-400 dark:border-violet-800",children:e.planType}),e.dataSize&&r.jsx(c.C,{variant:"outline",className:"text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800",children:e.dataSize>=1024?`${(e.dataSize/1024).toFixed(2)} GB`:`${e.dataSize} MB`})]}),(0,r.jsxs)("div",{className:"mt-2 text-2xl font-extrabold bg-gradient-to-r from-blue-500 to-violet-500 bg-clip-text text-transparent",children:["$",e.price.toFixed(2)]})]}),(0,r.jsxs)(d.Button,{className:"mt-2 w-full bg-gradient-to-r from-blue-500 to-violet-500 text-white font-bold shadow-lg hover:scale-105 transition-transform",onClick:()=>a(!0),variant:"default",children:[r.jsx(u.Z,{className:"mr-2 h-5 w-5"}),"View & Buy"]})]}),r.jsx(l.Vq,{open:t,onOpenChange:a,children:(0,r.jsxs)(l.cZ,{className:"max-w-lg",children:[(0,r.jsxs)(l.fK,{children:[r.jsx(l.$N,{children:e.name}),r.jsx(l.Be,{children:e.description})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[e.images?.[0]&&r.jsx("img",{src:e.images[0],alt:e.name,className:"rounded-lg w-full object-cover max-h-48"}),r.jsx(i.EsimBuyNowForm,{product:{id:e.id,stock:e.stock},variants:Array.isArray(e.variants)?e.variants.map(e=>({id:e.id,price:"number"==typeof e.price?e.price:Number(e.price),duration:e.duration,durationType:e.durationType})):[]})]})]})})]})}var x=a(30361),f=a(62881);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,f.Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var h=a(54014);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,f.Z)("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);var b=a(88307),v=a(48705);function j({allProducts:e,localDestinations:t,regionalDestinations:a}){let[l,i]=(0,s.useState)("Local"),[n,d]=(0,s.useState)(""),c=(0,s.useMemo)(()=>"Local"===l?t:a,[l,t,a]),m=(0,s.useMemo)(()=>n?"Local"===l?e.filter(e=>e.country?.trim()===n&&e.country&&1===e.country.split(/[,;]/).length):e.filter(e=>e.country&&e.country.split(/[,;]/).map(e=>e.trim()).includes(n)):[],[e,l,n]);return(0,r.jsxs)("div",{className:"space-y-12",children:[r.jsx("div",{className:"flex flex-col sm:flex-row gap-6 items-center mb-6",children:["Local","Regional"].map(e=>(0,r.jsxs)("button",{className:`group flex-1 rounded-2xl border-2 transition-all duration-200 px-0 py-0 bg-gradient-to-br from-white via-blue-50 to-blue-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 shadow-md hover:shadow-xl flex flex-col items-center justify-center h-28 cursor-pointer relative ${l===e?"border-blue-500 ring-2 ring-blue-400":"border-transparent"}`,onClick:()=>{i(e),d("")},"aria-pressed":l===e,children:[r.jsx("span",{className:"absolute top-3 right-3",children:l===e&&r.jsx(x.Z,{className:"text-blue-500 h-6 w-6"})}),r.jsx("span",{className:"mb-2",children:"Local"===e?r.jsx(g,{className:"h-8 w-8 text-blue-400 group-hover:scale-110 transition-transform"}):r.jsx(h.Z,{className:"h-8 w-8 text-violet-400 group-hover:scale-110 transition-transform"})}),r.jsx("span",{className:"text-lg font-bold text-gray-900 dark:text-gray-100",children:e}),r.jsx("span",{className:"text-xs text-muted-foreground mt-1",children:"Local"===e?"Single Country":"Multi-country Region"})]},e))}),(0,r.jsxs)("div",{className:"max-w-lg mx-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[r.jsx(y,{className:"h-5 w-5 text-blue-400"}),r.jsx("span",{className:"font-medium text-base",children:"Select Destination"})]}),(0,r.jsxs)(o.Ph,{value:n,onValueChange:d,children:[r.jsx(o.i4,{className:"w-full h-14 text-base rounded-xl border-2 border-blue-100 dark:border-gray-800 bg-white dark:bg-gray-950 shadow-sm",children:r.jsx(o.ki,{placeholder:`Choose ${"Local"===l?"Country":"Region"}`})}),r.jsx(o.Bw,{className:"max-h-72 overflow-y-auto rounded-xl shadow-lg",children:0===c.length?r.jsx("div",{className:"p-4 text-muted-foreground",children:"No destinations available"}):c.map(e=>r.jsx(o.Ql,{value:e,className:"truncate rounded-lg",children:e},e))})]})]}),r.jsx("div",{children:""===n?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-16",children:[r.jsx(b.Z,{className:"h-16 w-16 text-blue-200 mb-4"}),r.jsx("div",{className:"text-lg text-muted-foreground font-medium",children:"Please select a destination to view available eSIM plans."})]}):0===m.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-16",children:[r.jsx(v.Z,{className:"h-16 w-16 text-blue-200 mb-4"}),r.jsx("div",{className:"text-lg text-muted-foreground font-medium",children:"No eSIM plans found for this destination."})]}):r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:m.map(e=>r.jsx(p,{product:e},e.id))})})]})}},91148:(e,t,a)=>{"use strict";a.d(t,{EsimBuyNowForm:()=>d});var r=a(10326),s=a(35047),o=a(17577),l=a(90772),i=a(57671),n=a(19990);function d({product:e,variants:t}){let a=(0,s.useRouter)(),[d,c]=(0,o.useState)(!1),[m,u]=(0,o.useState)(t?.[0]??null);return(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[t&&t.length>0&&r.jsx(n.A,{variants:t,onVariantSelect:u,selectedVariant:m}),(0,r.jsxs)(l.Button,{size:"lg",className:"w-full md:w-auto bg-gradient-to-r from-blue-500 to-violet-500 text-white font-bold shadow-lg hover:scale-105 transition-transform text-lg py-6 px-10 flex items-center justify-center gap-2",onClick:()=>{c(!0);let t=new URLSearchParams({buyNow:"1",productId:e.id});m&&t.append("variantId",m.id),a.push(`/checkout?${t.toString()}`)},disabled:d||0===e.stock||t.length>0&&!m,children:[r.jsx(i.Z,{className:"h-6 w-6"})," ",d?"Redirecting...":"Buy Now"]})]})}},19990:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(10326),s=a(17577),o=a(31048),l=a(88846),i=a(77863);function n({variants:e,onVariantSelect:t,selectedVariant:a}){let n=Array.from(new Set(e.map(e=>e.durationType||""))).filter(Boolean),[d,c]=(0,s.useState)(a?.durationType||n[0]||""),m=Array.from(new Set(e.filter(e=>e.durationType===d).map(e=>e.duration?.toString()||""))).filter(Boolean).sort((e,t)=>Number(e)-Number(t)),[u,p]=(0,s.useState)(a?.duration?.toString()||m[0]||"");return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{className:"text-base",children:"Unit"}),r.jsx(l.E,{value:d,onValueChange:e=>{c(e)},children:r.jsx("div",{className:"flex flex-wrap gap-2",children:n.map(e=>(0,r.jsxs)("div",{children:[r.jsx(l.m,{value:e,id:`unit-${e}`,className:"peer sr-only"}),r.jsx(o._,{htmlFor:`unit-${e}`,className:"flex cursor-pointer items-center justify-center rounded-md border-2 border-muted bg-popover px-3 py-2 text-sm font-medium ring-offset-background transition-all hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:text-primary",children:e})]},e))})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(o._,{className:"text-base",children:"Duration"}),r.jsx(l.E,{value:u,onValueChange:e=>{p(e)},children:r.jsx("div",{className:"flex flex-wrap gap-2",children:m.map(e=>(0,r.jsxs)("div",{children:[r.jsx(l.m,{value:e,id:`period-${e}`,className:"peer sr-only"}),r.jsx(o._,{htmlFor:`period-${e}`,className:"flex cursor-pointer items-center justify-center rounded-md border-2 border-muted bg-popover px-3 py-2 text-sm font-medium ring-offset-background transition-all hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary peer-data-[state=checked]:text-primary",children:e})]},e))})})]}),a&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("p",{className:"text-lg font-semibold",children:["Selected: ",u," ",d]}),r.jsx("p",{className:"text-2xl font-bold",children:(0,i.T4)(a.price)})]})]})}},567:(e,t,a)=>{"use strict";a.d(t,{C:()=>i});var r=a(10326);a(17577);var s=a(79360),o=a(77863);let l=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...a}){return r.jsx("div",{className:(0,o.cn)(l({variant:t}),e),...a})}},33071:(e,t,a)=>{"use strict";a.d(t,{Ol:()=>i,SZ:()=>d,Zb:()=>l,aY:()=>c,eW:()=>m,ll:()=>n});var r=a(10326),s=a(17577),o=a(77863);let l=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));l.displayName="Card";let i=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let n=s.forwardRef(({className:e,...t},a)=>r.jsx("h3",{ref:a,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...t}));n.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},a)=>r.jsx("p",{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let m=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter"},62288:(e,t,a)=>{"use strict";a.d(t,{$N:()=>f,Be:()=>g,Vq:()=>n,cN:()=>x,cZ:()=>u,fK:()=>p,hg:()=>d});var r=a(10326),s=a(17577),o=a(11123),l=a(94019),i=a(77863);let n=o.fC,d=o.xz,c=o.h_;o.x8;let m=s.forwardRef(({className:e,...t},a)=>r.jsx(o.aV,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));m.displayName=o.aV.displayName;let u=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(c,{children:[r.jsx(m,{}),(0,r.jsxs)(o.VY,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,r.jsxs)(o.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(l.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=o.VY.displayName;let p=({className:e,...t})=>r.jsx("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let x=({className:e,...t})=>r.jsx("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});x.displayName="DialogFooter";let f=s.forwardRef(({className:e,...t},a)=>r.jsx(o.Dx,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));f.displayName=o.Dx.displayName;let g=s.forwardRef(({className:e,...t},a)=>r.jsx(o.dk,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=o.dk.displayName},31048:(e,t,a)=>{"use strict";a.d(t,{_:()=>d});var r=a(10326),s=a(17577),o=a(34478),l=a(79360),i=a(77863);let n=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},a)=>r.jsx(o.f,{ref:a,className:(0,i.cn)(n(),e),...t}));d.displayName=o.f.displayName},88846:(e,t,a)=>{"use strict";a.d(t,{E:()=>n,m:()=>d});var r=a(10326),s=a(17577),o=a(18623),l=a(53982),i=a(77863);let n=s.forwardRef(({className:e,...t},a)=>r.jsx(o.fC,{className:(0,i.cn)("grid gap-2",e),...t,ref:a}));n.displayName=o.fC.displayName;let d=s.forwardRef(({className:e,...t},a)=>r.jsx(o.ck,{ref:a,className:(0,i.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:r.jsx(o.z$,{className:"flex items-center justify-center",children:r.jsx(l.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=o.ck.displayName},34474:(e,t,a)=>{"use strict";a.d(t,{Bw:()=>u,Ph:()=>d,Ql:()=>p,i4:()=>m,ki:()=>c});var r=a(10326),s=a(17577),o=a(18792),l=a(941),i=a(32933),n=a(77863);let d=o.fC;o.ZA;let c=o.B4,m=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(o.xz,{ref:s,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:[t,r.jsx(o.JO,{asChild:!0,children:r.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=o.xz.displayName;let u=s.forwardRef(({className:e,children:t,position:a="popper",...s},l)=>r.jsx(o.h_,{children:r.jsx(o.VY,{ref:l,className:(0,n.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:r.jsx(o.l_,{className:(0,n.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));u.displayName=o.VY.displayName,s.forwardRef(({className:e,...t},a)=>r.jsx(o.__,{ref:a,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=o.__.displayName;let p=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(o.ck,{ref:s,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(o.wU,{children:r.jsx(i.Z,{className:"h-4 w-4"})})}),r.jsx(o.eT,{children:t})]}));p.displayName=o.ck.displayName,s.forwardRef(({className:e,...t},a)=>r.jsx(o.Z0,{ref:a,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=o.Z0.displayName},30361:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},17709:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(19510),s=a(71159),o=a(68570);let l=(0,o.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\esims\EsimsClient.tsx`),{__esModule:i,$$typeof:n}=l;l.default;let d=(0,o.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\esims\EsimsClient.tsx#default`);var c=a(72331),m=a(58585);async function u(){try{return(await c._.product.findMany({where:{status:{in:["ACTIVE","INACTIVE"]},category:{name:"qr_code"}},select:{id:!0,name:!0,description:!0,price:!0,images:!0,stock:!0,status:!0,country:!0,countryCode:!0,planType:!0,dataSize:!0,sku:!0,variants:{select:{id:!0,price:!0,duration:!0,durationType:!0}}},orderBy:{createdAt:"desc"}})).map(e=>({...e,variants:Array.isArray(e.variants)?e.variants.map(e=>({...e,price:"number"==typeof e.price?e.price:Number(e.price)})):[],price:"number"==typeof e.price?e.price:Number(e.price)}))}catch(e){return[]}}async function p(){let e=await u();if(!e)return(0,m.notFound)();let t=e.filter(e=>e.country&&1===e.country.split(/[,;]/).length),a=e.filter(e=>e.country&&e.country.split(/[,;]/).length>1),o=Array.from(new Set(t.map(e=>e.country?.trim()).filter(Boolean))),l=Array.from(new Set(a.flatMap(e=>e.country?e.country.split(/[,;]/).map(e=>e.trim()):[]).filter(Boolean)));return(0,r.jsxs)("main",{className:"max-w-5xl mx-auto px-4 py-8",children:[r.jsx("h1",{className:"text-3xl font-bold mb-2",children:"eSIM QR Code Plans"}),r.jsx("p",{className:"text-muted-foreground mb-8",children:"Buy a general eSIM QR-Code for your destination which can be used anywhere. Choose Local or Regional, then select your country/region to see available esims."}),r.jsx(s.Suspense,{fallback:r.jsx("div",{className:"text-center py-12",children:"Loading..."}),children:r.jsx(d,{allProducts:e,localDestinations:o,regionalDestinations:l})})]})}},72331:(e,t,a)=>{"use strict";a.d(t,{_:()=>s});var r=a(53524);let s=global.prisma||new r.PrismaClient({log:["error"]})},57481:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,8792,1123,4812,4824],()=>a(87279));module.exports=r})();