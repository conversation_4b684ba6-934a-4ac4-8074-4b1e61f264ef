"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
let HttpExceptionFilter = class HttpExceptionFilter {
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const status = exception.getStatus();
        const exceptionResponse = exception.getResponse();
        let errorCode = 'INTERNAL_ERROR';
        if (status === common_1.HttpStatus.UNAUTHORIZED)
            errorCode = 'UNAUTHORIZED';
        else if (status === common_1.HttpStatus.BAD_REQUEST)
            errorCode = 'VALIDATION_ERROR';
        else if (status === common_1.HttpStatus.NOT_FOUND)
            errorCode = 'RESOURCE_NOT_FOUND';
        else if (status === common_1.HttpStatus.CONFLICT)
            errorCode = 'ALREADY_EXISTS';
        const errorResponse = {
            error: {
                code: errorCode,
                message: exceptionResponse.message || exception.message,
                details: exceptionResponse.details || {},
            },
        };
        response.status(status).json(errorResponse);
    }
};
HttpExceptionFilter = __decorate([
    (0, common_1.Catch)(common_1.HttpException)
], HttpExceptionFilter);
exports.HttpExceptionFilter = HttpExceptionFilter;
//# sourceMappingURL=http-exception.filter.js.map