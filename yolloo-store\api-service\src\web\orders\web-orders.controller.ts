import { Controller, Get, Post, Patch, Delete, Body, Param, Query, Res, HttpStatus, Req } from '@nestjs/common';
import { Request, Response } from 'express';
import { WebOrdersService } from './web-orders.service';

/**
 * Web订单控制器
 * 处理原主应用的订单相关API
 * 路由: /api/web/orders/*
 */
@Controller('api/web/orders')
export class WebOrdersController {
  constructor(private readonly webOrdersService: WebOrdersService) {}

  /**
   * 获取订单列表
   * GET /api/web/orders
   */
  @Get()
  async getOrders(
    @Query() query: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const user = req['user'] as any;
      if (!user) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          error: 'Unauthorized',
        });
      }

      const orders = await this.webOrdersService.getOrders(user.id, query);
      return res.json(orders);
    } catch (error) {
      console.error('[WEB_ORDERS_GET]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch orders',
      });
    }
  }

  /**
   * 获取单个订单详情
   * GET /api/web/orders/:orderId
   */
  @Get(':orderId')
  async getOrder(
    @Param('orderId') orderId: string,
    @Res() res: Response,
  ) {
    try {
      const order = await this.webOrdersService.getOrderById(orderId);
      
      if (!order) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: 'Order not found',
        });
      }

      return res.json(order);
    } catch (error) {
      console.error('[WEB_ORDER_GET]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal error',
      });
    }
  }

  /**
   * 创建订单
   * POST /api/web/orders
   */
  @Post()
  async createOrder(
    @Body() createOrderDto: any,
    @Res() res: Response,
  ) {
    try {
      // TODO: 从session获取用户ID
      const userId = 'temp-user-id'; // 临时处理
      const order = await this.webOrdersService.createOrder(userId, createOrderDto);
      return res.status(HttpStatus.CREATED).json(order);
    } catch (error) {
      console.error('[WEB_ORDER_CREATE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to create order',
      });
    }
  }

  /**
   * 更新订单
   * PATCH /api/web/orders/:orderId
   */
  @Patch(':orderId')
  async updateOrder(
    @Param('orderId') orderId: string,
    @Body() updateOrderDto: any,
    @Res() res: Response,
  ) {
    try {
      const order = await this.webOrdersService.updateOrder(orderId, updateOrderDto);
      return res.json(order);
    } catch (error) {
      console.error('[WEB_ORDER_UPDATE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to update order',
      });
    }
  }

  /**
   * 获取可用eSIM
   * GET /api/web/orders/available-esims
   */
  @Get('available-esims')
  async getAvailableEsims(
    @Query() query: any,
    @Res() res: Response,
  ) {
    try {
      const esims = await this.webOrdersService.getAvailableEsims(query);
      return res.json(esims);
    } catch (error) {
      console.error('[WEB_ORDERS_AVAILABLE_ESIMS]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch available eSIMs',
      });
    }
  }
}
