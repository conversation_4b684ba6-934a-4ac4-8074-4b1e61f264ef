/**
 * 统一JWT认证系统测试脚本
 */

const API_BASE_URL = 'http://localhost:4001';

// 测试用户数据
const testUser = {
  name: 'Test User',
  email: '<EMAIL>',
  password: 'test123456'
};

let authCookies = '';

/**
 * 发送HTTP请求的辅助函数
 */
async function makeRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Cookie': authCookies,
      ...options.headers,
    },
  };

  const response = await fetch(url, { ...defaultOptions, ...options });
  
  // 提取Set-Cookie头部
  const setCookieHeader = response.headers.get('set-cookie');
  if (setCookieHeader) {
    authCookies = setCookieHeader;
  }

  const data = await response.json().catch(() => ({}));
  
  return {
    status: response.status,
    ok: response.ok,
    data,
    headers: response.headers,
  };
}

/**
 * 测试用户注册
 */
async function testSignup() {
  console.log('\n🧪 测试用户注册...');
  
  const response = await makeRequest('/api/mobile/api/web/auth/signup', {
    method: 'POST',
    body: JSON.stringify(testUser),
  });

  console.log(`状态码: ${response.status}`);
  console.log('响应:', response.data);
  
  if (response.ok) {
    console.log('✅ 用户注册成功');
    return true;
  } else {
    console.log('❌ 用户注册失败');
    return false;
  }
}

/**
 * 测试用户登录
 */
async function testLogin() {
  console.log('\n🧪 测试用户登录...');
  
  const response = await makeRequest('/api/mobile/api/web/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password,
    }),
  });

  console.log(`状态码: ${response.status}`);
  console.log('响应:', response.data);
  console.log('Cookies:', authCookies);
  
  if (response.ok) {
    console.log('✅ 用户登录成功');
    return true;
  } else {
    console.log('❌ 用户登录失败');
    return false;
  }
}

/**
 * 测试获取当前用户信息
 */
async function testGetCurrentUser() {
  console.log('\n🧪 测试获取当前用户信息...');
  
  const response = await makeRequest('/api/mobile/api/web/auth/me', {
    method: 'GET',
  });

  console.log(`状态码: ${response.status}`);
  console.log('响应:', response.data);
  
  if (response.ok) {
    console.log('✅ 获取用户信息成功');
    return true;
  } else {
    console.log('❌ 获取用户信息失败');
    return false;
  }
}

/**
 * 测试Token刷新
 */
async function testRefreshToken() {
  console.log('\n🧪 测试Token刷新...');
  
  const response = await makeRequest('/api/mobile/api/web/auth/refresh', {
    method: 'POST',
  });

  console.log(`状态码: ${response.status}`);
  console.log('响应:', response.data);
  console.log('新Cookies:', authCookies);
  
  if (response.ok) {
    console.log('✅ Token刷新成功');
    return true;
  } else {
    console.log('❌ Token刷新失败');
    return false;
  }
}

/**
 * 测试移动端JWT认证
 */
async function testMobileAuth() {
  console.log('\n🧪 测试移动端JWT认证...');
  
  // 先通过移动端API登录获取JWT token
  const loginResponse = await makeRequest('/api/mobile/auth/login', {
    method: 'POST',
    body: JSON.stringify({
      email: testUser.email,
      password: testUser.password,
    }),
  });

  console.log('移动端登录状态码:', loginResponse.status);
  console.log('移动端登录响应:', loginResponse.data);
  
  if (!loginResponse.ok) {
    console.log('❌ 移动端登录失败');
    return false;
  }

  const token = loginResponse.data.token;
  console.log('获取到JWT Token:', token);

  // 使用JWT token访问受保护的API
  const profileResponse = await makeRequest('/api/mobile/users/profile', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  console.log('用户资料状态码:', profileResponse.status);
  console.log('用户资料响应:', profileResponse.data);
  
  if (profileResponse.ok) {
    console.log('✅ 移动端JWT认证成功');
    return true;
  } else {
    console.log('❌ 移动端JWT认证失败');
    return false;
  }
}

/**
 * 测试登出
 */
async function testLogout() {
  console.log('\n🧪 测试用户登出...');
  
  const response = await makeRequest('/api/mobile/api/web/auth/logout', {
    method: 'POST',
  });

  console.log(`状态码: ${response.status}`);
  console.log('响应:', response.data);
  
  if (response.ok) {
    console.log('✅ 用户登出成功');
    return true;
  } else {
    console.log('❌ 用户登出失败');
    return false;
  }
}

/**
 * 测试健康检查
 */
async function testHealthCheck() {
  console.log('\n🧪 测试健康检查...');
  
  const response = await makeRequest('/api/mobile/health', {
    method: 'GET',
  });

  console.log(`状态码: ${response.status}`);
  console.log('响应:', response.data);
  
  if (response.ok) {
    console.log('✅ 健康检查成功');
    return true;
  } else {
    console.log('❌ 健康检查失败');
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试统一JWT认证系统');
  console.log('API服务地址:', API_BASE_URL);
  
  const results = [];
  
  try {
    // 1. 健康检查
    results.push(await testHealthCheck());
    
    // 2. 用户注册
    results.push(await testSignup());
    
    // 3. 用户登录
    results.push(await testLogin());
    
    // 4. 获取当前用户信息
    results.push(await testGetCurrentUser());
    
    // 5. Token刷新
    results.push(await testRefreshToken());
    
    // 6. 移动端JWT认证
    results.push(await testMobileAuth());
    
    // 7. 用户登出
    results.push(await testLogout());
    
    // 统计结果
    const passed = results.filter(r => r).length;
    const total = results.length;
    
    console.log('\n📊 测试结果统计:');
    console.log(`✅ 通过: ${passed}/${total}`);
    console.log(`❌ 失败: ${total - passed}/${total}`);
    
    if (passed === total) {
      console.log('\n🎉 所有测试通过！统一JWT认证系统工作正常！');
    } else {
      console.log('\n⚠️  部分测试失败，请检查相关功能');
    }
    
  } catch (error) {
    console.error('\n💥 测试过程中发生错误:', error);
  }
}

// 运行测试
runAllTests();
