import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import * as cookieParser from 'cookie-parser';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Set global prefix for all routes (移动端API保持原有前缀)
  // Web API使用 /api/web/* 前缀，在各自的控制器中定义
  app.setGlobalPrefix('api/mobile');

  // 添加Cookie解析中间件
  app.use(cookieParser());

  // Serve static files
  app.useStaticAssets(join(__dirname, '..', 'public'), {
    prefix: '/api/mobile/static/',
  });

  // Enable CORS with credentials support
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:8000',
      'https://esim.yolloo.com',
      'https://yolloo.com',
    ],
    credentials: true, // 支持Cookie
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });

  // Set up global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties that do not have any decorators
      transform: true, // Transform payloads to DTO instances
      forbidNonWhitelisted: true, // Throw errors if non-whitelisted properties are present
      transformOptions: {
        enableImplicitConversion: true, // Automatically transform primitive types
      },
    }),
  );

  // 使用环境变量中的端口，优先 MOBILE_API_PORT，其次 PORT，最后默认 4000
  const port = process.env.MOBILE_API_PORT || process.env.PORT || 4000;
  await app.listen(port, '0.0.0.0');
  console.log(`Mobile API is running on port ${port}, URL: ${await app.getUrl()}`);
}
bootstrap();
