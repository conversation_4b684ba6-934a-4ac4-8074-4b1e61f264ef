(()=>{var t={};t.id=3,t.ids=[3],t.modules={53524:t=>{"use strict";t.exports=require("@prisma/client")},47849:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external")},72934:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external")},54580:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:t=>{"use strict";t.exports=require("assert")},14300:t=>{"use strict";t.exports=require("buffer")},6113:t=>{"use strict";t.exports=require("crypto")},9523:t=>{"use strict";t.exports=require("dns")},82361:t=>{"use strict";t.exports=require("events")},13685:t=>{"use strict";t.exports=require("http")},95687:t=>{"use strict";t.exports=require("https")},41808:t=>{"use strict";t.exports=require("net")},22037:t=>{"use strict";t.exports=require("os")},71017:t=>{"use strict";t.exports=require("path")},63477:t=>{"use strict";t.exports=require("querystring")},12781:t=>{"use strict";t.exports=require("stream")},71576:t=>{"use strict";t.exports=require("string_decoder")},24404:t=>{"use strict";t.exports=require("tls")},76224:t=>{"use strict";t.exports=require("tty")},57310:t=>{"use strict";t.exports=require("url")},73837:t=>{"use strict";t.exports=require("util")},59796:t=>{"use strict";t.exports=require("zlib")},1435:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>f,pages:()=>s,routeModule:()=>h,tree:()=>l}),r(66136),r(85460),r(89090),r(26083),r(35866);var n=r(23191),o=r(88716),i=r(37922),a=r.n(i),c=r(95231),u={};for(let t in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66136)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,57481))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,57481))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],s=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\page.tsx"],f="/admin/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},36895:(t,e,r)=>{Promise.resolve().then(r.bind(r,42770))},42770:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>dL});var n={};r.r(n),r.d(n,{scaleBand:()=>nO,scaleDiverging:()=>function t(){var e=ia(cj()(oK));return e.copy=function(){return cx(e,t())},ny.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=iv(cj()).domain([.1,1,10]);return e.copy=function(){return cx(e,t()).base(e.base())},ny.apply(e,arguments)},scaleDivergingPow:()=>cS,scaleDivergingSqrt:()=>cP,scaleDivergingSymlog:()=>function t(){var e=ig(cj());return e.copy=function(){return cx(e,t()).constant(e.constant())},ny.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,oY),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,oY):[0,1],ia(n)},scaleImplicit:()=>ng,scaleLinear:()=>ic,scaleLog:()=>function t(){let e=iv(o2()).domain([1,10]);return e.copy=()=>o1(e,t()).base(e.base()),nd.apply(e,arguments),e},scaleOrdinal:()=>nx,scalePoint:()=>nw,scalePow:()=>iS,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=oo){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[oa(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(ot),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nd.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[oa(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nd.apply(ia(c),arguments)},scaleRadial:()=>function t(){var e,r=o3(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iA(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,oY)).map(iA)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nd.apply(i,arguments),ia(i)},scaleSequential:()=>function t(){var e=ia(cg()(oK));return e.copy=function(){return cx(e,t())},ny.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=iv(cg()).domain([1,10]);return e.copy=function(){return cx(e,t()).base(e.base())},ny.apply(e,arguments)},scaleSequentialPow:()=>cO,scaleSequentialQuantile:()=>function t(){var e=[],r=oK;function n(t){if(null!=t&&!isNaN(t=+t))return r((oa(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(ot),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return ik(t);if(e>=1)return iE(t);var n,o=(n-1)*e,i=Math.floor(o),a=iE((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?i_:function(t=ot){if(t===ot)return i_;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iM(e,n,r),i(e[o],a)>0&&iM(e,n,o);c<u;){for(iM(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iM(e,n,u):iM(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(ik(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},ny.apply(n,arguments)},scaleSequentialSqrt:()=>cw,scaleSequentialSymlog:()=>function t(){var e=ig(cg());return e.copy=function(){return cx(e,t()).constant(e.constant())},ny.apply(e,arguments)},scaleSqrt:()=>iP,scaleSymlog:()=>function t(){var e=ig(o2());return e.copy=function(){return o1(e,t()).constant(e.constant())},nd.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[oa(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nd.apply(i,arguments)},scaleTime:()=>cm,scaleUtc:()=>cb,tickFormat:()=>ii});var o=r(10326),i=r(17577),a=r.n(i),c=r(33071),u=r(79210),l=r(57372),s=r(65304),f=r(567),p=r(28758),h=r(41135),d=r(69450),y=r.n(d),v=r(4891),m=r.n(v),b=r(81719),g=r.n(b),x=r(9459),O=r.n(x),w=r(77717),j=r.n(w),S=function(t){return 0===t?0:t>0?1:-1},P=function(t){return m()(t)&&t.indexOf("%")===t.length-1},A=function(t){return j()(t)&&!g()(t)},E=function(t){return A(t)||m()(t)},k=0,_=function(t){var e=++k;return"".concat(t||"").concat(e)},M=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!A(t)&&!m()(t))return n;if(P(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return g()(r)&&(r=n),o&&r>e&&(r=e),r},T=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},C=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},N=function(t,e){return A(t)&&A(e)?function(r){return t+r*(e-t)}:function(){return e}};function D(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):O()(t,e))===r}):null}var I=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},B=r(20119),R=r.n(B),L=r(85586),z=r.n(L),U=r(62880),F=r.n(U),$=r(29507);function q(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function W(t){return(W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var X=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],H=["points","pathLength"],V={svg:["viewBox","children"],polygon:H,polyline:H},G=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Y=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,i.isValidElement)(t)&&(r=t.props),!F()(r))return null;var n={};return Object.keys(r).forEach(function(t){G.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},Z=function(t,e,r){if(!F()(t)||"object"!==W(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];G.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},K=["children"],J=["children"];function Q(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var te={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tr=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tn=null,to=null,ti=function t(e){if(e===tn&&Array.isArray(to))return to;var r=[];return i.Children.forEach(e,function(e){R()(e)||((0,$.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),to=r,tn=e,r};function ta(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tr(t)}):[tr(e)],ti(t).forEach(function(t){var e=O()(t,"type.displayName")||O()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tc(t,e){var r=ta(t,e);return r&&r[0]}var tu=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!A(r)&&!(r<=0)&&!!A(n)&&!(n<=0)},tl=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ts=function(t,e,r,n){var o,i=null!==(o=null==V?void 0:V[n])&&void 0!==o?o:[];return!z()(t)&&(n&&i.includes(e)||X.includes(e))||r&&G.includes(e)},tf=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,i.isValidElement)(t)&&(n=t.props),!F()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;ts(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tp=function t(e,r){if(e===r)return!0;var n=i.Children.count(e);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return th(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=e[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!th(a,c))return!1}return!0},th=function(t,e){if(R()(t)&&R()(e))return!0;if(!R()(t)&&!R()(e)){var r=t.props||{},n=r.children,o=Q(r,K),i=e.props||{},a=i.children,c=Q(i,J);if(n&&a)return q(o,c)&&tp(n,a);if(!n&&!a)return q(o,c)}return!1},td=function(t,e){var r=[],n={};return ti(t).forEach(function(t,o){if(t&&t.type&&m()(t.type)&&tl.indexOf(t.type)>=0)r.push(t);else if(t){var i=tr(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}}),r},ty=function(t){var e=t&&t.type;return e&&te[e]?te[e]:null};function tv(t){return(tv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tm(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=tv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tv(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tm(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tg(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tx=(0,i.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,c=void 0===o?{width:-1,height:-1}:o,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,d=void 0===p?0:p,v=t.minHeight,m=t.maxHeight,b=t.children,g=t.debounce,x=void 0===g?0:g,O=t.id,w=t.className,j=t.onResize,S=t.style,A=(0,i.useRef)(null),E=(0,i.useRef)();E.current=j,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})});var k=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tg(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tg(t,2)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),_=k[0],M=k[1],T=(0,i.useCallback)(function(t,e){M(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;T(n,o),null===(e=E.current)||void 0===e||e.call(E,n,o)};x>0&&(t=y()(t,x,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=A.current.getBoundingClientRect();return T(r.width,r.height),e.observe(A.current),function(){e.disconnect()}},[T,x]);var C=(0,i.useMemo)(function(){var t=_.containerWidth,e=_.containerHeight;if(t<0||e<0)return null;I(P(l)||P(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),I(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=P(l)?t:l,o=P(f)?e:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),m&&o>m&&(o=m)),I(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,l,f,d,v,n);var c=!Array.isArray(b)&&tr(b.type).endsWith("Chart");return a().Children.map(b,function(t){return a().isValidElement(t)?(0,i.cloneElement)(t,tb({width:r,height:o},c?{style:tb({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,b,f,m,v,d,_,l]);return a().createElement("div",{id:O?"".concat(O):void 0,className:(0,h.Z)("recharts-responsive-container",w),style:tb(tb({},void 0===S?{}:S),{},{width:l,height:f,minWidth:d,minHeight:v,maxHeight:m}),ref:A},C)}),tO=r(65680),tw=r.n(tO),tj=r(77529),tS=r.n(tj);function tP(t,e){if(!t)throw Error("Invariant failed")}var tA=["children","width","height","viewBox","className","style","title","desc"];function tE(){return(tE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tk(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tA),f=o||{width:r,height:n,x:0,y:0},p=(0,h.Z)("recharts-surface",i);return a().createElement("svg",tE({},tf(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var t_=["children","className"];function tM(){return(tM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tT=a().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t_),i=(0,h.Z)("recharts-layer",n);return a().createElement("g",tM({className:i},tf(o,!0),{ref:e}),r)});function tC(t){return(tC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tN(){return(tN=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tI(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=tC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tC(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tR(t){return Array.isArray(t)&&E(t[0])&&E(t[1])?t.join(" ~ "):t}var tL=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,d=t.label,y=t.labelFormatter,v=t.accessibilityLayer,m=tB({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),b=tB({margin:0},void 0===c?{}:c),g=!R()(d),x=g?d:"",O=(0,h.Z)("recharts-default-tooltip",f),w=(0,h.Z)("recharts-tooltip-label",p);return g&&y&&null!=u&&(x=y(d,u)),a().createElement("div",tN({className:O,style:m},void 0!==v&&v?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:b},a().isValidElement(x)?x:"".concat(x)),function(){if(u&&u.length){var t=(s?tS()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=tB({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||l||tR,c=t.value,s=t.name,f=c,p=s;if(o&&null!=f&&null!=p){var h=o(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tD(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tD(t,2)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},E(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,E(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tz(t){return(tz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tU(t,e,r){var n;return(n=function(t,e){if("object"!=tz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tz(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tF="recharts-tooltip-wrapper",t$={visibility:"hidden"};function tq(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&A(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tW(t){return(tW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tX(Object(r),!0).forEach(function(e){tZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tV(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tV=function(){return!!t})()}function tG(t){return(tG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tY(t,e){return(tY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tZ(t,e,r){return(e=tK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tK(t){var e=function(t,e){if("object"!=tW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tW(e)?e:e+""}var tJ=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=tG(e),tZ(t=function(t,e){if(e&&("object"===tW(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tV()?Reflect.construct(e,n||[],tG(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tZ(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tY(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,c,u,l,s,f,p,d,y,v,m,b,g,x,O=this,w=this.props,j=w.active,S=w.allowEscapeViewBox,P=w.animationDuration,E=w.animationEasing,k=w.children,_=w.coordinate,M=w.hasPayload,T=w.isAnimationActive,C=w.offset,N=w.position,D=w.reverseDirection,I=w.useTranslate3d,B=w.viewBox,R=w.wrapperStyle,L=(p=(t={allowEscapeViewBox:S,coordinate:_,offsetTopLeft:C,position:N,reverseDirection:D,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:B}).allowEscapeViewBox,d=t.coordinate,y=t.offsetTopLeft,v=t.position,m=t.reverseDirection,b=t.tooltipBox,g=t.useTranslate3d,x=t.viewBox,b.height>0&&b.width>0&&d?(r=(e={translateX:s=tq({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:y,position:v,reverseDirection:m,tooltipDimension:b.width,viewBox:x,viewBoxDimension:x.width}),translateY:f=tq({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:y,position:v,reverseDirection:m,tooltipDimension:b.height,viewBox:x,viewBoxDimension:x.height}),useTranslate3d:g}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=t$,{cssProperties:l,cssClasses:(i=(o={translateX:s,translateY:f,coordinate:d}).coordinate,c=o.translateX,u=o.translateY,(0,h.Z)(tF,tU(tU(tU(tU({},"".concat(tF,"-right"),A(c)&&i&&A(i.x)&&c>=i.x),"".concat(tF,"-left"),A(c)&&i&&A(i.x)&&c<i.x),"".concat(tF,"-bottom"),A(u)&&i&&A(i.y)&&u>=i.y),"".concat(tF,"-top"),A(u)&&i&&A(i.y)&&u<i.y)))}),z=L.cssClasses,U=L.cssProperties,F=tH(tH({transition:T&&j?"transform ".concat(P,"ms ").concat(E):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},R);return a().createElement("div",{tabIndex:-1,className:z,style:F,ref:function(t){O.wrapperNode=t}},k)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tK(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),tQ={isSsr:!0,get:function(t){return tQ[t]},set:function(t,e){if("string"==typeof t)tQ[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){tQ[e]=t[e]})}}},t0=r(82511),t1=r.n(t0);function t2(t,e,r){return!0===e?t1()(t,r):z()(e)?t1()(t,e):t}function t3(t){return(t3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t6(Object(r),!0).forEach(function(e){t9(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t4(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t4=function(){return!!t})()}function t7(t){return(t7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t8(t,e){return(t8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t9(t,e,r){return(e=et(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function et(t){var e=function(t,e){if("object"!=t3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t3(e)?e:e+""}function ee(t){return t.dataKey}var er=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=t7(t),function(t,e){if(e&&("object"===t3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t4()?Reflect.construct(t,e||[],t7(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&t8(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=t2(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,ee));var O=x.length>0;return a().createElement(tJ,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=t5(t5({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tL,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,et(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);t9(er,"displayName","Tooltip"),t9(er,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!tQ.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var en=r(32009),eo=r.n(en);let ei=Math.cos,ea=Math.sin,ec=Math.sqrt,eu=Math.PI,el=2*eu,es={draw(t,e){let r=ec(e/eu);t.moveTo(r,0),t.arc(0,0,r,0,el)}},ef=ec(1/3),ep=2*ef,eh=ea(eu/10)/ea(7*eu/10),ed=ea(el/10)*eh,ey=-ei(el/10)*eh,ev=ec(3),em=ec(3)/2,eb=1/ec(12),eg=(eb/2+1)*3;function ex(t){return function(){return t}}let eO=Math.PI,ew=2*eO,ej=ew-1e-6;function eS(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eP{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eS:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eS;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6){if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((eO-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%ew+ew),f>ej?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eO)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function eA(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eP(e)}function eE(t){return(eE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eP.prototype,ec(3),ec(3);var ek=["type","size","sizeType"];function e_(){return(e_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eM(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=eE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eE(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eC={symbolCircle:es,symbolCross:{draw(t,e){let r=ec(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=ec(e/ep),n=r*ef;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=ec(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=ec(.8908130915292852*e),n=ed*r,o=ey*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=el*e/5,a=ei(i),c=ea(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-ec(e/(3*ev));t.moveTo(0,2*r),t.lineTo(-ev*r,-r),t.lineTo(ev*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=ec(e/eg),n=r/2,o=r*eb,i=r*eb+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-em*o,em*n+-.5*o),t.lineTo(-.5*n-em*i,em*n+-.5*i),t.lineTo(-.5*a-em*i,em*a+-.5*i),t.lineTo(-.5*n+em*o,-.5*o-em*n),t.lineTo(-.5*n+em*i,-.5*i-em*n),t.lineTo(-.5*a+em*i,-.5*i-em*a),t.closePath()}}},eN=Math.PI/180,eD=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eN;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eI=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,c=t.sizeType,u=void 0===c?"area":c,l=eT(eT({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ek)),{},{type:n,size:i,sizeType:u}),s=l.className,f=l.cx,p=l.cy,d=tf(l,!0);return f===+f&&p===+p&&i===+i?a().createElement("path",e_({},d,{className:(0,h.Z)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eC["symbol".concat(eo()(n))]||es,(function(t,e){let r=null,n=eA(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:ex(t||es),e="function"==typeof e?e:ex(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:ex(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eD(i,u,n))())})):null};function eB(t){return(eB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eR(){return(eR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ez(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ez=function(){return!!t})()}function eU(t){return(eU=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eF(t,e){return(eF=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e$(t,e,r){return(e=eq(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eq(t){var e=function(t,e){if("object"!=eB(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eB(e)?e:e+""}eI.registerSymbol=function(t,e){eC["symbol".concat(eo()(t))]=e};var eW=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=eU(t),function(t,e){if(e&&("object"===eB(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ez()?Reflect.construct(t,e||[],eU(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eF(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eL(Object(r),!0).forEach(function(e){e$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,a().cloneElement(t.legendIcon,i)}return a().createElement(eI,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===o?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,f=(0,h.Z)(e$(e$({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=z()(e.value)?null:e.value;I(!z()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?c:e.color;return a().createElement("li",eR({className:f,style:l,key:"legend-item-".concat(r)},Z(t.props,e,r)),a().createElement(tk,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eq(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function eX(t){return(eX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e$(eW,"displayName","Legend"),e$(eW,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eH=["ref"];function eV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eV(Object(r),!0).forEach(function(e){eQ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eY(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e0(n.key),n)}}function eZ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eZ=function(){return!!t})()}function eK(t){return(eK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eJ(t,e){return(eJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eQ(t,e,r){return(e=e0(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e0(t){var e=function(t,e){if("object"!=eX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eX(e)?e:e+""}function e1(t){return t.value}var e2=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=eK(e),eQ(t=function(t,e){if(e&&("object"===eX(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eZ()?Reflect.construct(e,r||[],eK(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eJ(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?eG({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),eG(eG({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=eG(eG({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eH);return a().createElement(eW,r)}(r,eG(eG({},this.props),{},{payload:t2(u,c,e1)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=eG(eG({},this.defaultProps),t.props).layout;return"vertical"===r&&A(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&eY(n.prototype,e),r&&eY(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function e3(){return(e3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}eQ(e2,"displayName","Legend"),eQ(e2,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var e6=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,h.Z)("recharts-dot",o);return e===+e&&r===+r&&n===+n?a().createElement("circle",e3({},tf(t,!1),Y(t),{className:i,cx:e,cy:r,r:n})):null},e5=r(78439),e4=r.n(e5),e7=Object.getOwnPropertyNames,e8=Object.getOwnPropertySymbols,e9=Object.prototype.hasOwnProperty;function rt(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function re(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function rr(t){return e7(t).concat(e8(t))}var rn=Object.hasOwn||function(t,e){return e9.call(t,e)};function ro(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var ri=Object.getOwnPropertyDescriptor,ra=Object.keys;function rc(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function ru(t,e){return ro(t.getTime(),e.getTime())}function rl(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rs(t,e){return t===e}function rf(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rp(t,e,r){var n=ra(t),o=n.length;if(ra(e).length!==o)return!1;for(;o-- >0;)if(!rg(t,e,r,n[o]))return!1;return!0}function rh(t,e,r){var n,o,i,a=rr(t),c=a.length;if(rr(e).length!==c)return!1;for(;c-- >0;)if(!rg(t,e,r,n=a[c])||(o=ri(t,n),i=ri(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rd(t,e){return ro(t.valueOf(),e.valueOf())}function ry(t,e){return t.source===e.source&&t.flags===e.flags}function rv(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rm(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rb(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rg(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rn(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rx=Array.isArray,rO="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rw=Object.assign,rj=Object.prototype.toString.call.bind(Object.prototype.toString),rS=rP();function rP(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rh:rc,areDatesEqual:ru,areErrorsEqual:rl,areFunctionsEqual:rs,areMapsEqual:n?rt(rf,rh):rf,areNumbersEqual:ro,areObjectsEqual:n?rh:rp,arePrimitiveWrappersEqual:rd,areRegExpsEqual:ry,areSetsEqual:n?rt(rv,rh):rv,areTypedArraysEqual:n?rh:rm,areUrlsEqual:rb};if(r&&(o=rw({},o,r(o))),e){var i=re(o.areArraysEqual),a=re(o.areMapsEqual),c=re(o.areObjectsEqual),u=re(o.areSetsEqual);o=rw({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rx(t))return r(t,e,d);if(null!=rO&&rO(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rj(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rA(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rE(t){return(rE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rk(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r_(t){return(r_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rM(Object(r),!0).forEach(function(e){rC(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rC(t,e,r){var n;return(n=function(t,e){if("object"!==r_(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r_(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rP({strict:!0}),rP({circular:!0}),rP({circular:!0,strict:!0}),rP({createInternalComparator:function(){return ro}}),rP({strict:!0,createInternalComparator:function(){return ro}}),rP({circular:!0,createInternalComparator:function(){return ro}}),rP({circular:!0,createInternalComparator:function(){return ro},strict:!0});var rN=function(t){return t},rD=function(t,e){return Object.keys(e).reduce(function(r,n){return rT(rT({},r),{},rC({},n,t(n,e[n])))},{})},rI=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rB=function(t,e,r,n,o,i,a,c){};function rR(t,e){if(t){if("string"==typeof t)return rL(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rL(t,e)}}function rL(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rz=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rU=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rF=function(t,e){return function(r){return rU(rz(t,e),r)}},r$=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),4!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rR(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rB(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rB([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rF(i,c),h=rF(a,u),d=(t=i,e=c,function(r){var n;return rU([].concat(function(t){if(Array.isArray(t))return rL(t)}(n=rz(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rR(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},rq=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rW=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r$(n);case"spring":return rq();default:if("cubic-bezier"===n.split("(")[0])return r$(n);rB(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rB(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rX(t){return(rX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rH(t){return function(t){if(Array.isArray(t))return rK(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rZ(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rV(Object(r),!0).forEach(function(e){rY(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rY(t,e,r){var n;return(n=function(t,e){if("object"!==rX(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rX(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rZ(t,e){if(t){if("string"==typeof t)return rK(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rK(t,e)}}function rK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rJ=function(t,e,r){return t+(e-t)*r},rQ=function(t){return t.from!==t.to},r0=function t(e,r,n){var o=rD(function(t,r){if(rQ(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||rZ(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return rG(rG({},r),{},{from:i,velocity:a})}return r},r);return n<1?rD(function(t,e){return rQ(e)?rG(rG({},e),{},{velocity:rJ(e.velocity,o[t].velocity,n),from:rJ(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let r1=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return rG(rG({},r),{},rY({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return rG(rG({},r),{},rY({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=r0(r,l,a),o(rG(rG(rG({},t),e),rD(function(t,e){return e.from},l))),i=n,Object.values(l).filter(rQ).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=rD(function(t,e){return rJ.apply(void 0,rH(e).concat([r(c)]))},u);if(o(rG(rG(rG({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rD(function(t,e){return rJ.apply(void 0,rH(e).concat([r(1)]))},u);o(rG(rG(rG({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function r2(t){return(r2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r3=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function r6(t){return function(t){if(Array.isArray(t))return r5(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r5(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r5(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r5(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r4(Object(r),!0).forEach(function(e){r8(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r8(t,e,r){return(e=r9(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r9(t){var e=function(t,e){if("object"!==r2(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r2(e)?e:String(e)}function nt(t,e){return(nt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ne(t,e){if(e&&("object"===r2(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nr(t)}function nr(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nn(t){return(nn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var no=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nt(t,e)}(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nn(o);return t=e?Reflect.construct(r,arguments,nn(this).constructor):r.apply(this,arguments),ne(this,t)});function o(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);var r,i=(r=n.call(this,t,e)).props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nr(r)),r.changeStyle=r.changeStyle.bind(nr(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),ne(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},ne(r);r.state={style:c?r8({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?r8({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rS(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?r8({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(r7(r7({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=r1(r,n,rW(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(r6(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(r6(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=rI(p,i,c),d=r7(r7(r7({},f.style),u),{},{transition:h});return[].concat(r6(t),[d,i,s]).filter(rN)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n;this.manager=(e=function(){return null},r=!1,n=function t(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(t){if(Array.isArray(t))return t}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return rk(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rk(t,void 0)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rA(t.bind(null,a),i);return}t(i),rA(t.bind(null,a));return}"object"===rE(n)&&e(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(t){r=!1,n(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,c=t.to,u=t.easing,l=t.onAnimationStart,s=t.onAnimationEnd,f=t.steps,p=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof u||"function"==typeof p||"spring"===u){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var d=a?r8({},a,c):c,y=rI(Object.keys(d),i,u);h.start([l,o,r7(r7({},d),{},{transition:y}),i,s])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,r3)),c=i.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,i.cloneElement)(t,r7(r7({},o),{},{style:r7(r7({},void 0===r?{}:r),u),className:n}))};return 1===c?l(i.Children.only(e)):a().createElement("div",null,i.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,r9(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function ni(t){return(ni="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function na(){return(na=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nu(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=ni(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ni(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ni(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nu(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}no.displayName="Animate",no.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},no.propTypes={from:e4().oneOfType([e4().object,e4().string]),to:e4().oneOfType([e4().object,e4().string]),attributeName:e4().string,duration:e4().number,begin:e4().number,easing:e4().oneOfType([e4().string,e4().func]),steps:e4().arrayOf(e4().shape({duration:e4().number.isRequired,style:e4().object.isRequired,easing:e4().oneOfType([e4().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),e4().func]),properties:e4().arrayOf("string"),onAnimationEnd:e4().func})),children:e4().oneOfType([e4().node,e4().func]),isActive:e4().bool,canBegin:e4().bool,onAnimationEnd:e4().func,shouldReAnimate:e4().bool,onAnimationStart:e4().func,onAnimationReStart:e4().func};var ns=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nf=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;return!!(Math.abs(a)>0&&Math.abs(c)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+c)&&n<=Math.max(i,i+c)},np={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nh=function(t){var e,r=nl(nl({},np),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nc(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nc(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,d=r.radius,y=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var O=(0,h.Z)("recharts-rectangle",y);return x?a().createElement(no,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:m,animationEasing:v,isActive:x},function(t){var e=t.width,o=t.height,i=t.x,u=t.y;return a().createElement(no,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,isActive:g,easing:v},a().createElement("path",na({},tf(r,!0),{className:O,d:ns(i,u,e,o,d),ref:n})))}):a().createElement("path",na({},tf(r,!0),{className:O,d:ns(l,s,f,p,d)}))};function nd(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function ny(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nv extends Map{constructor(t,e=nb){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nm(this,t))}has(t){return super.has(nm(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nm({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nb(t){return null!==t&&"object"==typeof t?t.valueOf():t}let ng=Symbol("implicit");function nx(){var t=new nv,e=[],r=[],n=ng;function o(o){let i=t.get(o);if(void 0===i){if(n!==ng)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nv,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nx(e,r).unknown(n)},nd.apply(o,arguments),o}function nO(){var t,e,r=nx().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nO(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nd.apply(f(),arguments)}function nw(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nO.apply(null,arguments).paddingInner(1))}function nj(t){return(nj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nS(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=nj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nj(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var nA={widthCache:{},cacheCount:0},nE={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nk="recharts_measurement_span",n_=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||tQ.isSsr)return{width:0,height:0};var n=(Object.keys(e=nP({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nA.widthCache[o])return nA.widthCache[o];try{var i=document.getElementById(nk);i||((i=document.createElement("span")).setAttribute("id",nk),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nP(nP({},nE),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nA.widthCache[o]=u,++nA.cacheCount>2e3&&(nA.cacheCount=0,nA.widthCache={}),u}catch(t){return{width:0,height:0}}};function nM(t){return(nM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nT(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nC(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nC(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nN(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nM(e)?e:e+""}(n.key),n)}}var nD=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nI=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nB=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nR=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nL={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nz=Object.keys(nL),nU=function(){var t,e;function r(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nB.test(e)||(this.num=NaN,this.unit=""),nz.includes(e)&&(this.num=t*nL[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nT(null!==(e=nR.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nN(r.prototype,t),e&&nN(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nF(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nT(null!==(r=nD.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=nU.parse(null!=o?o:""),u=nU.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nD,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nT(null!==(s=nI.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],d=f[3],y=nU.parse(null!=p?p:""),v=nU.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nI,m.toString())}return e}var n$=/\(([^()]*)\)/;function nq(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nT(n$.exec(e),2)[1];e=e.replace(n$,nF(r))}return e}(e),e=nF(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nW=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nX=["dx","dy","angle","className","breakAll"];function nH(){return(nH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nV(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nG(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nY(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nY(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nZ=/[ \f\n\r\t\v\u2028\u2029]+/,nK=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];R()(e)||(o=r?e.toString().split(""):e.toString().split(nZ));var i=o.map(function(t){return{word:t,width:n_(t,n).width}}),a=r?0:n_("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},nJ=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=A(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(nK({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=nG(h(m-1),2),g=b[0],x=b[1],O=nG(h(m),1)[0];if(g||O||(d=m+1),g&&O&&(y=m-1),!g&&O){i=x;break}v++}return i||p},nQ=function(t){return[{words:R()(t)?[]:t.toString().split(nZ)}]},n0=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!tQ.isSsr){var c=nK({breakAll:i,children:n,style:o});return c?nJ({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,e,r):nQ(n)}return nQ(n)},n1="#808080",n2=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,c=void 0===o?0:o,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,d=void 0!==p&&p,y=t.textAnchor,v=t.verticalAnchor,m=t.fill,b=void 0===m?n1:m,g=nV(t,nW),x=(0,i.useMemo)(function(){return n0({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:d,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,d,g.style,g.width]),O=g.dx,w=g.dy,j=g.angle,S=g.className,P=g.breakAll,k=nV(g,nX);if(!E(n)||!E(c))return null;var _=n+(A(O)?O:0),M=c+(A(w)?w:0);switch(void 0===v?"end":v){case"start":e=nq("calc(".concat(f,")"));break;case"middle":e=nq("calc(".concat((x.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=nq("calc(".concat(x.length-1," * -").concat(l,")"))}var T=[];if(d){var C=x[0].width,N=g.width;T.push("scale(".concat((A(N)?N/C:1)/C,")"))}return j&&T.push("rotate(".concat(j,", ").concat(_,", ").concat(M,")")),T.length&&(k.transform=T.join(" ")),a().createElement("text",nH({},tf(k,!0),{x:_,y:M,className:(0,h.Z)("recharts-text",S),textAnchor:void 0===y?"start":y,fill:b.includes("url")?n1:b}),x.map(function(t,r){var n=t.words.join(P?"":" ");return a().createElement("tspan",{x:_,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let n3=Math.sqrt(50),n6=Math.sqrt(10),n5=Math.sqrt(2);function n4(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=n3?10:u>=n6?5:u>=n5?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?n4(t,e,2*r):[n,o,i]}function n7(t,e,r){if(e=+e,t=+t,!((r=+r)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?n4(e,t,r):n4(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n){if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a}else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function n8(t,e,r){return n4(t=+t,e=+e,r=+r)[2]}function n9(t,e,r){e=+e,t=+t,r=+r;let n=e<t,o=n?n8(e,t,r):n8(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function ot(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function oe(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function or(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>r(t[e],n)?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=ot,r=(e,r)=>ot(t(e),r),n=(e,r)=>t(e)-r):(e=t===ot||t===oe?t:on,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function on(){return 0}function oo(t){return null===t?NaN:+t}let oi=or(ot),oa=oi.right;function oc(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function ou(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function ol(){}oi.left,or(oo).center;var os="\\s*([+-]?\\d+)\\s*",of="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",op="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",oh=/^#([0-9a-f]{3,8})$/,od=RegExp(`^rgb\\(${os},${os},${os}\\)$`),oy=RegExp(`^rgb\\(${op},${op},${op}\\)$`),ov=RegExp(`^rgba\\(${os},${os},${os},${of}\\)$`),om=RegExp(`^rgba\\(${op},${op},${op},${of}\\)$`),ob=RegExp(`^hsl\\(${of},${op},${op}\\)$`),og=RegExp(`^hsla\\(${of},${op},${op},${of}\\)$`),ox={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function oO(){return this.rgb().formatHex()}function ow(){return this.rgb().formatRgb()}function oj(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=oh.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oS(e):3===r?new oE(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?oP(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?oP(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=od.exec(t))?new oE(e[1],e[2],e[3],1):(e=oy.exec(t))?new oE(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=ov.exec(t))?oP(e[1],e[2],e[3],e[4]):(e=om.exec(t))?oP(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=ob.exec(t))?oN(e[1],e[2]/100,e[3]/100,1):(e=og.exec(t))?oN(e[1],e[2]/100,e[3]/100,e[4]):ox.hasOwnProperty(t)?oS(ox[t]):"transparent"===t?new oE(NaN,NaN,NaN,0):null}function oS(t){return new oE(t>>16&255,t>>8&255,255&t,1)}function oP(t,e,r,n){return n<=0&&(t=e=r=NaN),new oE(t,e,r,n)}function oA(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof ol||(o=oj(o)),o)?new oE((o=o.rgb()).r,o.g,o.b,o.opacity):new oE:new oE(t,e,r,null==n?1:n)}function oE(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function ok(){return`#${oC(this.r)}${oC(this.g)}${oC(this.b)}`}function o_(){let t=oM(this.opacity);return`${1===t?"rgb(":"rgba("}${oT(this.r)}, ${oT(this.g)}, ${oT(this.b)}${1===t?")":`, ${t})`}`}function oM(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oT(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oC(t){return((t=oT(t))<16?"0":"")+t.toString(16)}function oN(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oI(t,e,r,n)}function oD(t){if(t instanceof oI)return new oI(t.h,t.s,t.l,t.opacity);if(t instanceof ol||(t=oj(t)),!t)return new oI;if(t instanceof oI)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oI(a,c,u,t.opacity)}function oI(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oB(t){return(t=(t||0)%360)<0?t+360:t}function oR(t){return Math.max(0,Math.min(1,t||0))}function oL(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oz(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}oc(ol,oj,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:oO,formatHex:oO,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oD(this).formatHsl()},formatRgb:ow,toString:ow}),oc(oE,oA,ou(ol,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oE(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oE(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oE(oT(this.r),oT(this.g),oT(this.b),oM(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ok,formatHex:ok,formatHex8:function(){return`#${oC(this.r)}${oC(this.g)}${oC(this.b)}${oC((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:o_,toString:o_})),oc(oI,function(t,e,r,n){return 1==arguments.length?oD(t):new oI(t,e,r,null==n?1:n)},ou(ol,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oI(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oI(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oE(oL(t>=240?t-240:t+120,o,n),oL(t,o,n),oL(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oI(oB(this.h),oR(this.s),oR(this.l),oM(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oM(this.opacity);return`${1===t?"hsl(":"hsla("}${oB(this.h)}, ${100*oR(this.s)}%, ${100*oR(this.l)}%${1===t?")":`, ${t})`}`}}));let oU=t=>()=>t;function oF(t,e){var r=e-t;return r?function(e){return t+e*r}:oU(isNaN(t)?e:t)}let o$=function t(e){var r,n=1==(r=+(r=e))?oF:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oU(isNaN(t)?e:t)};function o(t,e){var r=n((t=oA(t)).r,(e=oA(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oF(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function oq(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oA(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}function oW(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}oq(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oz((r-n/e)*e,a,o,i,c)}}),oq(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oz((r-n/e)*e,o,i,a,c)}});var oX=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,oH=RegExp(oX.source,"g");function oV(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oU(e):("number"===o?oW:"string"===o?(n=oj(e))?(e=n,o$):function(t,e){var r,n,o,i,a,c=oX.lastIndex=oH.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=oX.exec(t))&&(i=oH.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oW(o,i)})),c=oH.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oj?o$:e instanceof Date?function(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=oV(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=oV(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oW:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function oG(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function oY(t){return+t}var oZ=[0,1];function oK(t){return t}function oJ(t,e){var r;return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function oQ(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=oJ(o,n),i=r(a,i)):(n=oJ(n,o),i=r(i,a)),function(t){return i(n(t))}}function o0(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=oJ(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=oa(t,e,1,n)-1;return i[r](o[r](e))}}function o1(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function o2(){var t,e,r,n,o,i,a=oZ,c=oZ,u=oV,l=oK;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==oK&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?o0:oQ,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oW)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,oY),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oG,s()},f.clamp=function(t){return arguments.length?(l=!!t||oK,s()):l!==oK},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function o3(){return o2()(oK,oK)}var o6=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function o5(t){var e;if(!(e=o6.exec(t)))throw Error("invalid format: "+t);return new o4({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function o4(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function o7(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function o8(t){return(t=o7(Math.abs(t)))?t[1]:NaN}function o9(t,e){var r=o7(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}o5.prototype=o4.prototype,o4.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let it={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>o9(100*t,e),r:o9,s:function(t,e){var r=o7(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cT=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+o7(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function ie(t){return t}var ir=Array.prototype.map,io=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ii(t,e,r,n){var o,i,a=n9(t,e,r);switch((n=o5(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(i=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(o8(c)/3)))-o8(Math.abs(a))))||(n.precision=i),cD(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=Math.max(0,o8(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=a)))-o8(o))+1)||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=Math.max(0,-o8(Math.abs(a))))||(n.precision=i-("%"===n.type)*2)}return cN(n)}function ia(t){var e=t.domain;return t.ticks=function(t){var r=e();return n7(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ii(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=n8(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function ic(){var t=o3();return t.copy=function(){return o1(t,ic())},nd.apply(t,arguments),ia(t)}function iu(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function il(t){return Math.log(t)}function is(t){return Math.exp(t)}function ip(t){return-Math.log(-t)}function ih(t){return-Math.exp(-t)}function id(t){return isFinite(t)?+("1e"+t):t<0?0:t}function iy(t){return(e,r)=>-t(-e,r)}function iv(t){let e,r;let n=t(il,is),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?id:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=iy(e),r=iy(r),t(ip,ih)):t(il,is),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a;let c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=n7(u,l,h))}else d=n7(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=o5(o)).precision||(o.trim=!0),o=cN(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(iu(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function im(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function ib(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ig(t){var e=1,r=t(im(1),ib(e));return r.constant=function(r){return arguments.length?t(im(e=+r),ib(e)):e},ia(r)}function ix(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function iO(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iw(t){return t<0?-t*t:t*t}function ij(t){var e=t(oK,oK),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(oK,oK):.5===r?t(iO,iw):t(ix(r),ix(1/r)):r},ia(e)}function iS(){var t=ij(o2());return t.copy=function(){return o1(t,iS()).exponent(t.exponent())},nd.apply(t,arguments),t}function iP(){return iS.apply(null,arguments).exponent(.5)}function iA(t){return Math.sign(t)*t*t}function iE(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function ik(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function i_(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function iM(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}cN=(cC=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?ie:(e=ir.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?ie:(n=ir.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=o5(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):it[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=it[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?io[8+cT/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=o(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=o5(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(o8(e)/3))),o=Math.pow(10,-n),i=io[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cD=cC.formatPrefix;let iT=new Date,iC=new Date;function iN(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iN(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(iT.setTime(+e),iC.setTime(+n),t(iT),t(iC),Math.floor(r(iT,iC))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iD=iN(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iD.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iN(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iD:null,iD.range;let iI=iN(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iI.range;let iB=iN(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iB.range;let iR=iN(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iR.range;let iL=iN(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iL.range;let iz=iN(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iz.range;let iU=iN(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iU.range;let iF=iN(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iF.range;let i$=iN(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function iq(t){return iN(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}i$.range;let iW=iq(0),iX=iq(1),iH=iq(2),iV=iq(3),iG=iq(4),iY=iq(5),iZ=iq(6);function iK(t){return iN(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iW.range,iX.range,iH.range,iV.range,iG.range,iY.range,iZ.range;let iJ=iK(0),iQ=iK(1),i0=iK(2),i1=iK(3),i2=iK(4),i3=iK(5),i6=iK(6);iJ.range,iQ.range,i0.range,i1.range,i2.range,i3.range,i6.range;let i5=iN(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());i5.range;let i4=iN(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());i4.range;let i7=iN(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());i7.every=t=>isFinite(t=Math.floor(t))&&t>0?iN(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,i7.range;let i8=iN(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function i9(t,e,r,n,o,i){let a=[[iI,1,1e3],[iI,5,5e3],[iI,15,15e3],[iI,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=or(([,,t])=>t).right(a,o);if(i===a.length)return t.every(n9(e/31536e6,r/31536e6,n));if(0===i)return iD.every(Math.max(n9(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}i8.every=t=>isFinite(t=Math.floor(t))&&t>0?iN(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,i8.range;let[at,ae]=i9(i8,i4,iJ,i$,iz,iR),[ar,an]=i9(i7,i5,iW,iU,iL,iB);function ao(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ai(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function aa(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var ac={"-":"",_:" ",0:"0"},au=/^\s*\d+/,al=/^%/,as=/[\\^$*+?|[\]().{}]/g;function af(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function ap(t){return t.replace(as,"\\$&")}function ah(t){return RegExp("^(?:"+t.map(ap).join("|")+")","i")}function ad(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function ay(t,e,r){var n=au.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function av(t,e,r){var n=au.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function am(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function ab(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function ag(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function ax(t,e,r){var n=au.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aO(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aw(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aj(t,e,r){var n=au.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aS(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aP(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aA(t,e,r){var n=au.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function ak(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=au.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=au.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=au.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aC(t,e,r){var n=al.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aN(t,e,r){var n=au.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=au.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aI(t,e){return af(t.getDate(),e,2)}function aB(t,e){return af(t.getHours(),e,2)}function aR(t,e){return af(t.getHours()%12||12,e,2)}function aL(t,e){return af(1+iU.count(i7(t),t),e,3)}function az(t,e){return af(t.getMilliseconds(),e,3)}function aU(t,e){return az(t,e)+"000"}function aF(t,e){return af(t.getMonth()+1,e,2)}function a$(t,e){return af(t.getMinutes(),e,2)}function aq(t,e){return af(t.getSeconds(),e,2)}function aW(t){var e=t.getDay();return 0===e?7:e}function aX(t,e){return af(iW.count(i7(t)-1,t),e,2)}function aH(t){var e=t.getDay();return e>=4||0===e?iG(t):iG.ceil(t)}function aV(t,e){return t=aH(t),af(iG.count(i7(t),t)+(4===i7(t).getDay()),e,2)}function aG(t){return t.getDay()}function aY(t,e){return af(iX.count(i7(t)-1,t),e,2)}function aZ(t,e){return af(t.getFullYear()%100,e,2)}function aK(t,e){return af((t=aH(t)).getFullYear()%100,e,2)}function aJ(t,e){return af(t.getFullYear()%1e4,e,4)}function aQ(t,e){var r=t.getDay();return af((t=r>=4||0===r?iG(t):iG.ceil(t)).getFullYear()%1e4,e,4)}function a0(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+af(e/60|0,"0",2)+af(e%60,"0",2)}function a1(t,e){return af(t.getUTCDate(),e,2)}function a2(t,e){return af(t.getUTCHours(),e,2)}function a3(t,e){return af(t.getUTCHours()%12||12,e,2)}function a6(t,e){return af(1+iF.count(i8(t),t),e,3)}function a5(t,e){return af(t.getUTCMilliseconds(),e,3)}function a4(t,e){return a5(t,e)+"000"}function a7(t,e){return af(t.getUTCMonth()+1,e,2)}function a8(t,e){return af(t.getUTCMinutes(),e,2)}function a9(t,e){return af(t.getUTCSeconds(),e,2)}function ct(t){var e=t.getUTCDay();return 0===e?7:e}function ce(t,e){return af(iJ.count(i8(t)-1,t),e,2)}function cr(t){var e=t.getUTCDay();return e>=4||0===e?i2(t):i2.ceil(t)}function cn(t,e){return t=cr(t),af(i2.count(i8(t),t)+(4===i8(t).getUTCDay()),e,2)}function co(t){return t.getUTCDay()}function ci(t,e){return af(iQ.count(i8(t)-1,t),e,2)}function ca(t,e){return af(t.getUTCFullYear()%100,e,2)}function cc(t,e){return af((t=cr(t)).getUTCFullYear()%100,e,2)}function cu(t,e){return af(t.getUTCFullYear()%1e4,e,4)}function cl(t,e){var r=t.getUTCDay();return af((t=r>=4||0===r?i2(t):i2.ceil(t)).getUTCFullYear()%1e4,e,4)}function cs(){return"+0000"}function cf(){return"%"}function cp(t){return+t}function ch(t){return Math.floor(+t/1e3)}function cd(t){return new Date(t)}function cy(t){return t instanceof Date?+t:+new Date(+t)}function cv(t,e,r,n,o,i,a,c,u,l){var s=o3(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cy)):p().map(cd)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(iu(r,t)):s},s.copy=function(){return o1(s,cv(t,e,r,n,o,i,a,c,u,l))},s}function cm(){return nd.apply(cv(ar,an,i7,i5,iW,iU,iL,iB,iI,cB).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cb(){return nd.apply(cv(at,ae,i8,i4,iJ,iF,iz,iR,iI,cR).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cg(){var t,e,r,n,o,i=0,a=1,c=oK,u=!1;function l(e){return null==e||isNaN(e=+e)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(oV),l.rangeRound=s(oG),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function cx(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cO(){var t=ij(cg());return t.copy=function(){return cx(t,cO()).exponent(t.exponent())},ny.apply(t,arguments)}function cw(){return cO.apply(null,arguments).exponent(.5)}function cj(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=oK,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=oV);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c=+c),e=i(u=+u),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(oV),h.rangeRound=d(oG),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cS(){var t=ij(cj());return t.copy=function(){return cx(t,cS()).exponent(t.exponent())},ny.apply(t,arguments)}function cP(){return cS.apply(null,arguments).exponent(.5)}function cA(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cE(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function ck(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function c_(t,e){return t[e]}function cM(t){let e=[];return e.key=t,e}cB=(cI=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=ah(o),s=ad(o),f=ah(i),p=ad(i),h=ah(a),d=ad(a),y=ah(c),v=ad(c),m=ah(u),b=ad(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aI,e:aI,f:aU,g:aK,G:aQ,H:aB,I:aR,j:aL,L:az,m:aF,M:a$,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cp,s:ch,S:aq,u:aW,U:aX,V:aV,w:aG,W:aY,x:null,X:null,y:aZ,Y:aJ,Z:a0,"%":cf},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a1,e:a1,f:a4,g:cc,G:cl,H:a2,I:a3,j:a6,L:a5,m:a7,M:a8,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cp,s:ch,S:a9,u:ct,U:ce,V:cn,w:co,W:ci,x:null,X:null,y:ca,Y:cu,Z:cs,"%":cf},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aP,e:aP,f:aT,g:aO,G:ax,H:aE,I:aE,j:aA,L:aM,m:aS,M:ak,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aj,Q:aN,s:aD,S:a_,u:av,U:am,V:ab,w:ay,W:ag,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aO,Y:ax,Z:aw,"%":aC};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=ac[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=aa(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ai(aa(i.y,0,1))).getUTCDay())>4||0===o?iQ.ceil(n):iQ(n),n=iF.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=ao(aa(i.y,0,1))).getDay())>4||0===o?iX.ceil(n):iX(n),n=iU.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?ai(aa(i.y,0,1)).getUTCDay():ao(aa(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ai(i)):ao(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in ac?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cI.parse,cR=cI.utcFormat,cI.utcParse,Array.prototype.slice;var cT,cC,cN,cD,cI,cB,cR,cL,cz,cU=r(31955),cF=r.n(cU),c$=r(52692),cq=r.n(c$),cW=r(78352),cX=r.n(cW),cH=r(81711),cV=r.n(cH),cG=!0,cY="[DecimalError] ",cZ=cY+"Invalid argument: ",cK=cY+"Exponent out of range: ",cJ=Math.floor,cQ=Math.pow,c0=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c1=cJ(1286742750677284.5),c2={};function c3(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),cG?un(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,cG?un(e,f):e}function c6(t,e,r){if(t!==~~t||t<e||t>r)throw Error(cZ+t)}function c5(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=ut(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=ut(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}c2.absoluteValue=c2.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},c2.comparedTo=c2.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},c2.decimalPlaces=c2.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},c2.dividedBy=c2.div=function(t){return c4(this,new this.constructor(t))},c2.dividedToIntegerBy=c2.idiv=function(t){var e=this.constructor;return un(c4(this,new e(t),0,1),e.precision)},c2.equals=c2.eq=function(t){return!this.cmp(t)},c2.exponent=function(){return c8(this)},c2.greaterThan=c2.gt=function(t){return this.cmp(t)>0},c2.greaterThanOrEqualTo=c2.gte=function(t){return this.cmp(t)>=0},c2.isInteger=c2.isint=function(){return this.e>this.d.length-2},c2.isNegative=c2.isneg=function(){return this.s<0},c2.isPositive=c2.ispos=function(){return this.s>0},c2.isZero=function(){return 0===this.s},c2.lessThan=c2.lt=function(t){return 0>this.cmp(t)},c2.lessThanOrEqualTo=c2.lte=function(t){return 1>this.cmp(t)},c2.logarithm=c2.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cz))throw Error(cY+"NaN");if(this.s<1)throw Error(cY+(this.s?"NaN":"-Infinity"));return this.eq(cz)?new r(0):(cG=!1,e=c4(ue(this,o),ue(t,o),o),cG=!0,un(e,n))},c2.minus=c2.sub=function(t){return t=new this.constructor(t),this.s==t.s?uo(this,t):c3(this,(t.s=-t.s,t))},c2.modulo=c2.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(cY+"NaN");return this.s?(cG=!1,e=c4(this,t,0,1).times(t),cG=!0,this.minus(e)):un(new r(this),n)},c2.naturalExponential=c2.exp=function(){return c7(this)},c2.naturalLogarithm=c2.ln=function(){return ue(this)},c2.negated=c2.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},c2.plus=c2.add=function(t){return t=new this.constructor(t),this.s==t.s?c3(this,t):uo(this,(t.s=-t.s,t))},c2.precision=c2.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(cZ+t);if(e=c8(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},c2.squareRoot=c2.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(cY+"NaN")}for(t=c8(this),cG=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=c5(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=cJ((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(c4(this,i,a+2)).times(.5),c5(i.d).slice(0,a)===(e=c5(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(un(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return cG=!0,un(n,r)},c2.times=c2.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,cG?un(t,s.precision):t},c2.toDecimalPlaces=c2.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(c6(t,0,1e9),void 0===e?e=n.rounding:c6(e,0,8),un(r,t+c8(r)+1,e))},c2.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=ui(n,!0):(c6(t,0,1e9),void 0===e?e=o.rounding:c6(e,0,8),r=ui(n=un(new o(n),t+1,e),!0,t+1)),r},c2.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?ui(this):(c6(t,0,1e9),void 0===e?e=o.rounding:c6(e,0,8),r=ui((n=un(new o(this),t+c8(this)+1,e)).abs(),!1,t+c8(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c2.toInteger=c2.toint=function(){var t=this.constructor;return un(new t(this),c8(this)+1,t.rounding)},c2.toNumber=function(){return+this},c2.toPower=c2.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cz);if(!(c=new u(c)).s){if(t.s<1)throw Error(cY+"Infinity");return c}if(c.eq(cz))return c;if(n=u.precision,t.eq(cz))return un(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=9007199254740991){for(o=new u(cz),e=Math.ceil(n/7+4),cG=!1;r%2&&ua((o=o.times(c)).d,e),0!==(r=cJ(r/2));)ua((c=c.times(c)).d,e);return cG=!0,t.s<0?new u(cz).div(o):un(o,n)}}else if(i<0)throw Error(cY+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,cG=!1,o=t.times(ue(c,n+12)),cG=!0,(o=c7(o)).s=i,o},c2.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=c8(o),n=ui(o,r<=i.toExpNeg||r>=i.toExpPos)):(c6(t,1,1e9),void 0===e?e=i.rounding:c6(e,0,8),r=c8(o=un(new i(o),t,e)),n=ui(o,t<=r||r<=i.toExpNeg,t)),n},c2.toSignificantDigits=c2.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(c6(t,1,1e9),void 0===e?e=r.rounding:c6(e,0,8)),un(new r(this),t,e)},c2.toString=c2.valueOf=c2.val=c2.toJSON=c2[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=c8(this),e=this.constructor;return ui(this,t<=e.toExpNeg||t>=e.toExpPos)};var c4=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,P=n.constructor,A=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(cY+"Division by zero");for(l=0,u=n.e-o.e,j=k.length,O=E.length,d=(h=new P(A)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==i?i=P.precision:a?i+(c8(n)-c8(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),w=k[0],k[1]>=1e7/2&&++w;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?S:k,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,un(h,a?i+c8(h)+1:i)}}();function c7(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(c8(t)>16)throw Error(cK+c8(t));if(!t.s)return new l(cz);for(null==e?(cG=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(cQ(2,u))/Math.LN10*2+5|0,r=n=o=new l(cz),l.precision=a;;){if(n=un(n.times(t),a),r=r.times(++c),c5((i=o.plus(c4(n,r,a))).d).slice(0,a)===c5(o.d).slice(0,a)){for(;u--;)o=un(o.times(o),a);return l.precision=s,null==e?(cG=!0,un(o,s)):o}o=i}}function c8(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function c9(t,e,r){if(e>t.LN10.sd())throw cG=!0,r&&(t.precision=r),Error(cY+"LN10 precision limit exceeded");return un(new t(t.LN10),e)}function ut(t){for(var e="";t--;)e+="0";return e}function ue(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(cY+(p.s?"NaN":"-Infinity"));if(p.eq(cz))return new d(0);if(null==e?(cG=!1,l=y):l=e,p.eq(10))return null==e&&(cG=!0),c9(d,l);if(l+=10,d.precision=l,n=(r=c5(h)).charAt(0),!(15e14>Math.abs(i=c8(p))))return u=c9(d,l+2,y).times(i+""),p=ue(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(cG=!0,un(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=c5((p=p.times(t)).d)).charAt(0),f++;for(i=c8(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=c4(p.minus(cz),p.plus(cz),l),s=un(p.times(p),l),o=3;;){if(a=un(a.times(s),l),c5((u=c.plus(c4(a,new d(o),l))).d).slice(0,l)===c5(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(c9(d,l+2,y).times(i+""))),c=c4(c,new d(f),l),d.precision=y,null==e?(cG=!0,un(c,y)):c;c=u,o+=2}}function ur(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=cJ(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),cG&&(t.e>c1||t.e<-c1))throw Error(cK+r)}else t.s=0,t.e=0,t.d=[0];return t}function un(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=cQ(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/cQ(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=c8(t),f.length=1,e=e-i-1,f[0]=cQ(10,(7-e%7)%7),t.e=cJ(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=cQ(10,7-n),f[s]=o>0?(l/cQ(10,a-o)%cQ(10,o)|0)*i:0),u)for(;;){if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(cG&&(t.e>c1||t.e<-c1))throw Error(cK+c8(t));return t}function uo(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),cG?un(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,cG?un(e,h):e):new p(0)}function ui(t,e,r){var n,o=c8(t),i=c5(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+ut(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+ut(-o-1)+i,r&&(n=r-a)>0&&(i+=ut(n))):o>=a?(i+=ut(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+ut(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=ut(n))),t.s<0?"-"+i:i}function ua(t,e){if(t.length>e)return t.length=e,!0}function uc(t){if(!t||"object"!=typeof t)throw Error(cY+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(cJ(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(cZ+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(cZ+r+": "+n)}return this}var cL=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(cZ+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return ur(this,t.toString())}if("string"!=typeof t)throw Error(cZ+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c0.test(t))ur(this,t);else throw Error(cZ+t)}if(i.prototype=c2,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=uc,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cz=new cL(1);let uu=cL;function ul(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var us=function(t){return t},uf={},up=function(t){return t===uf},uh=function(t){return function e(){return 0==arguments.length||1==arguments.length&&up(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},ud=function(t){return function t(e,r){return 1===e?r:uh(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==uf}).length;return a>=e?r.apply(void 0,o):t(e-a,uh(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return up(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return ul(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return ul(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ul(t,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uy=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uv=ud(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),um=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return us;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},ub=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},ug=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};ud(function(t,e,r){var n=+t;return n+r*(+e-n)}),ud(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),ud(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let ux={rangeStep:function(t,e,r){for(var n=new uu(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new uu(t).abs().log(10).toNumber())+1}};function uO(t){return function(t){if(Array.isArray(t))return uS(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uj(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uw(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uj(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uj(t,e){if(t){if("string"==typeof t)return uS(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uS(t,e)}}function uS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uP(t){var e=uw(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uA(t,e,r){if(t.lte(0))return new uu(0);var n=ux.getDigitCount(t.toNumber()),o=new uu(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new uu(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new uu(Math.ceil(c))}function uE(t,e,r){var n=1,o=new uu(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new uu(10).pow(ux.getDigitCount(t)-1),o=new uu(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new uu(Math.floor(t)))}else 0===t?o=new uu(Math.floor((e-1)/2)):r||(o=new uu(Math.floor(t)));var a=Math.floor((e-1)/2);return um(uv(function(t){return o.add(new uu(t-a).mul(n)).toNumber()}),uy)(0,e)}var uk=ug(function(t){var e=uw(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uw(uP([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uO(uy(0,o-1).map(function(){return 1/0}))):[].concat(uO(uy(0,o-1).map(function(){return-1/0})),[l]);return r>n?ub(s):s}if(u===l)return uE(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uu(0),tickMin:new uu(0),tickMax:new uu(0)};var c=uA(new uu(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new uu(0):(i=new uu(e).add(r).div(2)).sub(new uu(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uu(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new uu(u).mul(c)),tickMax:i.add(new uu(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=ux.rangeStep(h,d.add(new uu(.1).mul(p)),p);return r>n?ub(y):y});ug(function(t){var e=uw(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uw(uP([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uE(u,o,i);var s=uA(new uu(l).sub(u).div(a-1),i,0),f=um(uv(function(t){return new uu(u).add(new uu(t).mul(s)).toNumber()}),uy)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?ub(f):f});var u_=ug(function(t,e){var r=uw(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uw(uP([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=uA(new uu(u).sub(c).div(Math.max(e,2)-1),i,0),s=[].concat(uO(ux.rangeStep(new uu(c),new uu(u).sub(new uu(.99).mul(l)),l)),[u]);return n>o?ub(s):s}),uM=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uT(t){return(uT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uC(){return(uC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uD(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uD=function(){return!!t})()}function uI(t){return(uI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uB(t,e){return(uB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uR(t,e,r){return(e=uL(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uL(t){var e=function(t,e){if("object"!=uT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uT(e)?e:e+""}var uz=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=uI(t),function(t,e){if(e&&("object"===uT(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,uD()?Reflect.construct(t,e||[],uI(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&uB(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tf(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uM),!1);"x"===this.props.direction&&"number"!==u.type&&tP(!1);var f=i.map(function(t){var i,f,p=c(t,o),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uN(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uN(t,2)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=u.scale,x=d+e,O=x+n,w=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var P=l.scale,A=h+e,E=A-n,k=A+n,_=P(y-i),M=P(y+f);m.push({x1:E,y1:M,x2:k,y2:M}),m.push({x1:A,y1:_,x2:A,y2:M}),m.push({x1:E,y1:_,x2:k,y2:_})}return a().createElement(tT,uC({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uC({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tT,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uL(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function uU(t){return(uU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uF(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=uU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uU(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uR(uz,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uR(uz,"displayName","ErrorBar");var uq=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=tc(r,e2);if(!a)return null;var c=e2.defaultProps,u=void 0!==c?u$(u$({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u$(u$({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:uQ(e),value:i||o,payload:n}}),u$(u$(u$({},u),e2.getWithHeight(a,o)),{},{payload:e,item:a})};function uW(t){return(uW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uX(t){return function(t){if(Array.isArray(t))return uH(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return uH(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uH(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uH(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uV(Object(r),!0).forEach(function(e){uY(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function uY(t,e,r){var n;return(n=function(t,e){if("object"!=uW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uW(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uZ(t,e,r){return R()(t)||R()(e)?r:E(e)?O()(t,e,r):z()(e)?e(t):r}function uK(t,e,r,n){var o=cX()(t,function(t){return uZ(t,e)});if("number"===r){var i=o.filter(function(t){return A(t)||parseFloat(t)});return i.length?[cq()(i),cF()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!R()(t)}):o).map(function(t){return E(t)||t instanceof Date?t:""})}var uJ=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(S(s-l)!==S(f-s)){var h=[];if(S(f-s)===S(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},uQ=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?uG(uG({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u0=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tr(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?uG(uG({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=R()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:R()(O)?void 0:M(O,r,0)})}}return i},u1=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=M(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(uX(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=M(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(uX(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},u2=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uq({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&A(t[f]))return uG(uG({},t),{},uY({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&A(t[p]))return uG(uG({},t),{},uY({},p,t[p]+(s||0)))}return t},u3=function(t,e,r,n,o){var i=ta(e.props.children,uz).filter(function(t){var e;return e=t.props.direction,!!R()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=uZ(e,r);if(R()(n))return t;var o=Array.isArray(n)?[cq()(n),cF()(n)]:[n,n],i=a.reduce(function(t,r){var n=uZ(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},u6=function(t,e,r,n,o){var i=e.map(function(e){return u3(t,e,r,o,n)}).filter(function(t){return!R()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},u5=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&u3(t,e,i,n)||uK(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},u4=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},u7=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},u8=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*S(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!g()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},u9=new WeakMap,lt=function(t,e){if("function"!=typeof e)return t;u9.has(t)||u9.set(t,new WeakMap);var r=u9.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},le=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nO(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:ic(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nw(),realScaleType:"point"}:"category"===i?{scale:nO(),realScaleType:"band"}:{scale:ic(),realScaleType:"linear"};if(m()(o)){var u="scale".concat(eo()(o));return{scale:(n[u]||nw)(),realScaleType:n[u]?u:"point"}}return z()(o)?{scale:o}:{scale:nw(),realScaleType:"point"}},lr=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},ln=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lo=function(t,e){if(!e||2!==e.length||!A(e[0])||!A(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!A(t[0])||t[0]<r)&&(o[0]=r),(!A(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},li={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=g()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cA(t,e)}},none:cA,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cA(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cA(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=g()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},la=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=li[r];return(function(){var t=ex([]),e=ck,r=cA,n=c_;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cM),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cE(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:ex(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:ex(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?ck:"function"==typeof t?t:ex(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cA:t,o):r},o})().keys(n).value(function(t,e){return+uZ(t,e,0)}).order(ck).offset(o)(t)},lc=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?uG(uG({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(E(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[_("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return uG(uG({},t),{},uY({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return uG(uG({},e),{},uY({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:la(t,a.items,o)}))},{})),uG(uG({},e),{},uY({},i,c))},{})},lu=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=uk(u,o,a);return t.domain([cq()(l),cF()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:u_(t.domain(),o,a)}:null};function ll(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!R()(o[e.dataKey])){var c=D(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=uZ(o,R()(a)?e.dataKey:a);return R()(u)?null:e.scale(u)}var ls=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=uZ(i,e.dataKey,e.domain[a]);return R()(c)?null:e.scale(c)-o/2+n},lf=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lp=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?uG(uG({},t.type.defaultProps),t.props):t.props).stackId;if(E(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},lh=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[cq()(e.concat([t[0]]).filter(A)),cF()(e.concat([t[1]]).filter(A))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},ld=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ly=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lv=function(t,e,r){if(z()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(A(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(ld.test(t[0])){var o=+ld.exec(t[0])[1];n[0]=e[0]-o}else z()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(A(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(ly.test(t[1])){var i=+ly.exec(t[1])[1];n[1]=e[1]+i}else z()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lm=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tS()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lb=function(t,e,r){return!t||!t.length||cV()(t,O()(r,"type.defaultProps.domain"))?e:t},lg=function(t,e){var r=t.type.defaultProps?uG(uG({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return uG(uG({},tf(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:uQ(t),value:uZ(e,n),type:c,payload:e,chartType:u,hide:l})};function lx(t){return(lx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lO(Object(r),!0).forEach(function(e){lj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lj(t,e,r){var n;return(n=function(t,e){if("object"!=lx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lx(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lS=["Webkit","Moz","O","ms"],lP=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lS.reduce(function(t,n){return lw(lw({},t),{},lj({},n+r,e))},{});return n[t]=e,n};function lA(t){return(lA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lE(){return(lE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lk(Object(r),!0).forEach(function(e){lD(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lM(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lI(n.key),n)}}function lT(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lT=function(){return!!t})()}function lC(t){return(lC=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lN(t,e){return(lN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lD(t,e,r){return(e=lI(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lI(t){var e=function(t,e){if("object"!=lA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lA(e)?e:e+""}var lB=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nw().domain(tw()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lR=function(t){return t.changedTouches&&!!t.changedTouches.length},lL=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=lC(r),lD(e=function(t,e){if(e&&("object"===lA(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,lT()?Reflect.construct(r,o||[],lC(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lD(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lD(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),lD(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lD(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lD(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lD(e,"handleSlideDragStart",function(t){var r=lR(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lN(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=n.getIndexInRange(o,Math.min(e,r)),l=n.getIndexInRange(o,Math.max(e,r));return{startIndex:u-u%a,endIndex:l===c?c:l-l%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=uZ(r[t],o,t);return z()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lR(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(lD(lD({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(lD({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,c=t.data,u=t.children,l=t.padding,s=i.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:o,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=l_(l_({},tf(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=h[y])||void 0===o?void 0:o.name);return a().createElement(tT,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth;return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(t,e)+c,y:n,width:Math.max(Math.abs(e-t)-c,0),height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tT,{className:"recharts-brush-texts"},a().createElement(n2,lE({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),a().createElement(n2,lE({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,d=s.isTextActive,y=s.isSlideMoving,v=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!A(o)||!A(i)||!A(c)||!A(u)||c<=0||u<=0)return null;var b=(0,h.Z)("recharts-brush",r),g=1===a().Children.count(n),x=lP("userSelect","none");return a().createElement(tT,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||y||v||m||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return a().isValidElement(t)?a().cloneElement(t,e):z()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return l_({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lB({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lM(n.prototype,e),r&&lM(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function lz(t){return(lz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lF(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lU(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=lz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lz(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}lD(lL,"displayName","Brush"),lD(lL,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var l$=Math.PI/180,lq=function(t,e,r,n){return{x:t+Math.cos(-l$*n)*r,y:e+Math.sin(-l$*n)*r}},lW=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},lX=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=lW({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},lH=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},lV=function(t,e){var r,n=lX({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=lH(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lF(lF({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function lG(t){return(lG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lY=["offset"];function lZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function lK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lK(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=lG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lG(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lQ(){return(lQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l0=function(t){var e=t.value,r=t.formatter,n=R()(t.children)?e:t.children;return z()(r)?r(n):n},l1=function(t,e,r){var n,o,i=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,d=c.outerRadius,y=c.startAngle,v=c.endAngle,m=c.clockWise,b=(p+d)/2,g=S(v-y)*Math.min(Math.abs(v-y),360),x=g>=0?1:-1;"insideStart"===i?(n=y+x*u,o=m):"insideEnd"===i?(n=v-x*u,o=!m):"end"===i&&(n=v+x*u,o=m),o=g<=0?o:!o;var O=lq(s,f,b,n),w=lq(s,f,b,n+(o?1:-1)*359),j="M".concat(O.x,",").concat(O.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(o?0:1,",\n    ").concat(w.x,",").concat(w.y),P=R()(t.id)?_("recharts-radial-line-"):t.id;return a().createElement("text",lQ({},r,{dominantBaseline:"central",className:(0,h.Z)("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:P,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(P)},e))},l2=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lq(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lq(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},l3=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return lJ(lJ({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return lJ(lJ({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return lJ(lJ({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return lJ(lJ({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?lJ({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?lJ({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?lJ({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?lJ({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?lJ({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?lJ({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?lJ({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?lJ({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):F()(o)&&(A(o.x)||P(o.x))&&(A(o.y)||P(o.y))?lJ({x:i+M(o.x,c),y:a+M(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):lJ({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function l6(t){var e,r=t.offset,n=lJ({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,lY)),o=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!o||R()(u)&&R()(l)&&!(0,i.isValidElement)(s)&&!z()(s))return null;if((0,i.isValidElement)(s))return(0,i.cloneElement)(s,n);if(z()(s)){if(e=(0,i.createElement)(s,n),(0,i.isValidElement)(e))return e}else e=l0(n);var d="cx"in o&&A(o.cx),y=tf(n,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return l1(n,e,y);var v=d?l2(n):l3(n);return a().createElement(n2,lQ({className:(0,h.Z)("recharts-label",void 0===f?"":f)},y,v,{breakAll:p}),e)}l6.displayName="Label";var l5=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(A(d)&&A(y)){if(A(s)&&A(f))return{x:s,y:f,width:d,height:y};if(A(p)&&A(h))return{x:p,y:h,width:d,height:y}}return A(s)&&A(f)?{x:s,y:f,width:0,height:0}:A(e)&&A(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};l6.parseViewBox=l5,l6.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var c=t.children,u=l5(t),l=ta(c,l6).map(function(t,r){return(0,i.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});return o?[(r=t.label,n=e||u,r?!0===r?a().createElement(l6,{key:"label-implicit",viewBox:n}):E(r)?a().createElement(l6,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===l6?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(l6,{key:"label-implicit",content:r,viewBox:n}):z()(r)?a().createElement(l6,{key:"label-implicit",content:r,viewBox:n}):F()(r)?a().createElement(l6,lQ({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return lZ(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return lZ(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lZ(t,void 0)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):l};var l4=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},l7=r(13880),l8=r.n(l7),l9=r(10853),st=r.n(l9),se=function(t){return null};se.displayName="Cell";var sr=r(90601),sn=r.n(sr);function so(t){return(so="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var si=["valueAccessor"],sa=["data","dataKey","clockWise","id","textBreakAll"];function sc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function su(){return(su=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ss(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sl(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=so(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=so(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==so(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sf(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sp=function(t){return Array.isArray(t.value)?sn()(t.value):t.value};function sh(t){var e=t.valueAccessor,r=void 0===e?sp:e,n=sf(t,si),o=n.data,i=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=sf(n,sa);return o&&o.length?a().createElement(tT,{className:"recharts-label-list"},o.map(function(t,e){var n=R()(i)?r(t,e):uZ(t&&t.payload,i),o=R()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(l6,su({},tf(t,!0),s,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:l6.parseViewBox(R()(c)?t:ss(ss({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sh.displayName="LabelList",sh.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=ta(t.children,sh).map(function(t,r){return(0,i.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label)?!0===r?a().createElement(sh,{key:"labelList-implicit",data:e}):a().isValidElement(r)||z()(r)?a().createElement(sh,{key:"labelList-implicit",data:e,content:r}):F()(r)?a().createElement(sh,su({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return sc(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sc(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sc(t,void 0)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sd=r(36153),sy=r.n(sd),sv=r(99388),sm=r.n(sv);function sb(t){return(sb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sg(){return(sg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sx(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sO(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sb(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sj=function(t,e,r,n,o){var i=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-i/2,",").concat(e+o)+"L ".concat(t+r-i/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sS={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sP=function(t){var e,r=sw(sw({},sS),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sx(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sx(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,y=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var x=(0,h.Z)("recharts-trapezoid",y);return g?a().createElement(no,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:d,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:d,x:l,y:s},duration:m,animationEasing:v,isActive:g},function(t){var e=t.upperWidth,o=t.lowerWidth,i=t.height,u=t.x,l=t.y;return a().createElement(no,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,easing:v},a().createElement("path",sg({},tf(r,!0),{className:x,d:sj(u,l,e,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",sg({},tf(r,!0),{className:x,d:sj(l,s,f,p,d)})))};function sA(t){return(sA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sE(){return(sE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sk(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sA(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sM=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/l$,f=u?o:o+i*s;return{center:lq(e,r,l,f),circleTangency:lq(e,r,n,f),lineTangency:lq(e,r,l*Math.cos(s*l$),u?o-i*s:o),theta:s}},sT=function(t){var e,r=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,a=t.startAngle,c=S((e=t.endAngle)-a)*Math.min(Math.abs(e-a),359.999),u=a+c,l=lq(r,n,i,a),s=lq(r,n,i,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(o>0){var p=lq(r,n,o,a),h=lq(r,n,o,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(r,",").concat(n," Z");return f},sC=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=S(l-u),f=sM({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sM({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sT({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var O=sM({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),w=O.circleTangency,j=O.lineTangency,P=O.theta,A=sM({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),E=A.circleTangency,k=A.lineTangency,_=A.theta,M=c?Math.abs(u-l):Math.abs(u-l)-P-_;if(M<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(E.x,",").concat(E.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sN={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sD=function(t){var e,r=s_(s_({},sN),t),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(c<i||f===p)return null;var y=(0,h.Z)("recharts-sector",d),v=c-i,m=M(u,v,0,!0);return e=m>0&&360>Math.abs(f-p)?sC({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(m,v/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):sT({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sE({},tf(r,!0),{className:y,d:e,role:"img"}))},sI=["option","shapeType","propTransformer","activeClassName","isActive"];function sB(t){return(sB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sR(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sB(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sB(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sz(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nh,r);case"trapezoid":return a().createElement(sP,r);case"sector":return a().createElement(sD,r);case"symbols":if("symbols"===e)return a().createElement(eI,r);break;default:return null}}function sU(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sI);if((0,i.isValidElement)(r))e=(0,i.cloneElement)(r,sL(sL({},l),(0,i.isValidElement)(r)?r.props:r));else if(z()(r))e=r(l);else if(sy()(r)&&!sm()(r)){var s=(void 0===o?function(t,e){return sL(sL({},e),t)}:o)(r,l);e=a().createElement(sz,{shapeType:n,elementProps:s})}else e=a().createElement(sz,{shapeType:n,elementProps:l});return u?a().createElement(tT,{className:void 0===c?"recharts-active-shape":c},e):e}function sF(t,e){return null!=e&&"trapezoids"in t.props}function s$(t,e){return null!=e&&"sectors"in t.props}function sq(t,e){return null!=e&&"points"in t.props}function sW(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function sX(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function sH(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var sV=["x","y"];function sG(t){return(sG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sY(){return(sY=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sZ(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sG(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sJ(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sV),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return sK(sK(sK(sK(sK({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function sQ(t){return a().createElement(sU,sY({shapeType:"rectangle",propTransformer:sJ,activeClassName:"recharts-active-bar"},t))}var s0=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||tP(!1),e)}},s1=["value","background"];function s2(t){return(s2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s3(){return(s3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s6(Object(r),!0).forEach(function(e){ft(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s4(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fe(n.key),n)}}function s7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(s7=function(){return!!t})()}function s8(t){return(s8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s9(t,e){return(s9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ft(t,e,r){return(e=fe(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fe(t){var e=function(t,e){if("object"!=s2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s2(e)?e:e+""}var fr=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=s8(e),ft(t=function(t,e){if(e&&("object"===s2(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,s7()?Reflect.construct(e,r||[],s8(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),ft(t,"id",_("recharts-bar-")),ft(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),ft(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&s9(t,e)}(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,c=r.activeBar,u=tf(this.props,!1);return t&&t.map(function(t,r){var l=r===i,s=s5(s5(s5({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tT,s3({className:"recharts-bar-rectangle"},Z(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),a().createElement(sQ,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(no,{begin:i,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=s&&s[e];if(r){var i=N(r.x,t.x),a=N(r.y,t.y),c=N(r.width,t.width),u=N(r.height,t.height);return s5(s5({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var l=N(0,t.height)(o);return s5(s5({},t),{},{y:t.y+t.height-l,height:l})}var f=N(0,t.width)(o);return s5(s5({},t),{},{width:f})});return a().createElement(tT,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!cV()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tf(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,s1);if(!c)return null;var l=s5(s5(s5(s5(s5({},u),{},{fill:"#eee"},c),i),Z(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(sQ,s3({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,c=r.layout,u=ta(r.children,uz);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:uZ(t,e)}};return a().createElement(tT,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,d=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,v=(0,h.Z)("recharts-bar",n),m=o&&o.allowDataOverflow,b=i&&i.allowDataOverflow,g=m||b,x=R()(d)?this.id:d;return a().createElement(tT,{className:v},m||b?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(x)},a().createElement("rect",{x:m?c:c-l/2,y:b?u:u-s/2,width:m?l:2*l,height:b?s:2*s}))):null,a().createElement(tT,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(x,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,x),(!f||y)&&sh.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&s4(n.prototype,e),r&&s4(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function fn(t){return(fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fu(n.key),n)}}function fi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fi(Object(r),!0).forEach(function(e){fc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fc(t,e,r){return(e=fu(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fu(t){var e=function(t,e){if("object"!=fn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fn(e)?e:e+""}ft(fr,"displayName","Bar"),ft(fr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!tQ.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),ft(fr,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=ln(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?s5(s5({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:i,O=l?x.scale.domain():null,w=lf({numericAxis:x}),j=ta(b,se),P=f.map(function(t,e){l?f=lo(l[s+e],O):Array.isArray(f=uZ(t,m))||(f=[w,f]);var n=s0(g,fr.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,P,A=[a.scale(f[0]),a.scale(f[1])],E=A[0],k=A[1];p=ls({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!==(P=null!=k?k:E)&&void 0!==P?P:void 0,v=h.size;var _=E-k;if(b=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=S(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var T=[i.scale(f[0]),i.scale(f[1])],C=T[0],N=T[1];if(p=C,y=ls({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=N-C,b=h.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var D=S(v||n)*(Math.abs(n)-Math.abs(v));v+=D}}return s5(s5(s5({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lg(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return s5({data:P,layout:d},p)});var fl=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tc(u,fr);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort();if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var k=M(t.barCategoryGap,A*E),_=A*E/2;u=_-k-(_-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(l=[l[1],l[0]]);var T=le(y,o,f),C=T.scale,N=T.realScaleType;C.domain(m).range(l),lr(C);var D=lu(C,fa(fa({},y),{},{realScaleType:N}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[w]-d*y.width,h=r.top);var I=fa(fa(fa({},y),D),{},{realScaleType:N,x:p,y:h,scale:C,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return I.bandSize=lm(I,D),y.hide||"xAxis"!==n?y.hide||(s[w]+=(d?-1:1)*I.width):s[w]+=(d?-1:1)*I.height,fa(fa({},i),{},fc({},a,I))},{})},fs=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},ff=function(){var t,e;function r(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fo(r.prototype,t),e&&fo(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fc(ff,"EPS",1e-4);var fp=function(t){var e=Object.keys(t).reduce(function(e,r){return fa(fa({},e),{},fc({},r,ff.create(t[r])))},{});return fa(fa({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return l8()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return st()(t,function(t,r){return e[r].isInRange(t)})}})},fh=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fd(){return(fd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fy(t){return(fy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fv(Object(r),!0).forEach(function(e){fO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fb(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fb=function(){return!!t})()}function fg(t){return(fg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fx(t,e){return(fx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fO(t,e,r){return(e=fw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fw(t){var e=function(t,e){if("object"!=fy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fy(e)?e:e+""}var fj=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fp({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return l4(t,"discard")&&!i.isInRange(a)?null:a},fS=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=fg(t),function(t,e){if(e&&("object"===fy(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fb()?Reflect.construct(t,e||[],fg(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fx(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,c=t.clipPathId,u=E(e),l=E(n);if(I(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fj(this.props);if(!s)return null;var f=s.x,p=s.y,d=this.props,y=d.shape,v=d.className,m=fm(fm({clipPath:l4(this.props,"hidden")?"url(#".concat(c,")"):void 0},tf(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tT,{className:(0,h.Z)("recharts-reference-dot",v)},r.renderDot(y,m),l6.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fw(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fO(fS,"displayName","ReferenceDot"),fO(fS,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fO(fS,"renderDot",function(t,e){return a().isValidElement(t)?a().cloneElement(t,e):z()(t)?t(e):a().createElement(e6,fd({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fP=r(34009),fA=r.n(fP),fE=r(9660),fk=r.n(fE),f_=r(97300),fM=r.n(f_)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fT=(0,i.createContext)(void 0),fC=(0,i.createContext)(void 0),fN=(0,i.createContext)(void 0),fD=(0,i.createContext)({}),fI=(0,i.createContext)(void 0),fB=(0,i.createContext)(0),fR=(0,i.createContext)(0),fL=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fM(o);return a().createElement(fT.Provider,{value:r},a().createElement(fC.Provider,{value:n},a().createElement(fD.Provider,{value:o},a().createElement(fN.Provider,{value:s},a().createElement(fI.Provider,{value:i},a().createElement(fB.Provider,{value:l},a().createElement(fR.Provider,{value:u},c)))))))},fz=function(t){var e=(0,i.useContext)(fT);null!=e||tP(!1);var r=e[t];return null!=r||tP(!1),r},fU=function(){var t=(0,i.useContext)(fC);return fk()(t,function(t){return st()(t.domain,Number.isFinite)})||T(t)},fF=function(t){var e=(0,i.useContext)(fC);null!=e||tP(!1);var r=e[t];return null!=r||tP(!1),r},f$=function(){return(0,i.useContext)(fR)},fq=function(){return(0,i.useContext)(fB)};function fW(t){return(fW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fX(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fX=function(){return!!t})()}function fH(t){return(fH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fV(t,e){return(fV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fG(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fG(Object(r),!0).forEach(function(e){fZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fG(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fZ(t,e,r){return(e=fK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fK(t){var e=function(t,e){if("object"!=fW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fW(e)?e:e+""}function fJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function fQ(){return(fQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f0=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(l4(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(l4(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return l4(u,"discard")&&fA()(g,function(e){return!t.isInRange(e)})?null:g}return null};function f1(t){var e,r,n,o=t.x,c=t.y,u=t.segment,l=t.xAxisId,s=t.yAxisId,f=t.shape,p=t.className,d=t.alwaysShow,y=(0,i.useContext)(fI),v=fz(l),m=fF(s),b=(0,i.useContext)(fN);if(!y||!b)return null;I(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var g=f0(fp({x:v.scale,y:m.scale}),E(o),E(c),u&&2===u.length,b,t.position,v.orientation,m.orientation,t);if(!g)return null;var x=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{for(i=(r=r.call(t)).next;!(u=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(g,2)||function(t,e){if(t){if("string"==typeof t)return fJ(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fJ(t,2)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),O=x[0],w=O.x,j=O.y,S=x[1],P=S.x,A=S.y,k=fY(fY({clipPath:l4(t,"hidden")?"url(#".concat(y,")"):void 0},tf(t,!0)),{},{x1:w,y1:j,x2:P,y2:A});return a().createElement(tT,{className:(0,h.Z)("recharts-reference-line",p)},(e=f,r=k,a().isValidElement(e)?a().cloneElement(e,r):z()(e)?e(r):a().createElement("line",fQ({},r,{className:"recharts-reference-line-line"}))),l6.renderCallByParent(t,fs({x:(n={x1:w,y1:j,x2:P,y2:A}).x1,y:n.y1},{x:n.x2,y:n.y2})))}var f2=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=fH(t),function(t,e){if(e&&("object"===fW(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fX()?Reflect.construct(t,e||[],fH(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fV(t,e)}(r,t),e=[{key:"render",value:function(){return a().createElement(f1,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fK(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function f3(){return(f3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f6(t){return(f6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f5(Object(r),!0).forEach(function(e){pt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f7=function(){return!!t})()}function f8(t){return(f8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f9(t,e){return(f9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pt(t,e,r){return(e=pe(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pe(t){var e=function(t,e){if("object"!=f6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f6(e)?e:e+""}fZ(f2,"displayName","ReferenceLine"),fZ(f2,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var pr=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fp({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!l4(o,"discard")||f.isInRange(p)&&f.isInRange(h)?fs(p,h):null},pn=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=f8(t),function(t,e){if(e&&("object"===f6(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,f7()?Reflect.construct(t,e||[],f8(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f9(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;I(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=E(e),f=E(n),p=E(o),d=E(i),y=this.props.shape;if(!s&&!f&&!p&&!d&&!y)return null;var v=pr(s,f,p,d,this.props);if(!v&&!y)return null;var m=l4(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tT,{className:(0,h.Z)("recharts-reference-area",c)},r.renderRect(y,f4(f4({clipPath:m},tf(this.props,!0)),v)),l6.renderCallByParent(this.props,v))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pe(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function po(t){return function(t){if(Array.isArray(t))return pi(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pi(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pi(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pi(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pt(pn,"displayName","ReferenceArea"),pt(pn,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pt(pn,"renderRect",function(t,e){return a().isValidElement(t)?a().cloneElement(t,e):z()(t)?t(e):a().createElement(nh,f3({},e,{className:"recharts-reference-area-rect"}))});var pa=function(t,e,r,n,o){var i=ta(t,f2),a=ta(t,fS),c=[].concat(po(i),po(a)),u=ta(t,pn),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&l4(e.props,"extendDomain")&&A(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&l4(e.props,"extendDomain")&&A(e.props[p])&&A(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return A(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pc=r(51028),pu=new(r.n(pc)()),pl="recharts.syncMouseEvents";function ps(t){return(ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pf(t,e,r){return(e=pp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pp(t){var e=function(t,e){if("object"!=ps(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ps(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ps(e)?e:e+""}var ph=function(){var t,e;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),pf(this,"activeIndex",0),pf(this,"coordinateList",[]),pf(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pp(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pd(){}function py(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pv(t){this._context=t}function pm(t){this._context=t}function pb(t){this._context=t}pv.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:py(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:py(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pm.prototype={areaStart:pd,areaEnd:pd,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:py(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:py(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pg{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function px(t){this._context=t}function pO(t){this._context=t}function pw(t){return new pO(t)}function pj(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pS(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pP(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pA(t){this._context=t}function pE(t){this._context=new pk(t)}function pk(t){this._context=t}function p_(t){this._context=t}function pM(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pT(t,e){this._context=t,this._t=e}function pC(t){return t[0]}function pN(t){return t[1]}function pD(t,e){var r=ex(!0),n=null,o=pw,i=null,a=eA(c);function c(c){var u,l,s,f=(c=cE(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pC:ex(t),e="function"==typeof e?e:void 0===e?pN:ex(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:ex(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:ex(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pI(t,e,r){var n=null,o=ex(!0),i=null,a=pw,c=null,u=eA(l);function l(l){var s,f,p,h,d,y=(l=cE(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v){if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pD().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pC:ex(+t),e="function"==typeof e?e:void 0===e?ex(0):ex(+e),r="function"==typeof r?r:void 0===r?pN:ex(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:ex(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:ex(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:ex(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:ex(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:ex(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:ex(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pB(t){return(pB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pR(){return(pR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pL(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pz(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pL(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=pB(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pB(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pL(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}px.prototype={areaStart:pd,areaEnd:pd,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},pO.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pA.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pP(this,this._t0,pS(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pP(this,pS(this,r=pj(this,t,e)),r);break;default:pP(this,this._t0,r=pj(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pE.prototype=Object.create(pA.prototype)).point=function(t,e){pA.prototype.point.call(this,e,t)},pk.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},p_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pM(t),o=pM(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pT.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pU={curveBasisClosed:function(t){return new pm(t)},curveBasisOpen:function(t){return new pb(t)},curveBasis:function(t){return new pv(t)},curveBumpX:function(t){return new pg(t,!0)},curveBumpY:function(t){return new pg(t,!1)},curveLinearClosed:function(t){return new px(t)},curveLinear:pw,curveMonotoneX:function(t){return new pA(t)},curveMonotoneY:function(t){return new pE(t)},curveNatural:function(t){return new p_(t)},curveStep:function(t){return new pT(t,.5)},curveStepAfter:function(t){return new pT(t,1)},curveStepBefore:function(t){return new pT(t,0)}},pF=function(t){return t.x===+t.x&&t.y===+t.y},p$=function(t){return t.x},pq=function(t){return t.y},pW=function(t,e){if(z()(t))return t;var r="curve".concat(eo()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pU["".concat(r).concat("vertical"===e?"Y":"X")]:pU[r]||pw},pX=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=pW(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pF(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pF(t)}):i,p=s.map(function(t,e){return pz(pz({},t),{},{base:f[e]})});return(e="vertical"===a?pI().y(pq).x1(p$).x0(function(t){return t.base.x}):pI().x(p$).y1(pq).y0(function(t){return t.base.y})).defined(pF).curve(l),e(p)}return(e="vertical"===a&&A(i)?pI().y(pq).x1(p$).x0(i):A(i)?pI().x(p$).y1(pq).y0(i):pD().x(p$).y(pq)).defined(pF).curve(l),e(s)},pH=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?pX(t):n;return a().createElement("path",pR({},tf(t,!1),Y(t),{className:(0,h.Z)("recharts-curve",e),d:i,ref:o}))};function pV(t){return(pV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var pG=["x","y","top","left","width","height","className"];function pY(){return(pY=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pZ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var pK=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,c=void 0===i?0:i,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,d=void 0===p?0:p,y=t.className,v=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pZ(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=pV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pV(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pZ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:c,left:l,width:f,height:d},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,pG));return A(r)&&A(o)&&A(f)&&A(d)&&A(c)&&A(l)?a().createElement("path",pY({},tf(v,!0),{className:(0,h.Z)("recharts-cross",y),d:"M".concat(r,",").concat(c,"v").concat(d,"M").concat(l,",").concat(o,"h").concat(f)})):null};function pJ(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lq(e,r,n,o),lq(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function pQ(t){return(pQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p0(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=pQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pQ(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p2(t){var e,r,n,o,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,d=t.tooltipAxisBandSize,y=t.layout,v=t.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!m||!u||!l||"ScatterChart"!==v&&"axis"!==c)return null;var b=pH;if("ScatterChart"===v)o=l,b=pK;else if("BarChart"===v)e=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===y?l.x-e:f.left+.5,y:"horizontal"===y?f.top+.5:l.y-e,width:"horizontal"===y?d:f.width-1,height:"horizontal"===y?f.height-1:d},b=nh;else if("radial"===y){var g=pJ(l),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=sD}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return pJ(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lq(c,u,l,f),h=lq(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(y,l,f)},b=pH;var j=p1(p1(p1(p1({stroke:"#ccc",pointerEvents:"none"},f),o),tf(m,!1)),{},{payload:s,payloadIndex:p,className:(0,h.Z)("recharts-tooltip-cursor",m.className)});return(0,i.isValidElement)(m)?(0,i.cloneElement)(m,j):(0,i.createElement)(b,j)}var p3=["item"],p6=["children","className","width","height","style","compact","title","desc"];function p5(t){return(p5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p4(){return(p4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p7(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hn(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p8(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function p9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(p9=function(){return!!t})()}function ht(t){return(ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function he(t,e){return(he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hr(t){return function(t){if(Array.isArray(t))return ho(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hn(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hn(t,e){if(t){if("string"==typeof t)return ho(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ho(t,e)}}function ho(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ha(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hi(Object(r),!0).forEach(function(e){hc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hc(t,e,r){return(e=hu(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hu(t){var e=function(t,e){if("object"!=p5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p5(e)?e:e+""}var hl={xAxis:["bottom","top"],yAxis:["left","right"]},hs={width:"100%",height:"100%"},hf={x:0,y:0};function hp(t){return t}var hh=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return ha(ha(ha({},n),lq(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return ha(ha(ha({},n),lq(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hf},hd=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hr(t),hr(r)):t},[]);return i.length>0?i:t&&t.length&&A(n)&&A(o)?t.slice(n,o+1):[]};function hy(t){return"number"===t?[0,"auto"]:void 0}var hv=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hd(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?D(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(hr(o),[lg(c,l)]):o},[])},hm=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=uJ(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hv(t,e,l,s),p=hh(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hb=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=u4(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?ha(ha({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var w=hd(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),j=w.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&A(n)&&A(o))return!0}return!1})(h.domain,v,d)&&(E=lv(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(_=uK(w,y,"category")));var S=hy(d);if(!E||0===E.length){var P,E,k,_,M,T=null!==(M=h.domain)&&void 0!==M?M:S;if(y){if(E=uK(w,y,d),"category"===d&&p){var N=C(E);m&&N?(k=E,E=tw()(0,j)):m||(E=lb(T,E,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hr(t),[e])},[]))}else if("category"===d)E=m?E.filter(function(t){return""!==t&&!R()(t)}):lb(T,E,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||R()(e)?t:[].concat(hr(t),[e])},[]);else if("number"===d){var D=u6(w,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)}),y,o,l);D&&(E=D)}p&&("number"===d||"auto"!==b)&&(_=uK(w,y,"category"))}else E=p?tw()(0,j):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:lh(a[O].stackGroups,c,u):u5(w,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);"number"===d?(E=pa(s,E,O,o,g),T&&(E=lv(T,E,v))):"category"===d&&T&&E.every(function(t){return T.indexOf(t)>=0})&&(E=T)}return ha(ha({},e),{},hc({},O,ha(ha({},h),{},{axisType:o,domain:E,categoricalDomain:_,duplicateDomain:k,originalDomain:null!==(P=h.domain)&&void 0!==P?P:S,isCategorical:p,layout:l})))},{})},hg=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hd(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=u4(l,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?ha(ha({},e.type.defaultProps),e.props):e.props)[i],m=hy("number");return t[v]?t:(d++,y=h?tw()(0,p):a&&a[v]&&a[v].hasStack?pa(s,y=lh(a[v].stackGroups,c,u),v,o):pa(s,y=lv(m,u5(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),ha(ha({},t),{},hc({},v,ha(ha({axisType:o},n.defaultProps),{},{hide:!0,orientation:O()(hl,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hx=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=ta(l,o),p={};return f&&f.length?p=hb(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=hg(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hO=function(t){var e=T(t),r=u8(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tS()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lm(e,r)}},hw=function(t){var e=t.children,r=t.defaultShowTooltip,n=tc(e,lL),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hj=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hS=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tc(s,lL),h=tc(s,e2),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:ha(ha({},t),{},hc({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:ha(ha({},t),{},hc({},n,O()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=ha(ha({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lL.defaultProps.height),h&&e&&(v=u2(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return ha(ha({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},hP=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,d=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hj(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tr(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hd(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?ha(ha({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tP(!1);var i=n[o];return ha(ha({},t),{},hc(hc({},r.axisType,i),"".concat(r.axisType,"Ticks"),u8(i)))},{}),A=P[v],E=P["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&lp(r,n[j].stackGroups),_=tr(r.type).indexOf("Bar")>=0,M=lm(A,E),T=[],C=m&&u0({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(_){var N,D,I=R()(w)?h:w,B=null!==(N=null!==(D=lm(A,E,!0))&&void 0!==D?D:I)&&void 0!==N?N:0;T=u1({barGap:f,barCategoryGap:p,bandSize:B!==M?B:M,sizeList:C[S],maxBarSize:I}),B!==M&&(T=T.map(function(t){return ha(ha({},t),{},{position:ha(ha({},t.position),{},{offset:t.position.offset-B/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:ha(ha({},L(ha(ha({},P),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:M,barPosition:T,offset:o,stackedData:k,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hc(hc(hc({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",i)),childIndex:ti(t.children).indexOf(r),item:r})}),b},v=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tu({props:o}))return null;var u=o.children,s=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=hj(s),m=v.numericAxisName,b=v.cateAxisName,g=ta(u,r),x=lc(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),O=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ha(ha({},t),{},hc({},r,hx(o,ha(ha({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=hS(ha(ha({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=f(o,O[t],w,t.replace("Map",""),e)});var j=hO(O["".concat(b,"Map")]),S=d(o,ha(ha({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:w}));return ha(ha({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},m=function(t){var r;function n(t){var r,o,c,u,l;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),u=n,l=[t],u=ht(u),hc(c=function(t,e){if(e&&("object"===p5(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,p9()?Reflect.construct(u,l||[],ht(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hc(c,"accessibilityManager",new ph),hc(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(ha({legendBBox:t},v({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},ha(ha({},c.state),{},{legendBBox:t}))))}}),hc(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hc(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return ha({dataStartIndex:e,dataEndIndex:r},v({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hc(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=ha(ha({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;z()(n)&&n(r,t)}}),hc(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?ha(ha({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;z()(n)&&n(r,t)}),hc(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hc(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hc(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hc(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;z()(r)&&r(e,t)}),hc(c,"handleOuterEvent",function(t){var e,r=ty(t),n=O()(c.props,"".concat(r));r&&z()(n)&&n(null!==(e=/.*touch.*/i.test(r)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))&&void 0!==e?e:{},t)}),hc(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=ha(ha({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;z()(n)&&n(r,t)}}),hc(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;z()(e)&&e(c.getMouseInfo(t),t)}),hc(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;z()(e)&&e(c.getMouseInfo(t),t)}),hc(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hc(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hc(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hc(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;z()(e)&&e(c.getMouseInfo(t),t)}),hc(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;z()(e)&&e(c.getMouseInfo(t),t)}),hc(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pu.emit(pl,c.props.syncId,t,c.eventEmitterSymbol)}),hc(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(ha({dataStartIndex:i,dataEndIndex:a},v({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ha(ha({},p),{},{x:p.left,y:p.top}),m=Math.min(u,y.x+y.width),b=Math.min(l,y.y+y.height),g=h[s]&&h[s].value,x=hv(c.state,c.props.data,s),O=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hf;c.setState(ha(ha({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hc(c,"renderCursor",function(t){var r,n=c.state,o=n.isTooltipActive,i=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:o,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(p2,{key:y,activeCoordinate:i,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hc(c,"renderPolarAxis",function(t,e,r){var n=O()(t,"type.axisType"),o=O()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?ha(ha({},a),t.props):t.props,l=o&&o[u["".concat(n,"Id")]];return(0,i.cloneElement)(t,ha(ha({},l),{},{className:(0,h.Z)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:u8(l,!0)}))}),hc(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=T(u),f=T(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(t,{polarAngles:Array.isArray(n)?n:u8(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:u8(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hc(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,o=e.height,a=c.props.margin||{},u=uq({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=p8(u,p3);return(0,i.cloneElement)(l,ha(ha({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hc(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,o=tc(r,er);if(!o)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=o.props.active)&&void 0!==t?t:u;return(0,i.cloneElement)(o,{viewBox:ha(ha({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hc(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,o=c.state,a=o.offset,u=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,i.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lt(c.handleBrushChange,t.props.onChange),data:n,x:A(t.props.x)?t.props.x:a.left,y:A(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:A(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hc(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,o=c.state,a=o.xAxisMap,u=o.yAxisMap,l=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,i.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hc(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?ha(ha({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ha(ha({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:uQ(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tf(s,!1)),Y(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,ha(ha({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),hc(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var o=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=tc(c.props.children,er),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?ha(ha({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w={};"axis"!==o&&p&&"click"===p.props.trigger?w={onClick:lt(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(w={onMouseLeave:lt(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lt(c.handleItemMouseEnter,t.props.onMouseEnter)});var j=(0,i.cloneElement)(t,ha(ha({},n.props),w));if(!g&&u&&p&&(b||x||O)){if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var S="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());A=D(d,S,f),E=y&&v&&D(v,S,f)}else A=null==d?void 0:d[s],E=y&&v&&v[s];if(O||x){var P=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,i.cloneElement)(t,ha(ha(ha({},n.props),w),{},{activeIndex:P})),null,null]}if(!R()(A))return[j].concat(hr(c.renderActivePoints({item:n,activePoint:A,basePoint:E,childIndex:s,isRange:y})))}else{var A,E,k,_=(null!==(k=c.getItemByXY(c.state.activeCoordinate))&&void 0!==k?k:{graphicalItem:j}).graphicalItem,M=_.item,T=void 0===M?t:M,C=_.childIndex,N=ha(ha(ha({},n.props),w),{},{activeIndex:C});return[(0,i.cloneElement)(T,N),null,null]}}return y?[j,null,null]:[j,null]}),hc(c,"renderCustomized",function(t,e,r){return(0,i.cloneElement)(t,ha(ha({key:"recharts-customized-".concat(r)},c.props),c.state))}),hc(c,"renderMap",{CartesianGrid:{handler:hp,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hp},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hp},YAxis:{handler:hp},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:_("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=y()(c.triggeredAfterMouseMove,null!==(o=t.throttleDelay)&&void 0!==o?o:1e3/60),c.state={},c}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&he(t,e)}(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=tc(e,er);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hv(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ha(ha({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tp([tc(t.children,er)],[tc(this.props.children,er)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tc(this.props.children,er);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap;if("axis"!==this.getTooltipEventType()&&u&&l){var s=T(u).scale,f=T(l).scale,p=s&&s.invert?s.invert(o.chartX):null,h=f&&f.invert?f.invert(o.chartY):null;return ha(ha({},o),{},{xValue:p,yValue:h})}var d=hm(this.state,this.props.data,this.props.layout,a);return d?ha(ha({},o),d):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?lV({x:o,y:i},T(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tc(t,er),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ha(ha({},Y(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pu.on(pl,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pu.removeListener(pl,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===tr(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=p7(e,2),n=r[0],o=r[1];return ha(ha({},t),{},hc({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=p7(e,2),n=r[0],o=r[1];return ha(ha({},t),{},hc({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?ha(ha({},u.type.defaultProps),u.props):u.props,s=tr(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nf(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return lV(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sF(a,n)||s$(a,n)||sq(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sF(i,o)?e="trapezoids":s$(i,o)?e="sectors":sq(i,o)&&(e="points"),e),u=sF(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:s$(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:sq(i,o)?o.payload:{},l=a.filter(function(t,e){var r=cV()(u,t),n=i.props[c].filter(function(t){var e;return(sF(i,o)?e=sW:s$(i,o)?e=sX:sq(i,o)&&(e=sH),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ha(ha({},a),{},{childIndex:d}),payload:sq(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tu(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,d=tf(p8(n,p6),!1);if(s)return a().createElement(fL,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tk,p4({},d,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),td(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,d.role=null!==(e=this.props.role)&&void 0!==e?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return a().createElement(fL,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",p4({className:(0,h.Z)("recharts-wrapper",i),style:ha({position:"relative",cursor:"default",width:c,height:u},l)},y,{ref:function(t){r.container=t}}),a().createElement(tk,p4({},d,{width:c,height:u,title:f,desc:p,style:hs}),this.renderClipPath(),td(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hu(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);hc(m,"displayName",e),hc(m,"defaultProps",ha({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hc(m,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hw(t);return ha(ha(ha({},p),{},{updateId:0},v(ha(ha({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!q(l,e.prevMargin)){var h=hw(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},y=ha(ha({},hm(e,n,c)),{},{updateId:e.updateId+1}),m=ha(ha(ha({},h),d),y);return ha(ha(ha({},m),v(ha({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!tp(o,e.prevChildren)){var b,g,x,O,w=tc(o,lL),j=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,S=w&&null!==(x=null===(O=w.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:f,P=R()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return ha(ha({updateId:P},v(ha(ha({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hc(m,"renderActiveDot",function(t,e,r){var n;return n=(0,i.isValidElement)(t)?(0,i.cloneElement)(t,e):z()(t)?t(e):a().createElement(e6,e),a().createElement(tT,{className:"recharts-active-dot",key:r},n)});var b=(0,i.forwardRef)(function(t,e){return a().createElement(m,p4({},t,{ref:e}))});return b.displayName=m.displayName,b},hA=["type","layout","connectNulls","ref"],hE=["key"];function hk(t){return(hk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h_(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hM(){return(hM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hT(Object(r),!0).forEach(function(e){hz(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hN(t){return function(t){if(Array.isArray(t))return hD(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return hD(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hD(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hI(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hU(n.key),n)}}function hB(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hB=function(){return!!t})()}function hR(t){return(hR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hL(t,e){return(hL=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hz(t,e,r){return(e=hU(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hU(t){var e=function(t,e){if("object"!=hk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hk(e)?e:e+""}var hF=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=hR(e),hz(t=function(t,e){if(e&&("object"===hk(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,hB()?Reflect.construct(e,r||[],hR(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),hz(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),hz(t,"getStrokeDasharray",function(e,r,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(r,e);for(var a=e%i,c=r-e,u=[],l=0,s=0;l<o.length;s+=o[l],++l)if(s+o[l]>a){u=[].concat(hN(o.slice(0,l)),[a-s]);break}var f=u.length%2==0?[0,c]:[c];return[].concat(hN(n.repeat(o,Math.floor(e/i))),hN(u),f).map(function(t){return"".concat(t,"px")}).join(", ")}),hz(t,"id",_("recharts-line-")),hz(t,"pathRef",function(e){t.mainCurve=e}),hz(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),hz(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hL(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,c=r.layout,u=ta(r.children,uz);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:uZ(t.payload,e)}};return a().createElement(tT,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,c=o.points,u=o.dataKey,l=tf(this.props,!1),s=tf(i,!0),f=c.map(function(t,e){var r=hC(hC(hC({key:"dot-".concat(e),r:3},l),s),{},{value:t.value,dataKey:u,cx:t.x,cy:t.y,index:e,payload:t.payload});return n.renderDotItem(i,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tT,hM({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var o=this.props,i=o.type,c=o.layout,u=o.connectNulls,l=hC(hC(hC({},tf((o.ref,h_(o,hA)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:i,layout:c,connectNulls:u});return a().createElement(pH,hM({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=n.animateNewValues,h=n.width,d=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return a().createElement(no,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/o.length,l=o.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],o=N(n.x,t.x),i=N(n.y,t.y);return hC(hC({},t),{},{x:o(c),y:i(c)})}if(p){var a=N(2*h,t.x),l=N(d/2,t.y);return hC(hC({},t),{},{x:a(c),y:l(c)})}return hC(hC({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=N(0,m)(c);if(i){var f="".concat(i).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(s,m,f)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!cV()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,i=e.className,c=e.xAxis,u=e.yAxis,l=e.top,s=e.left,f=e.width,p=e.height,d=e.isAnimationActive,y=e.id;if(r||!o||!o.length)return null;var v=this.state.isAnimationFinished,m=1===o.length,b=(0,h.Z)("recharts-line",i),g=c&&c.allowDataOverflow,x=u&&u.allowDataOverflow,O=g||x,w=R()(y)?this.id:y,j=null!==(t=tf(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},S=j.r,P=j.strokeWidth,A=(n&&"object"===tt(n)&&"clipDot"in n?n:{}).clipDot,E=void 0===A||A,k=2*(void 0===S?3:S)+(void 0===P?2:P);return a().createElement(tT,{className:b},g||x?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:g?s:s-f/2,y:x?l:l-p/2,width:g?f:2*f,height:x?p:2*p})),!E&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:s-k/2,y:l-k/2,width:f+k,height:p+k}))):null,!m&&this.renderCurve(O,w),this.renderErrorBar(O,w),(m||n)&&this.renderDots(O,E,w),(!d||v)&&sh.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(hN(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(hN(n),hN(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(z()(t))r=t(e);else{var n=e.key,o=h_(e,hE),i=(0,h.Z)("recharts-line-dot","boolean"!=typeof t?t.className:"");r=a().createElement(e6,hM({key:n},o,{className:i}))}return r}}],e&&hI(n.prototype,e),r&&hI(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function h$(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function hq(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hW(t){return(hW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hH(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hX(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=hW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hW(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hV(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(A(h)||tQ.isSsr)return h$(l,("number"==typeof h&&A(h)?h:0)+1);var m="top"===p||"bottom"===p?"width":"height",b=y&&"width"===m?n_(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},g=function(t,n){var o,i=z()(d)?d(t.value,n):t.value;return"width"===m?fh({width:(o=n_(i,{fontSize:e,letterSpacing:r})).width+b.width,height:o.height+b.height},v):n_(i,{fontSize:e,letterSpacing:r})[m]},x=l.length>=2?S(l[1].coordinate-l[0].coordinate):1,O=(n="width"===m,o=s.x,i=s.y,a=s.width,c=s.height,1===x?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:h$(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||hq(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(x,O,g,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=hH(hH({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hq(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=hH(hH({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=hH(hH({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=hH(hH({},i),{},{tickCoord:i.coordinate});hq(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=hH(hH({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(x,O,g,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=hH(hH({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=hH(hH({},l),{},{tickCoord:l.coordinate});hq(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=hH(hH({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(x,O,g,l,f)).filter(function(t){return t.isShow})}hz(hF,"displayName","Line"),hz(hF,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!tQ.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),hz(hF,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return hC({points:u.map(function(t,e){var u=uZ(t,a);return"horizontal"===s?{x:ll({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:R()(u)?null:n.scale(u),value:u,payload:t}:{x:R()(u)?null:r.scale(u),y:ll({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var hG=["viewBox"],hY=["viewBox"],hZ=["ticks"];function hK(t){return(hK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hJ(){return(hJ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hQ(Object(r),!0).forEach(function(e){h4(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h1(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function h2(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h7(n.key),n)}}function h3(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h3=function(){return!!t})()}function h6(t){return(h6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h5(t,e){return(h5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h4(t,e,r){return(e=h7(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h7(t){var e=function(t,e){if("object"!=hK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hK(e)?e:e+""}var h8=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=h6(r),(e=function(t,e){if(e&&("object"===hK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,h3()?Reflect.construct(r,o||[],h6(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h5(t,e)}(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=h1(t,hG),o=this.props,i=o.viewBox,a=h1(o,hY);return!q(r,i)||!q(n,a)||!q(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=A(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,c=t.mirror,u=t.axisLine,l=h0(h0(h0({},tf(this.props,!1)),tf(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!c||"bottom"===i&&c);l=h0(h0({},l),{},{x1:e,y1:r+s*o,x2:e+n,y2:r+s*o})}else{var f=+("left"===i&&!c||"right"===i&&c);l=h0(h0({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+o})}return a().createElement("line",hJ({},l,{className:(0,h.Z)("recharts-cartesian-axis-line",O()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,c=i.tickLine,u=i.stroke,l=i.tick,s=i.tickFormatter,f=i.unit,p=hV(h0(h0({},this.props),{},{ticks:t}),e,r),d=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),v=tf(this.props,!1),m=tf(l,!1),b=h0(h0({},v),{},{fill:"none"},tf(c,!1)),g=p.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,g=r.tick,x=h0(h0(h0(h0({textAnchor:d,verticalAnchor:y},v),{},{stroke:"none",fill:u},m),g),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tT,hJ({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},Z(o.props,t,e)),c&&a().createElement("line",hJ({},b,i,{className:(0,h.Z)("recharts-cartesian-axis-tick-line",O()(c,"className"))})),l&&n.renderTickItem(l,x,"".concat(z()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},g)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=h1(u,hZ),f=l;return(z()(i)&&(f=i(l&&l.length>0?this.props:s)),n<=0||o<=0||!f||!f.length)?null:a().createElement(tT,{className:(0,h.Z)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),l6.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){return a().isValidElement(t)?a().cloneElement(t,e):z()(t)?t(e):a().createElement(n2,hJ({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&h2(n.prototype,e),r&&h2(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function h9(t){return(h9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dt=function(){return!!t})()}function de(t){return(de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dr(t,e){return(dr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dn(t,e,r){return(e=di(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function di(t){var e=function(t,e){if("object"!=h9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h9(e)?e:e+""}function da(){return(da=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dc(t){var e=t.xAxisId,r=f$(),n=fq(),o=fz(e);return null==o?null:a().createElement(h8,da({},o,{className:(0,h.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return u8(t,!0)}}))}h4(h8,"displayName","CartesianAxis"),h4(h8,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var du=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=de(t),function(t,e){if(e&&("object"===h9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,dt()?Reflect.construct(t,e||[],de(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dr(t,e)}(r,t),e=[{key:"render",value:function(){return a().createElement(dc,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,di(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dl(t){return(dl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ds(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ds=function(){return!!t})()}function df(t){return(df=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dp(t,e){return(dp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dh(t,e,r){return(e=dd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dd(t){var e=function(t,e){if("object"!=dl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dl(e)?e:e+""}function dy(){return(dy=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}dn(du,"displayName","XAxis"),dn(du,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var dv=function(t){var e=t.yAxisId,r=f$(),n=fq(),o=fF(e);return null==o?null:a().createElement(h8,dy({},o,{className:(0,h.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return u8(t,!0)}}))},dm=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=df(t),function(t,e){if(e&&("object"===dl(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ds()?Reflect.construct(t,e||[],df(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dp(t,e)}(r,t),e=[{key:"render",value:function(){return a().createElement(dv,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dd(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);dh(dm,"displayName","YAxis"),dh(dm,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var db=hP({chartName:"LineChart",GraphicalChild:hF,axisComponents:[{axisType:"xAxis",AxisComp:du},{axisType:"yAxis",AxisComp:dm}],formatAxisMap:fl}),dg=["x1","y1","x2","y2","key"],dx=["offset"];function dO(t){return(dO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dw(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=dO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dO(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dS(){return(dS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dP(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dA=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:o,ry:u,width:i,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dE(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(z()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,c=e.y2,u=e.key,l=tf(dP(e,dg),!1),s=(l.offset,dP(l,dx));r=a().createElement("line",dS({},s,{x1:n,y1:o,x2:i,y2:c,fill:"none",key:u}))}return r}function dk(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dE(o,dj(dj({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function d_(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dE(o,dj(dj({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function dM(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:o+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:i,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dT(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:o+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dC=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return u7(hV(dj(dj(dj({},h8.defaultProps),r),{},{ticks:u8(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dN=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return u7(hV(dj(dj(dj({},h8.defaultProps),r),{},{ticks:u8(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dD={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dI(t){var e,r,n,o,c,u,l=f$(),s=fq(),f=(0,i.useContext)(fD),p=dj(dj({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:dD.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:dD.fill,horizontal:null!==(n=t.horizontal)&&void 0!==n?n:dD.horizontal,horizontalFill:null!==(o=t.horizontalFill)&&void 0!==o?o:dD.horizontalFill,vertical:null!==(c=t.vertical)&&void 0!==c?c:dD.vertical,verticalFill:null!==(u=t.verticalFill)&&void 0!==u?u:dD.verticalFill,x:A(t.x)?t.x:f.left,y:A(t.y)?t.y:f.top,width:A(t.width)?t.width:f.width,height:A(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=T((0,i.useContext)(fT)),O=fU();if(!A(y)||y<=0||!A(v)||v<=0||!A(h)||h!==+h||!A(d)||d!==+d)return null;var w=p.verticalCoordinatesGenerator||dC,j=p.horizontalCoordinatesGenerator||dN,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&z()(j)){var E=b&&b.length,k=j({yAxis:O?dj(dj({},O),{},{ticks:E?b:O.ticks}):void 0,width:l,height:s,offset:f},!!E||m);I(Array.isArray(k),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dO(k),"]")),Array.isArray(k)&&(S=k)}if((!P||!P.length)&&z()(w)){var _=g&&g.length,M=w({xAxis:x?dj(dj({},x),{},{ticks:_?g:x.ticks}):void 0,width:l,height:s,offset:f},!!_||m);I(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dO(M),"]")),Array.isArray(M)&&(P=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dA,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dk,dS({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:O})),a().createElement(d_,dS({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:O})),a().createElement(dM,dS({},p,{horizontalPoints:S})),a().createElement(dT,dS({},p,{verticalPoints:P})))}dI.displayName="CartesianGrid";var dB=hP({chartName:"BarChart",GraphicalChild:fr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:du},{axisType:"yAxis",AxisComp:dm}],formatAxisMap:fl}),dR=r(77863);function dL(){let[t,e]=(0,i.useState)(!0),[r,n]=(0,i.useState)("30d"),[a,h]=(0,i.useState)({overview:{totalRevenue:0,totalOrders:0,totalProducts:0,totalUsers:0,averageOrderValue:0},topProducts:[],topCategories:[],recentOrders:[],trends:{sales:[],users:[]}}),d=a.trends.sales.map(t=>({date:dR.CN.custom(t.date,"MMM dd","Invalid"),revenue:parseFloat(t.revenue)||0,orders:parseInt(t.orders)||0})),y=a.trends.users.map(t=>({date:dR.CN.custom(t.date,"MMM dd","Invalid"),users:parseInt(t.users)||0}));return(0,o.jsxs)("div",{className:"flex-1 space-y-4 p-4 md:p-8 pt-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between space-y-2",children:[o.jsx("h2",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),o.jsx("div",{className:"flex items-center space-x-2",children:o.jsx(u.Tabs,{defaultValue:r,onValueChange:n,children:(0,o.jsxs)(u.TabsList,{children:[o.jsx(u.TabsTrigger,{value:"7d",children:"7 Days"}),o.jsx(u.TabsTrigger,{value:"30d",children:"30 Days"}),o.jsx(u.TabsTrigger,{value:"90d",children:"90 Days"}),o.jsx(u.TabsTrigger,{value:"1y",children:"1 Year"})]})})})]}),(0,o.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,o.jsxs)(c.Zb,{children:[(0,o.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx(c.ll,{className:"text-sm font-medium",children:"Total Revenue"}),o.jsx(l.P.billing,{className:"h-4 w-4 text-muted-foreground"})]}),(0,o.jsxs)(c.aY,{children:[t?o.jsx(s.O,{className:"h-8 w-[100px]"}):(0,o.jsxs)("div",{className:"text-2xl font-bold",children:["$",a.overview.totalRevenue.toFixed(2)]}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"For the selected period"})]})]}),(0,o.jsxs)(c.Zb,{children:[(0,o.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx(c.ll,{className:"text-sm font-medium",children:"Orders"}),o.jsx(l.P.billing,{className:"h-4 w-4 text-muted-foreground"})]}),(0,o.jsxs)(c.aY,{children:[t?o.jsx(s.O,{className:"h-8 w-[60px]"}):o.jsx("div",{className:"text-2xl font-bold",children:a.overview.totalOrders}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Avg. $",a.overview.averageOrderValue.toFixed(2)," per order"]})]})]}),(0,o.jsxs)(c.Zb,{children:[(0,o.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx(c.ll,{className:"text-sm font-medium",children:"Products"}),o.jsx(l.P.media,{className:"h-4 w-4 text-muted-foreground"})]}),(0,o.jsxs)(c.aY,{children:[t?o.jsx(s.O,{className:"h-8 w-[60px]"}):o.jsx("div",{className:"text-2xl font-bold",children:a.overview.totalProducts}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Active in your store"})]})]}),(0,o.jsxs)(c.Zb,{children:[(0,o.jsxs)(c.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx(c.ll,{className:"text-sm font-medium",children:"Users"}),o.jsx(l.P.users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,o.jsxs)(c.aY,{children:[t?o.jsx(s.O,{className:"h-8 w-[60px]"}):o.jsx("div",{className:"text-2xl font-bold",children:a.overview.totalUsers}),o.jsx("p",{className:"text-xs text-muted-foreground",children:"Total registered users"})]})]})]}),(0,o.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-7",children:[(0,o.jsxs)(c.Zb,{className:"col-span-4",children:[(0,o.jsxs)(c.Ol,{children:[o.jsx(c.ll,{children:"Sales Trend"}),o.jsx(c.SZ,{children:"Revenue and order count over time"})]}),o.jsx(c.aY,{className:"h-[300px]",children:t?o.jsx("div",{className:"flex h-full items-center justify-center",children:o.jsx(l.P.spinner,{className:"h-8 w-8 animate-spin"})}):o.jsx(tx,{width:"100%",height:"100%",children:(0,o.jsxs)(db,{data:d,margin:{top:5,right:30,left:20,bottom:5},children:[o.jsx(dI,{strokeDasharray:"3 3"}),o.jsx(du,{dataKey:"date"}),o.jsx(dm,{yAxisId:"left"}),o.jsx(dm,{yAxisId:"right",orientation:"right"}),o.jsx(er,{}),o.jsx(hF,{yAxisId:"left",type:"monotone",dataKey:"revenue",stroke:"#8884d8",activeDot:{r:8},name:"Revenue ($)"}),o.jsx(hF,{yAxisId:"right",type:"monotone",dataKey:"orders",stroke:"#82ca9d",name:"Orders"})]})})})]}),(0,o.jsxs)(c.Zb,{className:"col-span-3",children:[(0,o.jsxs)(c.Ol,{children:[o.jsx(c.ll,{children:"User Growth"}),o.jsx(c.SZ,{children:"New user registrations over time"})]}),o.jsx(c.aY,{className:"h-[300px]",children:t?o.jsx("div",{className:"flex h-full items-center justify-center",children:o.jsx(l.P.spinner,{className:"h-8 w-8 animate-spin"})}):o.jsx(tx,{width:"100%",height:"100%",children:(0,o.jsxs)(dB,{data:y,margin:{top:5,right:30,left:20,bottom:5},children:[o.jsx(dI,{strokeDasharray:"3 3"}),o.jsx(du,{dataKey:"date"}),o.jsx(dm,{}),o.jsx(er,{}),o.jsx(fr,{dataKey:"users",fill:"#8884d8",name:"New Users"})]})})})]})]}),(0,o.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-7",children:[(0,o.jsxs)(c.Zb,{className:"col-span-4",children:[(0,o.jsxs)(c.Ol,{children:[o.jsx(c.ll,{children:"Recent Orders"}),o.jsx(c.SZ,{children:"Latest customer orders"})]}),o.jsx(c.aY,{children:t?o.jsx("div",{className:"space-y-2",children:[,,,,,].fill(0).map((t,e)=>o.jsx(s.O,{className:"h-16 w-full"},e))}):o.jsx("div",{className:"space-y-4",children:a.recentOrders.map(t=>(0,o.jsxs)("div",{className:"flex items-center justify-between space-x-4",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[o.jsx(p.qE,{children:o.jsx(p.Q5,{children:t.user?.name?.[0]||"U"})}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:t.user?.name||"Anonymous"}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:[dR.CN.custom(t.createdAt,"MMM dd, yyyy")," \xb7 $",t.total.toFixed(2)]})]})]}),o.jsx(f.C,{variant:"PAID"===t.status?"success":"secondary",children:t.status})]},t.id))})})]}),(0,o.jsxs)(c.Zb,{className:"col-span-3",children:[(0,o.jsxs)(c.Ol,{children:[o.jsx(c.ll,{children:"Top Products"}),o.jsx(c.SZ,{children:"Best selling products by quantity"})]}),o.jsx(c.aY,{children:t?o.jsx("div",{className:"space-y-2",children:[,,,,,].fill(0).map((t,e)=>o.jsx(s.O,{className:"h-12 w-full"},e))}):o.jsx("div",{className:"space-y-4",children:a.topProducts.map((t,e)=>o.jsx("div",{className:"flex items-center justify-between space-x-4",children:(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[o.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-primary/10",children:o.jsx("span",{className:"text-xs font-bold",children:e+1})}),(0,o.jsxs)("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:t.product?.name||"Unknown Product"}),(0,o.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t._sum.quantity," sold \xb7 $",parseFloat(t._sum.price).toFixed(2)," revenue"]})]})]})},t.productId))})})]})]})]})}},567:(t,e,r)=>{"use strict";r.d(e,{C:()=>c});var n=r(10326);r(17577);var o=r(79360),i=r(77863);let a=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function c({className:t,variant:e,...r}){return n.jsx("div",{className:(0,i.cn)(a({variant:e}),t),...r})}},33071:(t,e,r)=>{"use strict";r.d(e,{Ol:()=>c,SZ:()=>l,Zb:()=>a,aY:()=>s,eW:()=>f,ll:()=>u});var n=r(10326),o=r(17577),i=r(77863);let a=o.forwardRef(({className:t,...e},r)=>n.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...e}));a.displayName="Card";let c=o.forwardRef(({className:t,...e},r)=>n.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...e}));c.displayName="CardHeader";let u=o.forwardRef(({className:t,...e},r)=>n.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",t),...e}));u.displayName="CardTitle";let l=o.forwardRef(({className:t,...e},r)=>n.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",t),...e}));l.displayName="CardDescription";let s=o.forwardRef(({className:t,...e},r)=>n.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",t),...e}));s.displayName="CardContent";let f=o.forwardRef(({className:t,...e},r)=>n.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",t),...e}));f.displayName="CardFooter"},65304:(t,e,r)=>{"use strict";r.d(e,{O:()=>i});var n=r(10326),o=r(77863);function i({className:t,...e}){return n.jsx("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",t),...e})}},79210:(t,e,r)=>{"use strict";r.d(e,{Tabs:()=>c,TabsContent:()=>s,TabsList:()=>u,TabsTrigger:()=>l});var n=r(10326),o=r(17577),i=r(13239),a=r(77863);let c=i.fC,u=o.forwardRef(({className:t,...e},r)=>n.jsx(i.aV,{ref:r,className:(0,a.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...e}));u.displayName=i.aV.displayName;let l=o.forwardRef(({className:t,...e},r)=>n.jsx(i.xz,{ref:r,className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...e}));l.displayName=i.xz.displayName;let s=o.forwardRef(({className:t,...e},r)=>n.jsx(i.VY,{ref:r,className:(0,a.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...e}));s.displayName=i.VY.displayName},51028:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},44654:(t,e,r)=>{var n=r(7017)(r(39288),"DataView");t.exports=n},27513:(t,e,r)=>{var n=r(7392),o=r(29247),i=r(84190),a=r(66193),c=r(66681);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},58148:(t,e,r)=>{var n=r(38048),o=r(82142),i=r(83226),a=r(84001),c=r(31127);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},67926:(t,e,r)=>{var n=r(7017)(r(39288),"Map");t.exports=n},30095:(t,e,r)=>{var n=r(86487),o=r(93976),i=r(91053),a=r(29941),c=r(70144);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},9186:(t,e,r)=>{var n=r(7017)(r(39288),"Promise");t.exports=n},39746:(t,e,r)=>{var n=r(7017)(r(39288),"Set");t.exports=n},43484:(t,e,r)=>{var n=r(30095),o=r(51793),i=r(59191);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},82006:(t,e,r)=>{var n=r(58148),o=r(82795),i=r(9113),a=r(80934),c=r(68732),u=r(5525);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},76245:(t,e,r)=>{var n=r(39288).Symbol;t.exports=n},89377:(t,e,r)=>{var n=r(39288).Uint8Array;t.exports=n},35803:(t,e,r)=>{var n=r(7017)(r(39288),"WeakMap");t.exports=n},39137:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},12977:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},17536:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},34776:(t,e,r)=>{var n=r(44658);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},19544:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},77133:(t,e,r)=>{var n=r(84643),o=r(46148),i=r(32966),a=r(10750),c=r(19699),u=r(42191),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},29738:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},18939:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},83057:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},72375:t=>{t.exports=function(t){return t.split("")}},33646:(t,e,r)=>{var n=r(64111);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},15216:(t,e,r)=>{var n=r(99e3);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},57706:(t,e,r)=>{var n=r(47851),o=r(15271)(n);t.exports=o},78545:(t,e,r)=>{var n=r(57706);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},18401:(t,e,r)=>{var n=r(76871);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},47941:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},24354:(t,e,r)=>{var n=r(18939),o=r(62565);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},67917:(t,e,r)=>{var n=r(13012)();t.exports=n},47851:(t,e,r)=>{var n=r(67917),o=r(85865);t.exports=function(t,e){return t&&n(t,e,o)}},57305:(t,e,r)=>{var n=r(80204),o=r(1094);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},24003:(t,e,r)=>{var n=r(18939),o=r(32966);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},1534:(t,e,r)=>{var n=r(76245),o=r(34244),i=r(13390),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},20913:t=>{t.exports=function(t,e){return t>e}},50045:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},44658:(t,e,r)=>{var n=r(47941),o=r(22570),i=r(936);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},79574:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},88132:(t,e,r)=>{var n=r(80588),o=r(91380);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},80588:(t,e,r)=>{var n=r(82006),o=r(12317),i=r(99487),a=r(1958),c=r(74963),u=r(32966),l=r(10750),s=r(42191),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},7240:(t,e,r)=>{var n=r(82006),o=r(88132);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},22570:t=>{t.exports=function(t){return t!=t}},74104:(t,e,r)=>{var n=r(85586),o=r(15621),i=r(62880),a=r(81708),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},17633:(t,e,r)=>{var n=r(1534),o=r(99002),i=r(91380),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},44729:(t,e,r)=>{var n=r(71069),o=r(44723),i=r(24576),a=r(32966),c=r(94416);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},54190:(t,e,r)=>{var n=r(83314),o=r(66045),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},16409:t=>{t.exports=function(t,e){return t<e}},64635:(t,e,r)=>{var n=r(57706),o=r(17632);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},71069:(t,e,r)=>{var n=r(7240),o=r(35906),i=r(17106);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},44723:(t,e,r)=>{var n=r(88132),o=r(9459),i=r(1433),a=r(76958),c=r(61623),u=r(17106),l=r(1094);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},40620:(t,e,r)=>{var n=r(29738),o=r(57305),i=r(44729),a=r(64635),c=r(12704),u=r(96291),l=r(52463),s=r(24576),f=r(32966);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},78974:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},3243:(t,e,r)=>{var n=r(57305);t.exports=function(t){return function(e){return n(e,t)}}},97502:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},44563:(t,e,r)=>{var n=r(24576),o=r(21112),i=r(234);t.exports=function(t,e){return i(o(t,e,n),t+"")}},44578:(t,e,r)=>{var n=r(44347),o=r(99e3),i=r(24576),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},55804:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},64993:(t,e,r)=>{var n=r(57706);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},12704:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},84643:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},96115:(t,e,r)=>{var n=r(76245),o=r(29738),i=r(32966),a=r(76871),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},37192:(t,e,r)=>{var n=r(5587),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},96291:t=>{t.exports=function(t){return function(e){return t(e)}}},21213:(t,e,r)=>{var n=r(43484),o=r(34776),i=r(19544),a=r(5354),c=r(51512),u=r(78874);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},5354:t=>{t.exports=function(t,e){return t.has(e)}},80204:(t,e,r)=>{var n=r(32966),o=r(76958),i=r(31364),a=r(41029);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},23136:(t,e,r)=>{var n=r(55804);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},17523:(t,e,r)=>{var n=r(76871);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},52463:(t,e,r)=>{var n=r(17523);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},35987:(t,e,r)=>{var n=r(39288)["__core-js_shared__"];t.exports=n},15271:(t,e,r)=>{var n=r(17632);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},13012:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},64362:(t,e,r)=>{var n=r(23136),o=r(16888),i=r(78041),a=r(41029);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},59698:(t,e,r)=>{var n=r(44729),o=r(17632),i=r(85865);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},81592:(t,e,r)=>{var n=r(97502),o=r(47760),i=r(44148);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},51512:(t,e,r)=>{var n=r(39746),o=r(73525),i=r(78874),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},99e3:(t,e,r)=>{var n=r(7017),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},12317:(t,e,r)=>{var n=r(43484),o=r(83057),i=r(5354);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},99487:(t,e,r)=>{var n=r(76245),o=r(89377),i=r(64111),a=r(12317),c=r(59616),u=r(78874),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},1958:(t,e,r)=>{var n=r(67),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},37611:t=>{var e="object"==typeof global&&global&&global.Object===Object&&global;t.exports=e},67:(t,e,r)=>{var n=r(24003),o=r(26102),i=r(85865);t.exports=function(t){return n(t,i,o)}},4326:(t,e,r)=>{var n=r(24587);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},35906:(t,e,r)=>{var n=r(61623),o=r(85865);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},7017:(t,e,r)=>{var n=r(74104),o=r(12751);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},89083:(t,e,r)=>{var n=r(87181)(Object.getPrototypeOf,Object);t.exports=n},34244:(t,e,r)=>{var n=r(76245),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},26102:(t,e,r)=>{var n=r(17536),o=r(95252),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o;t.exports=c},74963:(t,e,r)=>{var n=r(44654),o=r(67926),i=r(9186),a=r(39746),c=r(35803),u=r(1534),l=r(81708),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},12751:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},23932:(t,e,r)=>{var n=r(80204),o=r(46148),i=r(32966),a=r(19699),c=r(99002),u=r(1094);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},16888:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},7392:(t,e,r)=>{var n=r(67193);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},29247:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},84190:(t,e,r)=>{var n=r(67193),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},66193:(t,e,r)=>{var n=r(67193),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},66681:(t,e,r)=>{var n=r(67193);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},62565:(t,e,r)=>{var n=r(76245),o=r(46148),i=r(32966),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},19699:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},47760:(t,e,r)=>{var n=r(64111),o=r(17632),i=r(19699),a=r(62880);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},76958:(t,e,r)=>{var n=r(32966),o=r(76871),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},24587:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},15621:(t,e,r)=>{var n=r(35987),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},83314:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},61623:(t,e,r)=>{var n=r(62880);t.exports=function(t){return t==t&&!n(t)}},38048:t=>{t.exports=function(){this.__data__=[],this.size=0}},82142:(t,e,r)=>{var n=r(33646),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},83226:(t,e,r)=>{var n=r(33646);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},84001:(t,e,r)=>{var n=r(33646);t.exports=function(t){return n(this.__data__,t)>-1}},31127:(t,e,r)=>{var n=r(33646);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},86487:(t,e,r)=>{var n=r(27513),o=r(58148),i=r(67926);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},93976:(t,e,r)=>{var n=r(4326);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},91053:(t,e,r)=>{var n=r(4326);t.exports=function(t){return n(this,t).get(t)}},29941:(t,e,r)=>{var n=r(4326);t.exports=function(t){return n(this,t).has(t)}},70144:(t,e,r)=>{var n=r(4326);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},59616:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},17106:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},85244:(t,e,r)=>{var n=r(97300);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},67193:(t,e,r)=>{var n=r(7017)(Object,"create");t.exports=n},66045:(t,e,r)=>{var n=r(87181)(Object.keys,Object);t.exports=n},7553:(t,e,r)=>{t=r.nmd(t);var n=r(37611),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},13390:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},87181:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},21112:(t,e,r)=>{var n=r(39137),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},39288:(t,e,r)=>{var n=r(37611),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},51793:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},59191:t=>{t.exports=function(t){return this.__data__.has(t)}},78874:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},234:(t,e,r)=>{var n=r(44578),o=r(72347)(n);t.exports=o},72347:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},82795:(t,e,r)=>{var n=r(58148);t.exports=function(){this.__data__=new n,this.size=0}},9113:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},80934:t=>{t.exports=function(t){return this.__data__.get(t)}},68732:t=>{t.exports=function(t){return this.__data__.has(t)}},5525:(t,e,r)=>{var n=r(58148),o=r(67926),i=r(30095);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},936:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},78041:(t,e,r)=>{var n=r(72375),o=r(16888),i=r(38582);t.exports=function(t){return o(t)?i(t):n(t)}},31364:(t,e,r)=>{var n=r(85244),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},1094:(t,e,r)=>{var n=r(76871),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},81708:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},5587:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},38582:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|")+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},44347:t=>{t.exports=function(t){return function(){return t}}},19788:(t,e,r)=>{var n=r(62880),o=r(22695),i=r(41309),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},64111:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},10853:(t,e,r)=>{var n=r(12977),o=r(78545),i=r(44729),a=r(32966),c=r(47760);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},9660:(t,e,r)=>{var n=r(59698)(r(38988));t.exports=n},38988:(t,e,r)=>{var n=r(47941),o=r(44729),i=r(57576),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},78352:(t,e,r)=>{var n=r(24354),o=r(59866);t.exports=function(t,e){return n(o(t,e),1)}},9459:(t,e,r)=>{var n=r(57305);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},1433:(t,e,r)=>{var n=r(50045),o=r(23932);t.exports=function(t,e){return null!=t&&o(t,e,n)}},24576:t=>{t.exports=function(t){return t}},46148:(t,e,r)=>{var n=r(79574),o=r(91380),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")};t.exports=u},32966:t=>{var e=Array.isArray;t.exports=e},17632:(t,e,r)=>{var n=r(85586),o=r(99002);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},99388:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},10750:(t,e,r)=>{t=r.nmd(t);var n=r(39288),o=r(89531),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},81711:(t,e,r)=>{var n=r(88132);t.exports=function(t,e){return n(t,e)}},85586:(t,e,r)=>{var n=r(1534),o=r(62880);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},99002:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},81719:(t,e,r)=>{var n=r(77717);t.exports=function(t){return n(t)&&t!=+t}},20119:t=>{t.exports=function(t){return null==t}},77717:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},62880:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},91380:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},36153:(t,e,r)=>{var n=r(1534),o=r(89083),i=r(91380),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},4891:(t,e,r)=>{var n=r(1534),o=r(32966),i=r(91380);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},76871:(t,e,r)=>{var n=r(1534),o=r(91380);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},42191:(t,e,r)=>{var n=r(17633),o=r(96291),i=r(7553),a=i&&i.isTypedArray,c=a?o(a):n;t.exports=c},85865:(t,e,r)=>{var n=r(77133),o=r(54190),i=r(17632);t.exports=function(t){return i(t)?n(t):o(t)}},90601:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},59866:(t,e,r)=>{var n=r(29738),o=r(44729),i=r(64635),a=r(32966);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},13880:(t,e,r)=>{var n=r(15216),o=r(47851),i=r(44729);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},31955:(t,e,r)=>{var n=r(18401),o=r(20913),i=r(24576);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},97300:(t,e,r)=>{var n=r(30095);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},52692:(t,e,r)=>{var n=r(18401),o=r(16409),i=r(24576);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},73525:t=>{t.exports=function(){}},22695:(t,e,r)=>{var n=r(39288);t.exports=function(){return n.Date.now()}},94416:(t,e,r)=>{var n=r(78974),o=r(3243),i=r(76958),a=r(1094);t.exports=function(t){return i(t)?n(a(t)):o(t)}},65680:(t,e,r)=>{var n=r(81592)();t.exports=n},34009:(t,e,r)=>{var n=r(83057),o=r(44729),i=r(64993),a=r(32966),c=r(47760);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},77529:(t,e,r)=>{var n=r(24354),o=r(40620),i=r(44563),a=r(47760),c=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=c},95252:t=>{t.exports=function(){return[]}},89531:t=>{t.exports=function(){return!1}},69450:(t,e,r)=>{var n=r(19788),o=r(62880);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},44148:(t,e,r)=>{var n=r(41309),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},57576:(t,e,r)=>{var n=r(44148);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},41309:(t,e,r)=>{var n=r(37192),o=r(62880),i=r(76871),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},41029:(t,e,r)=>{var n=r(96115);t.exports=function(t){return null==t?"":n(t)}},82511:(t,e,r)=>{var n=r(44729),o=r(21213);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},32009:(t,e,r)=>{var n=r(64362)("toUpperCase");t.exports=n},99899:(t,e,r)=>{"use strict";var n=r(56715);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},78439:(t,e,r)=>{t.exports=r(99899)()},56715:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},98255:(t,e)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),u=Symbol.for("react.context"),l=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case l:case u:case s:case d:case h:case c:return t;default:return e}}case n:return e}}}(t)===o}},29507:(t,e,r)=>{"use strict";t.exports=r(98255)},66136:(t,e,r)=>{"use strict";r.r(e),r.d(e,{$$typeof:()=>a,__esModule:()=>i,default:()=>c});var n=r(68570);let o=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\page.tsx`),{__esModule:i,$$typeof:a}=o;o.default;let c=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\page.tsx#default`)},58585:(t,e,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(e,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(e,{redirect:function(){return n.redirect}})},61085:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),o=r(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},16399:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let t=Error(r);throw t.digest=r,t}function o(t){return"object"==typeof t&&null!==t&&"digest"in t&&t.digest===r}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},8586:(t,e)=>{"use strict";var r;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(t){t[t.SeeOther=303]="SeeOther",t[t.TemporaryRedirect=307]="TemporaryRedirect",t[t.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},83953:(t,e,r)=>{"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{RedirectType:function(){return n},getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return p},isRedirectError:function(){return f},permanentRedirect:function(){return s},redirect:function(){return l}});let o=r(54580),i=r(72934),a=r(8586),c="NEXT_REDIRECT";function u(t,e,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(c);n.digest=c+";"+e+";"+t+";"+r+";";let i=o.requestAsyncStorage.getStore();return i&&(n.mutableCookies=i.mutableCookies),n}function l(t,e){void 0===e&&(e="replace");let r=i.actionAsyncStorage.getStore();throw u(t,e,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function s(t,e){void 0===e&&(e="replace");let r=i.actionAsyncStorage.getStore();throw u(t,e,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function f(t){if("object"!=typeof t||null===t||!("digest"in t)||"string"!=typeof t.digest)return!1;let[e,r,n,o]=t.digest.split(";",4),i=Number(o);return e===c&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(i)&&i in a.RedirectStatusCode}function p(t){return f(t)?t.digest.split(";",3)[2]:null}function h(t){if(!f(t))throw Error("Not a redirect error");return t.digest.split(";",2)[1]}function d(t){if(!f(t))throw Error("Not a redirect error");return Number(t.digest.split(";",4)[3])}(function(t){t.push="push",t.replace="replace"})(n||(n={})),("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,3239,4824,7123],()=>r(1435));module.exports=n})();