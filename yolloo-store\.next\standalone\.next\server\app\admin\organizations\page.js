(()=>{var e={};e.id=8815,e.ids=[8815],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},9938:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(69954),s(85460),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d=["",{children:["admin",{children:["organizations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,69954)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\organizations\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\organizations\\page.tsx"],u="/admin/organizations/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/organizations/page",pathname:"/admin/organizations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29851:(e,t,s)=>{Promise.resolve().then(s.bind(s,8589))},8589:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(10326),a=s(17577),i=s(35047),n=s(44099),o=s(83855),l=s(77506),d=s(88307),c=s(21405),u=s(90772),m=s(33071),p=s(54432),x=s(85999),f=s(74990),h=s(567),g=s(62288),j=s(31048),b=s(87673),y=s(74723),N=s(27256),v=s(74064),w=s(90434),R=s(34474),C=s(15940),_=s(90670);let S=N.z.object({name:N.z.string().min(3,"Name must be at least 3 characters"),description:N.z.string().optional(),logo:N.z.string().optional(),commissionRate:N.z.number().min(0).max(1),discountRate:N.z.number().min(0).max(1),adminEmail:N.z.string().email("Invalid email address")});function z(){(0,i.useRouter)();let[e,t]=(0,a.useState)(!0),[s,N]=(0,a.useState)([]),[z,E]=(0,a.useState)(!1),[P,k]=(0,a.useState)(!1),[O,q]=(0,a.useState)(""),[M,T]=(0,a.useState)("all"),[Z,F]=(0,a.useState)(1),[A,D]=(0,a.useState)(1),[I,V]=(0,a.useState)(0),[B,U]=(0,a.useState)(""),[L,$]=(0,a.useState)([]),[G,Y]=(0,a.useState)(!1),[H,Q]=(0,a.useState)(""),{register:W,handleSubmit:X,formState:{errors:K},reset:J,setValue:ee,watch:et}=(0,y.cI)({resolver:(0,v.F)(S),defaultValues:{commissionRate:.1,discountRate:.05}});et("adminEmail");let es=e=>{ee("adminEmail",e.email),Q(e.email),U(""),$([])},er=async e=>{try{k(!0);let t=await n.Z.post("/api/admin/organizations",e);N(e=>[t.data,...e]),x.A.success("Organization created successfully"),E(!1),J()}catch(e){console.error("Error creating organization:",e),x.A.error("Failed to create organization")}finally{k(!1)}},ea=e=>{switch(e){case"ACTIVE":return r.jsx(h.C,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Active"});case"INACTIVE":return r.jsx(h.C,{variant:"outline",className:"bg-yellow-50 text-yellow-700 border-yellow-200",children:"Inactive"});case"SUSPENDED":return r.jsx(h.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200",children:"Suspended"});default:return r.jsx(h.C,{variant:"outline",children:e})}};return(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold",children:"Organizations"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage affiliate organizations and their members"})]}),(0,r.jsxs)(g.Vq,{open:z,onOpenChange:E,children:[r.jsx(g.hg,{asChild:!0,children:(0,r.jsxs)(u.Button,{children:[r.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Create Organization"]})}),r.jsx(g.cZ,{className:"sm:max-w-[550px]",children:(0,r.jsxs)("form",{onSubmit:X(er),children:[(0,r.jsxs)(g.fK,{children:[r.jsx(g.$N,{children:"Create New Organization"}),r.jsx(g.Be,{children:"Create a new affiliate organization and assign an admin."})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(j._,{htmlFor:"name",children:"Organization Name"}),r.jsx(p.I,{id:"name",placeholder:"Enter organization name",...W("name")}),K.name&&r.jsx("p",{className:"text-sm text-red-500",children:K.name.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(j._,{htmlFor:"description",children:"Description"}),r.jsx(b.g,{id:"description",placeholder:"Enter organization description (optional)",...W("description")})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(j._,{htmlFor:"logo",children:"Logo URL"}),r.jsx(p.I,{id:"logo",placeholder:"Enter logo URL (optional)",...W("logo")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(j._,{htmlFor:"commissionRate",children:"Commission Rate"}),r.jsx(p.I,{id:"commissionRate",type:"number",step:"0.01",min:"0",max:"1",placeholder:"0.10",...W("commissionRate",{valueAsNumber:!0})}),K.commissionRate&&r.jsx("p",{className:"text-sm text-red-500",children:K.commissionRate.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(j._,{htmlFor:"discountRate",children:"Discount Rate"}),r.jsx(p.I,{id:"discountRate",type:"number",step:"0.01",min:"0",max:"1",placeholder:"0.05",...W("discountRate",{valueAsNumber:!0})}),K.discountRate&&r.jsx("p",{className:"text-sm text-red-500",children:K.discountRate.message})]})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(j._,{htmlFor:"adminEmail",children:"Admin Email"}),r.jsx("div",{className:"relative",children:H?(0,r.jsxs)("div",{className:"flex items-center border rounded-md p-2",children:[r.jsx("span",{className:"flex-1",children:H}),r.jsx(u.Button,{type:"button",variant:"ghost",size:"sm",onClick:()=>{ee("adminEmail",""),Q(""),U("")},className:"h-5 w-5 p-0",children:"\xd7"})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(p.I,{type:"text",placeholder:"Search for users by email or name",value:B,onChange:e=>U(e.target.value)}),r.jsx(p.I,{type:"hidden",...W("adminEmail")}),L.length>0&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-auto",children:L.map(e=>(0,r.jsxs)("div",{className:"p-2 hover:bg-gray-100 cursor-pointer flex items-center",onClick:()=>es(e),children:[r.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-xs mr-2 overflow-hidden",children:e.image?r.jsx("img",{src:e.image,alt:e.name||e.email,className:"w-full h-full object-cover"}):e.name?.substring(0,2)||e.email.substring(0,2)}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:e.name||"Unnamed User"}),r.jsx("div",{className:"text-xs text-gray-500",children:e.email})]})]},e.id))}),G&&r.jsx("div",{className:"absolute right-3 top-2.5",children:r.jsx(l.Z,{className:"h-4 w-4 animate-spin"})})]})}),K.adminEmail&&r.jsx("p",{className:"text-sm text-red-500",children:K.adminEmail.message}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"This user will be assigned as the organization admin. They must have an existing account."})]})]}),(0,r.jsxs)(g.cN,{children:[r.jsx(u.Button,{type:"button",variant:"outline",onClick:()=>E(!1),children:"Cancel"}),(0,r.jsxs)(u.Button,{type:"submit",disabled:P,children:[P&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Organization"]})]})]})})]})]}),(0,r.jsxs)(m.Zb,{className:"mb-8",children:[(0,r.jsxs)(m.Ol,{children:[r.jsx(m.ll,{children:"Search & Filter"}),r.jsx(m.SZ,{children:"Find organizations by name, code, or status"})]}),r.jsx(m.aY,{children:(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),F(1)},className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[r.jsx(d.Z,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),r.jsx(p.I,{placeholder:"Search by name or code...",className:"pl-9",value:O,onChange:e=>q(e.target.value)})]}),r.jsx("div",{className:"w-full md:w-48",children:(0,r.jsxs)(R.Ph,{value:M,onValueChange:e=>{T(e),F(1)},children:[r.jsx(R.i4,{children:r.jsx(R.ki,{placeholder:"Filter by status"})}),(0,r.jsxs)(R.Bw,{children:[r.jsx(R.Ql,{value:"all",children:"All Statuses"}),r.jsx(R.Ql,{value:"ACTIVE",children:"Active"}),r.jsx(R.Ql,{value:"INACTIVE",children:"Inactive"}),r.jsx(R.Ql,{value:"SUSPENDED",children:"Suspended"})]})]})}),(0,r.jsxs)(u.Button,{type:"submit",className:"md:w-auto",children:[r.jsx(d.Z,{className:"mr-2 h-4 w-4"}),"Filter"]}),(0,r.jsxs)(u.Button,{type:"button",variant:"outline",onClick:()=>{q(""),T("all"),F(1)},className:"md:w-auto",children:[r.jsx(c.Z,{className:"mr-2 h-4 w-4"}),"Reset"]})]})})]}),(0,r.jsxs)(m.Zb,{children:[(0,r.jsxs)(m.Ol,{children:[r.jsx(m.ll,{children:"Organizations"}),(0,r.jsxs)(m.SZ,{children:["Showing ",s?.length||0," of ",I," organizations"]})]}),r.jsx(m.aY,{children:e?r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx(l.Z,{className:"h-8 w-8 animate-spin text-primary"})}):s&&0!==s.length?r.jsx("div",{className:"rounded-md border",children:(0,r.jsxs)(C.iA,{children:[r.jsx(C.xD,{children:(0,r.jsxs)(C.SC,{children:[r.jsx(C.ss,{children:"Organization"}),r.jsx(C.ss,{children:"Code"}),r.jsx(C.ss,{children:"Members"}),r.jsx(C.ss,{children:"Commission"}),r.jsx(C.ss,{children:"Earnings"}),r.jsx(C.ss,{children:"Status"}),r.jsx(C.ss,{children:"Created"}),r.jsx(C.ss,{className:"text-right",children:"Actions"})]})}),r.jsx(C.RM,{children:(s||[]).map(e=>(0,r.jsxs)(C.SC,{children:[r.jsx(C.pj,{className:"font-medium",children:r.jsx(w.default,{href:`/admin/organizations/${e.id}`,className:"hover:underline",children:e.name})}),r.jsx(C.pj,{children:e.code}),r.jsx(C.pj,{children:e._count?.members||0}),(0,r.jsxs)(C.pj,{children:[(100*e.commissionRate).toFixed(0),"%"]}),(0,r.jsxs)(C.pj,{children:["$",e.totalEarnings.toFixed(2)]}),r.jsx(C.pj,{children:ea(e.status)}),r.jsx(C.pj,{children:(0,f.WU)(new Date(e.createdAt),"MMM d, yyyy")}),r.jsx(C.pj,{className:"text-right",children:r.jsx(u.Button,{asChild:!0,variant:"ghost",size:"sm",children:r.jsx(w.default,{href:`/admin/organizations/${e.id}`,children:"View"})})})]},e.id))})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-64 bg-muted/50 rounded-lg border border-dashed",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"No organizations found"}),(0,r.jsxs)(u.Button,{onClick:()=>E(!0),className:"mt-2",variant:"outline",size:"sm",children:[r.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Create Organization"]})]})}),(0,r.jsxs)(m.eW,{className:"flex justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",Math.min((Z-1)*10+1,I)," to ",Math.min(10*Z,I)," of ",I," organizations"]}),r.jsx(_.tl,{children:(0,r.jsxs)(_.ng,{children:[r.jsx(_.nt,{children:r.jsx(_.dN,{href:"#",onClick:e=>{e.preventDefault(),F(e=>Math.max(e-1,1))},className:1===Z?"pointer-events-none opacity-50":"cursor-pointer"})}),Array.from({length:Math.min(5,A)},(e,t)=>{let s=t+1,a=1===s||s===A||s>=Z-1&&s<=Z+1;return a||2!==s?a||s!==A-1?a?r.jsx(_.nt,{children:r.jsx(_.kN,{href:"#",isActive:Z===s,onClick:e=>{e.preventDefault(),F(s)},children:s})},s):null:r.jsx(_.nt,{children:r.jsx(_.Dj,{})},"ellipsis-end"):r.jsx(_.nt,{children:r.jsx(_.Dj,{})},"ellipsis-start")}),r.jsx(_.nt,{children:r.jsx(_.$0,{href:"#",onClick:e=>{e.preventDefault(),F(e=>Math.min(e+1,A))},className:Z===A?"pointer-events-none opacity-50":"cursor-pointer"})})]})})]})]})]})}},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>o});var r=s(10326);s(17577);var a=s(79360),i=s(77863);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return r.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},33071:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>l});var r=s(10326),a=s(17577),i=s(77863);let n=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let o=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},62288:(e,t,s)=>{"use strict";s.d(t,{$N:()=>f,Be:()=>h,Vq:()=>l,cN:()=>x,cZ:()=>m,fK:()=>p,hg:()=>d});var r=s(10326),a=s(17577),i=s(11123),n=s(94019),o=s(77863);let l=i.fC,d=i.xz,c=i.h_;i.x8;let u=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aV,{ref:s,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=i.aV.displayName;let m=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(c,{children:[r.jsx(u,{}),(0,r.jsxs)(i.VY,{ref:a,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,r.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.VY.displayName;let p=({className:e,...t})=>r.jsx("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let x=({className:e,...t})=>r.jsx("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});x.displayName="DialogFooter";let f=a.forwardRef(({className:e,...t},s)=>r.jsx(i.Dx,{ref:s,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));f.displayName=i.Dx.displayName;let h=a.forwardRef(({className:e,...t},s)=>r.jsx(i.dk,{ref:s,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=i.dk.displayName},31048:(e,t,s)=>{"use strict";s.d(t,{_:()=>d});var r=s(10326),a=s(17577),i=s(34478),n=s(79360),o=s(77863);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,o.cn)(l(),e),...t}));d.displayName=i.f.displayName},90670:(e,t,s)=>{"use strict";s.d(t,{$0:()=>h,Dj:()=>g,dN:()=>f,kN:()=>x,ng:()=>m,nt:()=>p,tl:()=>u});var r=s(10326),a=s(17577),i=s(11890),n=s(39183),o=s(15919),l=s(77863),d=s(90772),c=s(90434);let u=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,l.cn)("flex w-full flex-col items-center gap-4 sm:flex-row sm:gap-6",e),...t}));u.displayName="Pagination";let m=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,l.cn)("flex flex-wrap items-center gap-1",e),...t}));m.displayName="PaginationContent";let p=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,l.cn)("",e),...t}));p.displayName="PaginationItem";let x=({className:e,isActive:t,size:s="icon",...a})=>r.jsx(c.default,{"aria-current":t?"page":void 0,className:(0,l.cn)((0,d.d)({variant:t?"outline":"ghost",size:s}),e),...a});x.displayName="PaginationLink";let f=({className:e,...t})=>(0,r.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,l.cn)("gap-1 pl-2.5",e),...t,children:[r.jsx(i.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Previous"})]});f.displayName="PaginationPrevious";let h=({className:e,...t})=>(0,r.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,l.cn)("gap-1 pr-2.5",e),...t,children:[r.jsx("span",{children:"Next"}),r.jsx(n.Z,{className:"h-4 w-4"})]});h.displayName="PaginationNext";let g=({className:e,...t})=>(0,r.jsxs)("span",{"aria-hidden":!0,className:(0,l.cn)("flex h-9 w-9 items-center justify-center",e),...t,children:[r.jsx(o.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"More pages"})]});g.displayName="PaginationEllipsis"},34474:(e,t,s)=>{"use strict";s.d(t,{Bw:()=>m,Ph:()=>d,Ql:()=>p,i4:()=>u,ki:()=>c});var r=s(10326),a=s(17577),i=s(18792),n=s(941),o=s(32933),l=s(77863);let d=i.fC;i.ZA;let c=i.B4,u=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.xz,{ref:a,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:[t,r.jsx(i.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.xz.displayName;let m=a.forwardRef(({className:e,children:t,position:s="popper",...a},n)=>r.jsx(i.h_,{children:r.jsx(i.VY,{ref:n,className:(0,l.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:r.jsx(i.l_,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));m.displayName=i.VY.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(i.__,{ref:s,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.__.displayName;let p=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.ck,{ref:a,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(o.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:t})]}));p.displayName=i.ck.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(i.Z0,{ref:s,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.Z0.displayName},15940:(e,t,s)=>{"use strict";s.d(t,{RM:()=>l,SC:()=>d,iA:()=>n,pj:()=>u,ss:()=>c,xD:()=>o});var r=s(10326),a=s(17577),i=s(77863);let n=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...t})}));n.displayName="Table";let o=a.forwardRef(({className:e,...t},s)=>r.jsx("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",a.forwardRef(({className:e,...t},s)=>r.jsx("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("th",{ref:s,className:(0,i.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));c.displayName="TableHead";let u=a.forwardRef(({className:e,...t},s)=>r.jsx("td",{ref:s,className:(0,i.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));u.displayName="TableCell",a.forwardRef(({className:e,...t},s)=>r.jsx("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},87673:(e,t,s)=>{"use strict";s.d(t,{g:()=>n});var r=s(10326),a=s(17577),i=s(77863);let n=a.forwardRef(({className:e,...t},s)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Textarea"},15919:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62881).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},21405:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},69954:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>o});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\organizations\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let o=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\organizations\page.tsx#default`)},58585:(e,t,s)=>{"use strict";var r=s(61085);s.o(r,"notFound")&&s.d(t,{notFound:function(){return r.notFound}}),s.o(r,"redirect")&&s.d(t,{redirect:function(){return r.redirect}})},61085:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{ReadonlyURLSearchParams:function(){return n},RedirectType:function(){return r.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return r.permanentRedirect},redirect:function(){return r.redirect}});let r=s(83953),a=s(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class n extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{isNotFoundError:function(){return a},notFound:function(){return r}});let s="NEXT_NOT_FOUND";function r(){let e=Error(s);throw e.digest=s,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===s}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return s}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,s)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{RedirectType:function(){return r},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return x},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return m},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return d}});let a=s(54580),i=s(72934),n=s(8586),o="NEXT_REDIRECT";function l(e,t,s){void 0===s&&(s=n.RedirectStatusCode.TemporaryRedirect);let r=Error(o);r.digest=o+";"+t+";"+e+";"+s+";";let i=a.requestAsyncStorage.getStore();return i&&(r.mutableCookies=i.mutableCookies),r}function d(e,t){void 0===t&&(t="replace");let s=i.actionAsyncStorage.getStore();throw l(e,t,(null==s?void 0:s.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let s=i.actionAsyncStorage.getStore();throw l(e,t,(null==s?void 0:s.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,s,r,a]=e.digest.split(";",4),i=Number(a);return t===o&&("replace"===s||"push"===s)&&"string"==typeof r&&!isNaN(i)&&i in n.RedirectStatusCode}function m(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function x(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,6908,4099,4824,7123],()=>s(9938));module.exports=r})();