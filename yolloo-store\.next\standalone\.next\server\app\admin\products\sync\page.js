(()=>{var e={};e.id=3007,e.ids=[3007],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},81733:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>f,tree:()=>c}),r(73635),r(75718),r(85460),r(89090),r(26083),r(35866);var n=r(23191),s=r(88716),i=r(37922),o=r.n(i),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["admin",{children:["products",{children:["sync",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73635)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\sync\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75718)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\sync\\page.tsx"],u="/admin/products/sync/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/admin/products/sync/page",pathname:"/admin/products/sync",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},84784:(e,t,r)=>{Promise.resolve().then(r.bind(r,61712))},35303:()=>{},61712:(e,t,r)=>{"use strict";r.d(t,{ProductSyncPanel:()=>ey});var n=r(10326),s=r(17577),i=r(90772),o=r(33071),a=r(68762),l=r(54432),c=r(31048),d=r(79210),u=r(43273),p=r(69508),f=r(83855),m=r(62881);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,m.Z)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var x=r(94019);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,m.Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var y=r(41291);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,m.Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var b=r(77506);process.env.ODOO_ADDRESS,process.env.ODOO_CHANNEL_ID,process.env.ODOO_CHANNEL_LANGUAGE,process.env.ODOO_AUTH_SECRET,process.env.ODOO_SIGN_METHOD;let j=["esim","data","effective_date","external_data","other","esim-card"];var w=r(567),N=r(81563),S=r(62288);function C(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function E(...e){return s.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=C(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():C(e[t],null)}}}}(...e),e)}r(60962);var R=Symbol("radix.slottable");function P(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===R}var T=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let r,n;let{children:i,...o}=e,a=E(s.isValidElement(i)?(r=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning?i.ref:(r=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning?i.props.ref:i.props.ref||i.ref:void 0,t);if(s.isValidElement(i)){let e=function(e,t){let r={...t};for(let n in t){let s=e[n],i=t[n];/^on[A-Z]/.test(n)?s&&i?r[n]=(...e)=>{let t=i(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...i}:"className"===n&&(r[n]=[s,i].filter(Boolean).join(" "))}return{...e,...r}}(o,i.props);return i.type!==s.Fragment&&(e.ref=a),s.cloneElement(i,e)}return s.Children.count(i)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:i,...o}=e,a=s.Children.toArray(i),l=a.find(P);if(l){let e=l.props.children,i=a.map(t=>t!==l?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...o,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,n.jsx)(t,{...o,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),i=s.forwardRef((e,s)=>{let{asChild:i,...o}=e,a=i?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a,{...o,ref:s})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),_=globalThis?.document?s.useLayoutEffect:()=>{},O=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,i]=s.useState(),o=s.useRef(null),a=s.useRef(e),l=s.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>r[e][t]??e,t));return s.useEffect(()=>{let e=k(o.current);l.current="mounted"===c?e:"none"},[c]),_(()=>{let t=o.current,r=a.current;if(r!==e){let n=l.current,s=k(t);e?d("MOUNT"):"none"===s||t?.display==="none"?d("UNMOUNT"):r&&n!==s?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),_(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let s=k(o.current).includes(r.animationName);if(r.target===n&&s&&(d("ANIMATION_END"),!a.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},s=e=>{e.target===n&&(l.current=k(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:s.useCallback(e=>{o.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof r?r({present:n.isPresent}):s.Children.only(r),o=E(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||n.isPresent?s.cloneElement(i,{ref:o}):null};function k(e){return e?.animationName||"none"}function A(e){let t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...e)=>t.current?.(...e),[])}O.displayName="Presence";var D=s.createContext(void 0);function L(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var M="ScrollArea",[q,z]=function(e,t=[]){let r=[],i=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return i.scopeName=e,[function(t,i){let o=s.createContext(i),a=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,c=r?.[e]?.[a]||o,d=s.useMemo(()=>l,Object.values(l));return(0,n.jsx)(c.Provider,{value:d,children:i})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[a]||o,c=s.useContext(l);if(c)return c;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let s=r(e)[`__scope${n}`];return{...t,...s}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(i,...t)]}(M),[I,U]=q(M),F=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:o,scrollHideDelay:a=600,...l}=e,[c,d]=s.useState(null),[u,p]=s.useState(null),[f,m]=s.useState(null),[h,x]=s.useState(null),[v,y]=s.useState(null),[g,b]=s.useState(0),[j,w]=s.useState(0),[N,S]=s.useState(!1),[C,R]=s.useState(!1),P=E(t,e=>d(e)),_=function(e){let t=s.useContext(D);return e||t||"ltr"}(o);return(0,n.jsx)(I,{scope:r,type:i,dir:_,scrollHideDelay:a,scrollArea:c,viewport:u,onViewportChange:p,content:f,onContentChange:m,scrollbarX:h,onScrollbarXChange:x,scrollbarXEnabled:N,onScrollbarXEnabledChange:S,scrollbarY:v,onScrollbarYChange:y,scrollbarYEnabled:C,onScrollbarYEnabledChange:R,onCornerWidthChange:b,onCornerHeightChange:w,children:(0,n.jsx)(T.div,{dir:_,...l,ref:P,style:{position:"relative","--radix-scroll-area-corner-width":g+"px","--radix-scroll-area-corner-height":j+"px",...e.style}})})});F.displayName=M;var Z="ScrollAreaViewport",V=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:o,...a}=e,l=U(Z,r),c=E(t,s.useRef(null),l.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,n.jsx)(T.div,{"data-radix-scroll-area-viewport":"",...a,ref:c,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:l.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});V.displayName=Z;var W="ScrollAreaScrollbar",X=s.forwardRef((e,t)=>{let{forceMount:r,...i}=e,o=U(W,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=o,c="horizontal"===e.orientation;return s.useEffect(()=>(c?a(!0):l(!0),()=>{c?a(!1):l(!1)}),[c,a,l]),"hover"===o.type?(0,n.jsx)(H,{...i,ref:t,forceMount:r}):"scroll"===o.type?(0,n.jsx)($,{...i,ref:t,forceMount:r}):"auto"===o.type?(0,n.jsx)(B,{...i,ref:t,forceMount:r}):"always"===o.type?(0,n.jsx)(Y,{...i,ref:t}):null});X.displayName=W;var H=s.forwardRef((e,t)=>{let{forceMount:r,...i}=e,o=U(W,e.__scopeScrollArea),[a,l]=s.useState(!1);return s.useEffect(()=>{let e=o.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),l(!0)},n=()=>{t=window.setTimeout(()=>l(!1),o.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[o.scrollArea,o.scrollHideDelay]),(0,n.jsx)(O,{present:r||a,children:(0,n.jsx)(B,{"data-state":a?"visible":"hidden",...i,ref:t})})}),$=s.forwardRef((e,t)=>{var r,i;let{forceMount:o,...a}=e,l=U(W,e.__scopeScrollArea),c="horizontal"===e.orientation,d=ef(()=>p("SCROLL_END"),100),[u,p]=(r="hidden",i={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},s.useReducer((e,t)=>i[e][t]??e,r));return s.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>p("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,l.scrollHideDelay,p]),s.useEffect(()=>{let e=l.viewport,t=c?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(p("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,c,p,d]),(0,n.jsx)(O,{present:o||"hidden"!==u,children:(0,n.jsx)(Y,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:L(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:L(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),B=s.forwardRef((e,t)=>{let r=U(W,e.__scopeScrollArea),{forceMount:i,...o}=e,[a,l]=s.useState(!1),c="horizontal"===e.orientation,d=ef(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;l(c?e:t)}},10);return em(r.viewport,d),em(r.content,d),(0,n.jsx)(O,{present:i||a,children:(0,n.jsx)(Y,{"data-state":a?"visible":"hidden",...o,ref:t})})}),Y=s.forwardRef((e,t)=>{let{orientation:r="vertical",...i}=e,o=U(W,e.__scopeScrollArea),a=s.useRef(null),l=s.useRef(0),[c,d]=s.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=el(c.viewport,c.content),p={...i,sizes:c,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,r,n="ltr"){let s=ec(r),i=t||s/2,o=r.scrollbar.paddingStart+i,a=r.scrollbar.size-r.scrollbar.paddingEnd-(s-i),l=r.content-r.viewport;return eu([o,a],"ltr"===n?[0,l]:[-1*l,0])(e)}(e,l.current,c,t)}return"horizontal"===r?(0,n.jsx)(G,{...p,ref:t,onThumbPositionChange:()=>{if(o.viewport&&a.current){let e=ed(o.viewport.scrollLeft,c,o.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=f(e,o.dir))}}):"vertical"===r?(0,n.jsx)(J,{...p,ref:t,onThumbPositionChange:()=>{if(o.viewport&&a.current){let e=ed(o.viewport.scrollTop,c);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=f(e))}}):null}),G=s.forwardRef((e,t)=>{let{sizes:r,onSizesChange:i,...o}=e,a=U(W,e.__scopeScrollArea),[l,c]=s.useState(),d=s.useRef(null),u=E(t,d,a.onScrollbarXChange);return s.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(ee,{"data-orientation":"horizontal",...o,ref:u,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":ec(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&l&&i({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:ea(l.paddingLeft),paddingEnd:ea(l.paddingRight)}})}})}),J=s.forwardRef((e,t)=>{let{sizes:r,onSizesChange:i,...o}=e,a=U(W,e.__scopeScrollArea),[l,c]=s.useState(),d=s.useRef(null),u=E(t,d,a.onScrollbarYChange);return s.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(ee,{"data-orientation":"vertical",...o,ref:u,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":ec(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&l&&i({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:ea(l.paddingTop),paddingEnd:ea(l.paddingBottom)}})}})}),[K,Q]=q(W),ee=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:o,onThumbChange:a,onThumbPointerUp:l,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:u,onWheelScroll:p,onResize:f,...m}=e,h=U(W,r),[x,v]=s.useState(null),y=E(t,e=>v(e)),g=s.useRef(null),b=s.useRef(""),j=h.viewport,w=i.content-i.viewport,N=A(p),S=A(d),C=ef(f,10);function R(e){g.current&&u({x:e.clientX-g.current.left,y:e.clientY-g.current.top})}return s.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&N(e,w)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[j,x,w,N]),s.useEffect(S,[i,S]),em(x,C),em(h.content,C),(0,n.jsx)(K,{scope:r,scrollbar:x,hasThumb:o,onThumbChange:A(a),onThumbPointerUp:A(l),onThumbPositionChange:S,onThumbPointerDown:A(c),children:(0,n.jsx)(T.div,{...m,ref:y,style:{position:"absolute",...m.style},onPointerDown:L(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),g.current=x.getBoundingClientRect(),b.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",h.viewport&&(h.viewport.style.scrollBehavior="auto"),R(e))}),onPointerMove:L(e.onPointerMove,R),onPointerUp:L(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=b.current,h.viewport&&(h.viewport.style.scrollBehavior=""),g.current=null})})})}),et="ScrollAreaThumb",er=s.forwardRef((e,t)=>{let{forceMount:r,...s}=e,i=Q(et,e.__scopeScrollArea);return(0,n.jsx)(O,{present:r||i.hasThumb,children:(0,n.jsx)(en,{ref:t,...s})})}),en=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...o}=e,a=U(et,r),l=Q(et,r),{onThumbPositionChange:c}=l,d=E(t,e=>l.onThumbChange(e)),u=s.useRef(void 0),p=ef(()=>{u.current&&(u.current(),u.current=void 0)},100);return s.useEffect(()=>{let e=a.viewport;if(e){let t=()=>{if(p(),!u.current){let t=ep(e,c);u.current=t,c()}};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[a.viewport,p,c]),(0,n.jsx)(T.div,{"data-state":l.hasThumb?"visible":"hidden",...o,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:L(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;l.onThumbPointerDown({x:r,y:n})}),onPointerUp:L(e.onPointerUp,l.onThumbPointerUp)})});er.displayName=et;var es="ScrollAreaCorner",ei=s.forwardRef((e,t)=>{let r=U(es,e.__scopeScrollArea),s=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&s?(0,n.jsx)(eo,{...e,ref:t}):null});ei.displayName=es;var eo=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,o=U(es,r),[a,l]=s.useState(0),[c,d]=s.useState(0),u=!!(a&&c);return em(o.scrollbarX,()=>{let e=o.scrollbarX?.offsetHeight||0;o.onCornerHeightChange(e),d(e)}),em(o.scrollbarY,()=>{let e=o.scrollbarY?.offsetWidth||0;o.onCornerWidthChange(e),l(e)}),u?(0,n.jsx)(T.div,{...i,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===o.dir?0:void 0,left:"rtl"===o.dir?0:void 0,bottom:0,...e.style}}):null});function ea(e){return e?parseInt(e,10):0}function el(e,t){let r=e/t;return isNaN(r)?0:r}function ec(e){let t=el(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function ed(e,t,r="ltr"){let n=ec(t),s=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-s,o=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,o]:[-1*o,0]);return eu([0,o],[0,i-n])(a)}function eu(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var ep=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function s(){let i={left:e.scrollLeft,top:e.scrollTop},o=r.left!==i.left,a=r.top!==i.top;(o||a)&&t(),r=i,n=window.requestAnimationFrame(s)}(),()=>window.cancelAnimationFrame(n)};function ef(e,t){let r=A(e),n=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(n.current),[]),s.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function em(e,t){let r=A(t);_(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var eh=r(77863);let ex=s.forwardRef(({className:e,children:t,...r},s)=>(0,n.jsxs)(F,{ref:s,className:(0,eh.cn)("relative overflow-hidden",e),...r,children:[n.jsx(V,{className:"h-full w-full rounded-[inherit]",children:t}),n.jsx(ev,{}),n.jsx(ei,{})]}));ex.displayName=F.displayName;let ev=s.forwardRef(({className:e,orientation:t="vertical",...r},s)=>n.jsx(X,{ref:s,orientation:t,className:(0,eh.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 border-t border-t-transparent p-[1px]",e),...r,children:n.jsx(er,{className:"relative flex-1 rounded-full bg-border"})}));function ey(){let[e,t]=(0,s.useState)("standard"),[r,m]=(0,s.useState)(!1),[C,E]=(0,s.useState)(null),[R,P]=(0,s.useState)(null),[T,_]=(0,s.useState)([]),[O,k]=(0,s.useState)(""),[A,D]=(0,s.useState)(!0),[L,M]=(0,s.useState)(!1),[q,z]=(0,s.useState)(null),[I,U]=(0,s.useState)(null),[F,Z]=(0,s.useState)([]),[V,W]=(0,s.useState)(!0),[X,H]=(0,s.useState)([]),[$,B]=(0,s.useState)(""),[Y,G]=(0,s.useState)(null),[J,K]=(0,s.useState)(!1),Q="standard"===e?r:L,ee="standard"===e?C:q,et="standard"===e?R:I,er="standard"===e?T:F,en="standard"===e?_:Z,es=e=>{localStorage.setItem("customProductTypes",JSON.stringify(e)),H(e)},ei=()=>{if(!Y||!Y.value.trim())return;if(X.some((e,t)=>t!==Y.index&&e===Y.value.trim())){"standard"===e?P(`产品类型 "${Y.value.trim()}" 已存在`):U(`产品类型 "${Y.value.trim()}" 已存在`);return}let t=[...X];t[Y.index]=Y.value.trim(),es(t),G(null)},eo=e=>{let t=X[e];T.includes(t)&&_(T.filter(e=>e!==t)),F.includes(t)&&Z(F.filter(e=>e!==t)),es(X.filter((t,r)=>r!==e))},ea=e=>{er.includes(e)?en(er.filter(t=>t!==e)):en([...er,e])},el=()=>{er.length===X.length?en([]):en([...X])},ec=async()=>{m(!0),E(null),P(null);try{let e=await fetch("/api/admin/products/sync",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productTypes:T.length>0?T:void 0,categoryOverride:O||void 0,skipEmptyVariantsAndZeroPrice:A,allProductTypes:X})});if(!e.ok){let t=await e.json();throw Error(t.message||"Synchronization failed")}let t=await e.json();E(t)}catch(e){P(e instanceof Error?e.message:"Unknown error")}finally{m(!1)}},ed=async()=>{M(!0),z(null),U(null);try{let e=await fetch("/api/admin/products/sync-qr-products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({productTypes:F.length>0?F:void 0,skipEmptyVariantsAndZeroPrice:V,allProductTypes:X})});if(!e.ok){let t=await e.json();throw Error(t.message||"Synchronization failed")}let t=await e.json();z(t)}catch(e){U(e instanceof Error?e.message:"Unknown error")}finally{M(!1)}};return(0,n.jsxs)(o.Zb,{className:"w-full",children:[(0,n.jsxs)(o.Ol,{children:[n.jsx(o.ll,{children:"Product Synchronization"}),n.jsx(o.SZ,{children:"Synchronize product data from Odoo to the system"})]}),(0,n.jsxs)(o.aY,{children:[(0,n.jsxs)(d.Tabs,{value:e,onValueChange:t,children:[(0,n.jsxs)(d.TabsList,{className:"mb-4",children:[n.jsx(d.TabsTrigger,{value:"standard",children:"Standard Sync"}),n.jsx(d.TabsTrigger,{value:"qr",children:"QR Code Products Sync"})]}),n.jsx(d.TabsContent,{value:"standard",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[n.jsx(c._,{children:"Product Types"}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(i.Button,{variant:"outline",size:"sm",onClick:el,children:er.length===X.length?"Deselect All":"Select All"}),(0,n.jsxs)(i.Button,{variant:"outline",size:"sm",onClick:()=>K(!0),children:[n.jsx(p.Z,{className:"h-4 w-4 mr-1"}),"Manage Types"]})]})]}),n.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:X.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(a.X,{id:`type-${e}`,checked:er.includes(e),onCheckedChange:()=>ea(e)}),n.jsx(c._,{htmlFor:`type-${e}`,children:e})]},e))})]}),n.jsx(S.Vq,{open:J,onOpenChange:K,children:(0,n.jsxs)(S.cZ,{className:"sm:max-w-md",children:[(0,n.jsxs)(S.fK,{children:[n.jsx(S.$N,{children:"Manage Product Types"}),n.jsx(S.Be,{children:"Add, edit or delete product types. These types will be saved in your local browser."})]}),(0,n.jsxs)("div",{className:"space-y-4 py-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(l.I,{placeholder:"Enter new product type",value:$,onChange:e=>B(e.target.value),className:"flex-1"}),(0,n.jsxs)(i.Button,{variant:"outline",size:"sm",onClick:()=>{if($.trim()){if(X.includes($.trim())){"standard"===e?P(`产品类型 "${$.trim()}" 已存在`):U(`产品类型 "${$.trim()}" 已存在`);return}es([...X,$.trim()]),B("")}},disabled:!$.trim(),children:[n.jsx(f.Z,{className:"h-4 w-4 mr-1"}),"Add"]})]}),n.jsx("div",{className:"border rounded-md",children:n.jsx(ex,{className:"h-[200px]",children:n.jsx("div",{className:"p-2 space-y-2",children:0===X.length?n.jsx("p",{className:"text-sm text-muted-foreground text-center py-4",children:"No product types"}):X.map((e,t)=>n.jsx("div",{className:"flex items-center justify-between p-2 border rounded-md",children:Y&&Y.index===t?(0,n.jsxs)("div",{className:"flex-1 flex items-center gap-2",children:[n.jsx(l.I,{value:Y.value,onChange:e=>G({...Y,value:e.target.value}),className:"flex-1",autoFocus:!0}),n.jsx(i.Button,{variant:"ghost",size:"sm",onClick:ei,children:n.jsx(h,{className:"h-4 w-4"})}),n.jsx(i.Button,{variant:"ghost",size:"sm",onClick:()=>G(null),children:n.jsx(x.Z,{className:"h-4 w-4"})})]}):(0,n.jsxs)(n.Fragment,{children:[n.jsx("span",{className:"flex-1",children:e}),(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[n.jsx(i.Button,{variant:"ghost",size:"sm",onClick:()=>G({index:t,value:e}),children:n.jsx(p.Z,{className:"h-4 w-4"})}),n.jsx(i.Button,{variant:"ghost",size:"sm",onClick:()=>eo(t),disabled:X.length<=1,children:n.jsx(v,{className:"h-4 w-4 text-red-500"})})]})]})},`${e}-${t}`))})})})]}),(0,n.jsxs)(S.cN,{className:"flex justify-between",children:[n.jsx(i.Button,{variant:"outline",onClick:()=>{es([...j]),_(T.filter(e=>j.includes(e))),Z(F.filter(e=>j.includes(e)))},children:"Reset to Default"}),n.jsx(i.Button,{onClick:()=>K(!1),children:"Done"})]})]})}),(0,n.jsxs)("div",{children:[n.jsx(c._,{htmlFor:"category-override",children:"Force Category Name (Optional)"}),n.jsx(l.I,{id:"category-override",placeholder:"Leave empty to use default category",value:O,onChange:e=>k(e.target.value)})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(a.X,{id:"skip-empty",checked:A,onCheckedChange:e=>D(!!e)}),n.jsx(c._,{htmlFor:"skip-empty",children:"Skip products with 0 variants and 0 price"})]})]})}),n.jsx(d.TabsContent,{value:"qr",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(u.bZ,{children:[n.jsx(y.Z,{className:"h-4 w-4"}),n.jsx(u.Cd,{children:"QR Code Product Synchronization"}),n.jsx(u.X,{children:'This option will use the Odoo configuration for QR code products and force categorize all products as "qr_code"'})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[n.jsx(c._,{children:"Product Types"}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(i.Button,{variant:"outline",size:"sm",onClick:el,children:er.length===X.length?"Deselect All":"Select All"}),(0,n.jsxs)(i.Button,{variant:"outline",size:"sm",onClick:()=>K(!0),children:[n.jsx(p.Z,{className:"h-4 w-4 mr-1"}),"Manage Types"]})]})]}),n.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:X.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(a.X,{id:`qr-type-${e}`,checked:er.includes(e),onCheckedChange:()=>ea(e)}),n.jsx(c._,{htmlFor:`qr-type-${e}`,children:e})]},e))})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx(a.X,{id:"qr-skip-empty",checked:V,onCheckedChange:e=>W(!!e)}),n.jsx(c._,{htmlFor:"qr-skip-empty",children:"Skip products with 0 variants and 0 price"})]})]})})]}),et&&(0,n.jsxs)(u.bZ,{variant:"destructive",className:"mt-4",children:[n.jsx(y.Z,{className:"h-4 w-4"}),n.jsx(u.Cd,{children:"Synchronization Failed"}),n.jsx(u.X,{children:et})]}),ee&&(0,n.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,n.jsxs)(u.bZ,{variant:"default",className:"bg-green-50 border-green-200",children:[n.jsx(g,{className:"h-4 w-4 text-green-600"}),n.jsx(u.Cd,{className:"text-green-800",children:"Synchronization Successful"}),n.jsx(u.X,{className:"text-green-700",children:ee.message})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"p-4 pb-2",children:n.jsx(o.ll,{className:"text-sm font-medium",children:"Processed"})}),n.jsx(o.aY,{className:"p-4 pt-0",children:n.jsx("p",{className:"text-2xl font-bold",children:ee.stats.totalProcessed})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"p-4 pb-2",children:n.jsx(o.ll,{className:"text-sm font-medium",children:"Created"})}),n.jsx(o.aY,{className:"p-4 pt-0",children:n.jsx("p",{className:"text-2xl font-bold text-green-600",children:ee.stats.totalCreated})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"p-4 pb-2",children:n.jsx(o.ll,{className:"text-sm font-medium",children:"Updated"})}),n.jsx(o.aY,{className:"p-4 pt-0",children:n.jsx("p",{className:"text-2xl font-bold text-blue-600",children:ee.stats.totalUpdated})})]}),(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{className:"p-4 pb-2",children:n.jsx(o.ll,{className:"text-sm font-medium",children:"Skipped"})}),n.jsx(o.aY,{className:"p-4 pt-0",children:n.jsx("p",{className:"text-2xl font-bold text-amber-600",children:ee.stats.totalSkipped})})]})]}),ee.stats.byType&&(0,n.jsxs)("div",{className:"space-y-2",children:[n.jsx("h3",{className:"text-lg font-medium",children:"Statistics by Type"}),Object.entries(ee.stats.byType).map(([e,t])=>(0,n.jsxs)("div",{className:"border rounded-md p-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[n.jsx("h4",{className:"font-medium",children:e}),(0,n.jsxs)(w.C,{variant:"outline",children:[t.processed," products"]})]}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,n.jsxs)("span",{children:["Created: ",t.created]}),(0,n.jsxs)("span",{children:["Updated: ",t.updated]}),(0,n.jsxs)("span",{children:["Skipped: ",t.skipped]}),(0,n.jsxs)("span",{children:["Variants: ",t.variants]})]}),n.jsx(N.E,{value:(t.created+t.updated)/t.processed*100})]})]},e))]}),ee.errors&&ee.errors.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium mb-2",children:["Errors (",ee.errors.length,")"]}),n.jsx("div",{className:"max-h-40 overflow-y-auto border rounded-md p-2 bg-red-50",children:ee.errors.map((e,t)=>n.jsx("p",{className:"text-sm text-red-600",children:e},t))})]})]})]}),n.jsx(o.eW,{children:(0,n.jsxs)(i.Button,{onClick:()=>"standard"===e?ec():ed(),disabled:Q,className:"w-full",children:[Q&&n.jsx(b.Z,{className:"mr-2 h-4 w-4 animate-spin"}),Q?"Synchronizing...":"Start Synchronization"]})})]})}ev.displayName=X.displayName},43273:(e,t,r)=>{"use strict";r.d(t,{Cd:()=>c,X:()=>d,bZ:()=>l});var n=r(10326),s=r(17577),i=r(79360),o=r(77863);let a=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef(({className:e,variant:t,...r},s)=>n.jsx("div",{ref:s,role:"alert",className:(0,o.cn)(a({variant:t}),e),...r}));l.displayName="Alert";let c=s.forwardRef(({className:e,...t},r)=>n.jsx("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...t}));c.displayName="AlertTitle";let d=s.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...t}));d.displayName="AlertDescription"},68762:(e,t,r)=>{"use strict";r.d(t,{X:()=>E});var n=r(10326),s=r(17577),i=r(48051),o=r(93095),a=r(82561),l=r(52067),c=r(53405),d=r(2566),u=r(9815),p=r(45226),f="Checkbox",[m,h]=(0,o.b)(f),[x,v]=m(f),y=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:o,checked:c,defaultChecked:d,required:u,disabled:f,value:m="on",onCheckedChange:h,form:v,...y}=e,[g,b]=s.useState(null),S=(0,i.e)(t,e=>b(e)),C=s.useRef(!1),E=!g||v||!!g.closest("form"),[R=!1,P]=(0,l.T)({prop:c,defaultProp:d,onChange:h}),T=s.useRef(R);return s.useEffect(()=>{let e=g?.form;if(e){let t=()=>P(T.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[g,P]),(0,n.jsxs)(x,{scope:r,state:R,disabled:f,children:[(0,n.jsx)(p.WV.button,{type:"button",role:"checkbox","aria-checked":w(R)?"mixed":R,"aria-required":u,"data-state":N(R),"data-disabled":f?"":void 0,disabled:f,value:m,...y,ref:S,onKeyDown:(0,a.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.M)(e.onClick,e=>{P(e=>!!w(e)||!e),E&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),E&&(0,n.jsx)(j,{control:g,bubbles:!C.current,name:o,value:m,checked:R,required:u,disabled:f,form:v,style:{transform:"translateX(-100%)"},defaultChecked:!w(d)&&d})]})});y.displayName=f;var g="CheckboxIndicator",b=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:s,...i}=e,o=v(g,r);return(0,n.jsx)(u.z,{present:s||w(o.state)||!0===o.state,children:(0,n.jsx)(p.WV.span,{"data-state":N(o.state),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=g;var j=e=>{let{control:t,checked:r,bubbles:i=!0,defaultChecked:o,...a}=e,l=s.useRef(null),u=(0,c.D)(r),p=(0,d.t)(t);s.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==r&&t){let n=new Event("click",{bubbles:i});e.indeterminate=w(r),t.call(e,!w(r)&&r),e.dispatchEvent(n)}},[u,r,i]);let f=s.useRef(!w(r)&&r);return(0,n.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o??f.current,...a,tabIndex:-1,ref:l,style:{...e.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return"indeterminate"===e}function N(e){return w(e)?"indeterminate":e?"checked":"unchecked"}var S=r(32933),C=r(77863);let E=s.forwardRef(({className:e,...t},r)=>n.jsx(y,{ref:r,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:n.jsx(b,{className:(0,C.cn)("flex items-center justify-center text-current"),children:n.jsx(S.Z,{className:"h-4 w-4"})})}));E.displayName=y.displayName},79210:(e,t,r)=>{"use strict";r.d(t,{Tabs:()=>a,TabsContent:()=>d,TabsList:()=>l,TabsTrigger:()=>c});var n=r(10326),s=r(17577),i=r(13239),o=r(77863);let a=i.fC,l=s.forwardRef(({className:e,...t},r)=>n.jsx(i.aV,{ref:r,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=i.aV.displayName;let c=s.forwardRef(({className:e,...t},r)=>n.jsx(i.xz,{ref:r,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=i.xz.displayName;let d=s.forwardRef(({className:e,...t},r)=>n.jsx(i.VY,{ref:r,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=i.VY.displayName},41291:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(62881).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},75718:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(72331);async function s(){return await n._.product.findMany({orderBy:{createdAt:"desc"},include:{category:!0}})}async function i({children:e}){let t=await s();return e?"ProductsPage"===e.type.name?e.type({products:t}):e:null}},73635:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var n=r(19510),s=r(68570);let i=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\product-sync-panel.tsx`),{__esModule:o,$$typeof:a}=i;i.default;let l=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\admin\product-sync-panel.tsx#ProductSyncPanel`),c={title:"Product Synchronization",description:"Synchronize product data from Odoo"};function d(){return(0,n.jsxs)("div",{className:"flex-1 space-y-4 p-8 pt-6",children:[n.jsx("div",{className:"flex items-center justify-between",children:n.jsx("h2",{className:"text-3xl font-bold tracking-tight",children:"Product Synchronization"})}),n.jsx("div",{className:"grid gap-4",children:n.jsx(l,{})})]})}},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return n.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),s=r(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return c}});let s=r(54580),i=r(72934),o=r(8586),a="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let n=Error(a);n.digest=a+";"+t+";"+e+";"+r+";";let i=s.requestAsyncStorage.getStore();return i&&(n.mutableCookies=i.mutableCookies),n}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,s]=e.digest.split(";",4),i=Number(s);return t===a&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(i)&&i in o.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,1123,3239,4824,7123,3638],()=>r(81733));module.exports=n})();