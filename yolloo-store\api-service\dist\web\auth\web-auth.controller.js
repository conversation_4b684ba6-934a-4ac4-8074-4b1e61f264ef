"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebAuthController = void 0;
const common_1 = require("@nestjs/common");
const web_auth_service_1 = require("./web-auth.service");
let WebAuthController = class WebAuthController {
    webAuthService;
    constructor(webAuthService) {
        this.webAuthService = webAuthService;
    }
    async signup(signupDto, res) {
        try {
            const { name, email, password } = signupDto;
            if (!name || !email || !password) {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    error: 'Missing required fields',
                });
            }
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    error: 'Invalid email format',
                });
            }
            const result = await this.webAuthService.signup(name, email, password);
            return res.status(common_1.HttpStatus.CREATED).json(result);
        }
        catch (error) {
            if (error.message === 'Email already exists') {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    error: 'Email already exists',
                });
            }
            console.error('[WEB_AUTH_SIGNUP]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal server error',
            });
        }
    }
    async forgotPassword(body, res) {
        try {
            const result = await this.webAuthService.forgotPassword(body.email);
            return res.json(result);
        }
        catch (error) {
            console.error('[WEB_AUTH_FORGOT_PASSWORD]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal server error',
            });
        }
    }
    async resetPassword(body, res) {
        try {
            const result = await this.webAuthService.resetPassword(body.token, body.password);
            return res.json(result);
        }
        catch (error) {
            if (error.message === 'Invalid or expired token') {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    error: 'Invalid or expired token',
                });
            }
            console.error('[WEB_AUTH_RESET_PASSWORD]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal server error',
            });
        }
    }
    async sendCode(body, res) {
        try {
            const result = await this.webAuthService.sendVerificationCode(body.email, body.type);
            return res.json(result);
        }
        catch (error) {
            console.error('[WEB_AUTH_SEND_CODE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal server error',
            });
        }
    }
    async verifyCode(body, res) {
        try {
            const result = await this.webAuthService.verifyCode(body.email, body.code, body.type);
            return res.json(result);
        }
        catch (error) {
            if (error.message === 'Invalid or expired code') {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    error: 'Invalid or expired code',
                });
            }
            console.error('[WEB_AUTH_VERIFY_CODE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal server error',
            });
        }
    }
    async changePassword(body, req, res) {
        try {
            return res.status(common_1.HttpStatus.NOT_IMPLEMENTED).json({
                error: 'Session authentication not implemented yet',
            });
        }
        catch (error) {
            console.error('[WEB_AUTH_CHANGE_PASSWORD]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal server error',
            });
        }
    }
};
__decorate([
    (0, common_1.Post)('signup'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebAuthController.prototype, "signup", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebAuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebAuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Post)('send-code'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebAuthController.prototype, "sendCode", null);
__decorate([
    (0, common_1.Post)('verify-code'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebAuthController.prototype, "verifyCode", null);
__decorate([
    (0, common_1.Post)('change-password'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAuthController.prototype, "changePassword", null);
WebAuthController = __decorate([
    (0, common_1.Controller)('api/web/auth'),
    __metadata("design:paramtypes", [web_auth_service_1.WebAuthService])
], WebAuthController);
exports.WebAuthController = WebAuthController;
//# sourceMappingURL=web-auth.controller.js.map