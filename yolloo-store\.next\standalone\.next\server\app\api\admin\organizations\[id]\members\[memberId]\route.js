"use strict";(()=>{var e={};e.id=3893,e.ids=[3893],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},20817:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>R,patchFetch:()=>j,requestAsyncStorage:()=>_,routeModule:()=>x,serverHooks:()=>v,staticGenerationAsyncStorage:()=>b});var i={};t.r(i),t.d(i,{DELETE:()=>y,GET:()=>h,PATCH:()=>g,dynamic:()=>m,fetchCache:()=>p,revalidate:()=>f});var n=t(49303),a=t(88716),o=t(60670),s=t(87070),l=t(75571),u=t(90455),c=t(72331),d=t(7410);let m="force-dynamic",p="force-no-store",f=0,w=d.z.object({commissionRate:d.z.number().min(0).max(1).optional(),isAdmin:d.z.boolean().optional()});async function h(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=await c._.user.findUnique({where:{id:e.user.id},select:{role:!0}});if(!t||"ADMIN"!==t.role)return s.NextResponse.json({error:"Admin access required"},{status:403});let{id:i,memberId:n}=r;if(!await c._.affiliateOrganization.findUnique({where:{id:i}}))return s.NextResponse.json({error:"Organization not found"},{status:404});let a=await c._.affiliateProfile.findFirst({where:{id:n,organizationId:i},include:{user:{select:{id:!0,name:!0,email:!0,image:!0}}}});if(!a)return s.NextResponse.json({error:"Member not found"},{status:404});return s.NextResponse.json(a)}catch(e){return console.error("Error fetching member:",e),s.NextResponse.json({error:"Failed to fetch member"},{status:500})}}async function g(e,{params:r}){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let i=await c._.user.findUnique({where:{id:t.user.id},select:{role:!0}});if(!i||"ADMIN"!==i.role)return s.NextResponse.json({error:"Admin access required"},{status:403});let{id:n,memberId:a}=r;if(!await c._.affiliateOrganization.findUnique({where:{id:n}}))return s.NextResponse.json({error:"Organization not found"},{status:404});if(!await c._.affiliateProfile.findFirst({where:{id:a,organizationId:n}}))return s.NextResponse.json({error:"Member not found"},{status:404});let o=await e.json(),d=w.safeParse(o);if(!d.success)return s.NextResponse.json({error:d.error.errors},{status:400});let m=await c._.affiliateProfile.update({where:{id:a},data:d.data,include:{user:{select:{id:!0,name:!0,email:!0,image:!0}}}});return s.NextResponse.json(m)}catch(e){return console.error("Error updating member:",e),s.NextResponse.json({error:"Failed to update member"},{status:500})}}async function y(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=await c._.user.findUnique({where:{id:e.user.id},select:{role:!0}});if(!t||"ADMIN"!==t.role)return s.NextResponse.json({error:"Admin access required"},{status:403});let{id:i,memberId:n}=r;if(!await c._.affiliateOrganization.findUnique({where:{id:i}}))return s.NextResponse.json({error:"Organization not found"},{status:404});if(!await c._.affiliateProfile.findFirst({where:{id:n,organizationId:i}}))return s.NextResponse.json({error:"Member not found"},{status:404});return await c._.affiliateProfile.update({where:{id:n},data:{organizationId:null,isAdmin:!1}}),s.NextResponse.json({success:!0})}catch(e){return console.error("Error removing member:",e),s.NextResponse.json({error:"Failed to remove member"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/organizations/[id]/members/[memberId]/route",pathname:"/api/admin/organizations/[id]/members/[memberId]",filename:"route",bundlePath:"app/api/admin/organizations/[id]/members/[memberId]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\organizations\\[id]\\members\\[memberId]\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:_,staticGenerationAsyncStorage:b,serverHooks:v}=x,R="/api/admin/organizations/[id]/members/[memberId]/route";function j(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:b})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var i=t(7585),n=t(72331),a=t(77234),o=t(53797),s=t(42023),l=t.n(s),u=t(93475);let c={adapter:{...(0,i.N)(n._),getUser:async e=>{let r=await n._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await n._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await n._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await n._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await n._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await n._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await n._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:i,request:a}){try{if(t&&t.id){let r=a?.headers||new Headers,o=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";i?u=i.code&&!i.password?"email_code":"password":e&&(u=e.provider),await n._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],i=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,n=new URL(i).searchParams.get("callbackUrl");if(n){let e=decodeURIComponent(n);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let i=new URL(e);if(t.some(e=>i.hostname===e||i.hostname.includes(e)||i.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(i);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return i}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:i}){if("update"===t&&i)return{...e,...i.user};let a=await n._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return a?{id:a.id,name:a.name,email:a.email,picture:a.image,role:a.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>n});var i=t(53524);let n=global.prisma||new i.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var i=t(62197),n=t.n(i);let a=null;function o(){if(!a){let e=process.env.REDIS_URL||"redis://localhost:6379";(a=new(n())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),a.on("connect",()=>{console.log("Successfully connected to Redis")})}return a}async function s(e,r,t=300){try{let i=o(),n=`verification_code:${e}`;return await i.setex(n,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let i=o(),n=`rate_limit:${e}`,a=await i.get(n),s=a?parseInt(a):0;if(s>=r)return!1;return 0===s?await i.setex(n,t,"1"):await i.incr(n),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=n?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,t&&t.set(e,i),i}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[8948,1615,9092,5972,2197,2023,7005,7410],()=>t(20817));module.exports=i})();