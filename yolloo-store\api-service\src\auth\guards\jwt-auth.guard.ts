import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../../common/decorators/public.decorator';
import { Observable } from 'rxjs';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractToken(request);

    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET') || 'your-secret-key'
      });

      // Attach the user to the request object with proper structure
      request['user'] = {
        id: payload.sub,
        email: payload.email,
        role: payload.role,
        name: payload.name
      };
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * 统一的token提取方法，支持多种方式：
   * 1. Authorization Header (Bearer token) - 移动端和API调用
   * 2. Cookie (auth-token) - Web端
   * 3. Query parameter (token) - 特殊场景
   */
  private extractToken(request: any): string | undefined {
    // 1. 优先从Authorization Header获取 (移动端)
    const authHeader = request.headers?.authorization;
    if (authHeader) {
      const [type, token] = authHeader.split(' ') ?? [];
      if (type === 'Bearer' && token) {
        return token;
      }
    }

    // 2. 从Cookie获取 (Web端)
    const cookieToken = request.cookies?.['auth-token'];
    if (cookieToken) {
      return cookieToken;
    }

    // 3. 从Query参数获取 (特殊场景，如WebSocket)
    const queryToken = request.query?.token;
    if (queryToken) {
      return queryToken;
    }

    return undefined;
  }
}
