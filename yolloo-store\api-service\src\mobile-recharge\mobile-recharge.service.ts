import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { RechargeQueryDto, RechargeOrderDto } from './dto/recharge-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
import { DateFormatter, DateUtils } from '../common/utils';

@Injectable()
export class MobileRechargeService {
  private readonly logger = new Logger(MobileRechargeService.name);

  constructor(private prisma: PrismaService) {}

  async getRechargeOptions(query: RechargeQueryDto, ctx: RequestContext) {
    console.log('Context in getRechargeOptions:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询充值相关的产品
      // 假设充值产品的名称包含"充值"、"recharge"、"topup"等关键词
      const products = await this.prisma.product.findMany({
        where: {
          AND: [
            { status: 'ACTIVE' },
            { off_shelve: false },
            {
              OR: [
                { name: { contains: '充值', mode: 'insensitive' } },
                { name: { contains: 'recharge', mode: 'insensitive' } },
                { name: { contains: 'topup', mode: 'insensitive' } },
                { name: { contains: 'credit', mode: 'insensitive' } },
                { description: { contains: '充值', mode: 'insensitive' } },
                { description: { contains: 'recharge', mode: 'insensitive' } },
              ]
            }
          ]
        },
        include: {
          category: true,
          variants: {
            orderBy: {
              price: 'asc',
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // 如果没有找到充值产品，返回模拟数据
      if (products.length === 0) {
        this.logger.warn('No recharge products found in database, using fallback data');
        return this.getFallbackRechargeOptions(query, ctx);
      }

      this.logger.log(`Found ${products.length} recharge products`);

      // 格式化产品数据为充值选项格式
      const rechargeOptions = products.map((product, index) => {
        const variant = product.variants[0];
        const amount = variant ? parseFloat(variant.price.toString()) : product.price;
        const discount = Math.floor(amount * 0.05); // 5%折扣
        const finalAmount = amount - discount;

        // 根据产品名称或描述推断运营商
        let operator = 'china-mobile';
        let operatorName = isZh ? '中国移动' : 'China Mobile';

        const productName = product.name.toLowerCase();
        if (productName.includes('联通') || productName.includes('unicom')) {
          operator = 'china-unicom';
          operatorName = isZh ? '中国联通' : 'China Unicom';
        } else if (productName.includes('电信') || productName.includes('telecom')) {
          operator = 'china-telecom';
          operatorName = isZh ? '中国电信' : 'China Telecom';
        }

        return {
          id: product.id,
          operator: operator,
          operatorName: operatorName,
          amount: amount,
          currency: variant?.currency || 'USD',
          discount: discount,
          finalAmount: finalAmount,
          accountType: 'prepaid',
          description: product.description || (isZh ? `${amount}元话费充值` : `${amount} Yuan Credit Recharge`),
          processingTime: isZh ? '即时到账' : 'Instant',
          imageUrl: product.images[0] || `https://example.com/${operator}.jpg`,
          isPopular: index === 0, // 第一个产品标记为热门
        };
      });

      // Filter by operator if specified
      let filteredOptions = rechargeOptions;
      if (query.operator) {
        filteredOptions = rechargeOptions.filter(option => option.operator === query.operator);
      }

      // Filter by account type if specified
      if (query.accountType) {
        filteredOptions = filteredOptions.filter(option => option.accountType === query.accountType);
      }

      // Sort options
      if (query.sortBy === 'amount') {
        filteredOptions.sort((a, b) => {
          return query.sortOrder === 'desc' ? b.amount - a.amount : a.amount - b.amount;
        });
      }

      // Pagination
      const total = filteredOptions.length;
      const skip = (query.page! - 1) * query.pageSize!;
      const paginatedOptions = filteredOptions.slice(skip, skip + query.pageSize!);

      return {
        rechargeOptions: paginatedOptions,
        pagination: {
          total,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: skip + paginatedOptions.length < total,
        },
        operators: await this.getOperators(isZh),
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };
    } catch (error) {
      this.logger.error('Error fetching recharge options:', error);
      // 如果数据库查询失败，返回模拟数据
      return this.getFallbackRechargeOptions(query, ctx);
    }
  }

  private async getFallbackRechargeOptions(query: RechargeQueryDto, ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    const rechargeOptions = [
      {
        id: '1',
        operator: 'china-mobile',
        operatorName: isZh ? '中国移动' : 'China Mobile',
        amount: 10,
        currency: ctx.currency,
        discount: 0,
        finalAmount: 10,
        accountType: 'prepaid',
        description: isZh ? '10元话费充值' : '10 Yuan Credit Recharge',
        processingTime: isZh ? '即时到账' : 'Instant',
        imageUrl: 'https://example.com/china-mobile.jpg',
      },
      {
        id: '2',
        operator: 'china-mobile',
        operatorName: isZh ? '中国移动' : 'China Mobile',
        amount: 20,
        currency: ctx.currency,
        discount: 1,
        finalAmount: 19,
        accountType: 'prepaid',
        description: isZh ? '20元话费充值' : '20 Yuan Credit Recharge',
        processingTime: isZh ? '即时到账' : 'Instant',
        imageUrl: 'https://example.com/china-mobile.jpg',
        isPopular: true,
      },
    ];

    // Apply filters and pagination
    let filteredOptions = rechargeOptions;
    if (query.operator) {
      filteredOptions = rechargeOptions.filter(option => option.operator === query.operator);
    }
    if (query.accountType) {
      filteredOptions = filteredOptions.filter(option => option.accountType === query.accountType);
    }

    const total = filteredOptions.length;
    const skip = (query.page! - 1) * query.pageSize!;
    const paginatedOptions = filteredOptions.slice(skip, skip + query.pageSize!);

    return {
      rechargeOptions: paginatedOptions,
      pagination: {
        total,
        page: query.page!,
        pageSize: query.pageSize!,
        hasMore: skip + paginatedOptions.length < total,
      },
      operators: await this.getOperators(isZh),
      context: {
        language: ctx.language,
        theme: ctx.theme,
        currency: ctx.currency,
      },
    };
  }

  async createRechargeOrder(orderData: RechargeOrderDto, ctx: RequestContext) {
    console.log('Context in createRechargeOrder:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查找对应的产品
      let product = await this.prisma.product.findFirst({
        where: {
          AND: [
            { status: 'ACTIVE' },
            { off_shelve: false },
            {
              OR: [
                { name: { contains: '充值', mode: 'insensitive' } },
                { name: { contains: 'recharge', mode: 'insensitive' } },
                { name: { contains: 'topup', mode: 'insensitive' } },
              ]
            }
          ]
        },
        include: {
          variants: {
            orderBy: {
              price: 'asc',
            },
          },
        },
      });

      if (!product) {
        this.logger.warn('No recharge product found for order creation, using default product');
        // 如果没有找到产品，使用默认产品信息
        product = {
          id: 'mobile-recharge-default',
          name: 'Mobile Recharge',
          description: 'Mobile phone credit recharge service',
          price: 0,
          variants: [],
        } as any;
      } else {
        this.logger.log(`Using product for recharge order: ${product.name}`);
      }

      const variant = product?.variants?.[0];
      const productPrice = variant ? parseFloat(variant.price.toString()) : (product?.price || 0);

      // 创建真实的订单记录
      // 首先需要确保有用户ID，如果没有则创建匿名用户或使用默认用户
      let userId: string;

      // 查找或创建匿名用户
      let anonymousUser = await this.prisma.user.findFirst({
        where: { email: '<EMAIL>' }
      });

      if (!anonymousUser) {
        anonymousUser = await this.prisma.user.create({
          data: {
            email: '<EMAIL>',
            name: 'Anonymous User',
            hashedPassword: 'anonymous',
          }
        });
      }
      userId = anonymousUser.id;

      // 创建订单
      const order = await this.prisma.order.create({
        data: {
          userId: userId,
          status: 'PROCESSING',
          total: orderData.amount,
        },
      });

      // 创建订单项 - 使用正确的模型名称
      await this.prisma.orderItem.create({
        data: {
          orderId: order.id,
          productCode: product?.id || 'mobile-recharge-default',
          variantCode: variant?.id || 'default',
          variantText: `${orderData.operator} ${orderData.amount} recharge`,
          quantity: 1,
          price: orderData.amount,
        },
      });

      const orderResponse = {
        id: order.id,
        phoneNumber: orderData.phoneNumber,
        operator: orderData.operator,
        amount: orderData.amount,
        currency: ctx.currency,
        status: 'pending',
        statusText: isZh ? '处理中' : 'Processing',
        createdAt: DateFormatter.iso(order.createdAt),
        estimatedCompletionTime: DateFormatter.iso(DateUtils.addMinutes(new Date(), 5)),
        description: isZh ? `${orderData.amount}元话费充值` : `${orderData.amount} Yuan Credit Recharge`,
      };

      return {
        order: orderResponse,
        message: isZh ? '充值订单创建成功，正在处理中' : 'Recharge order created successfully, processing',
      };
    } catch (error) {
      this.logger.error('Error creating recharge order:', error);

      // 如果数据库操作失败，返回模拟订单
      const order = {
        id: `recharge_${Date.now()}`,
        phoneNumber: orderData.phoneNumber,
        operator: orderData.operator,
        amount: orderData.amount,
        currency: ctx.currency,
        status: 'pending',
        statusText: isZh ? '处理中' : 'Processing',
        createdAt: DateFormatter.iso(new Date()),
        estimatedCompletionTime: DateFormatter.iso(DateUtils.addMinutes(new Date(), 5)),
        description: isZh ? `${orderData.amount}元话费充值` : `${orderData.amount} Yuan Credit Recharge`,
      };

      return {
        order,
        message: isZh ? '充值订单创建成功，正在处理中' : 'Recharge order created successfully, processing',
      };
    }
  }

  async getRechargeHistory(userId: string, query: RechargeQueryDto, ctx: RequestContext) {
    console.log('Context in getRechargeHistory:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 首先尝试从RechargeHistory表查询真实数据
      const rechargeHistories = await this.prisma.rechargeHistory.findMany({
        where: {
          userId: userId,
        },
        include: {
          operator: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (query.page! - 1) * query.pageSize!,
        take: query.pageSize!,
      });

      if (rechargeHistories.length > 0) {
        // 使用真实的RechargeHistory数据
        const total = await this.prisma.rechargeHistory.count({
          where: { userId: userId },
        });

        const history = rechargeHistories.map(record => ({
          id: record.id,
          phoneNumber: record.phoneNumber,
          operator: record.operator.code,
          operatorName: isZh ? record.operator.nameZh : record.operator.nameEn,
          amount: record.amount,
          currency: record.currency,
          status: record.status.toLowerCase(),
          statusText: this.getStatusText(record.status, isZh),
          createdAt: DateFormatter.iso(record.createdAt),
          completedAt: record.completedAt ? DateFormatter.iso(record.completedAt) : null,
          failureReason: record.status === 'FAILED' ? (isZh ? '处理失败' : 'Processing failed') : null,
        }));

        return {
          history,
          pagination: {
            total,
            page: query.page!,
            pageSize: query.pageSize!,
            hasMore: (query.page! * query.pageSize!) < total,
          },
        };
      }

      // 如果RechargeHistory表没有数据，查询用户的充值订单历史
      const orders = await this.prisma.order.findMany({
        where: {
          AND: [
            userId ? { userId: userId } : {},
            {
              items: {
                some: {
                  variantText: {
                    contains: 'recharge'
                  }
                }
              }
            }
          ]
        },
        include: {
          items: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (query.page! - 1) * query.pageSize!,
        take: query.pageSize!,
      });

      // 获取总数
      const total = await this.prisma.order.count({
        where: {
          AND: [
            userId ? { userId: userId } : {},
            {
              items: {
                some: {
                  variantText: {
                    contains: 'recharge'
                  }
                }
              }
            }
          ]
        },
      });

      // 格式化历史记录
      const history = orders.map(order => {
        // 从OrderItem中提取充值信息
        const rechargeItem = order.items.find(item => item.variantText?.includes('recharge'));

        // 解析variantText来获取运营商和金额信息
        let operator = 'china-mobile';
        let phoneNumber = '138****8888';

        if (rechargeItem?.variantText) {
          const variantText = rechargeItem.variantText;
          if (variantText.includes('china-unicom')) {
            operator = 'china-unicom';
          } else if (variantText.includes('china-telecom')) {
            operator = 'china-telecom';
          }
        }

        let operatorName = isZh ? '未知运营商' : 'Unknown Operator';
        if (operator === 'china-mobile') {
          operatorName = isZh ? '中国移动' : 'China Mobile';
        } else if (operator === 'china-unicom') {
          operatorName = isZh ? '中国联通' : 'China Unicom';
        } else if (operator === 'china-telecom') {
          operatorName = isZh ? '中国电信' : 'China Telecom';
        }

        let statusText = isZh ? '未知状态' : 'Unknown Status';
        if (order.status === 'DELIVERED') {
          statusText = isZh ? '充值成功' : 'Completed';
        } else if (order.status === 'PROCESSING') {
          statusText = isZh ? '处理中' : 'Processing';
        } else if (order.status === 'CANCELLED') {
          statusText = isZh ? '已取消' : 'Cancelled';
        } else if (order.status === 'REFUNDED') {
          statusText = isZh ? '充值失败' : 'Failed';
        }

        return {
          id: order.id,
          phoneNumber: phoneNumber,
          operator: operator,
          operatorName: operatorName,
          amount: parseFloat(order.total.toString()),
          currency: ctx.currency,
          status: order.status.toLowerCase(),
          statusText: statusText,
          createdAt: DateFormatter.iso(order.createdAt),
          completedAt: order.updatedAt ? DateFormatter.iso(order.updatedAt) : null,
          failureReason: order.status === 'REFUNDED' ? (isZh ? '处理失败' : 'Processing failed') : null,
        };
      });

      return {
        history,
        pagination: {
          total,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: (query.page! * query.pageSize!) < total,
        },
      };
    } catch (error) {
      this.logger.error('Error fetching recharge history:', error);

      // 如果数据库查询失败，返回模拟数据
      const history = [
        {
          id: 'recharge_001',
          phoneNumber: '138****8888',
          operator: 'china-mobile',
          operatorName: isZh ? '中国移动' : 'China Mobile',
          amount: 20,
          currency: ctx.currency,
          status: 'completed',
          statusText: isZh ? '充值成功' : 'Completed',
          createdAt: '2023-12-01T10:30:00Z',
          completedAt: '2023-12-01T10:31:00Z',
        },
      ];

      return {
        history,
        pagination: {
          total: history.length,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: false,
        },
      };
    }
  }

  private async getOperators(isZh: boolean) {
    try {
      // 从数据库获取运营商数据
      const operators = await this.prisma.mobileOperator.findMany({
        where: {
          isActive: true,
        },
        include: {
          country: true,
        },
        orderBy: {
          nameEn: 'asc',
        },
      });

      if (operators.length > 0) {
        return operators.map(operator => ({
          id: operator.code,
          name: isZh ? operator.nameZh : operator.nameEn,
          logo: operator.logoUrl || `https://example.com/${operator.code}.jpg`,
          country: isZh ? operator.country.nameZh : operator.country.nameEn,
        }));
      }

      // 如果数据库中没有运营商数据，返回fallback
      return [
        { id: 'china-mobile', name: isZh ? '中国移动' : 'China Mobile', logo: 'https://example.com/china-mobile.jpg' },
        { id: 'china-unicom', name: isZh ? '中国联通' : 'China Unicom', logo: 'https://example.com/china-unicom.jpg' },
        { id: 'china-telecom', name: isZh ? '中国电信' : 'China Telecom', logo: 'https://example.com/china-telecom.jpg' },
      ];

    } catch (error) {
      this.logger.error('Error fetching operators:', error);

      // 错误时返回fallback数据
      return [
        { id: 'china-mobile', name: isZh ? '中国移动' : 'China Mobile', logo: 'https://example.com/china-mobile.jpg' },
        { id: 'china-unicom', name: isZh ? '中国联通' : 'China Unicom', logo: 'https://example.com/china-unicom.jpg' },
        { id: 'china-telecom', name: isZh ? '中国电信' : 'China Telecom', logo: 'https://example.com/china-telecom.jpg' },
      ];
    }
  }

  private getStatusText(status: string, isZh: boolean): string {
    const statusMap = {
      'PENDING': isZh ? '处理中' : 'Pending',
      'PROCESSING': isZh ? '处理中' : 'Processing',
      'COMPLETED': isZh ? '充值成功' : 'Completed',
      'FAILED': isZh ? '充值失败' : 'Failed',
      'CANCELLED': isZh ? '已取消' : 'Cancelled',
    };

    return statusMap[status] || (isZh ? '未知状态' : 'Unknown Status');
  }
}
