import { Controller, Post, Body, Get, Req, Res, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { WebAuthService } from './web-auth.service';
import { AuthService } from '../../auth/auth.service';
import { Public } from '../../common/decorators/public.decorator';

/**
 * Web认证控制器
 * 处理原主应用的认证相关API
 * 路由: /api/web/auth/*
 */
@Controller('api/web/auth')
export class WebAuthController {
  constructor(
    private readonly webAuthService: WebAuthService,
    private readonly authService: AuthService,
  ) {}

  /**
   * Web端登录 - 支持Cookie认证
   * POST /api/web/auth/login
   */
  @Public()
  @Post('login')
  async login(
    @Body() loginDto: { email: string; password: string },
    @Res() res: Response,
  ) {
    try {
      const result = await this.authService.webLogin(loginDto);

      // 设置httpOnly Cookie
      res.cookie('auth-token', result.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 15 * 60 * 1000, // 15分钟
      });

      res.cookie('refresh-token', result.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
      });

      return res.json({
        user: result.user,
        message: 'Login successful',
      });
    } catch (error) {
      console.error('[WEB_AUTH_LOGIN]', error);
      return res.status(HttpStatus.UNAUTHORIZED).json({
        error: error.message || 'Login failed',
      });
    }
  }

  /**
   * 刷新令牌
   * POST /api/web/auth/refresh
   */
  @Public()
  @Post('refresh')
  async refresh(@Req() req: Request, @Res() res: Response) {
    try {
      const refreshToken = req.cookies?.['refresh-token'];

      if (!refreshToken) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          error: 'No refresh token provided',
        });
      }

      const result = await this.authService.refreshToken(refreshToken);

      // 更新Cookie
      res.cookie('auth-token', result.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 15 * 60 * 1000, // 15分钟
      });

      res.cookie('refresh-token', result.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
      });

      return res.json({
        message: 'Token refreshed successfully',
      });
    } catch (error) {
      console.error('[WEB_AUTH_REFRESH]', error);
      return res.status(HttpStatus.UNAUTHORIZED).json({
        error: 'Token refresh failed',
      });
    }
  }

  /**
   * 登出
   * POST /api/web/auth/logout
   */
  @Post('logout')
  async logout(@Res() res: Response) {
    // 清除Cookie
    res.clearCookie('auth-token');
    res.clearCookie('refresh-token');

    return res.json({
      message: 'Logout successful',
    });
  }

  /**
   * 获取当前用户信息
   * GET /api/web/auth/me
   */
  @Get('me')
  async getCurrentUser(@Req() req: Request, @Res() res: Response) {
    try {
      const user = req['user'];
      if (!user) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          error: 'Not authenticated',
        });
      }

      return res.json({ user });
    } catch (error) {
      console.error('[WEB_AUTH_ME]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }

  /**
   * 用户注册
   * POST /api/web/auth/signup
   */
  @Public()
  @Post('signup')
  async signup(
    @Body() signupDto: { name: string; email: string; password: string },
    @Res() res: Response,
  ) {
    try {
      const { name, email, password } = signupDto;

      if (!name || !email || !password) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Missing required fields',
        });
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Invalid email format',
        });
      }

      const result = await this.webAuthService.signup(name, email, password);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      if (error.message === 'Email already exists') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Email already exists',
        });
      }
      
      console.error('[WEB_AUTH_SIGNUP]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }

  /**
   * 密码重置
   * POST /api/web/auth/forgot-password
   */
  @Post('forgot-password')
  async forgotPassword(
    @Body() body: { email: string },
    @Res() res: Response,
  ) {
    try {
      const result = await this.webAuthService.forgotPassword(body.email);
      return res.json(result);
    } catch (error) {
      console.error('[WEB_AUTH_FORGOT_PASSWORD]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }

  /**
   * 重置密码
   * POST /api/web/auth/reset-password
   */
  @Post('reset-password')
  async resetPassword(
    @Body() body: { token: string; password: string },
    @Res() res: Response,
  ) {
    try {
      const result = await this.webAuthService.resetPassword(body.token, body.password);
      return res.json(result);
    } catch (error) {
      if (error.message === 'Invalid or expired token') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Invalid or expired token',
        });
      }
      
      console.error('[WEB_AUTH_RESET_PASSWORD]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }

  /**
   * 发送验证码
   * POST /api/web/auth/send-code
   */
  @Post('send-code')
  async sendCode(
    @Body() body: { email: string; type: string },
    @Res() res: Response,
  ) {
    try {
      const result = await this.webAuthService.sendVerificationCode(body.email, body.type);
      return res.json(result);
    } catch (error) {
      console.error('[WEB_AUTH_SEND_CODE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }

  /**
   * 验证码验证
   * POST /api/web/auth/verify-code
   */
  @Post('verify-code')
  async verifyCode(
    @Body() body: { email: string; code: string; type: string },
    @Res() res: Response,
  ) {
    try {
      const result = await this.webAuthService.verifyCode(body.email, body.code, body.type);
      return res.json(result);
    } catch (error) {
      if (error.message === 'Invalid or expired code') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Invalid or expired code',
        });
      }
      
      console.error('[WEB_AUTH_VERIFY_CODE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }

  /**
   * 修改密码
   * POST /api/web/auth/change-password
   */
  @Post('change-password')
  async changePassword(
    @Body() body: { currentPassword: string; newPassword: string },
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      // 这里需要从session中获取用户信息
      // 暂时返回未实现状态
      return res.status(HttpStatus.NOT_IMPLEMENTED).json({
        error: 'Session authentication not implemented yet',
      });
    } catch (error) {
      console.error('[WEB_AUTH_CHANGE_PASSWORD]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal server error',
      });
    }
  }
}
