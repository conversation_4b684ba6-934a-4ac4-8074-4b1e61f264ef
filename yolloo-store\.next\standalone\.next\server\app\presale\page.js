(()=>{var e={};e.id=2972,e.ids=[2972],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},44911:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(70568),r(89090),r(26083),r(35866);var a=r(23191),s=r(88716),o=r(37922),i=r.n(o),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d=["",{children:["presale",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,70568)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\presale\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\presale\\page.tsx"],p="/presale/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/presale/page",pathname:"/presale",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},24897:(e,t,r)=>{Promise.resolve().then(r.bind(r,93930))},93930:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eh});var a,s=r(10326),o=r(17577);let i={data:""},n=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||i,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,c=/\n+/g,p=(e,t)=>{let r="",a="",s="";for(let o in e){let i=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+i+";":a+="f"==o[1]?p(i,o):o+"{"+p(i,"k"==o[1]?"":t)+"}":"object"==typeof i?a+=p(i,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=i&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=p.p?p.p(o,i):o+":"+i+";")}return r+(t&&s?t+"{"+s+"}":s)+a},u={},m=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+m(e[r]);return t}return e},x=(e,t,r,a,s)=>{let o=m(e),i=u[o]||(u[o]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(o));if(!u[i]){let t=o!==e?e:(e=>{let t,r,a=[{}];for(;t=l.exec(e.replace(d,""));)t[4]?a.shift():t[3]?(r=t[3].replace(c," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(c," ").trim();return a[0]})(e);u[i]=p(s?{["@keyframes "+i]:t}:t,r?"":"."+i)}let n=r&&u.g?u.g:null;return r&&(u.g=u[i]),((e,t,r,a)=>{a?t.data=t.data.replace(a,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(u[i],t,a,n),i},f=(e,t,r)=>e.reduce((e,a,s)=>{let o=t[s];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":p(e,""):!1===e?"":e}return e+a+(null==o?"":o)},"");function h(e){let t=this||{},r=e.call?e(t.p):e;return x(r.unshift?r.raw?f(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,n(t.target),t.g,t.o,t.k)}h.bind({g:1});let g,b,y,v=h.bind({k:1});function w(e,t){let r=this||{};return function(){let a=arguments;function s(o,i){let n=Object.assign({},o),l=n.className||s.className;r.p=Object.assign({theme:b&&b()},n),r.o=/ *go\d+/.test(l),n.className=h.apply(r,a)+(l?" "+l:""),t&&(n.ref=i);let d=e;return e[0]&&(d=n.as||e,delete n.as),y&&d[0]&&y(n),g(d,n)}return t?t(s):s}}var j=e=>"function"==typeof e,k=(e,t)=>j(e)?e(t):e,N=(()=>{let e=0;return()=>(++e).toString()})(),E=(()=>{let e;return()=>e})(),C=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return C(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+s}))}}},O=[],P={toasts:[],pausedAt:void 0},_=e=>{P=C(P,e),O.forEach(e=>{e(P)})},D={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},A=(e={})=>{let[t,r]=(0,o.useState)(P);(0,o.useEffect)(()=>(O.push(r),()=>{let e=O.indexOf(r);e>-1&&O.splice(e,1)}),[t]);let a=t.toasts.map(t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||D[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...t,toasts:a}},S=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||N()}),z=e=>(t,r)=>{let a=S(t,e,r);return _({type:2,toast:a}),a.id},$=(e,t)=>z("blank")(e,t);$.error=z("error"),$.success=z("success"),$.loading=z("loading"),$.custom=z("custom"),$.dismiss=e=>{_({type:3,toastId:e})},$.remove=e=>_({type:4,toastId:e}),$.promise=(e,t,r)=>{let a=$.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?k(t.success,e):void 0;return s?$.success(s,{id:a,...r,...null==r?void 0:r.success}):$.dismiss(a),e}).catch(e=>{let s=t.error?k(t.error,e):void 0;s?$.error(s,{id:a,...r,...null==r?void 0:r.error}):$.dismiss(a)}),e};var L=(e,t)=>{_({type:1,toast:{id:e,height:t}})},R=()=>{_({type:5,time:Date.now()})},T=new Map,F=1e3,I=(e,t=F)=>{if(T.has(e))return;let r=setTimeout(()=>{T.delete(e),_({type:4,toastId:e})},t);T.set(e,r)},M=e=>{let{toasts:t,pausedAt:r}=A(e);(0,o.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&$.dismiss(t.id);return}return setTimeout(()=>$.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,o.useCallback)(()=>{r&&_({type:6,time:Date.now()})},[r]),s=(0,o.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:o}=r||{},i=t.filter(t=>(t.position||o)===(e.position||o)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+s,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)I(e.id,e.removeDelay);else{let t=T.get(e.id);t&&(clearTimeout(t),T.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:L,startPause:R,endPause:a,calculateOffset:s}}},q=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,B=v`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,U=v`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,G=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${q} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${B} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${U} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,H=v`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=w("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${H} 1s linear infinite;
`,W=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,J=v`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,V=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${W} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${J} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,X=w("div")`
  position: absolute;
`,Z=w("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,K=v`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Q=w("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${K} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ee=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?o.createElement(Q,null,t):t:"blank"===r?null:o.createElement(Z,null,o.createElement(Y,{...a}),"loading"!==r&&o.createElement(X,null,"error"===r?o.createElement(G,{...a}):o.createElement(V,{...a})))},et=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,er=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=w("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,es=w("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let r=e.includes("top")?1:-1,[a,s]=E()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[et(r),er(r)];return{animation:t?`${v(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${v(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},ei=o.memo(({toast:e,position:t,style:r,children:a})=>{let s=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=o.createElement(ee,{toast:e}),n=o.createElement(es,{...e.ariaProps},k(e.message,e));return o.createElement(ea,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:i,message:n}):o.createElement(o.Fragment,null,i,n))});a=o.createElement,p.p=void 0,g=a,b=void 0,y=void 0;var en=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let i=o.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return o.createElement("div",{ref:i,className:t,style:r},s)},el=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:E()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ed=h`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ec=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:i,containerClassName:n})=>{let{toasts:l,handlers:d}=M(r);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},l.map(r=>{let i=r.position||t,n=el(i,d.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return o.createElement(en,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?ed:"",style:n},"custom"===r.type?k(r.message,r):s?s(r):o.createElement(ei,{toast:r,position:i}))}))},ep=r(35047);/*! js-cookie v3.0.5 | MIT */function eu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)e[a]=r[a]}return e}var em=function e(t,r){function a(e,a,s){if("undefined"!=typeof document){"number"==typeof(s=eu({},r,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var i in s)s[i]&&(o+="; "+i,!0!==s[i]&&(o+="="+s[i].split(";")[0]));return document.cookie=e+"="+t.write(a,e)+o}}return Object.create({set:a,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],a={},s=0;s<r.length;s++){var o=r[s].split("="),i=o.slice(1).join("=");try{var n=decodeURIComponent(o[0]);if(a[n]=t.read(i,n),e===n)break}catch(e){}}return e?a[e]:a}},remove:function(e,t){a(e,"",eu({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,eu({},this.attributes,t))},withConverter:function(t){return e(eu({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),ex=r(8654),ef=r(77863);function eh(){(0,ep.useSearchParams)();let[e,t]=(0,o.useState)(!1),r=(0,ef.MF)(process.env.NEXT_PUBLIC_PRESALE_TARGET_DATE||"2025-01-08T00:00:00")||new Date("2025-01-08T00:00:00"),a=new Date,i=Math.max(0,ef.ED.daysBetween(a,r)),n=async r=>{if(r.preventDefault(),e)return;let a=r.target.email.value,s=em.get("referralCode_local");try{t(!0),console.log(a,s);let e=await fetch("/api/presale/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,referralCode:s})}),o=await e.json();e.ok?($.success("Welcome to YOLLOO! Your exclusive 30% discount code will be sent to "+a,{duration:5e3,position:"top-center",style:{background:"rgba(0, 0, 0, 0.8)",color:"#fff",border:"1px solid rgba(220, 38, 38, 0.3)",backdropFilter:"blur(8px)"}}),r.target.reset()):$.error(o.message||"Something went wrong. Please try again.",{duration:5e3,position:"top-center",style:{background:"rgba(0, 0, 0, 0.8)",color:"#fff",border:"1px solid rgba(220, 38, 38, 0.3)",backdropFilter:"blur(8px)"}})}catch(e){$.error("Failed to subscribe. Please try again later.",{duration:5e3,position:"top-center",style:{background:"rgba(0, 0, 0, 0.8)",color:"#fff",border:"1px solid rgba(220, 38, 38, 0.3)",backdropFilter:"blur(8px)"}})}finally{t(!1),await new Promise(e=>setTimeout(e,2e3))}};return(0,s.jsxs)("div",{className:"overflow-x-hidden",children:[s.jsx(ec,{}),(0,s.jsxs)("div",{className:"relative min-h-[100dvh] w-full bg-gradient-to-b from-black via-red-900 to-black overflow-hidden",children:[s.jsx("div",{className:"absolute top-6 left-6",children:s.jsx(ex.Z,{className:"h-7 sm:h-12 w-auto"})}),s.jsx("div",{className:"relative z-10 flex flex-col items-center justify-center min-h-[100dvh] text-white px-4",children:(0,s.jsxs)("div",{className:"text-center space-y-12 md:space-y-32 -mt-8",children:[(0,s.jsxs)("div",{className:"space-y-0.5 md:space-y-2",children:[s.jsx("h2",{className:"text-4xl sm:text-5xl md:text-7xl font-bold italic text-white-500 tracking-wider animate-pulse",children:"ONE CARD,"}),s.jsx("h2",{className:"text-4xl sm:text-5xl md:text-7xl font-bold italic text-white-500 tracking-wider animate-pulse",children:"ONE WORLD"})]}),s.jsx("div",{className:"w-64 h-40 sm:w-96 sm:h-56 md:w-[32rem] md:h-72 mx-auto relative",children:s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-red-600 to-red-900 rounded-xl shadow-2xl transform rotate-12 transition-transform hover:rotate-0 duration-500",children:s.jsx("div",{className:"absolute bottom-8 right-8 text-xl sm:text-2xl md:text-3xl font-bold tracking-wider text-transparent bg-clip-text bg-gradient-to-b from-red-500/40 to-red-700/40 select-none",style:{textShadow:"1px 1px 2px rgba(255,255,255,0.1), -1px -1px 2px rgba(0,0,0,0.2)"},children:"YOLLOO"})})}),(0,s.jsxs)("div",{className:"space-y-2 md:space-y-8",children:[s.jsx("div",{className:"text-7xl sm:text-8xl md:text-9xl font-bold text-red-500 tracking-tight",children:i}),s.jsx("div",{className:"text-xl sm:text-2xl md:text-4xl font-semibold tracking-widest",children:"DAYS LEFT"})]})]})}),s.jsx("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,0,0,0.1)_0%,transparent_50%)] animate-pulse"}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 flex justify-center items-center h-24",children:s.jsx("button",{onClick:()=>{let e=document.getElementById("subscription-section");e&&e.scrollIntoView({behavior:"smooth"})},className:"cursor-pointer animate-bounce z-20","aria-label":"Scroll to subscription section",children:s.jsx("div",{className:"w-10 h-10 md:w-12 md:h-12 flex items-center justify-center rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 group",children:s.jsx("svg",{className:"w-5 h-5 md:w-6 md:h-6 text-white transform rotate-90 group-hover:scale-110 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M9 5l7 7-7 7"})})})})})]}),s.jsx("div",{id:"subscription-section",className:"relative z-10 min-h-[100dvh] flex flex-col items-center justify-center px-4 py-16 bg-gradient-to-b from-black via-red-800 to-black",children:(0,s.jsxs)("div",{className:"w-full max-w-xl space-y-6 md:space-y-10 p-6 md:p-12 bg-black/30 backdrop-blur-lg rounded-2xl border border-red-500/20 shadow-2xl hover:border-red-500/40 transition-all duration-500",children:[(0,s.jsxs)("div",{className:"text-center space-y-3 md:space-y-4",children:[s.jsx("h2",{className:"text-2xl md:text-4xl font-bold text-white tracking-tight",children:"Join the Exclusive Launch"}),s.jsx("p",{className:"text-base md:text-lg font-light leading-relaxed text-gray-200 px-2",children:"Be the first to experience YOLLOO and receive an exclusive early-access discount."}),s.jsx("div",{className:"mt-2 inline-block px-4 py-1.5 md:px-6 md:py-2 bg-red-500/20 rounded-full",children:s.jsx("span",{className:"text-sm md:text-base text-red-300 font-semibold",children:"\uD83C\uDF89 Special Offer: 30% OFF for Early Birds"})})]}),s.jsx("form",{onSubmit:n,className:"mt-4 md:mt-8 space-y-6",children:(0,s.jsxs)("div",{className:"relative group",children:[s.jsx("input",{id:"email",name:"email",type:"email",required:!0,className:"block w-full px-4 py-3.5 md:px-6 md:py-5 rounded-xl bg-black/40 border-2 border-red-500/30 text-white placeholder:text-gray-300 focus:outline-none focus:border-red-500/60 focus:ring-1 focus:ring-red-500/60 transition-all duration-300 text-base",placeholder:"Enter your email address"}),s.jsx("button",{type:"submit",disabled:e,className:"absolute right-1.5 top-1/2 -translate-y-1/2 px-3 md:px-8 py-2 md:py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg text-sm md:text-base font-medium hover:from-red-400 hover:to-red-500 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2 focus:ring-offset-black disabled:opacity-50 disabled:cursor-not-allowed",children:e?(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("svg",{className:"animate-spin h-4 w-4",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4",fill:"none"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s.jsx("span",{children:"Subscribing..."})]}):"Subscribe"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3 md:gap-8 justify-items-center text-xs md:text-sm text-gray-300",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("svg",{className:"w-4 md:w-5 h-4 md:h-5 text-red-400 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),s.jsx("span",{children:"Exclusive Offers"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("svg",{className:"w-4 md:w-5 h-4 md:h-5 text-red-400 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),s.jsx("span",{children:"Early Access"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("svg",{className:"w-4 md:w-5 h-4 md:h-5 text-red-400 flex-shrink-0",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),s.jsx("span",{children:"Special Updates"})]})]})]})})]})}},70568:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>n});var a=r(68570);let s=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\presale\page.tsx`),{__esModule:o,$$typeof:i}=s;s.default;let n=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\presale\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(44911));module.exports=a})();