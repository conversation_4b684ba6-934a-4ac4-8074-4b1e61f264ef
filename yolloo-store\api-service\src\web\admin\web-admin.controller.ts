import { Controller, Get, Post, Patch, Delete, Body, Param, Query, Res, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { Request, Response } from 'express';
import { WebAdminService } from './web-admin.service';

/**
 * Web管理员控制器
 * 处理原主应用的管理员相关API
 * 路由: /api/web/admin/*
 */
@Controller('api/web/admin')
export class WebAdminController {
  constructor(private readonly webAdminService: WebAdminService) {}

  /**
   * 管理员权限检查中间件
   */
  private checkAdminPermission(req: Request, res: Response): boolean {
    const user = req['user'] as any;
    if (!user || user.role !== 'ADMIN') {
      res.status(HttpStatus.FORBIDDEN).json({
        error: 'Admin access required',
      });
      return false;
    }
    return true;
  }

  /**
   * 获取仪表板数据
   * GET /api/web/admin/dashboard
   */
  @Get('dashboard')
  async getDashboard(@Req() req: Request, @Res() res: Response) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const dashboard = await this.webAdminService.getDashboardData();
      return res.json(dashboard);
    } catch (error) {
      console.error('[WEB_ADMIN_DASHBOARD]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch dashboard data',
      });
    }
  }

  /**
   * 获取用户列表
   * GET /api/web/admin/users
   */
  @Get('users')
  async getUsers(
    @Query() query: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const users = await this.webAdminService.getUsers(query);
      return res.json(users);
    } catch (error) {
      console.error('[WEB_ADMIN_USERS]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch users',
      });
    }
  }

  /**
   * 获取单个用户详情
   * GET /api/web/admin/users/:userId
   */
  @Get('users/:userId')
  async getUser(
    @Param('userId') userId: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const user = await this.webAdminService.getUserById(userId);
      if (!user) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: 'User not found',
        });
      }

      return res.json(user);
    } catch (error) {
      console.error('[WEB_ADMIN_USER]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch user',
      });
    }
  }

  /**
   * 创建用户
   * POST /api/web/admin/users
   */
  @Post('users')
  async createUser(
    @Body() createUserDto: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const user = await this.webAdminService.createUser(createUserDto);
      return res.status(HttpStatus.CREATED).json(user);
    } catch (error) {
      console.error('[WEB_ADMIN_CREATE_USER]', error);
      if (error.message === 'Email already exists') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          error: 'Email already exists',
        });
      }
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to create user',
      });
    }
  }

  /**
   * 更新用户
   * PATCH /api/web/admin/users/:userId
   */
  @Patch('users/:userId')
  async updateUser(
    @Param('userId') userId: string,
    @Body() updateUserDto: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const user = await this.webAdminService.updateUser(userId, updateUserDto);
      return res.json(user);
    } catch (error) {
      console.error('[WEB_ADMIN_UPDATE_USER]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to update user',
      });
    }
  }

  /**
   * 删除用户
   * DELETE /api/web/admin/users/:userId
   */
  @Delete('users/:userId')
  async deleteUser(
    @Param('userId') userId: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      await this.webAdminService.deleteUser(userId);
      return res.status(HttpStatus.NO_CONTENT).send();
    } catch (error) {
      console.error('[WEB_ADMIN_DELETE_USER]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to delete user',
      });
    }
  }

  /**
   * 获取订单列表
   * GET /api/web/admin/orders
   */
  @Get('orders')
  async getOrders(
    @Query() query: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const orders = await this.webAdminService.getOrders(query);
      return res.json(orders);
    } catch (error) {
      console.error('[WEB_ADMIN_ORDERS]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch orders',
      });
    }
  }

  /**
   * 获取产品列表
   * GET /api/web/admin/products
   */
  @Get('products')
  async getProducts(
    @Query() query: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      if (!this.checkAdminPermission(req, res)) return;

      const products = await this.webAdminService.getProducts(query);
      return res.json(products);
    } catch (error) {
      console.error('[WEB_ADMIN_PRODUCTS]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch products',
      });
    }
  }
}
