"use strict";(()=>{var e={};e.id=9914,e.ids=[9914],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},50852:e=>{e.exports=require("async_hooks")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},84492:e=>{e.exports=require("node:stream")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},56276:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>j,patchFetch:()=>q,requestAsyncStorage:()=>_,routeModule:()=>v,serverHooks:()=>R,staticGenerationAsyncStorage:()=>b});var i={};t.r(i),t.d(i,{GET:()=>y,POST:()=>x,dynamic:()=>p,fetchCache:()=>m,revalidate:()=>g});var a=t(49303),n=t(88716),o=t(60670),s=t(87070),l=t(75571),u=t(90455),d=t(72331),c=t(7410),f=t(60682);let p="force-dynamic",m="force-no-store",g=0,w=c.z.object({affiliateId:c.z.string().optional(),userId:c.z.string().optional(),email:c.z.string().email().optional(),commissionRate:c.z.number().min(0).max(1).optional(),isAdmin:c.z.boolean().optional()});async function h(e,r){let t=await d._.user.findUnique({where:{id:e},select:{role:!0,affiliate:{select:{id:!0,organizationId:!0,isAdmin:!0}}}});return!!t&&("ADMIN"===t.role||t.affiliate?.organizationId===r&&!!t.affiliate.isAdmin)}async function y(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=r.id;if(!await h(e.user.id,t))return s.NextResponse.json({error:"Access denied"},{status:403});let i=await d._.affiliateProfile.findMany({where:{organizationId:t},select:{id:!0,code:!0,commissionRate:!0,totalEarnings:!0,isAdmin:!0,createdAt:!0,user:{select:{id:!0,name:!0,email:!0,image:!0}}},orderBy:{createdAt:"desc"}});return s.NextResponse.json({members:i})}catch(e){return console.error("Error fetching organization members:",e),s.NextResponse.json({error:"Failed to fetch organization members"},{status:500})}}async function x(e,{params:r}){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let i=r.id;if(!await h(t.user.id,i))return s.NextResponse.json({error:"Access denied"},{status:403});let a=await e.json(),n=w.safeParse(a);if(!n.success)return s.NextResponse.json({error:n.error.errors},{status:400});let{affiliateId:o,userId:c,email:p,commissionRate:m,isAdmin:g}=n.data,y=await d._.affiliateOrganization.findUnique({where:{id:i}});if(!y)return s.NextResponse.json({error:"Organization not found"},{status:404});if(o){let e=await d._.affiliateProfile.findUnique({where:{id:o}});if(!e)return s.NextResponse.json({error:"Affiliate not found"},{status:404});if(e.organizationId)return s.NextResponse.json({error:"Affiliate is already a member of an organization"},{status:400});let r=await d._.affiliateProfile.update({where:{id:o},data:{organizationId:i,...void 0!==m&&{commissionRate:m},...void 0!==g&&{isAdmin:g}}});return s.NextResponse.json(r)}if(c){let e=await d._.user.findUnique({where:{id:c},include:{affiliate:!0}});if(!e)return s.NextResponse.json({error:"User not found"},{status:404});if(e.affiliate){if(e.affiliate.organizationId)return s.NextResponse.json({error:"User is already a member of an organization"},{status:400});let r=await d._.affiliateProfile.update({where:{id:e.affiliate.id},data:{organizationId:i,...void 0!==m&&{commissionRate:m},...void 0!==g&&{isAdmin:g}}});return s.NextResponse.json(r)}let r=`${e.name?.substring(0,3)||"AFF"}-${Math.random().toString(36).substring(2,8).toUpperCase()}`,t=await d._.affiliateProfile.create({data:{userId:e.id,code:r,organizationId:i,...void 0!==m&&{commissionRate:m},...void 0!==g&&{isAdmin:g}}});return s.NextResponse.json(t)}if(p){let e=await d._.user.findUnique({where:{email:p},include:{affiliate:!0}}),r=`INV-${Math.random().toString(36).substring(2,8).toUpperCase()}`,a=await d._.organizationInvite.create({data:{organizationId:i,email:p,inviteCode:r,...e?.affiliate&&{affiliateId:e.affiliate.id},expiresAt:new Date(Date.now()+6048e5)}});if(y){let e=`http://localhost:8000/invite/${r}`;await (0,f.Z_)(p,y.name,e,a.expiresAt,t.user.name||void 0)}return s.NextResponse.json(a)}return s.NextResponse.json({error:"Either affiliateId, userId, or email must be provided"},{status:400})}catch(e){return console.error("Error adding organization member:",e),s.NextResponse.json({error:"Failed to add organization member"},{status:500})}}c.z.object({commissionRate:c.z.number().min(0).max(1).optional(),isAdmin:c.z.boolean().optional()});let v=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/members/route",pathname:"/api/affiliate/organizations/[id]/members",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/members/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\members\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:_,staticGenerationAsyncStorage:b,serverHooks:R}=v,j="/api/affiliate/organizations/[id]/members/route";function q(){return(0,o.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:b})}},90455:(e,r,t)=>{t.d(r,{L:()=>d});var i=t(7585),a=t(72331),n=t(77234),o=t(53797),s=t(42023),l=t.n(s),u=t(93475);let d={adapter:{...(0,i.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:i,request:n}){try{if(t&&t.id){let r=n?.headers||new Headers,o=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";i?u=i.code&&!i.password?"email_code":"password":e&&(u=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],i=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(i).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let i=new URL(e);if(t.some(e=>i.hostname===e||i.hostname.includes(e)||i.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(i);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return i}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:i}){if("update"===t&&i)return{...e,...i.user};let n=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>a});var i=t(53524);let a=global.prisma||new i.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>d});var i=t(62197),a=t.n(i);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,r,t=300){try{let i=o(),a=`verification_code:${e}`;return await i.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function d(e,r,t){try{let i=o(),a=`rate_limit:${e}`,n=await i.get(a),s=n?parseInt(n):0;if(s>=r)return!1;return 0===s?await i.setex(a,t,"1"):await i.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=a?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(i,n,s):i[n]=e[n]}return i.default=e,t&&t.set(e,i),i}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7410,5637,682],()=>t(56276));module.exports=i})();