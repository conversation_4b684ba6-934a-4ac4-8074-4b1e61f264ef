{"/_not-found/page": "app/_not-found/page.js", "/about/page": "app/about/page.js", "/activate/page": "app/activate/page.js", "/api/admin/orders/[orderId]/update-status/route": "app/api/admin/orders/[orderId]/update-status/route.js", "/api/admin/products/bulk-price-update/route": "app/api/admin/products/bulk-price-update/route.js", "/api/admin/products/[productId]/force-delete/route": "app/api/admin/products/[productId]/force-delete/route.js", "/auth/error/page": "app/auth/error/page.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/auth/login/page": "app/auth/login/page.js", "/auth/reset-password/page": "app/auth/reset-password/page.js", "/checkout/page": "app/checkout/page.js", "/bind-card/page": "app/bind-card/page.js", "/contact/page": "app/contact/page.js", "/esims/[productId]/page": "app/esims/[productId]/page.js", "/help/page": "app/help/page.js", "/faq/page": "app/faq/page.js", "/cards/page": "app/cards/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/cards/[cardId]/page": "app/cards/[cardId]/page.js", "/how-it-works/page": "app/how-it-works/page.js", "/invite/error/page": "app/invite/error/page.js", "/odoo-integration/page": "app/odoo-integration/page.js", "/esims/page": "app/esims/page.js", "/cart/page": "app/cart/page.js", "/landing/page": "app/landing/page.js", "/page": "app/page.js", "/orders/page": "app/orders/page.js", "/orders/[orderId]/page": "app/orders/[orderId]/page.js", "/terms/page": "app/terms/page.js", "/presale/page": "app/presale/page.js", "/products/[productId]/page": "app/products/[productId]/page.js", "/test/uid-extraction/page": "app/test/uid-extraction/page.js", "/products/page": "app/products/page.js", "/yolloo-smart/page": "app/yolloo-smart/page.js", "/pricing/page": "app/pricing/page.js", "/api/addresses/default/route": "app/api/addresses/default/route.js", "/api/[...path]/route": "app/api/[...path]/route.js", "/api/addresses/[addressId]/route": "app/api/addresses/[addressId]/route.js", "/api/admin/categories/route": "app/api/admin/categories/route.js", "/api/admin/commissions/[id]/route": "app/api/admin/commissions/[id]/route.js", "/api/admin/affiliates/route": "app/api/admin/affiliates/route.js", "/api/admin/cards/[cardId]/route": "app/api/admin/cards/[cardId]/route.js", "/api/admin/affiliates/[affiliateId]/route": "app/api/admin/affiliates/[affiliateId]/route.js", "/api/admin/commissions/route": "app/api/admin/commissions/route.js", "/api/admin/cards/import/route": "app/api/admin/cards/import/route.js", "/api/addresses/route": "app/api/addresses/route.js", "/api/admin/categories/[categoryId]/route": "app/api/admin/categories/[categoryId]/route.js", "/api/admin/dashboard/route": "app/api/admin/dashboard/route.js", "/api/admin/organizations/[id]/analytics/route": "app/api/admin/organizations/[id]/analytics/route.js", "/api/admin/orders/route": "app/api/admin/orders/route.js", "/api/admin/cards/route": "app/api/admin/cards/route.js", "/api/admin/orders/[orderId]/route": "app/api/admin/orders/[orderId]/route.js", "/api/admin/organizations/[id]/invites/route": "app/api/admin/organizations/[id]/invites/route.js", "/api/admin/organizations/[id]/members/[memberId]/route": "app/api/admin/organizations/[id]/members/[memberId]/route.js", "/api/admin/orders/[orderId]/odoo/route": "app/api/admin/orders/[orderId]/odoo/route.js", "/api/admin/organizations/[id]/members/route": "app/api/admin/organizations/[id]/members/route.js", "/api/admin/organizations/[id]/route": "app/api/admin/organizations/[id]/route.js", "/api/admin/products/[productId]/check-orders/route": "app/api/admin/products/[productId]/check-orders/route.js", "/api/admin/organizations/[id]/members/batch/route": "app/api/admin/organizations/[id]/members/batch/route.js", "/api/admin/organizations/[id]/refresh-stats/route": "app/api/admin/organizations/[id]/refresh-stats/route.js", "/api/admin/organizations/route": "app/api/admin/organizations/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/products/[productId]/route": "app/api/admin/products/[productId]/route.js", "/api/admin/products/sync-qr-products/route": "app/api/admin/products/sync-qr-products/route.js", "/api/admin/products/route": "app/api/admin/products/route.js", "/api/admin/products/sync/route": "app/api/admin/products/sync/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/admin/users/search/route": "app/api/admin/users/search/route.js", "/api/admin/users/batch/route": "app/api/admin/users/batch/route.js", "/api/affiliate/dashboard/route": "app/api/affiliate/dashboard/route.js", "/api/admin/users/[userId]/route": "app/api/admin/users/[userId]/route.js", "/api/affiliate/generate-link/route": "app/api/affiliate/generate-link/route.js", "/api/affiliate/invites/accept-logged-in/route": "app/api/affiliate/invites/accept-logged-in/route.js", "/api/affiliate/invites/verify/route": "app/api/affiliate/invites/verify/route.js", "/api/affiliate/invites/accept/route": "app/api/affiliate/invites/accept/route.js", "/api/admin/sync-products/route": "app/api/admin/sync-products/route.js", "/api/affiliate/invites/reject/route": "app/api/affiliate/invites/reject/route.js", "/api/affiliate/organizations/[id]/invites/batch/route": "app/api/affiliate/organizations/[id]/invites/batch/route.js", "/api/affiliate/organizations/[id]/invites/[inviteId]/route": "app/api/affiliate/organizations/[id]/invites/[inviteId]/route.js", "/api/affiliate/organizations/[id]/analytics/route": "app/api/affiliate/organizations/[id]/analytics/route.js", "/api/admin/products/countries/route": "app/api/admin/products/countries/route.js", "/api/affiliate/organizations/[id]/members/[memberId]/route": "app/api/affiliate/organizations/[id]/members/[memberId]/route.js", "/api/affiliate/organizations/[id]/members/batch/route": "app/api/affiliate/organizations/[id]/members/batch/route.js", "/api/affiliate/organizations/[id]/invites/general/route": "app/api/affiliate/organizations/[id]/invites/general/route.js", "/api/admin/products/delete-all/route": "app/api/admin/products/delete-all/route.js", "/api/affiliate/organizations/[id]/members/create-accounts/route": "app/api/affiliate/organizations/[id]/members/create-accounts/route.js", "/api/affiliate/organizations/[id]/refresh-stats/route": "app/api/affiliate/organizations/[id]/refresh-stats/route.js", "/api/affiliate/profile/leave-organization/route": "app/api/affiliate/profile/leave-organization/route.js", "/api/affiliate/organizations/[id]/invites/route": "app/api/affiliate/organizations/[id]/invites/route.js", "/api/affiliate/organizations/[id]/withdrawals/route": "app/api/affiliate/organizations/[id]/withdrawals/route.js", "/api/affiliate/visit/route": "app/api/affiliate/visit/route.js", "/api/affiliate/organizations/[id]/route": "app/api/affiliate/organizations/[id]/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/affiliate/track/route": "app/api/affiliate/track/route.js", "/api/affiliate/organizations/[id]/members/route": "app/api/affiliate/organizations/[id]/members/route.js", "/api/auth/forgot-password/route": "app/api/auth/forgot-password/route.js", "/api/auth/change-password/route": "app/api/auth/change-password/route.js", "/api/auth/send-code/route": "app/api/auth/send-code/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/auth/reset-password/route": "app/api/auth/reset-password/route.js", "/api/cards/[cardId]/activate/route": "app/api/cards/[cardId]/activate/route.js", "/api/boss/orders/route": "app/api/boss/orders/route.js", "/api/auth/verify-code/route": "app/api/auth/verify-code/route.js", "/api/cards/bind/route": "app/api/cards/bind/route.js", "/api/affiliate/organizations/route": "app/api/affiliate/organizations/route.js", "/api/cards/route": "app/api/cards/route.js", "/api/cards/[cardId]/route": "app/api/cards/[cardId]/route.js", "/api/auth/reset-password/verify/route": "app/api/auth/reset-password/verify/route.js", "/api/cart/route": "app/api/cart/route.js", "/api/boss/packages/route": "app/api/boss/packages/route.js", "/api/email/route": "app/api/email/route.js", "/api/cart/[cartItemId]/route": "app/api/cart/[cartItemId]/route.js", "/api/esims/parse-qr/route": "app/api/esims/parse-qr/route.js", "/api/esims/route": "app/api/esims/route.js", "/api/odoo/invoices/route": "app/api/odoo/invoices/route.js", "/api/esims/profiles/route": "app/api/esims/profiles/route.js", "/api/odoo/orders/qrcode/route": "app/api/odoo/orders/qrcode/route.js", "/api/odoo/orders/route": "app/api/odoo/orders/route.js", "/api/odoo/orders/status/route": "app/api/odoo/orders/status/route.js", "/api/odoo/products/route": "app/api/odoo/products/route.js", "/api/esims/external-activation/route": "app/api/esims/external-activation/route.js", "/api/odoo/products/price/route": "app/api/odoo/products/price/route.js", "/api/orders/available-esims/route": "app/api/orders/available-esims/route.js", "/api/payments/route": "app/api/payments/route.js", "/api/odoo/webhook/route": "app/api/odoo/webhook/route.js", "/api/orders/[orderId]/route": "app/api/orders/[orderId]/route.js", "/api/presale/subscribe/route": "app/api/presale/subscribe/route.js", "/api/products/[productId]/route": "app/api/products/[productId]/route.js", "/api/products/[productId]/status/route": "app/api/products/[productId]/status/route.js", "/api/products/batch/route": "app/api/products/batch/route.js", "/api/orders/route": "app/api/orders/route.js", "/api/products/external-data/route": "app/api/products/external-data/route.js", "/api/products/get-card-links/route": "app/api/products/get-card-links/route.js", "/api/products/by-code/route": "app/api/products/by-code/route.js", "/api/products/route": "app/api/products/route.js", "/api/products/countries/route": "app/api/products/countries/route.js", "/api/products/paginated/route": "app/api/products/paginated/route.js", "/api/stock/check/route": "app/api/stock/check/route.js", "/api/route": "app/api/route.js", "/api/test-orders/route": "app/api/test-orders/route.js", "/api/users/route": "app/api/users/route.js", "/api/test-organization-data/[id]/route": "app/api/test-organization-data/[id]/route.js", "/api/yolloo-smart/products/route": "app/api/yolloo-smart/products/route.js", "/invite/[code]/page": "app/invite/[code]/page.js", "/api/payments/webhook/route": "app/api/payments/webhook/route.js", "/admin/commissions/page": "app/admin/commissions/page.js", "/admin/affiliates/page": "app/admin/affiliates/page.js", "/admin/organizations/page": "app/admin/organizations/page.js", "/admin/organizations/[id]/members/[memberId]/page": "app/admin/organizations/[id]/members/[memberId]/page.js", "/admin/subscribers/page": "app/admin/subscribers/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/organizations/[id]/page": "app/admin/organizations/[id]/page.js", "/admin/cards/[cardId]/page": "app/admin/cards/[cardId]/page.js", "/admin/cards/import/page": "app/admin/cards/import/page.js", "/admin/cards/new/page": "app/admin/cards/new/page.js", "/admin/cards/page": "app/admin/cards/page.js", "/admin/page": "app/admin/page.js", "/affiliate/organization/[id]/analytics/page": "app/affiliate/organization/[id]/analytics/page.js", "/affiliate/organization/[id]/edit/page": "app/affiliate/organization/[id]/edit/page.js", "/affiliate/organization/[id]/members/invite/page": "app/affiliate/organization/[id]/members/invite/page.js", "/affiliate/organization/[id]/members/page": "app/affiliate/organization/[id]/members/page.js", "/affiliate/organization/[id]/page": "app/affiliate/organization/[id]/page.js", "/affiliate/organization/page": "app/affiliate/organization/page.js", "/affiliate/organization/[id]/withdrawals/page": "app/affiliate/organization/[id]/withdrawals/page.js", "/affiliate/page": "app/affiliate/page.js", "/auth/signup/page": "app/auth/signup/page.js", "/account/page": "app/account/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/admin/orders/[orderId]/page": "app/admin/orders/[orderId]/page.js", "/admin/orders/page": "app/admin/orders/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/products/[productId]/page": "app/admin/products/[productId]/page.js", "/admin/users/[userId]/page": "app/admin/users/[userId]/page.js", "/admin/products/page": "app/admin/products/page.js", "/admin/products/new/page": "app/admin/products/new/page.js", "/admin/products/sync/page": "app/admin/products/sync/page.js"}