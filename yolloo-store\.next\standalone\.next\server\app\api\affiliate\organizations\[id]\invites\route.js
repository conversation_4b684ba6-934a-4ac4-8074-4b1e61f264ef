"use strict";(()=>{var e={};e.id=6708,e.ids=[6708],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},50852:e=>{e.exports=require("async_hooks")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},84492:e=>{e.exports=require("node:stream")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},483:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>O,patchFetch:()=>R,requestAsyncStorage:()=>_,routeModule:()=>E,serverHooks:()=>I,staticGenerationAsyncStorage:()=>D});var i={};r.r(i),r.d(i,{GET:()=>v,POST:()=>x,dynamic:()=>p,fetchCache:()=>g,revalidate:()=>y});var n=r(49303),a=r(88716),o=r(60670),s=r(87070),l=r(75571),u=r(90455),c=r(72331),d=r(7410),f=r(60682),m=r(50650);let p="force-dynamic",g="force-no-store",y=0,h=d.z.object({email:d.z.string().email(),affiliateId:d.z.string().optional()});async function w(e,t){let r=await c._.user.findUnique({where:{id:e},select:{role:!0,affiliate:{select:{id:!0,organizationId:!0,isAdmin:!0}}}});return!!r&&("ADMIN"===r.role||r.affiliate?.organizationId===t&&!!r.affiliate.isAdmin)}async function v(e,{params:t}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let r=t.id;if(!await w(e.user.id,r))return s.NextResponse.json({error:"Access denied"},{status:403});let i=await c._.organizationInvite.findMany({where:{organizationId:r},include:{affiliate:{select:{id:!0,user:{select:{id:!0,name:!0,email:!0,image:!0}}}}},orderBy:{createdAt:"desc"}});return s.NextResponse.json(i)}catch(e){return console.error("Error fetching organization invites:",e),s.NextResponse.json({error:"Failed to fetch organization invites"},{status:500})}}async function x(e,{params:t}){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let i=t.id;if(!await w(r.user.id,i))return s.NextResponse.json({error:"Access denied"},{status:403});let n=await e.json(),a=h.safeParse(n);if(!a.success)return s.NextResponse.json({error:a.error.errors},{status:400});let{email:o,affiliateId:d}=a.data,p=await c._.affiliateOrganization.findUnique({where:{id:i}});if(!p)return s.NextResponse.json({error:"Organization not found"},{status:404});if(await c._.organizationInvite.findFirst({where:{organizationId:i,email:o,status:"PENDING",expiresAt:{gt:new Date}}}))return s.NextResponse.json({error:"An active invite already exists for this email"},{status:400});if(d){let e=await c._.affiliateProfile.findUnique({where:{id:d}});if(!e)return s.NextResponse.json({error:"Affiliate not found"},{status:404});if(e.organizationId)return s.NextResponse.json({error:"Affiliate is already a member of an organization"},{status:400})}let g=`INV-${Math.random().toString(36).substring(2,8).toUpperCase()}`,y=await c._.organizationInvite.create({data:{organizationId:i,email:o,inviteCode:g,...d&&{affiliateId:d},expiresAt:m.ED.addDays(new Date,7)}}),v=`http://localhost:8000/invite/${g}`;return await (0,f.Z_)(o,p.name,v,y.expiresAt,r.user.name||void 0),s.NextResponse.json(y)}catch(e){return console.error("Error creating invite:",e),s.NextResponse.json({error:"Failed to create invite"},{status:500})}}let E=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/invites/route",pathname:"/api/affiliate/organizations/[id]/invites",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/invites/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\invites\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:_,staticGenerationAsyncStorage:D,serverHooks:I}=E,O="/api/affiliate/organizations/[id]/invites/route";function R(){return(0,o.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:D})}},90455:(e,t,r)=>{r.d(t,{L:()=>c});var i=r(7585),n=r(72331),a=r(77234),o=r(53797),s=r(42023),l=r.n(s),u=r(93475);let c={adapter:{...(0,i.N)(n._),getUser:async e=>{let t=await n._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await n._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await n._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await n._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await l().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,u.Ak)(e.email);if(!t||t!==e.code)return null;await (0,u.qc)(e.email);let r=await n._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await n._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await n._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:i,request:a}){try{if(r&&r.id){let t=a?.headers||new Headers,o=t.get("user-agent")||"",s=t.get("x-forwarded-for"),l=s?s.split(/, /)[0]:t.get("REMOTE_ADDR")||"",u="unknown";i?u=i.code&&!i.password?"email_code":"password":e&&(u=e.provider),await n._.userLoginHistory.create({data:{userId:r.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],i=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,n=new URL(i).searchParams.get("callbackUrl");if(n){let e=decodeURIComponent(n);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let i=new URL(e);if(r.some(e=>i.hostname===e||i.hostname.includes(e)||i.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(i);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return i}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:i}){if("update"===r&&i)return{...e,...i.user};let a=await n._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return a?{id:a.id,name:a.name,email:a.email,picture:a.image,role:a.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,t,r)=>{r.d(t,{_:()=>n});var i=r(53524);let n=global.prisma||new i.PrismaClient({log:["error"]})},93475:(e,t,r)=>{r.d(t,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var i=r(62197),n=r.n(i);let a=null;function o(){if(!a){let e=process.env.REDIS_URL||"redis://localhost:6379";(a=new(n())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),a.on("connect",()=>{console.log("Successfully connected to Redis")})}return a}async function s(e,t,r=300){try{let i=o(),n=`verification_code:${e}`;return await i.setex(n,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let t=o(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let t=o(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let i=o(),n=`rate_limit:${e}`,a=await i.get(n),s=a?parseInt(a):0;if(s>=t)return!1;return 0===s?await i.setex(n,r,"1"):await i.incr(n),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},50650:(e,t,r)=>{r.d(t,{CN:()=>p,ED:()=>g,QG:()=>h,T4:()=>c,cn:()=>u,eP:()=>v,mo:()=>w,vI:()=>y});var i=r(55761),n=r(62386),a=r(6180),o=r(4284),s=r(35772),l=r(21740);function u(...e){return(0,n.m6)((0,i.W)(e))}function c(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let d={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function f(){return d.TIMEZONE}function m(e){if(!e)return null;try{let t;if(e instanceof Date)t=e;else if("string"==typeof e)t=e.includes("T")||e.includes("Z")?(0,a.D)(e):new Date(e);else{if("number"!=typeof e)return null;t=new Date(e)}return(0,o.J)(t)?t:null}catch(t){return console.warn("Date parsing error:",t,"Input:",e),null}}let p={short:(e,t="Invalid Date")=>{let r=m(e);return r?(0,s.WU)(r,d.DATE_FORMAT):t},full:(e,t="Invalid Date")=>{let r=m(e);return r?(0,s.WU)(r,d.DATETIME_FORMAT):t},long:(e,t="Invalid Date")=>{let r=m(e);return r?r.toLocaleDateString(d.LOCALE,{year:"numeric",month:"long",day:"numeric"}):t},relative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,l.Q)(r,{addSuffix:!0}):t},time:(e,t="Invalid Time")=>{let r=m(e);return r?(0,s.WU)(r,"HH:mm:ss"):t},timeShort:(e,t="Invalid Time")=>{let r=m(e);return r?(0,s.WU)(r,"HH:mm"):t},withTimezone:(e,t,r="Invalid Date")=>{let i=m(e);return i?new Intl.DateTimeFormat(d.LOCALE,{timeZone:t||d.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(i):r},forUser:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let i=f();return new Intl.DateTimeFormat(d.LOCALE,{timeZone:i,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserShort:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let i=f();return new Intl.DateTimeFormat(d.LOCALE,{timeZone:i,year:"numeric",month:"2-digit",day:"2-digit"}).format(r)},forUserSafe:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let i=d.TIMEZONE;return new Intl.DateTimeFormat(d.LOCALE,{timeZone:i,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserRelative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,l.Q)(r,{addSuffix:!0}):t},iso:(e,t="")=>{let r=m(e);return r?r.toISOString():t},custom:(e,t,r="Invalid Date")=>{let i=m(e);return i?(0,s.WU)(i,t):r}},g={addDays:(e,t)=>new Date((m(e)||new Date).getTime()+864e5*t),addHours:(e,t)=>new Date((m(e)||new Date).getTime()+36e5*t),addMinutes:(e,t)=>new Date((m(e)||new Date).getTime()+6e4*t),daysBetween:(e,t)=>{let r=m(e),i=m(t);return r&&i?Math.floor((i.getTime()-r.getTime())/864e5):0},isExpired:e=>{let t=m(e);return!!t&&t.getTime()<Date.now()}};function y(e){return e?e.replace(/\D/g,""):""}function h(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function w(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function v(e){let t=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return t?{badge:t[1].toUpperCase(),name:t[2]}:{name:e}}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var n=r(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=n?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}(r(45609));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7624,7410,5637,682],()=>r(483));module.exports=i})();