(()=>{var e={};e.id=6047,e.ids=[6047],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},27618:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d}),t(26731),t(89090),t(26083),t(35866);var s=t(23191),a=t(88716),o=t(37922),i=t.n(o),l=t(95231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let d=["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26731)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\forgot-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\forgot-password\\page.tsx"],m="/auth/forgot-password/page",p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74955:(e,r,t)=>{Promise.resolve().then(t.bind(t,79066))},79066:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var s=t(10326),a=t(17577),o=t(35047),i=t(90434),l=t(27256),n=t(74064),d=t(74723),c=t(90772),m=t(54432),p=t(55632),u=t(33071),x=t(77506),f=t(85999);let h=l.z.object({email:l.z.string().email("Please enter a valid email address")});function y(){(0,o.useRouter)();let[e,r]=(0,a.useState)(!1),[t,l]=(0,a.useState)(!1),y=(0,d.cI)({resolver:(0,n.F)(h),defaultValues:{email:""}}),j=async e=>{try{r(!0);let t=await fetch("/api/auth/forgot-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();throw Error(e.error||"Error requesting password reset")}l(!0),f.A.success("If your email is in our system, you will receive a password reset link shortly.")}catch(r){let e="Error requesting password reset";r instanceof Error&&(e=r.message),f.A.error(e)}finally{r(!1)}};return t?s.jsx("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:(0,s.jsxs)(u.Zb,{className:"w-[400px]",children:[(0,s.jsxs)(u.Ol,{className:"text-center",children:[s.jsx(u.ll,{children:"Check Your Email"}),s.jsx(u.SZ,{children:"If your email is in our system, you will receive a password reset link."})]}),(0,s.jsxs)(u.aY,{className:"text-center py-6",children:[s.jsx("p",{className:"mb-4 text-muted-foreground",children:"Please check your inbox and spam folder. The reset link will be valid for 7 days."}),s.jsx(c.Button,{asChild:!0,children:s.jsx(i.default,{href:"/auth/signin",children:"Return to Sign In"})})]})]})}):s.jsx("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:(0,s.jsxs)(u.Zb,{className:"w-[400px]",children:[(0,s.jsxs)(u.Ol,{children:[s.jsx(u.ll,{children:"Forgot Password"}),s.jsx(u.SZ,{children:"Enter your email address and we'll send you a link to reset your password."})]}),s.jsx(u.aY,{children:s.jsx(p.l0,{...y,children:(0,s.jsxs)("form",{onSubmit:y.handleSubmit(j),className:"space-y-4",children:[s.jsx(p.Wi,{control:y.control,name:"email",render:({field:r})=>(0,s.jsxs)(p.xJ,{children:[s.jsx(p.lX,{children:"Email"}),s.jsx(p.NI,{children:s.jsx(m.I,{type:"email",placeholder:"<EMAIL>",...r,disabled:e})}),s.jsx(p.zG,{})]})}),(0,s.jsxs)(c.Button,{type:"submit",className:"w-full",disabled:e,children:[e&&s.jsx(x.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Send Reset Link"]})]})})}),s.jsx(u.eW,{className:"flex justify-center",children:s.jsx(c.Button,{variant:"link",asChild:!0,children:s.jsx(i.default,{href:"/auth/signin",children:"Return to Sign In"})})})]})})}},33071:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>l,SZ:()=>d,Zb:()=>i,aY:()=>c,eW:()=>m,ll:()=>n});var s=t(10326),a=t(17577),o=t(77863);let i=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));i.displayName="Card";let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let n=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,o.cn)("font-semibold leading-none tracking-tight",e),...r}));n.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",e),...r}));m.displayName="CardFooter"},55632:(e,r,t)=>{"use strict";t.d(r,{NI:()=>h,Wi:()=>m,l0:()=>d,lX:()=>f,pf:()=>y,xJ:()=>x,zG:()=>j});var s=t(10326),a=t(17577),o=t(34214),i=t(74723),l=t(77863),n=t(31048);let d=i.RV,c=a.createContext({}),m=({...e})=>s.jsx(c.Provider,{value:{name:e.name},children:s.jsx(i.Qr,{...e})}),p=()=>{let e=a.useContext(c),r=a.useContext(u),{getFieldState:t,formState:s}=(0,i.Gc)(),o=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...o}},u=a.createContext({}),x=a.forwardRef(({className:e,...r},t)=>{let o=a.useId();return s.jsx(u.Provider,{value:{id:o},children:s.jsx("div",{ref:t,className:(0,l.cn)("space-y-2",e),...r})})});x.displayName="FormItem";let f=a.forwardRef(({className:e,...r},t)=>{let{error:a,formItemId:o}=p();return s.jsx(n._,{ref:t,className:(0,l.cn)(a&&"text-destructive",e),htmlFor:o,...r})});f.displayName="FormLabel";let h=a.forwardRef(({...e},r)=>{let{error:t,formItemId:a,formDescriptionId:i,formMessageId:l}=p();return s.jsx(o.g7,{ref:r,id:a,"aria-describedby":t?`${i} ${l}`:`${i}`,"aria-invalid":!!t,...e})});h.displayName="FormControl";let y=a.forwardRef(({className:e,...r},t)=>{let{formDescriptionId:a}=p();return s.jsx("p",{ref:t,id:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...r})});y.displayName="FormDescription";let j=a.forwardRef(({className:e,children:r,...t},a)=>{let{error:o,formMessageId:i}=p(),n=o?String(o?.message):r;return n?s.jsx("p",{ref:a,id:i,className:(0,l.cn)("text-sm font-medium text-destructive",e),...t,children:n}):null});j.displayName="FormMessage"},31048:(e,r,t)=>{"use strict";t.d(r,{_:()=>d});var s=t(10326),a=t(17577),o=t(34478),i=t(79360),l=t(77863);let n=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>s.jsx(o.f,{ref:t,className:(0,l.cn)(n(),e),...r}));d.displayName=o.f.displayName},26731:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>i,__esModule:()=>o,default:()=>l});var s=t(68570);let a=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\forgot-password\page.tsx`),{__esModule:o,$$typeof:i}=a;a.default;let l=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\forgot-password\page.tsx#default`)},57481:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1615,5772,7624,5634,6621,6908,4824],()=>t(27618));module.exports=s})();