"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebModule = void 0;
const common_1 = require("@nestjs/common");
const web_auth_module_1 = require("./auth/web-auth.module");
const web_products_module_1 = require("./products/web-products.module");
const web_orders_module_1 = require("./orders/web-orders.module");
const web_carts_module_1 = require("./carts/web-carts.module");
const web_users_module_1 = require("./users/web-users.module");
const web_admin_module_1 = require("./admin/web-admin.module");
const web_payments_module_1 = require("./payments/web-payments.module");
const web_esims_module_1 = require("./esims/web-esims.module");
const web_affiliate_module_1 = require("./affiliate/web-affiliate.module");
let WebModule = class WebModule {
};
WebModule = __decorate([
    (0, common_1.Module)({
        imports: [
            web_auth_module_1.WebAuthModule,
            web_products_module_1.WebProductsModule,
            web_orders_module_1.WebOrdersModule,
            web_carts_module_1.WebCartsModule,
            web_users_module_1.WebUsersModule,
            web_admin_module_1.WebAdminModule,
            web_payments_module_1.WebPaymentsModule,
            web_esims_module_1.WebEsimsModule,
            web_affiliate_module_1.WebAffiliateModule,
        ],
        exports: [
            web_auth_module_1.WebAuthModule,
            web_products_module_1.WebProductsModule,
            web_orders_module_1.WebOrdersModule,
            web_carts_module_1.WebCartsModule,
            web_users_module_1.WebUsersModule,
            web_admin_module_1.WebAdminModule,
            web_payments_module_1.WebPaymentsModule,
            web_esims_module_1.WebEsimsModule,
            web_affiliate_module_1.WebAffiliateModule,
        ],
    })
], WebModule);
exports.WebModule = WebModule;
//# sourceMappingURL=web.module.js.map