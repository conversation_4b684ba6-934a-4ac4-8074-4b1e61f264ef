"use strict";(()=>{var e={};e.id=7904,e.ids=[7904],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},24350:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>x,requestAsyncStorage:()=>f,routeModule:()=>h,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var o={};r.r(o),r.d(o,{POST:()=>c,dynamic:()=>u,fetchCache:()=>p,revalidate:()=>d});var s=r(49303),i=r(88716),n=r(60670),a=r(87070),l=r(10835);let u="force-dynamic",p="force-no-store",d=0;async function c(e){try{let{customer_order_ref:t}=await e.json();console.log("Getting QR code for order:",t);let r=await l.ZK.getOrderQRCode(t);return console.log("Full QR code response:",JSON.stringify(r,null,2)),r?.result?.data?.[0]&&(console.log("QR code data for first item:",JSON.stringify(r.result.data[0],null,2)),r.result.data[0].qrcode&&console.log("QR codes:",JSON.stringify(r.result.data[0].qrcode,null,2))),a.NextResponse.json(r)}catch(e){return console.error("Error getting QR code from Odoo:",e),a.NextResponse.json({error:"Failed to get QR code"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/odoo/orders/qrcode/route",pathname:"/api/odoo/orders/qrcode",filename:"route",bundlePath:"app/api/odoo/orders/qrcode/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\odoo\\orders\\qrcode\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:g}=h,y="/api/odoo/orders/qrcode/route";function x(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,i={};function n(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),o=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?o:`${o}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[o,s],...i]=a(e),{domain:n,expires:l,httponly:d,maxage:c,path:h,samesite:f,secure:m,partitioned:g,priority:y}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(s),domain:n,...l&&{expires:new Date(l)},...d&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:h,...f&&{sameSite:u.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...y&&{priority:p.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(i,{RequestCookies:()=>d,ResponseCookies:()=>c,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>n}),e.exports=((e,i,n,a)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let n of o(i))s.call(e,n)||void 0===n||t(e,n,{get:()=>i[n],enumerable:!(a=r(i,n))||a.enumerable});return e})(t({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],p=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>n(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>n(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},c=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let s=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,o,s,i,n=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,i=!1;l();)if(","===(r=e.charAt(a))){for(o=a,a+=1,l(),s=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(i=!0,a=s,n.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!i||a>=e.length)&&n.push(e.substring(t,e.length))}return n}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=n(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(n).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(79925)}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,9092,5972,9712,835],()=>r(24350));module.exports=o})();