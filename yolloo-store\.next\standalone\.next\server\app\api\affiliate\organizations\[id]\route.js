"use strict";(()=>{var e={};e.id=6059,e.ids=[6059],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},72151:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>j,patchFetch:()=>q,requestAsyncStorage:()=>v,routeModule:()=>_,serverHooks:()=>E,staticGenerationAsyncStorage:()=>R});var i={};t.r(i),t.d(i,{DELETE:()=>x,GET:()=>h,PATCH:()=>y,dynamic:()=>p,fetchCache:()=>m,revalidate:()=>f});var a=t(49303),n=t(88716),o=t(60670),s=t(87070),l=t(75571),u=t(90455),c=t(72331),d=t(7410);let p="force-dynamic",m="force-no-store",f=0,g=d.z.object({name:d.z.string().min(3,"Name must be at least 3 characters").optional(),description:d.z.string().optional(),logo:d.z.string().optional(),commissionRate:d.z.number().min(0).max(1).optional(),discountRate:d.z.number().min(0).max(1).optional(),status:d.z.enum(["ACTIVE","INACTIVE","SUSPENDED"]).optional()});async function w(e,r){let t=await c._.user.findUnique({where:{id:e},select:{role:!0,affiliate:{select:{id:!0,organizationId:!0,isAdmin:!0}}}});return!!t&&("ADMIN"===t.role||t.affiliate?.organizationId===r&&!!t.affiliate.isAdmin)}async function h(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=r.id;await c._.affiliateProfile.findUnique({where:{userId:e.user.id},include:{organization:!0}});let i=await c._.affiliateOrganization.findUnique({where:{id:t},include:{_count:{select:{members:!0}},members:{where:{isAdmin:!0},select:{id:!0,user:{select:{id:!0,name:!0,email:!0,image:!0}}}}}});if(!i)return s.NextResponse.json({error:"Organization not found"},{status:404});let a=await c._.affiliateProfile.findFirst({where:{userId:e.user.id,organizationId:t}}),n=a?.isAdmin||!1,o=i.members.map(e=>({id:e.user.id,name:e.user.name,email:e.user.email,image:e.user.image}));return s.NextResponse.json({organization:{id:i.id,name:i.name,description:i.description,logo:i.logo,code:i.code,commissionRate:i.commissionRate,discountRate:i.discountRate,status:i.status,createdAt:i.createdAt,_count:i._count,administrators:o},isMember:!!a,isAdmin:n})}catch(e){return console.error("[ORGANIZATION_GET]",e),s.NextResponse.json({error:"Internal error"},{status:500})}}async function y(e,{params:r}){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let i=r.id;if(!await w(t.user.id,i))return s.NextResponse.json({error:"Access denied"},{status:403});let a=await e.json(),n=g.safeParse(a);if(!n.success)return s.NextResponse.json({error:n.error.errors},{status:400});let o=await c._.affiliateOrganization.update({where:{id:i},data:n.data});return s.NextResponse.json(o)}catch(e){return console.error("Error updating organization:",e),s.NextResponse.json({error:"Failed to update organization"},{status:500})}}async function x(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=await c._.user.findUnique({where:{id:e.user.id},select:{role:!0}});if(!t||"ADMIN"!==t.role)return s.NextResponse.json({error:"Admin access required"},{status:403});let i=r.id;return await c._.affiliateProfile.updateMany({where:{organizationId:i},data:{organizationId:null,isAdmin:!1}}),await c._.affiliateOrganization.delete({where:{id:i}}),s.NextResponse.json({success:!0})}catch(e){return console.error("Error deleting organization:",e),s.NextResponse.json({error:"Failed to delete organization"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/route",pathname:"/api/affiliate/organizations/[id]",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:v,staticGenerationAsyncStorage:R,serverHooks:E}=_,j="/api/affiliate/organizations/[id]/route";function q(){return(0,o.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:R})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var i=t(7585),a=t(72331),n=t(77234),o=t(53797),s=t(42023),l=t.n(s),u=t(93475);let c={adapter:{...(0,i.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:i,request:n}){try{if(t&&t.id){let r=n?.headers||new Headers,o=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";i?u=i.code&&!i.password?"email_code":"password":e&&(u=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],i=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(i).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let i=new URL(e);if(t.some(e=>i.hostname===e||i.hostname.includes(e)||i.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(i);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return i}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:i}){if("update"===t&&i)return{...e,...i.user};let n=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>a});var i=t(53524);let a=global.prisma||new i.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var i=t(62197),a=t.n(i);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,r,t=300){try{let i=o(),a=`verification_code:${e}`;return await i.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let i=o(),a=`rate_limit:${e}`,n=await i.get(a),s=n?parseInt(n):0;if(s>=r)return!1;return 0===s?await i.setex(a,t,"1"):await i.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=a?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(i,n,s):i[n]=e[n]}return i.default=e,t&&t.set(e,i),i}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[8948,1615,9092,5972,2197,2023,7005,7410],()=>t(72151));module.exports=i})();