"use strict";(()=>{var e={};e.id=3731,e.ids=[3731],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},99912:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>$,patchFetch:()=>v,requestAsyncStorage:()=>y,routeModule:()=>h,serverHooks:()=>O,staticGenerationAsyncStorage:()=>w});var o={};t.r(o),t.d(o,{POST:()=>g,dynamic:()=>p,fetchCache:()=>f,revalidate:()=>m});var a=t(49303),s=t(88716),i=t(60670),n=t(87070),d=t(75571),l=t(72331),c=t(90455),u=t(10835);let p="force-dynamic",f="force-no-store",m=0;async function g(e,{params:r}){try{let e=await (0,d.getServerSession)(c.L);if(!e||"ADMIN"!==e.user.role)return new n.NextResponse("Unauthorized",{status:401});let t=await l._.order.findUnique({where:{id:r.orderId},select:{id:!0,userId:!0,total:!0,status:!0,addressId:!0,shippingAddressSnapshot:!0,paymentId:!0,createdAt:!0,updatedAt:!0,referralCode:!0,items:{select:{id:!0,quantity:!0,price:!0,uid:!0,lpaString:!0,productCode:!0,variantCode:!0,variantText:!0}},shippingAddress:!0,user:{select:{id:!0,name:!0,email:!0}}}});if(!t)return new n.NextResponse("Order not found",{status:404});let o=t.shippingAddressSnapshot||t.shippingAddress;if(!o)return new n.NextResponse("Shipping address is required",{status:400});let a="object"==typeof o?o:JSON.parse(o),s=await Promise.all(t.items.map(async e=>{if(!e.productCode)return{...e,product:null};let r=await l._.product.findFirst({where:{sku:e.productCode},include:{category:!0}});return{...e,product:r}}));console.log(`Preparing order lines for ${s.length} items`);let i={};for(let e of s){let r=e.variantCode||"default";i[r]||(i[r]=[]),i[r].push(e)}let p={};for(let[e,r]of Object.entries(i)){let t={};for(let e of r){let r=e.uid||"no-uid";t[r]||(t[r]=[]),t[r].push(e)}for(let[r,o]of Object.entries(t))p[`${e}:::${r}`]=o}let f=s.some(e=>e.product?.category?.name?.toLowerCase()==="qr_code"),m=f?(0,u.$x)(s.filter(e=>e.product?.category?.name?.toLowerCase()==="qr_code")):(0,u.$x)(s.filter(e=>e.product?.category?.name?.toLowerCase()!=="qr_code")),g=f?"QR Odoo Service":"Standard Odoo Service";console.log(`Using ${g} for order ${t.id}`);let h={shipping_address:{name:a.name||"",city:`${a.city||""} ${a.state||""}`,zip:a.postalCode||"",address:`${a.address2||""} ${a.address1||""}, ${a.city||""} ${a.state||""}, ${a.country||""}`,phone:a.phone||"",country:a.country||""},payment_method:"prepaid",email:t.user?.email||""},y=[];try{for(let[e,r]of Object.entries(p)){let[o,a]=e.includes(":::")?e.split(":::"):[e.split("-")[0],e.split("-").slice(1).join("-")];console.log(`Creating Odoo order for group ${e} (variant: ${o}, uid: ${a}) with ${r.length} items`);let s={customer_order_ref:`${t.id}-${e}`,...h,note:`Order manually synced from admin panel (Variant: ${o}, UID: ${a})`,order_lines:r.map(e=>({customer_order_line_ref:`${t.id}-${e.id}`,product_code:e.variantCode||e.productCode||"",card_uid:e.uid||void 0,product_uom_qty:e.quantity,lpa_string:e.lpaString||void 0}))};try{let r=await m.createOrder(s);console.log(`Odoo order created for variant ${o}:`,r);let t=r.result?.status||"unknown",i=r.result?.message||"No response message",n="ok"===t||"success"===t,d=n?"processing":"error",l=n?`Successful: ${i}`:`Failed: ${i}`;y.push({groupKey:e,variantCode:o,uid:a,response:r,status:d,description:l,success:n,odooOrderRef:r.result?.data?.order_name||null})}catch(r){console.error(`Error creating Odoo order for group ${e} (variant: ${o}, uid: ${a}):`,r),y.push({groupKey:e,variantCode:o,uid:a,response:null,status:"error",description:`Error: ${r instanceof Error?r.message:"Unknown error"}`,success:!1,odooOrderRef:null})}}console.log(`删除订单 ${r.orderId} 的默认 odooOrderStatus 记录`);let e=await l._.odooOrderStatus.deleteMany({where:{orderId:r.orderId,variantCode:"default"}});for(let t of(console.log(`已删除 ${e.count} 个默认 odooOrderStatus 记录`),console.log(`准备将 ${y.length} 个Odoo响应记录到数据库`),y))try{let e=t.variantCode||"default",o="no-uid"===t.uid?null:t.uid,a=o?o.replace(/[^0-9,]/g,""):null,s=t.success?"processing":"error";console.log(`[ODOO_CREATE] Processing order ${r.orderId}, variant ${e}, uid=${a||"none"}`);let i=await l._.odooOrderStatus.findFirst({where:{orderId:r.orderId,variantCode:e,uid:a}});i?await l._.odooOrderStatus.update({where:{id:i.id},data:{status:s,description:t.description,odooOrderRef:t.odooOrderRef,lastCheckedAt:new Date}}):await l._.odooOrderStatus.create({data:{orderId:r.orderId,variantCode:e,uid:a,status:s,description:t.description,odooOrderRef:t.odooOrderRef,isDigital:!1,deliveredQty:0,lastCheckedAt:new Date}}),console.log(`OdooOrderStatus 成功记录: orderId=${r.orderId}, variant=${e}, uid=${a||"none"}, status=${s}`)}catch(e){console.error(`记录 OdooOrderStatus 失败: orderId=${r.orderId}, variant=${formattedVariantCode}, uid=${formattedUid||"none"}`),console.error(`错误详情:`,e),e instanceof Error&&(console.error(`错误名称: ${e.name}`),console.error(`错误消息: ${e.message}`),console.error(`堆栈跟踪: ${e.stack}`));try{console.log(`尝试使用替代方法记录 OdooOrderStatus...`),await l._.odooOrderStatus.create({data:{orderId:r.orderId,variantCode:t.variantCode||"unknown",uid:"no-uid"===t.uid?null:t.uid,status:t.success?"processing":"error",description:`${t.description} (备用记录)`,odooOrderRef:t.odooOrderRef,isDigital:!1,deliveredQty:0,lastCheckedAt:new Date}}),console.log(`使用替代方法成功记录 OdooOrderStatus`)}catch(e){console.error(`替代记录方法也失败:`,e)}}let o=y.every(e=>e.success);console.log(`Created ${y.length} Odoo orders for order ${r.orderId}, all success: ${o}`)}catch(e){throw console.error("Failed to create Odoo orders:",e),e}let w=await l._.odooOrderStatus.findMany({where:{orderId:r.orderId}}),O=y.every(e=>e.success)?"success":"error",$="success"===O?`All ${y.length} Odoo orders created successfully using ${g}`:`Some Odoo orders failed to create using ${g}`;return n.NextResponse.json({success:!0,message:$,odooResponses:y.map(e=>({variantCode:e.variantCode,uid:"no-uid"===e.uid?null:e.uid,status:e.status,description:e.description,odooOrderRef:e.odooOrderRef})),odooStatuses:w,serviceType:g})}catch(e){return console.error("[ODOO_CREATE_ORDER]",e),new n.NextResponse(`Error creating Odoo order: ${e instanceof Error?e.message:"Unknown error"}`,{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/orders/[orderId]/odoo/route",pathname:"/api/admin/orders/[orderId]/odoo",filename:"route",bundlePath:"app/api/admin/orders/[orderId]/odoo/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\orders\\[orderId]\\odoo\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:y,staticGenerationAsyncStorage:w,serverHooks:O}=h,$="/api/admin/orders/[orderId]/odoo/route";function v(){return(0,i.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:w})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var o=t(7585),a=t(72331),s=t(77234),i=t(53797),n=t(42023),d=t.n(n),l=t(93475);let c={adapter:{...(0,o.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,i.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await d().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,i.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,l.Ak)(e.email);if(!r||r!==e.code)return null;await (0,l.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:o,request:s}){try{if(t&&t.id){let r=s?.headers||new Headers,i=r.get("user-agent")||"",n=r.get("x-forwarded-for"),d=n?n.split(/, /)[0]:r.get("REMOTE_ADDR")||"",l="unknown";o?l=o.code&&!o.password?"email_code":"password":e&&(l=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:d||null,userAgent:i||null,loginMethod:l,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],o=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(o).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let o=new URL(e);if(t.some(e=>o.hostname===e||o.hostname.includes(e)||o.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(o);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return o}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:o}){if("update"===t&&o)return{...e,...o.user};let s=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},93475:(e,r,t)=>{t.d(r,{AL:()=>n,Ak:()=>d,qc:()=>l,yz:()=>c});var o=t(62197),a=t.n(o);let s=null;function i(){if(!s){let e=process.env.REDIS_URL||"redis://localhost:6379";(s=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),s.on("connect",()=>{console.log("Successfully connected to Redis")})}return s}async function n(e,r,t=300){try{let o=i(),a=`verification_code:${e}`;return await o.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function d(e){try{let r=i(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function l(e){try{let r=i(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let o=i(),a=`rate_limit:${e}`,s=await o.get(a),n=s?parseInt(s):0;if(n>=r)return!1;return 0===n?await o.setex(a,t,"1"):await o.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=a?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(o,s,n):o[s]=e[s]}return o.default=e,t&&t.set(e,o),o}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1615,9092,5972,2197,2023,7005,9712,835],()=>t(99912));module.exports=o})();