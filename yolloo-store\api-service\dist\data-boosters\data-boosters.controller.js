"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBoostersController = void 0;
const common_1 = require("@nestjs/common");
const data_boosters_service_1 = require("./data-boosters.service");
const data_boosters_query_dto_1 = require("./dto/data-boosters-query.dto");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const public_decorator_1 = require("../common/decorators/public.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let DataBoostersController = class DataBoostersController {
    dataBoostersService;
    constructor(dataBoostersService) {
        this.dataBoostersService = dataBoostersService;
    }
    getDataBoosters(query, ctx) {
        return this.dataBoostersService.getDataBoosters(query, ctx);
    }
    getBoosterById(boosterId, ctx) {
        return this.dataBoostersService.getBoosterById(boosterId, ctx);
    }
    createBoosterOrder(orderData, ctx) {
        return this.dataBoostersService.createBoosterOrder(orderData, ctx);
    }
    getBoosterHistory(userId, query, ctx) {
        return this.dataBoostersService.getBoosterHistory(userId, query, ctx);
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_boosters_query_dto_1.DataBoostersQueryDto, Object]),
    __metadata("design:returntype", void 0)
], DataBoostersController.prototype, "getDataBoosters", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(':boosterId'),
    __param(0, (0, common_1.Param)('boosterId')),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], DataBoostersController.prototype, "getBoosterById", null);
__decorate([
    (0, common_1.Post)('order'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_boosters_query_dto_1.DataBoosterOrderDto, Object]),
    __metadata("design:returntype", void 0)
], DataBoostersController.prototype, "createBoosterOrder", null);
__decorate([
    (0, common_1.Get)('history/user'),
    __param(0, (0, current_user_decorator_1.CurrentUser)('id')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, data_boosters_query_dto_1.DataBoostersQueryDto, Object]),
    __metadata("design:returntype", void 0)
], DataBoostersController.prototype, "getBoosterHistory", null);
DataBoostersController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('data-boosters'),
    __metadata("design:paramtypes", [data_boosters_service_1.DataBoostersService])
], DataBoostersController);
exports.DataBoostersController = DataBoostersController;
//# sourceMappingURL=data-boosters.controller.js.map