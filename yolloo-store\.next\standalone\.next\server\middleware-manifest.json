{"version": 3, "middleware": {"/": {"files": ["prerender-manifest.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"missing": [{"type": "header", "key": "Stripe-Signature"}], "regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/payments\\/webhook(.json)?[\\/#\\?]?$", "originalSource": "/api/payments/webhook"}], "wasm": [], "assets": [], "environments": {"previewModeId": "724ddf47befe99297451a61140480bac", "previewModeSigningKey": "bb5a25655f083a52119e20c3f65f7711d225a376889e203267cec7cfee008261", "previewModeEncryptionKey": "3b0d9c62cd45fb11edcaf93c674e239ef596a050bef1571aee35dac452dcfe4f"}}}, "functions": {}, "sortedMiddleware": ["/"]}