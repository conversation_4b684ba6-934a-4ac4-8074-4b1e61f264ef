import { PrismaService } from '../../prisma.service';
export declare class WebUsersService {
    private prisma;
    constructor(prisma: PrismaService);
    getUsers(): Promise<{
        id: string;
        name: string | null;
        email: string | null;
        role: import(".prisma/client").UserRole;
        createdAt: Date;
    }[]>;
    createUser(createUserDto: any): Promise<{
        id: string;
        name: string | null;
        email: string | null;
        role: import(".prisma/client").UserRole;
        createdAt: Date;
    }>;
}
