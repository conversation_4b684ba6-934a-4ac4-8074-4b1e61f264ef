(()=>{var e={};e.id=5445,e.ids=[5445],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},79767:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(45923),r(89090),r(26083),r(35866);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["test",{children:["uid-extraction",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,45923)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\test\\uid-extraction\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\test\\uid-extraction\\page.tsx"],u="/test/uid-extraction/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/uid-extraction/page",pathname:"/test/uid-extraction",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},22989:(e,t,r)=>{Promise.resolve().then(r.bind(r,18226))},18226:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(10326),a=r(17577),n=r(90772),i=r(33071),o=r(567),l=r(87673),d=r(77863);function c(){let[e,t]=(0,a.useState)(`{
  "data": [
    {
      "uid": "29901000000000000042"
    },
    {
      "uid": "29901000000000000043"
    },
    {
      "uid": "29901000000000000044"
    }
  ]
}`),[r,c]=(0,a.useState)(null),[u,p]=(0,a.useState)(null),[m,x]=(0,a.useState)(!1),f=e=>{t(JSON.stringify(e.data,null,2))},g=async()=>{x(!0),p(null);try{let t=JSON.parse(e),r=await fetch("/api/test/uid-extraction",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({testCase:{data:t}})}),s=await r.json();if(!r.ok)throw Error(s.message||"API 请求失败");c(s.extracted)}catch(e){p(e instanceof Error?e.message:String(e)),c(null)}finally{x(!1)}};return(0,s.jsxs)("div",{className:"container py-8",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"UID 提取测试"}),(0,s.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold mb-4",children:"测试用例"}),s.jsx("div",{className:"space-y-2",children:[{name:"单个 UID",data:{data:[{uid:"29901000000000000042"}]}},{name:"多个 UID",data:{data:[{uid:"29901000000000000042"},{uid:"29901000000000000043"},{uid:"29901000000000000044"}]}},{name:"带空格的 UID",data:{data:[{uid:" 29901000000000000042 "},{uid:"29901000000000000043 "}]}},{name:"非数组格式的单个 UID",data:{uid:"29901000000000000042"}},{name:"空数组",data:{data:[]}},{name:"无 UID 数据",data:{data:[{other_field:"value"}]}}].map((e,t)=>s.jsx(n.Button,{variant:"outline",onClick:()=>f(e),className:"mr-2 mb-2",children:e.name},t))}),(0,s.jsxs)("div",{className:"mt-6",children:[s.jsx("h2",{className:"text-lg font-semibold mb-4",children:"输入 JSON"}),s.jsx(l.g,{value:e,onChange:e=>t(e.target.value),className:"font-mono h-64",placeholder:"输入要测试的 JSON 数据"}),s.jsx(n.Button,{onClick:g,className:"mt-4",disabled:m,children:m?"处理中...":"测试 UID 提取"})]})]}),(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-lg font-semibold mb-4",children:"提取结果"}),u&&(0,s.jsxs)(i.Zb,{className:"p-4 bg-red-50 text-red-800 mb-4",children:[s.jsx("p",{className:"font-semibold",children:"错误:"}),s.jsx("p",{children:u})]}),null!==r&&(0,s.jsxs)(i.Zb,{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-4",children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"提取的 UID 字符串:"}),s.jsx("p",{className:"font-mono bg-gray-100 p-2 rounded",children:r||"(无)"})]}),r&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"显示效果:"}),s.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:r.split(",").map(e=>e.trim()).filter(e=>e.length>0).map((e,t)=>s.jsx(o.C,{variant:"outline",className:"h-6 px-2 text-xs bg-purple-50",children:(0,d.QG)(e)},t))})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[s.jsx("h2",{className:"text-lg font-semibold mb-4",children:"说明"}),(0,s.jsxs)(i.Zb,{className:"p-4",children:[s.jsx("p",{className:"mb-2",children:"此页面用于测试 UID 提取逻辑。您可以:"}),(0,s.jsxs)("ul",{className:"list-disc pl-5 space-y-1",children:[s.jsx("li",{children:"选择预定义的测试用例"}),s.jsx("li",{children:"编辑 JSON 输入"}),s.jsx("li",{children:'点击"测试 UID 提取"按钮查看结果'}),s.jsx("li",{children:"查看提取的 UID 字符串和显示效果"})]})]})]})]})]})]})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var s=r(10326);r(17577);var a=r(79360),n=r(77863);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},33071:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>i,aY:()=>c,eW:()=>u,ll:()=>l});var s=r(10326),a=r(17577),n=r(77863);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},87673:(e,t,r)=>{"use strict";r.d(t,{g:()=>i});var s=r(10326),a=r(17577),n=r(77863);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));i.displayName="Textarea"},45923:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\test\uid-extraction\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\test\uid-extraction\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(79767));module.exports=s})();