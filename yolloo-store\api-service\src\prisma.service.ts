import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PrismaService implements OnModuleInit, OnModuleDestroy {
  private prisma: PrismaClient;

  constructor(private configService: ConfigService) {
    // 使用自定义的数据库URL环境变量
    const url = this.configService.get<string>('MOBILE_API_DATABASE_URL');
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url,
        },
      },
    });
  }

  async onModuleInit() {
    await this.prisma.$connect();
  }

  async onModuleDestroy() {
    await this.prisma.$disconnect();
  }

  get user() {
    return this.prisma.user;
  }

  get yollooCard() {
    return this.prisma.yollooCard;
  }

  get esim() {
    return this.prisma.esim;
  }

  get product() {
    return this.prisma.product;
  }

  get category() {
    return this.prisma.category;
  }

  get productVariant() {
    return this.prisma.productVariant;
  }

  get cartItem() {
    return this.prisma.cartItem;
  }

  get order() {
    return this.prisma.order;
  }

  get orderItem() {
    return this.prisma.orderItem;
  }

  get review() {
    return this.prisma.review;
  }

  get notification() {
    return this.prisma.notification;
  }

  get wallet() {
    return this.prisma.wallet;
  }

  get transaction() {
    return this.prisma.transaction;
  }

  get paymentCard() {
    return this.prisma.paymentCard;
  }

  get coupon() {
    return this.prisma.coupon;
  }

  get userCoupon() {
    return this.prisma.userCoupon;
  }

  get packageUsage() {
    return this.prisma.packageUsage;
  }

  get page() {
    return this.prisma.page;
  }

  get article() {
    return this.prisma.article;
  }

  get banner() {
    return this.prisma.banner;
  }

  get homeFeature() {
    return this.prisma.homeFeature;
  }

  get travelTip() {
    return this.prisma.travelTip;
  }

  get continent() {
    return this.prisma.continent;
  }

  get country() {
    return this.prisma.country;
  }

  get mobileOperator() {
    return this.prisma.mobileOperator;
  }

  get rechargeHistory() {
    return this.prisma.rechargeHistory;
  }

  // New models added for services
  get refund() {
    return this.prisma.refund;
  }

  get productView() {
    return this.prisma.productView;
  }

  get socialAccount() {
    return this.prisma.socialAccount;
  }

  get upload() {
    return this.prisma.upload;
  }
}