"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SUPPORTED_FORMATS = exports.IMAGE_SIZES = exports.ImageUtil = void 0;
class ImageUtil {
    static BASE_PATH = '/api/mobile/static/images';
    static getBannerUrl(type, id, language = 'zh', format = 'webp') {
        return `${this.BASE_PATH}/banners/${type}/banner_${type}_${id}_${language}.${format}`;
    }
    static getIconUrl(name, format = 'svg', size) {
        const sizeStr = size && format === 'png' ? `_${size}` : '';
        return `${this.BASE_PATH}/icons/icon_${name}${sizeStr}.${format}`;
    }
    static getProductImageUrl(productId, imageType = 'main', index, format = 'webp') {
        const indexStr = imageType === 'gallery' && index !== undefined ? `_${index}` : '';
        return `${this.BASE_PATH}/products/${productId}_${imageType}${indexStr}.${format}`;
    }
    static getAvatarUrl(userId, size = 128, format = 'webp') {
        return `${this.BASE_PATH}/avatars/avatar_${userId}_${size}.${format}`;
    }
    static getCountryFlagUrl(countryCode, format = 'svg') {
        return `${this.BASE_PATH}/flags/flag_${countryCode.toLowerCase()}.${format}`;
    }
    static getPlaceholderUrl(width, height, text) {
        const textParam = text ? `&text=${encodeURIComponent(text)}` : '';
        return `https://via.placeholder.com/${width}x${height}?${textParam}`;
    }
    static isLocalStaticImage(url) {
        return url.startsWith(this.BASE_PATH);
    }
    static getFullImageUrl(path, baseUrl) {
        if (path.startsWith('http://') || path.startsWith('https://')) {
            return path;
        }
        if (baseUrl) {
            return `${baseUrl.replace(/\/$/, '')}${path}`;
        }
        return path;
    }
    static getResponsiveImageUrl(baseName, type, language = 'zh', deviceType = 'mobile', format = 'webp') {
        const deviceSuffix = deviceType !== 'mobile' ? `_${deviceType}` : '';
        return `${this.BASE_PATH}/${type}s/${baseName}_${language}${deviceSuffix}.${format}`;
    }
}
exports.ImageUtil = ImageUtil;
exports.IMAGE_SIZES = {
    BANNER: {
        HOME: { width: 750, height: 300 },
        CATEGORY: { width: 750, height: 200 },
        PROMOTION: { width: 750, height: 300 },
    },
    ICON: {
        SMALL: 24,
        MEDIUM: 48,
        LARGE: 64,
        XLARGE: 128,
    },
    AVATAR: {
        SMALL: 32,
        MEDIUM: 64,
        LARGE: 128,
        XLARGE: 256,
    },
    PRODUCT: {
        THUMB: { width: 150, height: 150 },
        MAIN: { width: 400, height: 400 },
        GALLERY: { width: 800, height: 600 },
    },
};
exports.SUPPORTED_FORMATS = ['webp', 'jpg', 'jpeg', 'png', 'svg'];
//# sourceMappingURL=image.util.js.map