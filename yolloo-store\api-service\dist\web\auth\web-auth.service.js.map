{"version": 3, "file": "web-auth.service.js", "sourceRoot": "", "sources": ["../../../src/web/auth/web-auth.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAuG;AACvG,yDAAqD;AACrD,iCAAiC;AACjC,iCAAiC;AAEjC,IACa,cAAc,GAD3B,MACa,cAAc;IACL;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAK7C,KAAK,CAAC,MAAM,CAAC,IAAY,EAAE,KAAa,EAAE,QAAgB;QAExD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACrD,KAAK,EAAE,EAAE,KAAK,EAAE;SACjB,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;SACrD;QAGD,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAGvD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACzC,IAAI,EAAE;gBACJ,IAAI;gBACJ,KAAK;gBACL,cAAc;gBACd,IAAI,EAAE,UAAU;aACjB;YACD,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,SAAS,EAAE,IAAI;aAChB;SACF,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,2BAA2B;YACpC,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,KAAa;QAChC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,KAAK,EAAE;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;SAC/C;QAGD,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC;QAGjD,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAC9C,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;SAC3B,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC1C,IAAI,EAAE;gBACJ,KAAK;gBACL,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,SAAS;aACV;SACF,CAAC,CAAC;QAKH,OAAO;YACL,OAAO,EAAE,2BAA2B;SACrC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,KAAa,EAAE,WAAmB;QACpD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;YACjE,KAAK,EAAE,EAAE,KAAK,EAAE;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE;YACpD,MAAM,IAAI,4BAAmB,CAAC,0BAA0B,CAAC,CAAC;SAC3D;QAGD,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC1D,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,MAAM,EAAE;YAChC,IAAI,EAAE,EAAE,cAAc,EAAE;SACzB,CAAC,CAAC;QAGH,MAAM,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC1C,KAAK,EAAE,EAAE,KAAK,EAAE;SACjB,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,6BAA6B;SACvC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,KAAa,EAAE,IAAY;QAIpD,OAAO;YACL,OAAO,EAAE,wBAAwB;YACjC,KAAK;YACL,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,KAAa,EAAE,IAAY,EAAE,IAAY;QAIxD,OAAO;YACL,OAAO,EAAE,4BAA4B;YACrC,KAAK;YACL,IAAI;SACL,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,eAAuB,EAAE,WAAmB;QAC/E,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACjC,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;SAC/C;QAGD,MAAM,sBAAsB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1F,IAAI,CAAC,sBAAsB,EAAE;YAC3B,MAAM,IAAI,4BAAmB,CAAC,+BAA+B,CAAC,CAAC;SAChE;QAGD,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC7D,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACrB,IAAI,EAAE,EAAE,cAAc,EAAE,iBAAiB,EAAE;SAC5C,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,+BAA+B;SACzC,CAAC;IACJ,CAAC;CACF,CAAA;AAvKY,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,cAAc,CAuK1B;AAvKY,wCAAc"}