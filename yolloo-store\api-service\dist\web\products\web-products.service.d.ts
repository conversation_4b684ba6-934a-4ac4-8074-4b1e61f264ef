import { PrismaService } from '../../prisma.service';
export declare class WebProductsService {
    private prisma;
    constructor(prisma: PrismaService);
    getProducts(query: any): Promise<{
        id: string;
        name: string;
        description: string;
        websiteDescription: string;
        price: number;
        off_shelve: boolean;
        parameters: (import("@prisma/client/runtime").GetResult<{
            id: string;
            code: string;
            name: string;
            value: string;
            productId: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        category: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string | null;
            image: string | null;
            parentId: string | null;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {};
        country: string | null;
        countryCode: string | null;
        dataSize: number | null;
        planType: string | null;
    }[]>;
    getProductById(productId: string): Promise<({
        category: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string | null;
            image: string | null;
            parentId: string | null;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {};
        variants: (import("@prisma/client/runtime").GetResult<{
            id: string;
            price: import("@prisma/client/runtime").Decimal;
            currency: string;
            productId: string;
            variantCode: string | null;
            duration: number | null;
            durationType: string | null;
            attributes: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        parameters: (import("@prisma/client/runtime").GetResult<{
            id: string;
            code: string;
            name: string;
            value: string;
            productId: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        reviews: ({
            user: {
                id: string;
                name: string | null;
                image: string | null;
            };
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            productId: string;
            rating: number;
            comment: string;
            createdAt: Date;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        name: string;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        status: import(".prisma/client").ProductStatus;
        sku: string;
        requiredUID: boolean;
        createdAt: Date;
        updatedAt: Date;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        country: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }, unknown> & {}) | null>;
    getProductCountries(): Promise<unknown[]>;
    getProductsBatch(productIds: string[]): Promise<({
        category: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string | null;
            image: string | null;
            parentId: string | null;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {};
        variants: (import("@prisma/client/runtime").GetResult<{
            id: string;
            price: import("@prisma/client/runtime").Decimal;
            currency: string;
            productId: string;
            variantCode: string | null;
            duration: number | null;
            durationType: string | null;
            attributes: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        name: string;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        status: import(".prisma/client").ProductStatus;
        sku: string;
        requiredUID: boolean;
        createdAt: Date;
        updatedAt: Date;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        country: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }, unknown> & {})[]>;
    getProductByCode(code: string): Promise<({
        category: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string | null;
            image: string | null;
            parentId: string | null;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {};
        variants: (import("@prisma/client/runtime").GetResult<{
            id: string;
            price: import("@prisma/client/runtime").Decimal;
            currency: string;
            productId: string;
            variantCode: string | null;
            duration: number | null;
            durationType: string | null;
            attributes: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        parameters: (import("@prisma/client/runtime").GetResult<{
            id: string;
            code: string;
            name: string;
            value: string;
            productId: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        name: string;
        description: string;
        websiteDescription: string;
        price: number;
        images: string[];
        categoryId: string;
        stock: number;
        specifications: import(".prisma/client").Prisma.JsonValue;
        status: import(".prisma/client").ProductStatus;
        sku: string;
        requiredUID: boolean;
        createdAt: Date;
        updatedAt: Date;
        mcc: string | null;
        off_shelve: boolean;
        dataSize: number | null;
        planType: string | null;
        country: string | null;
        countryCode: string | null;
        odooLastSyncAt: Date | null;
        popularityScore: number | null;
        isPopular: boolean;
    }, unknown> & {}) | null>;
    getProductExternalData(productId: string): Promise<{
        productId: string;
        externalData: {};
        message: string;
    }>;
    getProductCardLinks(productId: string, query: any): Promise<{
        productId: string;
        cardLinks: never[];
        message: string;
    }>;
    getPaginatedProducts(query: any): Promise<{
        products: ({
            category: import("@prisma/client/runtime").GetResult<{
                id: string;
                name: string;
                description: string | null;
                image: string | null;
                parentId: string | null;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {};
            variants: (import("@prisma/client/runtime").GetResult<{
                id: string;
                price: import("@prisma/client/runtime").Decimal;
                currency: string;
                productId: string;
                variantCode: string | null;
                duration: number | null;
                durationType: string | null;
                attributes: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {})[];
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string;
            websiteDescription: string;
            price: number;
            images: string[];
            categoryId: string;
            stock: number;
            specifications: import(".prisma/client").Prisma.JsonValue;
            status: import(".prisma/client").ProductStatus;
            sku: string;
            requiredUID: boolean;
            createdAt: Date;
            updatedAt: Date;
            mcc: string | null;
            off_shelve: boolean;
            dataSize: number | null;
            planType: string | null;
            country: string | null;
            countryCode: string | null;
            odooLastSyncAt: Date | null;
            popularityScore: number | null;
            isPopular: boolean;
        }, unknown> & {})[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
    }>;
    getEnhancedProducts(query: any): Promise<{
        products: {
            id: string;
            name: string;
            description: string;
            websiteDescription: string;
            price: number;
            off_shelve: boolean;
            parameters: (import("@prisma/client/runtime").GetResult<{
                id: string;
                code: string;
                name: string;
                value: string;
                productId: string;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {})[];
            category: import("@prisma/client/runtime").GetResult<{
                id: string;
                name: string;
                description: string | null;
                image: string | null;
                parentId: string | null;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {};
            country: string | null;
            countryCode: string | null;
            dataSize: number | null;
            planType: string | null;
        }[];
        enhanced: boolean;
        message: string;
    }>;
    getCacheFirstProducts(query: any): Promise<{
        id: string;
        name: string;
        description: string;
        websiteDescription: string;
        price: number;
        off_shelve: boolean;
        parameters: (import("@prisma/client/runtime").GetResult<{
            id: string;
            code: string;
            name: string;
            value: string;
            productId: string;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        category: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string | null;
            image: string | null;
            parentId: string | null;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {};
        country: string | null;
        countryCode: string | null;
        dataSize: number | null;
        planType: string | null;
    }[]>;
}
