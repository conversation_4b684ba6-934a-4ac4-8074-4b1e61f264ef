import { Controller, Get, Post, Body, Query, Param, UseGuards } from '@nestjs/common';
import { UserPackagesService } from './user-packages.service';
import { UserPackagesQueryDto, ActivatePackageDto, UsageStatsQueryDto, UsageHistoryQueryDto } from './dto/user-packages-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('user-packages')
export class UserPackagesController {
  constructor(private readonly userPackagesService: UserPackagesService) {}

  @Get()
  getUserPackages(
    @CurrentUser() user: any,
    @Query() query: UserPackagesQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.userPackagesService.getUserPackages(user.id, query, ctx);
  }

  @Get(':packageId')
  getPackageById(
    @CurrentUser() user: any,
    @Param('packageId') packageId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.userPackagesService.getPackageById(user.id, packageId, ctx);
  }

  @Post(':packageId/activate')
  activatePackage(
    @CurrentUser() user: any,
    @Param('packageId') packageId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    const activateData: ActivatePackageDto = { packageId };
    return this.userPackagesService.activatePackage(user.id, activateData, ctx);
  }

  @Get(':packageId/usage-stats')
  getPackageUsageStats(
    @CurrentUser() user: any,
    @Param('packageId') packageId: string,
    @Query() query: UsageStatsQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.userPackagesService.getPackageUsageStats(user.id, packageId, query, ctx);
  }

  @Get(':packageId/usage-history')
  getPackageUsageHistory(
    @CurrentUser() user: any,
    @Param('packageId') packageId: string,
    @Query() query: UsageHistoryQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.userPackagesService.getPackageUsageHistory(user.id, packageId, query, ctx);
  }
}
