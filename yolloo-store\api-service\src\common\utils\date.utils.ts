import { format, formatDistanceToNow, isValid, parseISO } from 'date-fns';

// 应用配置
export const APP_CONFIG = {
  TIMEZONE: process.env.TIMEZONE || 'Asia/Shanghai',
  LOCALE: process.env.LOCALE || 'zh-CN',
  DATE_FORMAT: process.env.DATE_FORMAT || 'yyyy-MM-dd',
  DATETIME_FORMAT: process.env.DATETIME_FORMAT || 'yyyy-MM-dd HH:mm:ss',
} as const;

// 安全的日期解析函数
export function safeParseDate(input: unknown): Date | null {
  if (!input) return null;

  try {
    let date: Date;

    if (input instanceof Date) {
      date = input;
    } else if (typeof input === 'string') {
      // 尝试解析ISO字符串
      if (input.includes('T') || input.includes('Z')) {
        date = parseISO(input);
      } else {
        date = new Date(input);
      }
    } else if (typeof input === 'number') {
      date = new Date(input);
    } else {
      return null;
    }

    return isValid(date) ? date : null;
  } catch (error) {
    console.warn('Date parsing error:', error, 'Input:', input);
    return null;
  }
}

// 统一的日期格式化工具
export const DateFormatter = {
  // 短日期格式 (2024-01-15)
  short: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return format(date, APP_CONFIG.DATE_FORMAT);
  },

  // 完整日期时间格式 (2024-01-15 14:30:25)
  full: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return format(date, APP_CONFIG.DATETIME_FORMAT);
  },

  // 本地化长日期格式 (January 15, 2024)
  long: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return date.toLocaleDateString(APP_CONFIG.LOCALE, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  },

  // 相对时间格式 (2 hours ago)
  relative: (input: unknown, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return formatDistanceToNow(date, { addSuffix: true });
  },

  // 时间格式 (14:30:25)
  time: (input: unknown, fallback = 'Invalid Time'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return format(date, 'HH:mm:ss');
  },

  // 简短时间格式 (14:30)
  timeShort: (input: unknown, fallback = 'Invalid Time'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return format(date, 'HH:mm');
  },

  // 带时区的完整格式 (默认中国时区)
  withTimezone: (input: unknown, timezone?: string, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;

    return new Intl.DateTimeFormat(APP_CONFIG.LOCALE, {
      timeZone: timezone || APP_CONFIG.TIMEZONE,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    }).format(date);
  },

  // ISO字符串格式 (用于API传输)
  iso: (input: unknown, fallback = ''): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return date.toISOString();
  },

  // 自定义格式
  custom: (input: unknown, formatString: string, fallback = 'Invalid Date'): string => {
    const date = safeParseDate(input);
    if (!date) return fallback;
    return format(date, formatString);
  }
};

// 日期计算工具函数
export const DateUtils = {
  // 安全的日期加法 (避免跨月份问题)
  addDays: (date: Date | string | number, days: number): Date => {
    const baseDate = safeParseDate(date) || new Date();
    return new Date(baseDate.getTime() + days * 24 * 60 * 60 * 1000);
  },

  addHours: (date: Date | string | number, hours: number): Date => {
    const baseDate = safeParseDate(date) || new Date();
    return new Date(baseDate.getTime() + hours * 60 * 60 * 1000);
  },

  addMinutes: (date: Date | string | number, minutes: number): Date => {
    const baseDate = safeParseDate(date) || new Date();
    return new Date(baseDate.getTime() + minutes * 60 * 1000);
  },

  // 计算两个日期之间的天数差
  daysBetween: (date1: Date | string | number, date2: Date | string | number): number => {
    const d1 = safeParseDate(date1);
    const d2 = safeParseDate(date2);
    if (!d1 || !d2) return 0;
    return Math.floor((d2.getTime() - d1.getTime()) / (1000 * 60 * 60 * 24));
  },

  // 检查日期是否过期
  isExpired: (date: Date | string | number): boolean => {
    const targetDate = safeParseDate(date);
    if (!targetDate) return false;
    return targetDate.getTime() < Date.now();
  }
};

// 向后兼容的函数 (保持现有API不变)
export function formatDate(input: string | number | Date): string {
  return DateFormatter.long(input);
}

export function formatDatetime(input: string | number | Date): string {
  return DateFormatter.full(input);
}

export function formatDateYMDHM(input: string | number | Date): string {
  return DateFormatter.custom(input, 'yyyy/MM/dd HH:mm');
}
