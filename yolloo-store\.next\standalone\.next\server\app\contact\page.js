(()=>{var e={};e.id=1327,e.ids=[1327],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(33871),r(89090),r(26083),r(35866);var s=r(23191),o=r(88716),a=r(37922),n=r.n(a),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33871)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\contact\\page.tsx"],u="/contact/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},35303:()=>{},33871:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(19510);function o(){return s.jsx("div",{className:"py-16 bg-background",children:s.jsx("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[s.jsx("h1",{className:"text-4xl font-bold heading-gradient mb-8",children:"Contact Us"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-2xl font-semibold text-foreground mb-4",children:"Get in Touch"}),s.jsx("p",{className:"text-muted-foreground mb-6",children:"Have questions about our eSIM services? We're here to help. Fill out the form and we'll get back to you as soon as possible."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-foreground",children:"Email"}),s.jsx("p",{className:"text-muted-foreground",children:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-foreground",children:"Social Media"}),(0,s.jsxs)("p",{className:"text-muted-foreground",children:["Follow us on ",s.jsx("a",{href:"https://x.com/yollooesim",className:"text-primary hover:underline",children:"Twitter"})]})]})]})]}),s.jsx("div",{children:(0,s.jsxs)("form",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"name",className:"text-sm font-medium text-foreground",children:"Name"}),s.jsx("input",{type:"text",id:"name",name:"name",className:"w-full px-4 py-3 rounded-lg border border-input bg-background text-foreground shadow-sm transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-primary hover:border-primary",placeholder:"Your name"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-foreground",children:"Email"}),s.jsx("input",{type:"email",id:"email",name:"email",className:"w-full px-4 py-3 rounded-lg border border-input bg-background text-foreground shadow-sm transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-primary hover:border-primary",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"message",className:"text-sm font-medium text-foreground",children:"Message"}),s.jsx("textarea",{id:"message",name:"message",rows:4,className:"w-full px-4 py-3 rounded-lg border border-input bg-background text-foreground shadow-sm transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-primary hover:border-primary resize-none",placeholder:"How can we help you?"})]}),s.jsx("button",{type:"submit",className:"w-full button-gradient py-3 px-4 rounded-lg text-white font-medium transition-all duration-200 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",children:"Send Message"})]})})]})]})})})}r(71159)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(66621);let o=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(324));module.exports=s})();