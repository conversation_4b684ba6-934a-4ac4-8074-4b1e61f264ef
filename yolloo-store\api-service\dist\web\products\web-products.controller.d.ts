import { Response } from 'express';
import { WebProductsService } from './web-products.service';
export declare class WebProductsController {
    private readonly webProductsService;
    constructor(webProductsService: WebProductsService);
    getProducts(query: any, res: Response): Promise<Response<any, Record<string, any>>>;
    getProduct(productId: string, res: Response): Promise<Response<any, Record<string, any>>>;
    getProductCountries(res: Response): Promise<Response<any, Record<string, any>>>;
    getProductsBatch(body: {
        productIds: string[];
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    getProductByCode(code: string, res: Response): Promise<Response<any, Record<string, any>>>;
    getProductExternalData(productId: string, res: Response): Promise<Response<any, Record<string, any>>>;
    getProductCardLinks(productId: string, query: any, res: Response): Promise<Response<any, Record<string, any>>>;
    getPaginatedProducts(query: any, res: Response): Promise<Response<any, Record<string, any>>>;
    getEnhancedProducts(query: any, res: Response): Promise<Response<any, Record<string, any>>>;
    getCacheFirstProducts(query: any, res: Response): Promise<Response<any, Record<string, any>>>;
}
