import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { HomeService } from './home.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { HomeQueryDto } from './dto/home-query.dto';
import { BannerQueryDto } from './dto/banner-query.dto';
import { TravelTipsQueryDto } from './dto/travel-tips-query.dto';
import { RecommendationsQueryDto } from './dto/recommendations-query.dto';
import { NearbyGuidesQueryDto } from './dto/nearby-guides-query.dto';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller()
export class HomeController {
  constructor(private readonly homeService: HomeService) {}

  @Public()
  @Get('home')
  getHomeData(
    @CurrentUser() user: any,
    @Query() query: HomeQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    // Handle case when user is not authenticated
    const userId = user?.id || null;
    // Pass context to service
    return this.homeService.getHomeData(userId, query, ctx);
  }

  @Public()
  @Get('home/banners')
  getBanners(
    @CurrentUser() user: any,
    @Query() query: BannerQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    const userId = user?.id || null;
    return this.homeService.getBanners(userId, query, ctx);
  }

  @Public()
  @Get('home/travel-tips')
  getTravelTips(
    @CurrentUser() user: any,
    @Query() query: TravelTipsQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    const userId = user?.id || null;
    return this.homeService.getTravelTips(userId, query, ctx);
  }

  @Public()
  @Get('home/recommendations')
  getRecommendations(
    @CurrentUser() user: any,
    @Query() query: RecommendationsQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    const userId = user?.id || null;
    return this.homeService.getRecommendations(userId, query, ctx);
  }

  @Public()
  @Get('guides/nearby')
  getNearbyGuides(
    @Query() query: NearbyGuidesQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.homeService.getNearbyGuides(query, ctx);
  }
}
