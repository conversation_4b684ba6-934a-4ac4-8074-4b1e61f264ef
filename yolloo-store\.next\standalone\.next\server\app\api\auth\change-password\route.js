"use strict";(()=>{var e={};e.id=8814,e.ids=[8814],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},95225:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>q,patchFetch:()=>R,requestAsyncStorage:()=>_,routeModule:()=>x,serverHooks:()=>P,staticGenerationAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{POST:()=>y,dynamic:()=>f,fetchCache:()=>w,revalidate:()=>h});var s=t(49303),o=t(88716),n=t(60670),i=t(87070),l=t(75571),c=t(72331),u=t(90455),d=t(42023),p=t.n(d),m=t(7410);let f="force-dynamic",w="force-no-store",h=0,g=m.z.object({currentPassword:m.z.string().min(1,"Current password is required"),newPassword:m.z.string().min(8,"New password must be at least 8 characters"),confirmPassword:m.z.string().min(8,"Confirm password must be at least 8 characters")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});async function y(e){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user?.id)return i.NextResponse.json({success:!1,message:"Unauthorized"},{status:401});let t=await e.json(),a=g.safeParse(t);if(!a.success)return i.NextResponse.json({success:!1,message:"Validation failed",errors:a.error.errors},{status:400});let{currentPassword:s,newPassword:o}=a.data,n=await c._.user.findUnique({where:{id:r.user.id},select:{id:!0,hashedPassword:!0}});if(!n||!n.hashedPassword)return i.NextResponse.json({success:!1,message:"User not found or password not set"},{status:404});if(!await p().compare(s,n.hashedPassword))return i.NextResponse.json({success:!1,message:"Current password is incorrect"},{status:400});let d=await p().hash(o,10);return await c._.user.update({where:{id:n.id},data:{hashedPassword:d}}),i.NextResponse.json({success:!0,message:"Password updated successfully"})}catch(e){return console.error("[CHANGE_PASSWORD]",e),i.NextResponse.json({success:!1,message:"An error occurred while changing password"},{status:500})}}let x=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/change-password/route",pathname:"/api/auth/change-password",filename:"route",bundlePath:"app/api/auth/change-password/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\auth\\change-password\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:_,staticGenerationAsyncStorage:v,serverHooks:P}=x,q="/api/auth/change-password/route";function R(){return(0,n.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:v})}},90455:(e,r,t)=>{t.d(r,{L:()=>u});var a=t(7585),s=t(72331),o=t(77234),n=t(53797),i=t(42023),l=t.n(i),c=t(93475);let u={adapter:{...(0,a.N)(s._),getUser:async e=>{let r=await s._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await s._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await s._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,n.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await s._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,c.Ak)(e.email);if(!r||r!==e.code)return null;await (0,c.qc)(e.email);let t=await s._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await s._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await s._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:o}){try{if(t&&t.id){let r=o?.headers||new Headers,n=r.get("user-agent")||"",i=r.get("x-forwarded-for"),l=i?i.split(/, /)[0]:r.get("REMOTE_ADDR")||"",c="unknown";a?c=a.code&&!a.password?"email_code":"password":e&&(c=e.provider),await s._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:n||null,loginMethod:c,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,s=new URL(a).searchParams.get("callbackUrl");if(s){let e=decodeURIComponent(s);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let o=await s._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return o?{id:o.id,name:o.name,email:o.email,picture:o.image,role:o.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>s});var a=t(53524);let s=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>i,Ak:()=>l,qc:()=>c,yz:()=>u});var a=t(62197),s=t.n(a);let o=null;function n(){if(!o){let e=process.env.REDIS_URL||"redis://localhost:6379";(o=new(s())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),o.on("connect",()=>{console.log("Successfully connected to Redis")})}return o}async function i(e,r,t=300){try{let a=n(),s=`verification_code:${e}`;return await a.setex(s,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=n(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function c(e){try{let r=n(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function u(e,r,t){try{let a=n(),s=`rate_limit:${e}`,o=await a.get(s),i=o?parseInt(o):0;if(i>=r)return!1;return 0===i?await a.setex(s,t,"1"):await a.incr(s),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var s=t(69955);Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var i=s?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(a,o,i):a[o]=e[o]}return a.default=e,t&&t.set(e,a),a}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005,7410],()=>t(95225));module.exports=a})();