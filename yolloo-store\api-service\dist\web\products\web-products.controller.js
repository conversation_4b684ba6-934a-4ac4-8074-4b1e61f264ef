"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebProductsController = void 0;
const common_1 = require("@nestjs/common");
const web_products_service_1 = require("./web-products.service");
let WebProductsController = class WebProductsController {
    webProductsService;
    constructor(webProductsService) {
        this.webProductsService = webProductsService;
    }
    async getProducts(query, res) {
        try {
            const products = await this.webProductsService.getProducts(query);
            return res.json({ products });
        }
        catch (error) {
            console.error('[WEB_PRODUCTS_GET]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch products',
            });
        }
    }
    async getProduct(productId, res) {
        try {
            const product = await this.webProductsService.getProductById(productId);
            if (!product) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: 'Product not found',
                });
            }
            return res.json(product);
        }
        catch (error) {
            console.error('[WEB_PRODUCT_GET]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal error',
            });
        }
    }
    async getProductCountries(res) {
        try {
            const countries = await this.webProductsService.getProductCountries();
            return res.json(countries);
        }
        catch (error) {
            console.error('[WEB_PRODUCTS_COUNTRIES]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch countries',
            });
        }
    }
    async getProductsBatch(body, res) {
        try {
            const products = await this.webProductsService.getProductsBatch(body.productIds);
            return res.json({ products });
        }
        catch (error) {
            console.error('[WEB_PRODUCTS_BATCH]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch products',
            });
        }
    }
    async getProductByCode(code, res) {
        try {
            const product = await this.webProductsService.getProductByCode(code);
            if (!product) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: 'Product not found',
                });
            }
            return res.json(product);
        }
        catch (error) {
            console.error('[WEB_PRODUCT_BY_CODE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal error',
            });
        }
    }
    async getProductExternalData(productId, res) {
        try {
            const externalData = await this.webProductsService.getProductExternalData(productId);
            return res.json(externalData);
        }
        catch (error) {
            console.error('[WEB_PRODUCT_EXTERNAL_DATA]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch external data',
            });
        }
    }
    async getProductCardLinks(productId, query, res) {
        try {
            const cardLinks = await this.webProductsService.getProductCardLinks(productId, query);
            return res.json(cardLinks);
        }
        catch (error) {
            console.error('[WEB_PRODUCT_CARD_LINKS]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch card links',
            });
        }
    }
    async getPaginatedProducts(query, res) {
        try {
            const result = await this.webProductsService.getPaginatedProducts(query);
            return res.json(result);
        }
        catch (error) {
            console.error('[WEB_PRODUCTS_PAGINATED]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch paginated products',
            });
        }
    }
    async getEnhancedProducts(query, res) {
        try {
            const products = await this.webProductsService.getEnhancedProducts(query);
            return res.json(products);
        }
        catch (error) {
            console.error('[WEB_PRODUCTS_ENHANCED]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch enhanced products',
            });
        }
    }
    async getCacheFirstProducts(query, res) {
        try {
            const products = await this.webProductsService.getCacheFirstProducts(query);
            return res.json(products);
        }
        catch (error) {
            console.error('[WEB_PRODUCTS_CACHE_FIRST]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch products',
            });
        }
    }
};
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProducts", null);
__decorate([
    (0, common_1.Get)(':productId'),
    __param(0, (0, common_1.Param)('productId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProduct", null);
__decorate([
    (0, common_1.Get)('countries'),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProductCountries", null);
__decorate([
    (0, common_1.Post)('batch'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProductsBatch", null);
__decorate([
    (0, common_1.Get)('by-code/:code'),
    __param(0, (0, common_1.Param)('code')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProductByCode", null);
__decorate([
    (0, common_1.Get)(':productId/external-data'),
    __param(0, (0, common_1.Param)('productId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProductExternalData", null);
__decorate([
    (0, common_1.Get)(':productId/get-card-links'),
    __param(0, (0, common_1.Param)('productId')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getProductCardLinks", null);
__decorate([
    (0, common_1.Get)('paginated'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getPaginatedProducts", null);
__decorate([
    (0, common_1.Get)('enhanced'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getEnhancedProducts", null);
__decorate([
    (0, common_1.Get)('cache-first'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebProductsController.prototype, "getCacheFirstProducts", null);
WebProductsController = __decorate([
    (0, common_1.Controller)('api/web/products'),
    __metadata("design:paramtypes", [web_products_service_1.WebProductsService])
], WebProductsController);
exports.WebProductsController = WebProductsController;
//# sourceMappingURL=web-products.controller.js.map