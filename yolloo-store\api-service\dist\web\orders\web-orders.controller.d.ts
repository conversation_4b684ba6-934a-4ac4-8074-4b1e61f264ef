/// <reference types="cookie-parser" />
import { Request, Response } from 'express';
import { WebOrdersService } from './web-orders.service';
export declare class WebOrdersController {
    private readonly webOrdersService;
    constructor(webOrdersService: WebOrdersService);
    getOrders(query: any, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    getOrder(orderId: string, res: Response): Promise<Response<any, Record<string, any>>>;
    createOrder(createOrderDto: any, res: Response): Promise<Response<any, Record<string, any>>>;
    updateOrder(orderId: string, updateOrderDto: any, res: Response): Promise<Response<any, Record<string, any>>>;
    getAvailableEsims(query: any, res: Response): Promise<Response<any, Record<string, any>>>;
}
