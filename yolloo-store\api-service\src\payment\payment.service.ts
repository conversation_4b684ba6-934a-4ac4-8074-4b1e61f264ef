import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma.service';

export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled';
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'alipay' | 'wechat_pay' | 'bank_transfer';
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
  };
}

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private readonly stripeEnabled: boolean;
  private readonly alipayEnabled: boolean;
  private readonly wechatPayEnabled: boolean;

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService
  ) {
    this.stripeEnabled = !!this.configService.get('STRIPE_SECRET_KEY');
    this.alipayEnabled = !!this.configService.get('ALIPAY_APP_ID');
    this.wechatPayEnabled = !!this.configService.get('WECHAT_PAY_MCH_ID');
  }

  async createPaymentIntent(
    amount: number,
    currency: string,
    paymentMethodTypes: string[] = ['card'],
    metadata?: Record<string, string>
  ): Promise<PaymentIntent> {
    try {
      if (this.stripeEnabled && paymentMethodTypes.includes('card')) {
        return await this.createStripePaymentIntent(amount, currency, metadata);
      }

      if (this.alipayEnabled && paymentMethodTypes.includes('alipay')) {
        return await this.createAlipayPaymentIntent(amount, currency, metadata);
      }

      if (this.wechatPayEnabled && paymentMethodTypes.includes('wechat_pay')) {
        return await this.createWechatPaymentIntent(amount, currency, metadata);
      }

      // 如果没有配置真实的支付网关，返回模拟的支付意图
      return this.createMockPaymentIntent(amount, currency, metadata);

    } catch (error) {
      this.logger.error('Failed to create payment intent:', error);
      throw new BadRequestException('Failed to create payment intent');
    }
  }

  private async createStripePaymentIntent(
    amount: number,
    currency: string,
    metadata?: Record<string, string>
  ): Promise<PaymentIntent> {
    // 这里应该集成真实的Stripe API
    // const stripe = require('stripe')(this.configService.get('STRIPE_SECRET_KEY'));
    // const paymentIntent = await stripe.paymentIntents.create({
    //   amount: Math.round(amount * 100), // Stripe uses cents
    //   currency: currency.toLowerCase(),
    //   metadata,
    // });

    // 目前返回模拟数据
    return this.createMockPaymentIntent(amount, currency, metadata);
  }

  private async createAlipayPaymentIntent(
    amount: number,
    currency: string,
    metadata?: Record<string, string>
  ): Promise<PaymentIntent> {
    // 这里应该集成真实的支付宝API
    // 支付宝SDK集成代码
    
    return this.createMockPaymentIntent(amount, currency, metadata);
  }

  private async createWechatPaymentIntent(
    amount: number,
    currency: string,
    metadata?: Record<string, string>
  ): Promise<PaymentIntent> {
    // 这里应该集成真实的微信支付API
    // 微信支付SDK集成代码
    
    return this.createMockPaymentIntent(amount, currency, metadata);
  }

  private createMockPaymentIntent(
    amount: number,
    currency: string,
    metadata?: Record<string, string>
  ): PaymentIntent {
    const id = `pi_mock_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    return {
      id,
      clientSecret: `${id}_secret_${Math.random().toString(36).substring(2)}`,
      amount,
      currency,
      status: 'requires_payment_method',
    };
  }

  async confirmPaymentIntent(paymentIntentId: string): Promise<PaymentIntent> {
    try {
      // 这里应该调用真实的支付网关API来确认支付
      
      // 模拟支付确认
      return {
        id: paymentIntentId,
        clientSecret: '',
        amount: 0,
        currency: 'USD',
        status: 'succeeded',
      };

    } catch (error) {
      this.logger.error('Failed to confirm payment intent:', error);
      throw new BadRequestException('Failed to confirm payment');
    }
  }

  async getPaymentMethods(customerId?: string): Promise<PaymentMethod[]> {
    try {
      // 这里应该从支付网关获取用户的支付方式
      
      // 返回模拟的支付方式
      return [
        {
          id: 'pm_mock_card_visa',
          type: 'card',
          card: {
            brand: 'visa',
            last4: '4242',
            exp_month: 12,
            exp_year: 2025,
          },
        },
        {
          id: 'pm_mock_alipay',
          type: 'alipay',
        },
        {
          id: 'pm_mock_wechat',
          type: 'wechat_pay',
        },
      ];

    } catch (error) {
      this.logger.error('Failed to get payment methods:', error);
      return [];
    }
  }

  async processRefund(
    paymentIntentId: string,
    amount?: number,
    reason?: string
  ): Promise<{ id: string; status: string; amount: number }> {
    try {
      // 这里应该调用真实的支付网关API来处理退款
      
      // 模拟退款处理
      const refundId = `re_mock_${Date.now()}`;
      
      // 记录退款到数据库
      await this.prisma.refund.create({
        data: {
          paymentId: paymentIntentId,
          amount: amount || 0,
          reason: reason || 'requested_by_customer',
          status: 'COMPLETED',
        },
      });

      return {
        id: refundId,
        status: 'COMPLETED',
        amount: amount || 0,
      };

    } catch (error) {
      this.logger.error('Failed to process refund:', error);
      throw new BadRequestException('Failed to process refund');
    }
  }

  async getAvailablePaymentMethods(): Promise<string[]> {
    const methods: string[] = ['card']; // 默认支持卡支付

    if (this.alipayEnabled) {
      methods.push('alipay');
    }

    if (this.wechatPayEnabled) {
      methods.push('wechat_pay');
    }

    return methods;
  }

  async validateWebhook(payload: string, signature: string): Promise<boolean> {
    try {
      // 这里应该验证来自支付网关的webhook签名
      // 例如Stripe的webhook验证：
      // const stripe = require('stripe')(this.configService.get('STRIPE_SECRET_KEY'));
      // const endpointSecret = this.configService.get('STRIPE_WEBHOOK_SECRET');
      // const event = stripe.webhooks.constructEvent(payload, signature, endpointSecret);
      
      return true; // 模拟验证成功

    } catch (error) {
      this.logger.error('Webhook validation failed:', error);
      return false;
    }
  }

  async handleWebhook(payload: any): Promise<void> {
    try {
      // 处理支付网关的webhook事件
      const { type, data } = payload;

      switch (type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(data.object);
          break;
        default:
          this.logger.log(`Unhandled webhook event type: ${type}`);
      }

    } catch (error) {
      this.logger.error('Failed to handle webhook:', error);
    }
  }

  private async handlePaymentSucceeded(paymentIntent: any): Promise<void> {
    // 更新订单状态为已支付
    const orderId = paymentIntent.metadata?.orderId;
    if (orderId) {
      await this.prisma.order.update({
        where: { id: orderId },
        data: { status: 'PAID' },
      });
    }
  }

  private async handlePaymentFailed(paymentIntent: any): Promise<void> {
    // 更新订单状态为支付失败
    const orderId = paymentIntent.metadata?.orderId;
    if (orderId) {
      await this.prisma.order.update({
        where: { id: orderId },
        data: { status: 'PAYMENT_FAILED' },
      });
    }
  }

  async createCustomer(email: string, name?: string): Promise<string> {
    try {
      // 这里应该在支付网关创建客户
      // 例如Stripe：
      // const stripe = require('stripe')(this.configService.get('STRIPE_SECRET_KEY'));
      // const customer = await stripe.customers.create({ email, name });
      // return customer.id;

      // 模拟客户创建
      return `cus_mock_${Date.now()}`;

    } catch (error) {
      this.logger.error('Failed to create customer:', error);
      throw new BadRequestException('Failed to create customer');
    }
  }
}
