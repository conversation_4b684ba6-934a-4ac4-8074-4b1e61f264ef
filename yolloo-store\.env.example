# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# NextAuth.js (将被统一JWT认证替代)
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# 统一JWT认证
JWT_SECRET="your-jwt-secret-key"
API_SERVICE_URL="http://localhost:4000"
NEXT_PUBLIC_API_SERVICE_URL="http://localhost:4000"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Redis
REDIS_URL="redis://localhost:6379"

# Odoo Configuration
ODOO_ADDRESS="https://your-odoo-instance.com"
ODOO_QR_ADDRESS="https://your-qr-odoo-instance.com"
ODOO_USERNAME="your-odoo-username"
ODOO_PASSWORD="your-odoo-password"
ODOO_QR_USERNAME="your-qr-odoo-username"
ODOO_QR_PASSWORD="your-qr-odoo-password"

# Boss Service Configuration
BOSS_ACCOUNT="your-boss-account"
BOSS_SECRET="your-boss-secret"
BOSS_BASE_URL="https://your-boss-api.com"

# Date and Time Configuration
NEXT_PUBLIC_TIMEZONE="Asia/Shanghai"
NEXT_PUBLIC_LOCALE="zh-CN"
NEXT_PUBLIC_DATE_FORMAT="yyyy-MM-dd"
NEXT_PUBLIC_DATETIME_FORMAT="yyyy-MM-dd HH:mm:ss"

# Application Configuration
NEXT_PUBLIC_APP_NAME="Yolloo Store"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_PRESALE_TARGET_DATE="2025-01-08T00:00:00"

# Email Configuration (if needed)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# File Upload Configuration
UPLOAD_MAX_SIZE="********"  # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,image/webp"

# API Rate Limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="900000"  # 15 minutes in milliseconds

# Development/Production Mode
NODE_ENV="development"
