(()=>{var e={};e.id=9346,e.ids=[9346],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},77490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l}),r(16400),r(43488),r(89090),r(26083),r(35866);var s=r(23191),a=r(88716),n=r(37922),o=r.n(n),i=r(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l=["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,16400)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\account\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,43488)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\account\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\account\\page.tsx"],u="/account/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},20890:(e,t,r)=>{Promise.resolve().then(r.bind(r,60408)),Promise.resolve().then(r.bind(r,74681)),Promise.resolve().then(r.bind(r,12370)),Promise.resolve().then(r.bind(r,68483))},35303:()=>{},60408:(e,t,r)=>{"use strict";r.d(t,{AccountForm:()=>m});var s=r(10326),a=r(17577),n=r(46226),o=r(35047),i=r(90772),d=r(54432),l=r(31048),c=r(57372),u=r(85999);function m({user:e}){let t=(0,o.useRouter)(),[r,m]=(0,a.useState)(!1),[p,f]=(0,a.useState)(null);async function h(e){e.preventDefault(),m(!0),f(null);try{let r=new FormData(e.currentTarget).get("name");if(!r||""===r.trim()){f("Name is required"),m(!1);return}let s=await fetch("/api/users",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:r})}),a=await s.json();if(!s.ok)throw a.details&&Array.isArray(a.details)&&a.details.forEach(e=>{e.path.includes("name")&&f(e.message)}),Error(a.error||"Failed to update profile");u.A.success(a.message||"Your profile has been updated."),t.refresh()}catch(e){u.A.error(e.message||"Something went wrong. Please try again.")}finally{m(!1)}}return(0,s.jsxs)("form",{onSubmit:h,className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h4",{className:"text-sm font-medium",children:"Profile"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[e.image&&s.jsx(n.default,{src:e.image,alt:e.name||"Avatar",width:40,height:40,className:"rounded-full"}),s.jsx(i.Button,{variant:"outline",type:"button",disabled:!0,children:"Change Avatar"})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(l._,{htmlFor:"name",children:"Name"}),s.jsx(d.I,{id:"name",name:"name",className:`max-w-[400px] ${p?"border-red-500":""}`,size:32,defaultValue:e.name||""}),p&&s.jsx("p",{className:"text-sm text-red-500",children:p})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(l._,{htmlFor:"email",children:"Email (Cannot be modified)"}),s.jsx(d.I,{id:"email",name:"email",type:"email",className:"max-w-[400px] bg-muted",size:32,defaultValue:e.email||"",disabled:!0}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"Email address cannot be changed at this time"})]})]})]}),(0,s.jsxs)(i.Button,{type:"submit",disabled:r,children:[r&&s.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Profile"]})]})}},74681:(e,t,r)=>{"use strict";r.d(t,{AddressForm:()=>p});var s=r(10326),a=r(17577),n=r(35047),o=r(90772),i=r(54432),d=r(31048),l=r(33071),c=r(57372),u=r(99440),m=r(85999);function p({userId:e,defaultAddresses:t}){let r=(0,n.useRouter)(),[p,f]=(0,a.useState)(!1),[h,x]=(0,a.useState)(t),[g,y]=(0,a.useState)(!1),[j,w]=(0,a.useState)(null);async function v(e){e.preventDefault(),f(!0);let t=new FormData(e.currentTarget),s=await fetch("/api/addresses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t.get("name"),phone:t.get("phone"),address1:t.get("address1"),address2:t.get("address2"),city:t.get("city"),state:t.get("state"),postalCode:t.get("postalCode"),country:t.get("country")})});if(f(!1),!s?.ok)return m.A.error("Something went wrong.");x([...h,await s.json()]),y(!1),m.A.success("Address added successfully."),r.refresh()}async function b(e){f(!0);try{let t=await fetch(`/api/addresses/${e}`,{method:"DELETE"});if(!t?.ok)throw Error("Failed to delete address");x(h.filter(t=>t.id!==e)),m.A.success("Address deleted successfully."),r.refresh()}catch(e){m.A.error("Something went wrong.")}finally{f(!1),w(null)}}return(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"text-sm font-medium",children:"Addresses"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage your shipping addresses"})]}),s.jsx(o.Button,{variant:"outline",onClick:()=>y(!g),disabled:p,children:g?s.jsx(s.Fragment,{children:"Cancel"}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(c.P.add,{className:"mr-2 h-4 w-4"}),"Add Address"]})})]}),(0,s.jsxs)("div",{className:"grid gap-4",children:[h.map(e=>s.jsx(l.Zb,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[s.jsx("p",{className:"font-medium",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.address1,e.address2&&`, ${e.address2}`,s.jsx("br",{}),e.city,", ",e.state," ",e.postalCode,s.jsx("br",{}),e.country,s.jsx("br",{}),"Phone: ",e.phone]})]}),s.jsx(o.Button,{variant:"ghost",size:"icon",className:"text-red-500 hover:text-red-600 hover:bg-red-50",onClick:()=>w(e.id),disabled:p,children:s.jsx(c.P.trash,{className:"h-4 w-4"})})]})},e.id)),g&&(0,s.jsxs)("form",{onSubmit:v,className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"name",children:"Full Name"}),s.jsx(i.I,{id:"name",name:"name",required:!0,placeholder:"Enter your full name"})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"phone",children:"Phone Number"}),s.jsx(i.I,{id:"phone",name:"phone",type:"tel",required:!0,placeholder:"Enter your phone number"})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"address1",children:"Address Line 1"}),s.jsx(i.I,{id:"address1",name:"address1",required:!0,placeholder:"Street address or P.O. Box"})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"address2",children:"Address Line 2"}),s.jsx(i.I,{id:"address2",name:"address2",placeholder:"Apartment, suite, unit, etc."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"city",children:"City"}),s.jsx(i.I,{id:"city",name:"city",required:!0})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"state",children:"State / Province"}),s.jsx(i.I,{id:"state",name:"state",required:!0})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"postalCode",children:"Postal Code"}),s.jsx(i.I,{id:"postalCode",name:"postalCode",required:!0})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"country",children:"Country"}),s.jsx(i.I,{id:"country",name:"country",required:!0})]})]}),(0,s.jsxs)(o.Button,{type:"submit",disabled:p,children:[p&&s.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Add Address"]})]})]}),s.jsx(u.aR,{open:!!j,onOpenChange:()=>w(null),children:(0,s.jsxs)(u._T,{children:[(0,s.jsxs)(u.fY,{children:[s.jsx(u.f$,{children:"Delete Address"}),s.jsx(u.yT,{children:"Are you sure you want to delete this address? This action cannot be undone."})]}),(0,s.jsxs)(u.xo,{children:[s.jsx(u.le,{children:"Cancel"}),s.jsx(u.OL,{className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",onClick:()=>j&&b(j),children:p?(0,s.jsxs)(s.Fragment,{children:[s.jsx(c.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})]})}},12370:(e,t,r)=>{"use strict";r.d(t,{PasswordForm:()=>x});var s=r(10326),a=r(17577),n=r(35047),o=r(90772),i=r(54432),d=r(31048),l=r(57372),c=r(85999),u=r(27256),m=r(74064),p=r(74723),f=r(33071);let h=u.z.object({currentPassword:u.z.string().min(1,"Current password is required"),newPassword:u.z.string().min(8,"New password must be at least 8 characters"),confirmPassword:u.z.string().min(8,"Confirm password must be at least 8 characters")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function x(){let e=(0,n.useRouter)(),[t,r]=(0,a.useState)(!1),[u,x]=(0,a.useState)(!1),[g,y]=(0,a.useState)(!1),[j,w]=(0,a.useState)(!1),[v,b]=(0,a.useState)(!1),{register:N,handleSubmit:P,reset:_,formState:{errors:R}}=(0,p.cI)({resolver:(0,m.F)(h),defaultValues:{currentPassword:"",newPassword:"",confirmPassword:""}});async function S(t){r(!0);try{let r={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)},s=await fetch("/api/auth/change-password",r);if(!s.ok){let e="Failed to change password";try{let t=await s.json();t.message&&(e=t.message)}catch(e){console.error("Failed to parse error response:",e)}throw Error(e)}let a=await s.json();c.A.success(a.message||"Your password has been changed successfully."),_(),x(!1),e.refresh()}catch(e){console.error("Password change error:",e),c.A.error(e.message||"Something went wrong. Please try again.")}finally{r(!1)}}return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"text-sm font-medium",children:"Password"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Change your account password"})]}),s.jsx(o.Button,{variant:u?"secondary":"outline",onClick:()=>{x(!u),u&&_()},children:u?"Cancel":"Change Password"})]}),u&&s.jsx(f.Zb,{className:"border border-muted",children:s.jsx(f.aY,{className:"pt-6",children:(0,s.jsxs)("form",{onSubmit:P(S),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"currentPassword",children:"Current Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(i.I,{id:"currentPassword",type:g?"text":"password",className:"max-w-[400px] pr-10",...N("currentPassword")}),(0,s.jsxs)(o.Button,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground",onClick:()=>y(!g),children:[g?s.jsx(l.P.eyeOff,{className:"h-4 w-4"}):s.jsx(l.P.eye,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:g?"Hide password":"Show password"})]})]}),R.currentPassword&&s.jsx("p",{className:"text-sm text-red-500",children:R.currentPassword.message})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"newPassword",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(i.I,{id:"newPassword",type:j?"text":"password",className:"max-w-[400px] pr-10",...N("newPassword")}),(0,s.jsxs)(o.Button,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground",onClick:()=>w(!j),children:[j?s.jsx(l.P.eyeOff,{className:"h-4 w-4"}):s.jsx(l.P.eye,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:j?"Hide password":"Show password"})]})]}),R.newPassword&&s.jsx("p",{className:"text-sm text-red-500",children:R.newPassword.message})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[s.jsx(d._,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx(i.I,{id:"confirmPassword",type:v?"text":"password",className:"max-w-[400px] pr-10",...N("confirmPassword")}),(0,s.jsxs)(o.Button,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground",onClick:()=>b(!v),children:[v?s.jsx(l.P.eyeOff,{className:"h-4 w-4"}):s.jsx(l.P.eye,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:v?"Hide password":"Show password"})]})]}),R.confirmPassword&&s.jsx("p",{className:"text-sm text-red-500",children:R.confirmPassword.message})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(o.Button,{type:"submit",disabled:t,children:[t&&s.jsx(l.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Save Changes"]}),s.jsx(o.Button,{type:"button",variant:"outline",onClick:()=>{_(),x(!1)},children:"Cancel"})]})]})})})]})}},99440:(e,t,r)=>{"use strict";r.d(t,{OL:()=>x,_T:()=>u,aR:()=>i,f$:()=>f,fY:()=>m,le:()=>g,vW:()=>d,xo:()=>p,yT:()=>h});var s=r(10326),a=r(17577),n=r(12194),o=r(77863);let i=n.fC,d=n.xz,l=n.h_,c=a.forwardRef(({className:e,...t},r)=>s.jsx(n.aV,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:r}));c.displayName=n.aV.displayName;let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsxs)(l,{children:[s.jsx(c,{}),s.jsx(n.VY,{ref:r,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));u.displayName=n.VY.displayName;let m=({className:e,...t})=>s.jsx("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});m.displayName="AlertDialogHeader";let p=({className:e,...t})=>s.jsx("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});p.displayName="AlertDialogFooter";let f=a.forwardRef(({className:e,...t},r)=>s.jsx(n.Dx,{ref:r,className:(0,o.cn)("text-lg font-semibold",e),...t}));f.displayName=n.Dx.displayName;let h=a.forwardRef(({className:e,...t},r)=>s.jsx(n.dk,{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=n.dk.displayName;let x=a.forwardRef(({className:e,...t},r)=>s.jsx(n.aU,{ref:r,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",e),...t}));x.displayName=n.aU.displayName;let g=a.forwardRef(({className:e,...t},r)=>s.jsx(n.$j,{ref:r,className:(0,o.cn)("mt-2 inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-semibold ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 sm:mt-0",e),...t}));g.displayName=n.$j.displayName},33071:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>l,Zb:()=>o,aY:()=>c,eW:()=>u,ll:()=>d});var s=r(10326),a=r(17577),n=r(77863);let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));o.displayName="Card";let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));l.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},31048:(e,t,r)=>{"use strict";r.d(t,{_:()=>l});var s=r(10326),a=r(17577),n=r(34478),o=r(79360),i=r(77863);let d=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef(({className:e,...t},r)=>s.jsx(n.f,{ref:r,className:(0,i.cn)(d(),e),...t}));l.displayName=n.f.displayName},68483:(e,t,r)=>{"use strict";r.d(t,{Separator:()=>c});var s=r(10326),a=r(17577),n=r(45226),o="horizontal",i=["horizontal","vertical"],d=a.forwardRef((e,t)=>{let{decorative:r,orientation:a=o,...d}=e,l=i.includes(a)?a:o;return(0,s.jsx)(n.WV.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...d,ref:t})});d.displayName="Separator";var l=r(77863);let c=a.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...a},n)=>s.jsx(d,{ref:n,decorative:r,orientation:t,className:(0,l.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...a}));c.displayName=d.displayName},43488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>a,fetchCache:()=>n,revalidate:()=>o});var s=r(19510);let a="force-dynamic",n="force-no-store",o=0;function i({children:e}){return s.jsx("div",{className:"account-layout",children:e})}},16400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>O,dynamic:()=>_,fetchCache:()=>R,metadata:()=>E,revalidate:()=>S});var s=r(19510),a=r(58585),n=r(75571),o=r(90455),i=r(68570);let d=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\separator.tsx`),{__esModule:l,$$typeof:c}=d;d.default;let u=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\separator.tsx#Separator`),m=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\account\account-form.tsx`),{__esModule:p,$$typeof:f}=m;m.default;let h=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\account\account-form.tsx#AccountForm`),x=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\account\address-form.tsx`),{__esModule:g,$$typeof:y}=x;x.default;let j=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\account\address-form.tsx#AddressForm`),w=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\account\password-form.tsx`),{__esModule:v,$$typeof:b}=w;w.default;let N=(0,i.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\account\password-form.tsx#PasswordForm`);var P=r(72331);let _="force-dynamic",R="force-no-store",S=0,E={title:"Account",description:"Manage your account settings"};async function C(){try{let e=await (0,n.getServerSession)(o.L);e||(0,a.redirect)("/auth/signin");let t=await P._.user.findUnique({where:{id:e.user.id},include:{addresses:!0}});return t||(0,a.redirect)("/auth/signin"),t}catch(e){console.error("Error in getAccountData:",e),(0,a.redirect)("/auth/signin")}}async function O(){let e=await C();return s.jsx("div",{className:"container py-8",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-medium",children:"Account"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage your account settings and set your preferred addresses."})]}),s.jsx(u,{}),(0,s.jsxs)("div",{className:"grid gap-10",children:[s.jsx(h,{user:{id:e.id,name:e.name,email:e.email,image:e.image}}),e.hashedPassword&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(u,{}),s.jsx(N,{})]}),s.jsx(u,{}),s.jsx(j,{userId:e.id,defaultAddresses:e.addresses})]})]})})}},90455:(e,t,r)=>{"use strict";r.d(t,{L:()=>c});var s=r(7585),a=r(72331),n=r(77234),o=r(53797),i=r(42023),d=r.n(i),l=r(93475);let c={adapter:{...(0,s.N)(a._),getUser:async e=>{let t=await a._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await a._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await a._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await d().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,l.Ak)(e.email);if(!t||t!==e.code)return null;await (0,l.qc)(e.email);let r=await a._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await a._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await a._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:s,request:n}){try{if(r&&r.id){let t=n?.headers||new Headers,o=t.get("user-agent")||"",i=t.get("x-forwarded-for"),d=i?i.split(/, /)[0]:t.get("REMOTE_ADDR")||"",l="unknown";s?l=s.code&&!s.password?"email_code":"password":e&&(l=e.provider),await a._.userLoginHistory.create({data:{userId:r.id,ipAddress:d||null,userAgent:o||null,loginMethod:l,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],s=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,a=new URL(s).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let s=new URL(e);if(r.some(e=>s.hostname===e||s.hostname.includes(e)||s.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(s);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return s}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:s}){if("update"===r&&s)return{...e,...s.user};let n=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,t,r)=>{"use strict";r.d(t,{_:()=>a});var s=r(53524);let a=global.prisma||new s.PrismaClient({log:["error"]})},93475:(e,t,r)=>{"use strict";r.d(t,{AL:()=>i,Ak:()=>d,qc:()=>l,yz:()=>c});var s=r(62197),a=r.n(s);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function i(e,t,r=300){try{let s=o(),a=`verification_code:${e}`;return await s.setex(a,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function d(e){try{let t=o(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function l(e){try{let t=o(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let s=o(),a=`rate_limit:${e}`,n=await s.get(a),i=n?parseInt(n):0;if(i>=t)return!1;return 0===i?await s.setex(a,r,"1"):await s.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var a=r(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var i=a?Object.getOwnPropertyDescriptor(e,n):null;i&&(i.get||i.set)?Object.defineProperty(s,n,i):s[n]=e[n]}return s.default=e,r&&r.set(e,s),s}(r(45609));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})},58585:(e,t,r)=>{"use strict";var s=r(61085);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"redirect")&&r.d(t,{redirect:function(){return s.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return s.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=r(83953),a=r(16399);class n extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new n}delete(){throw new n}set(){throw new n}sort(){throw new n}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return a},notFound:function(){return s}});let r="NEXT_NOT_FOUND";function s(){let e=Error(r);throw e.digest=r,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return s},getRedirectError:function(){return d},getRedirectStatusCodeFromError:function(){return f},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return m},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return l}});let a=r(54580),n=r(72934),o=r(8586),i="NEXT_REDIRECT";function d(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let s=Error(i);s.digest=i+";"+t+";"+e+";"+r+";";let n=a.requestAsyncStorage.getStore();return n&&(s.mutableCookies=n.mutableCookies),s}function l(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw d(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw d(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,s,a]=e.digest.split(";",4),n=Number(a);return t===i&&("replace"===r||"push"===r)&&"string"==typeof s&&!isNaN(n)&&n in o.RedirectStatusCode}function m(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function f(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,6908,2194,4824],()=>r(77490));module.exports=s})();