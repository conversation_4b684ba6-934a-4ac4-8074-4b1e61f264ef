"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebUsersController = void 0;
const common_1 = require("@nestjs/common");
const web_users_service_1 = require("./web-users.service");
let WebUsersController = class WebUsersController {
    webUsersService;
    constructor(webUsersService) {
        this.webUsersService = webUsersService;
    }
    async getUsers(res) {
        try {
            const users = await this.webUsersService.getUsers();
            return res.json(users);
        }
        catch (error) {
            console.error('[WEB_USERS_GET]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch users',
            });
        }
    }
    async createUser(createUserDto, res) {
        try {
            const user = await this.webUsersService.createUser(createUserDto);
            return res.status(common_1.HttpStatus.CREATED).json(user);
        }
        catch (error) {
            console.error('[WEB_USER_CREATE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to create user',
            });
        }
    }
};
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebUsersController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebUsersController.prototype, "createUser", null);
WebUsersController = __decorate([
    (0, common_1.Controller)('api/web/users'),
    __metadata("design:paramtypes", [web_users_service_1.WebUsersService])
], WebUsersController);
exports.WebUsersController = WebUsersController;
//# sourceMappingURL=web-users.controller.js.map