{"version": 3, "file": "web-auth.controller.js", "sourceRoot": "", "sources": ["../../../src/web/auth/web-auth.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmF;AAEnF,yDAAoD;AACpD,0DAAsD;AACtD,+EAAkE;AAOlE,IACa,iBAAiB,GAD9B,MACa,iBAAiB;IAET;IACA;IAFnB,YACmB,cAA8B,EAC9B,WAAwB;QADxB,mBAAc,GAAd,cAAc,CAAgB;QAC9B,gBAAW,GAAX,WAAW,CAAa;IACxC,CAAC;IAQE,AAAN,KAAK,CAAC,KAAK,CACD,QAA6C,EAC9C,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAGzD,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3C,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;aACvB,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,YAAY,EAAE;gBAC/C,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;aAChC,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC,IAAI,CAAC;gBACd,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,kBAAkB;aAC5B,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI,cAAc;aACvC,CAAC,CAAC;SACJ;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,OAAO,CAAQ,GAAY,EAAS,GAAa;QACrD,IAAI;YACF,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,eAAe,CAAC,CAAC;YAEpD,IAAI,CAAC,YAAY,EAAE;gBACjB,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;oBAC9C,KAAK,EAAE,2BAA2B;iBACnC,CAAC,CAAC;aACJ;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAGjE,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,EAAE;gBAC3C,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;aACvB,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,YAAY,EAAE;gBAC/C,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;aAChC,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC,IAAI,CAAC;gBACd,OAAO,EAAE,8BAA8B;aACxC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;gBAC9C,KAAK,EAAE,sBAAsB;aAC9B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,MAAM,CAAQ,GAAa;QAE/B,GAAG,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC9B,GAAG,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAEjC,OAAO,GAAG,CAAC,IAAI,CAAC;YACd,OAAO,EAAE,mBAAmB;SAC7B,CAAC,CAAC;IACL,CAAC;IAOK,AAAN,KAAK,CAAC,cAAc,CAAQ,GAAY,EAAS,GAAa;QAC5D,IAAI;YACF,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;YACzB,IAAI,CAAC,IAAI,EAAE;gBACT,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC;oBAC9C,KAAK,EAAE,mBAAmB;iBAC3B,CAAC,CAAC;aACJ;YAED,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;SAC3B;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACtC,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAQK,AAAN,KAAK,CAAC,MAAM,CACF,SAA4D,EAC7D,GAAa;QAEpB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAChC,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,yBAAyB;iBACjC,CAAC,CAAC;aACJ;YAGD,MAAM,UAAU,GAAG,4BAA4B,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC3B,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,sBAAsB;iBAC9B,CAAC,CAAC;aACJ;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACvE,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACpD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,KAAK,sBAAsB,EAAE;gBAC5C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,sBAAsB;iBAC9B,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,cAAc,CACV,IAAuB,EACxB,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpE,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,aAAa,CACT,IAAyC,EAC1C,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,KAAK,0BAA0B,EAAE;gBAChD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,0BAA0B;iBAClC,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,QAAQ,CACJ,IAAqC,EACtC,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACrF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,UAAU,CACN,IAAmD,EACpD,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACtF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,KAAK,yBAAyB,EAAE;gBAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,yBAAyB;iBACjC,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,cAAc,CACV,IAAsD,EACvD,GAAY,EACZ,GAAa;QAEpB,IAAI;YAGF,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC;gBACjD,KAAK,EAAE,4CAA4C;aACpD,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAA;AAlRO;IAFL,IAAA,yBAAM,GAAE;IACR,IAAA,aAAI,EAAC,OAAO,CAAC;IAEX,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;8CA8BP;AAQK;IAFL,IAAA,yBAAM,GAAE;IACR,IAAA,aAAI,EAAC,SAAS,CAAC;IACD,WAAA,IAAA,YAAG,GAAE,CAAA;IAAgB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;gDAoCxC;AAOK;IADL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACD,WAAA,IAAA,YAAG,GAAE,CAAA;;;;+CAQlB;AAOK;IADL,IAAA,YAAG,EAAC,IAAI,CAAC;IACY,WAAA,IAAA,YAAG,GAAE,CAAA;IAAgB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;uDAgB/C;AAQK;IAFL,IAAA,yBAAM,GAAE;IACR,IAAA,aAAI,EAAC,QAAQ,CAAC;IAEZ,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;+CAiCP;AAOK;IADL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;uDAWP;AAOK;IADL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IAEpB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;sDAiBP;AAOK;IADL,IAAA,aAAI,EAAC,WAAW,CAAC;IAEf,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;iDAWP;AAOK;IADL,IAAA,aAAI,EAAC,aAAa,CAAC;IAEjB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;mDAiBP;AAOK;IADL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;IACL,WAAA,IAAA,YAAG,GAAE,CAAA;;;;uDAcP;AA7RU,iBAAiB;IAD7B,IAAA,mBAAU,EAAC,cAAc,CAAC;qCAGU,iCAAc;QACjB,0BAAW;GAHhC,iBAAiB,CA8R7B;AA9RY,8CAAiB"}