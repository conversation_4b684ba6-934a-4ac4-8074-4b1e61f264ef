{"version": 3, "file": "web-auth.controller.js", "sourceRoot": "", "sources": ["../../../src/web/auth/web-auth.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmF;AAEnF,yDAAoD;AAOpD,IACa,iBAAiB,GAD9B,MACa,iBAAiB;IACC;IAA7B,YAA6B,cAA8B;QAA9B,mBAAc,GAAd,cAAc,CAAgB;IAAG,CAAC;IAOzD,AAAN,KAAK,CAAC,MAAM,CACF,SAA4D,EAC7D,GAAa;QAEpB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,SAAS,CAAC;YAE5C,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;gBAChC,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,yBAAyB;iBACjC,CAAC,CAAC;aACJ;YAGD,MAAM,UAAU,GAAG,4BAA4B,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC3B,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,sBAAsB;iBAC9B,CAAC,CAAC;aACJ;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACvE,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACpD;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,KAAK,sBAAsB,EAAE;gBAC5C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,sBAAsB;iBAC9B,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,cAAc,CACV,IAAuB,EACxB,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpE,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,aAAa,CACT,IAAyC,EAC1C,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,KAAK,0BAA0B,EAAE;gBAChD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,0BAA0B;iBAClC,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,QAAQ,CACJ,IAAqC,EACtC,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACrF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,UAAU,CACN,IAAmD,EACpD,GAAa;QAEpB,IAAI;YACF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACtF,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzB;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,CAAC,OAAO,KAAK,yBAAyB,EAAE;gBAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,yBAAyB;iBACjC,CAAC,CAAC;aACJ;YAED,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;IAOK,AAAN,KAAK,CAAC,cAAc,CACV,IAAsD,EACvD,GAAY,EACZ,GAAa;QAEpB,IAAI;YAGF,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC;gBACjD,KAAK,EAAE,4CAA4C;aACpD,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC;SACJ;IACH,CAAC;CACF,CAAA;AAxJO;IADL,IAAA,aAAI,EAAC,QAAQ,CAAC;IAEZ,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;+CAiCP;AAOK;IADL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;uDAWP;AAOK;IADL,IAAA,aAAI,EAAC,gBAAgB,CAAC;IAEpB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;sDAiBP;AAOK;IADL,IAAA,aAAI,EAAC,WAAW,CAAC;IAEf,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;iDAWP;AAOK;IADL,IAAA,aAAI,EAAC,aAAa,CAAC;IAEjB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;;;;mDAiBP;AAOK;IADL,IAAA,aAAI,EAAC,iBAAiB,CAAC;IAErB,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,YAAG,GAAE,CAAA;IACL,WAAA,IAAA,YAAG,GAAE,CAAA;;;;uDAcP;AA/JU,iBAAiB;IAD7B,IAAA,mBAAU,EAAC,cAAc,CAAC;qCAEoB,iCAAc;GADhD,iBAAiB,CAgK7B;AAhKY,8CAAiB"}