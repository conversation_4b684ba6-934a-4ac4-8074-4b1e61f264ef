import { IsOptional, IsString, IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>Arra<PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class TravelPackagesQueryDto {
  @IsOptional()
  @IsString()
  destination?: string;

  @IsOptional()
  @IsString()
  region?: string; // 地区：亚洲、欧洲、美洲等

  @IsOptional()
  @IsIn(['7', '15', '30', '90'])
  duration?: '7' | '15' | '30' | '90'; // 天数

  @IsOptional()
  @IsIn(['1GB', '3GB', '5GB', '10GB', '20GB', 'unlimited'])
  dataSize?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'price';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

export class TravelPackageOrderDto {
  @IsString()
  packageId: string;

  @IsOptional()
  @IsString()
  startDate?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  countries?: string[];
}
