(()=>{var e={};e.id=9979,e.ids=[9979],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},25005:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(24537),r(89090),r(26083),r(35866);var a=r(23191),s=r(88716),o=r(37922),i=r.n(o),l=r(95231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d=["",{children:["pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24537)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\pricing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\pricing\\page.tsx"],x="/pricing/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/pricing/page",pathname:"/pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83487:(e,t,r)=>{Promise.resolve().then(r.bind(r,33787))},33787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(10326),s=r(17577),o=r(32933),i=r(90434),l=r(41135),n=r(77109),d=r(35677);let c=({title:e,description:t,price:r,originalPrice:s,features:d,isPopular:c=!1,productLink:x,buttonText:m})=>{let{status:p}=(0,n.useSession)(),b="authenticated"===p?x:`/auth/signin?callbackUrl=${encodeURIComponent(x)}`;return(0,a.jsxs)("div",{className:(0,l.Z)("relative p-8 rounded-3xl transition-all duration-500 hover:-translate-y-1 flex flex-col backdrop-blur-sm",c?"bg-gradient-to-br from-[#B82E4E] to-[#F799A6] border-2 border-[#F799A6] shadow-[0_8px_32px_rgba(247,153,166,0.3)]":"bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 border border-[#F799A6]/30 hover:border-[#F799A6]/50 hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)]"),children:[c&&a.jsx("span",{className:"absolute -top-4 left-1/2 -translate-x-1/2 px-6 py-1.5 bg-white    text-[#B82E4E] text-sm font-semibold rounded-full shadow-lg shadow-pink-500/30",children:"Most Popular"}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx("h3",{className:(0,l.Z)("text-2xl font-semibold mb-2",c?"text-white":"text-gray-900"),children:e}),a.jsx("p",{className:(0,l.Z)("mb-6 text-lg",c?"text-white/90":"text-gray-600"),children:t}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsxs)("span",{className:(0,l.Z)("text-5xl font-bold",c?"text-white":"text-[#B82E4E]"),children:["$",r]}),s&&(0,a.jsxs)("span",{className:(0,l.Z)("text-xl line-through",c?"text-white/60":"text-gray-400"),children:["$",s]})]})]}),a.jsx("div",{className:"flex-grow",children:a.jsx("ul",{className:"space-y-4 mb-8",children:d.map((e,t)=>(0,a.jsxs)("li",{className:"flex items-start gap-3",children:[a.jsx("div",{className:(0,l.Z)("w-5 h-5 flex-shrink-0 flex items-center justify-center rounded-full shadow-sm",c?"bg-white/20 shadow-white/10":"bg-[#F799A6]/20 shadow-pink-500/10"),children:a.jsx(o.Z,{className:(0,l.Z)("w-3 h-3",c?"text-white":"text-[#B82E4E]")})}),a.jsx("span",{className:(0,l.Z)("text-lg",c?"text-white":"text-gray-600"),children:e})]},t))})}),a.jsx(i.default,{href:b,className:(0,l.Z)("block w-full py-4 px-6 text-center rounded-full font-semibold transition-all duration-500 transform hover:-translate-y-0.5 text-lg",c?"bg-white text-[#B82E4E] shadow-lg hover:shadow-xl shadow-white/30":"bg-gradient-to-r from-[#B82E4E] to-[#F799A6] text-white hover:from-[#A02745] hover:to-[#E88A97] shadow-lg hover:shadow-xl shadow-pink-500/20"),children:m})]})},x=()=>(0,a.jsxs)("div",{className:"max-w-4xl mx-auto mt-20",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl sm:text-4xl font-bold mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E]    text-transparent bg-clip-text",children:"Frequently Asked Questions"}),a.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Everything you need to know about our eSIM cards and services"})]}),a.jsx("div",{className:"grid gap-6 md:grid-cols-2",children:[{question:"What is an eSIM card?",answer:"An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan without having to use a physical SIM card. Our Yolloo cards enable you to store and use multiple eSIM profiles on a single device."},{question:"How many eSIMs can I store?",answer:"All our cards support storing up to 15 eSIM profiles, allowing you to switch between different carriers and plans as needed."},{question:"Do I need to buy data plans separately?",answer:"Yes, the card purchase gives you the ability to store and manage eSIMs. Data plans are purchased separately based on your travel needs."},{question:"Which devices are compatible?",answer:"Our Lite and Plus cards support Android devices with eSIM capability. The Max card supports both iOS and Android devices with eSIM capability."},{question:"How do I activate my card?",answer:"After purchasing, you'll receive instructions to activate your card through our app or website. The process is simple and takes just a few minutes."},{question:"Can I use my card internationally?",answer:"Yes, all our cards offer global coverage, allowing you to use your eSIMs worldwide where supported by the carrier."}].map((e,t)=>(0,a.jsxs)("div",{className:"p-6 rounded-xl bg-white shadow-md border border-gray-100 hover:shadow-lg transition-shadow",children:[a.jsx("h3",{className:"text-xl font-semibold mb-3 text-gray-900",children:e.question}),a.jsx("p",{className:"text-gray-600",children:e.answer})]},t))})]}),m=()=>(0,a.jsxs)("div",{className:"max-w-5xl mx-auto mt-20 overflow-x-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl sm:text-4xl font-bold mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E]    text-transparent bg-clip-text",children:"Compare Plans"}),a.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Find the perfect Yolloo card for your needs"})]}),a.jsx("div",{className:"min-w-[768px]",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"p-4 text-left bg-gray-50 border-b-2 border-gray-200"}),(0,a.jsxs)("th",{className:"p-4 text-center bg-gray-50 border-b-2 border-gray-200",children:[a.jsx("span",{className:"text-xl font-semibold text-gray-700",children:"Lite"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Basic"})]}),(0,a.jsxs)("th",{className:"p-4 text-center bg-gray-50 border-b-2 border-gray-200",children:[a.jsx("span",{className:"text-xl font-semibold text-gray-700",children:"Plus"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Popular"})]}),(0,a.jsxs)("th",{className:"p-4 text-center bg-[#F799A6]/10 border-b-2 border-[#F799A6]",children:[a.jsx("span",{className:"text-xl font-semibold text-[#B82E4E]",children:"Max"}),a.jsx("p",{className:"text-sm text-[#B82E4E]/70",children:"Best Value"})]})]})}),(0,a.jsxs)("tbody",{children:[(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"Price"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"$12"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"$21"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"$23"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"eSIM Downloads"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"3 free"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"Unlimited"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"Unlimited"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"Device Support"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"Android"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"Android"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"iOS & Android"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"eSIM Storage"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"15 profiles"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"15 profiles"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"15 profiles"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"Global Coverage"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"✓"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"✓"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"✓"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"Support"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"Basic"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"Priority"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"24/7 Priority"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"Free Updates"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"-"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"✓"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"✓"})]}),(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-4 border-b border-gray-200 font-medium",children:"Advanced Features"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"-"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center",children:"-"}),a.jsx("td",{className:"p-4 border-b border-gray-200 text-center bg-[#F799A6]/5 font-medium",children:"✓"})]})]})]})})]}),p=()=>(0,a.jsxs)("div",{className:"max-w-6xl mx-auto mt-20",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl sm:text-4xl font-bold mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E]    text-transparent bg-clip-text",children:"What Our Customers Say"}),a.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Hear from travelers who have experienced the convenience of Yolloo cards"})]}),a.jsx("div",{className:"grid gap-6 md:grid-cols-3",children:[{name:"Sarah T.",location:"Business Traveler, USA",quote:"The Max card has been a game-changer for my international business trips. I can easily switch between eSIMs without carrying multiple physical cards.",rating:5},{name:"Michael L.",location:"Digital Nomad, Canada",quote:"I've been using the Plus card for 6 months now while traveling through Southeast Asia. The unlimited eSIM downloads have saved me so much money on local data plans.",rating:5},{name:"Elena K.",location:"Frequent Traveler, Germany",quote:"The Lite card is perfect for my occasional trips. Easy to use, great coverage, and the customer support has been excellent when I needed help.",rating:4}].map((e,t)=>(0,a.jsxs)("div",{className:"p-6 rounded-xl bg-white shadow-md border border-gray-100 hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"flex items-center mb-4",children:[void 0,void 0,void 0,void 0,void 0].map((t,r)=>a.jsx("svg",{className:`w-5 h-5 ${r<e.rating?"text-yellow-400":"text-gray-300"}`,fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})},r))}),(0,a.jsxs)("p",{className:"text-gray-600 mb-4 italic",children:['"',e.quote,'"']}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-semibold text-gray-900",children:e.name}),a.jsx("p",{className:"text-sm text-gray-500",children:e.location})]})]},t))})]});function b(){let[e,t]=(0,s.useState)({lite:"",plus:"",max:""});return(0,a.jsxs)("div",{className:"min-h-screen",children:[a.jsx(d.X,{withTopGradient:!0,isWhite:!1,className:"py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[a.jsx("div",{className:"inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm    rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10",children:"Pricing Plans"}),a.jsx("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E]    text-transparent bg-clip-text",children:"Choose Your Perfect Yolloo Card"}),a.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium",children:"Select your ideal Yolloo card with eSIM capabilities. Our cards allow you to store and manage multiple eSIM profiles, making international travel seamless and affordable."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto",children:[a.jsx(c,{title:"Lite",description:"Perfect for light travelers",price:12,originalPrice:17,features:["3 free eSIM downloads","Android device support","15 eSIM profile storage","Global coverage","Basic support"],productLink:e.lite,buttonText:"Get Card"}),a.jsx(c,{title:"Plus",description:"Best for regular travelers",price:21,originalPrice:30,features:["Unlimited eSIM downloads","Android device support","15 eSIM profile storage","Global coverage","Priority support","Free updates"],productLink:e.plus,buttonText:"Get Card"}),a.jsx(c,{title:"Max",description:"Ultimate flexibility",price:23,originalPrice:33,features:["Unlimited eSIM downloads","iOS & Android support","15 eSIM profile storage","Global coverage","24/7 Priority support","Advanced features"],isPopular:!0,productLink:e.max,buttonText:"Get Card"})]}),a.jsx("div",{className:"text-center mt-8 text-sm text-gray-500",children:"* Physical card supported with all plans. eSIM data plans are sold separately."})]})}),a.jsx(d.X,{isWhite:!0,className:"py-20",children:a.jsx(m,{})}),a.jsx(d.X,{isWhite:!1,className:"py-20",children:a.jsx(p,{})}),a.jsx(d.X,{isWhite:!0,className:"py-20",children:a.jsx(x,{})})]})}},35677:(e,t,r)=>{"use strict";r.d(t,{X:()=>s});var a=r(10326);function s({children:e,className:t="",withGradient:r=!0,withGrid:s=!0,withTopGradient:o=!1,isDark:i=!1,isWhite:l=!0}){return(0,a.jsxs)("section",{className:`relative py-32 overflow-hidden ${i?"bg-[#1a1818]":l?"bg-white":"bg-[#f7f9fc]"} ${t}`,children:[(0,a.jsxs)("div",{className:"absolute inset-0 -z-10",children:[r&&a.jsx("div",{className:`absolute inset-0 bg-gradient-to-b ${i?"from-[#F799A6]/30 via-transparent to-transparent":"from-[#F799A6]/15 via-transparent to-[#B82E4E]/15"}`}),o&&a.jsx("div",{className:`absolute top-0 inset-x-0 h-32 bg-gradient-to-b ${i?"from-[#151313] to-[#1a1818]":l?"from-white to-white":"from-white to-[#f7f9fc]"}`}),a.jsx("div",{className:`absolute -top-[30%] -left-[10%] h-[60%] w-[60%] 
        bg-[radial-gradient(circle,${i?"rgba(247,153,166,0.4)":"rgba(247,153,166,0.3)"},transparent_70%)] blur-3xl`}),a.jsx("div",{className:`absolute -bottom-[20%] -right-[10%] h-[60%] w-[60%] 
        bg-[radial-gradient(circle,${i?"rgba(184,46,78,0.35)":"rgba(184,46,78,0.25)"},transparent_70%)] blur-3xl`}),a.jsx("div",{className:`absolute top-[40%] left-[50%] -translate-x-1/2 -translate-y-1/2 h-[40%] w-[80%] 
        bg-[radial-gradient(ellipse,rgba(247,153,166,0.25),transparent_70%)] blur-3xl`}),a.jsx("div",{className:`absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,${i?"rgba(247,153,166,0.25)":"rgba(247,153,166,0.2)"},transparent_50%)]`}),a.jsx("div",{className:`absolute inset-0 bg-[radial-gradient(circle_at_top_right,${i?"rgba(184,46,78,0.25)":"rgba(184,46,78,0.2)"},transparent_50%)]`}),s&&a.jsx("div",{className:`absolute inset-0 ${i?"bg-grid-white/[0.03]":"bg-grid-[#B82E4E]/[0.05]"}`}),(0,a.jsxs)("div",{className:"absolute inset-0",children:[a.jsx("div",{className:`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${i?"via-[#F799A6]/20":"via-[#F799A6]/30"} to-transparent`}),a.jsx("div",{className:`absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent ${i?"via-[#B82E4E]/20":"via-[#B82E4E]/30"} to-transparent`})]}),!i&&a.jsx("div",{className:"absolute inset-0 bg-white/40 backdrop-blur-[1px]"})]}),e]})}},24537:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>l});var a=r(68570);let s=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\pricing\page.tsx`),{__esModule:o,$$typeof:i}=s;s.default;let l=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\pricing\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(25005));module.exports=a})();