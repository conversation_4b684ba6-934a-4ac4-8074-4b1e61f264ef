(()=>{var e={};e.id=1085,e.ids=[1085],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},61761:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c}),s(18089),s(89090),s(26083),s(35866);var a=s(23191),r=s(88716),l=s(37922),i=s.n(l),n=s(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["yolloo-smart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,18089)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\yolloo-smart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\yolloo-smart\\page.tsx"],x="/yolloo-smart/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/yolloo-smart/page",pathname:"/yolloo-smart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31359:(e,t,s)=>{Promise.resolve().then(s.bind(s,2646))},2646:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var a=s(10326),r=s(17577),l=s(90434),i=s(90772),n=s(54432),o=s(63062),c=s(79256),d=s(88307),x=s(34474),m=s(90670);let u=({content:e,title:t})=>{let[s,l]=(0,r.useState)(!1);return(0,a.jsxs)("div",{className:"relative inline-block",children:[a.jsx("div",{onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),className:"cursor-help",children:a.jsx(o.Z,{className:"w-3 h-3 text-gray-400 hover:text-gray-600 transition-colors"})}),s&&(0,a.jsxs)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-3",children:[a.jsx("div",{className:"text-sm font-semibold text-gray-900 mb-1",children:t}),a.jsx("div",{className:"text-xs text-gray-600 leading-relaxed",children:e}),a.jsx("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"})]})]})},h=({regions:e,direction:t="left",speed:s=1})=>{let i=(0,r.useRef)(null),[n,o]=(0,r.useState)(!1),c=(0,r.useRef)(),d=(0,r.useRef)(0),x=(0,r.useRef)(!1),m=[...e,...e,...e,...e];return(0,r.useEffect)(()=>{let a=i.current;if(!a)return;let r=272*e.length,l="left"===t?-(.3*s):.3*s;x.current||(d.current=-r,a.style.transform=`translateX(${d.current}px)`,x.current=!0);let o=()=>{!n&&a&&(d.current+=l,d.current<=-(2*r)?d.current=-r:d.current>=0&&(d.current=-r),a.style.transform=`translateX(${d.current}px)`),c.current=requestAnimationFrame(o)};return c.current=requestAnimationFrame(o),()=>{c.current&&cancelAnimationFrame(c.current)}},[n,t,s,e.length]),a.jsx("div",{className:"overflow-hidden",onMouseEnter:()=>o(!0),onMouseLeave:()=>o(!1),children:a.jsx("div",{ref:i,className:"flex gap-4",style:{width:"max-content"},children:m.map((e,s)=>a.jsx(l.default,{href:`/products/${e.originalProduct.id}`,className:"bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300 border border-pink-100 hover:border-[#DF4362]/30 group flex-shrink-0 w-64",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"w-8 h-8 bg-gradient-to-r from-[#DF4362] to-[#B82E4E] rounded-full flex items-center justify-center flex-shrink-0",children:[a.jsx("img",{src:`https://flagcdn.com/w20/${e.flagCode}.png`,alt:`${e.name} flag`,className:"w-4 h-3 object-cover rounded-sm",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.nextElementSibling;t&&t.classList.remove("hidden")}}),a.jsx("div",{className:"w-4 h-3 bg-[#DF4362] rounded-sm hidden flex items-center justify-center",children:a.jsx("span",{className:"text-white text-xs font-bold",children:e.flagCode.toUpperCase()})})]}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[a.jsx("h3",{className:"font-semibold text-gray-900 text-sm truncate",children:e.name}),a.jsx("p",{className:"text-[#DF4362] font-medium text-xs",children:e.price}),(0,a.jsxs)("p",{className:"text-gray-500 text-xs",children:[e.dataSize," • ",e.planType]})]}),a.jsx("div",{className:"text-gray-400 group-hover:text-[#DF4362] transition-colors flex-shrink-0",children:a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})},`scroll-${t}-${s}`))})})},p=()=>{let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0);if((0,r.useEffect)(()=>{(async function(){try{let e=await fetch("/api/yolloo-smart/products?limit=50"),s=await e.json();if(s.products){let e=s.products.map(e=>{let t=e.country?e.country.split(/[,;]/)[0].trim():"Global",s=e.countryCode?e.countryCode.split(/[,;]/)[0].trim().toLowerCase():"global";return{name:t,flagCode:s,dataSize:(e=>{if(!e)return"1GB";if(!(e>=1024))return`${e}MB`;{let t=e/1024;return Number.isInteger(t)?`${t}GB`:`${t.toFixed(1)}GB`}})(e.dataSize),planType:e.planType||"Daily",price:`From $${e.price.toFixed(1)}`,originalProduct:{id:e.id,name:e.name,description:e.description,websiteDescription:e.websiteDescription,price:e.price,minPrice:e.price}}});t(e)}}catch(e){console.error("Error fetching products:",e)}finally{l(!1)}})()},[]),s)return a.jsx("div",{className:"flex justify-center items-center py-16",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#DF4362]"})});if(0===e.length)return a.jsx("div",{className:"text-center py-16 text-gray-500",children:"No products available"});let i=e.slice(0,Math.ceil(e.length/2)),n=e.slice(Math.ceil(e.length/2));return(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(h,{regions:i,direction:"left",speed:1}),a.jsx(h,{regions:n,direction:"right",speed:1})]})},f=()=>a.jsx("button",{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-8 right-24 w-12 h-12 bg-[#DF4362] hover:bg-[#C73A56] text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-50 group","aria-label":"回到顶部",children:a.jsx(c.Z,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-200"})});function g(){let[e,t]=(0,r.useState)([]),[s,c]=(0,r.useState)(null),[h,g]=(0,r.useState)(!0),[j,b]=(0,r.useState)(null),[v,y]=(0,r.useState)([]),[N,w]=(0,r.useState)(1),[P,k]=(0,r.useState)("all"),[F,E]=(0,r.useState)(""),[C,S]=(0,r.useState)("default"),[B,A]=(0,r.useState)(""),[D,$]=(0,r.useState)(""),_=(0,r.useRef)(null),R=(e,t)=>{e.preventDefault(),w(t),setTimeout(()=>{_.current&&_.current.scrollIntoView({behavior:"smooth",block:"start"})},100)},Z=(0,r.useCallback)(async()=>{try{g(!0),b(null);let e=new URLSearchParams({page:N.toString(),limit:"12"});B&&e.append("search",B),"all"!==P&&e.append("regionType",P),F&&e.append("country",F),"default"!==C&&e.append("sort",C);let s=await fetch(`/api/yolloo-smart/products?${e.toString()}`);if(!s.ok)throw Error(`HTTP error! status: ${s.status}`);let a=await s.json();t(a.products),c(a.pagination)}catch(e){console.error("Error fetching products:",e),b(e instanceof Error?e.message:"Failed to fetch products"),t([]),c(null)}finally{g(!1)}},[N,B,P,F,C,12]);(0,r.useCallback)(async()=>{try{let e=await fetch("/api/products/countries");if(e.ok){let t=await e.json();y(t.countries)}}catch(e){console.error("Error fetching countries:",e)}},[]);let z=e=>{let t=v.find(t=>t.name===e);return t?t.count:0};return(0,a.jsxs)("div",{className:"min-h-screen relative",children:[(0,a.jsxs)("section",{className:"relative py-20 px-4 overflow-hidden min-h-[600px]",style:{backgroundImage:"url('/yolloo smart hero.svg')",backgroundSize:"auto 100%",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:[a.jsx("div",{className:"container mx-auto relative z-10",children:(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{className:"text-white",children:[a.jsx("div",{className:"inline-flex items-center px-6 py-3 rounded-full font-medium mb-8 backdrop-blur-sm bg-[#FFF0F0] text-[#DF4362] text-2xl whitespace-nowrap",children:"Stay Connected Anywhere with eSIM"}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"font-semibold bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] bg-clip-text text-transparent text-sm lg:text-base whitespace-nowrap",children:"Instant activation, flexible plans, and global coverage — starting at just $9.9."}),a.jsx("p",{className:"text-white/90 text-sm lg:text-base whitespace-nowrap",children:"Choose from 100+ destinations with 5G-ready data and hotspot sharing."})]})]}),a.jsx("div",{className:"relative flex justify-center lg:justify-end",children:a.jsx("img",{src:"/image 6.png",alt:"eSIM Card",className:"w-80 h-auto object-contain drop-shadow-2xl"})})]})}),(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[a.jsx("div",{className:"absolute top-20 left-20 w-32 h-32 border border-white/20 rounded-full"}),a.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 border border-white/20 rounded-full"}),a.jsx("div",{className:"absolute top-1/2 left-1/4 w-16 h-16 border border-white/20 rounded-full"})]})]}),a.jsx("section",{className:"relative -mt-8 pb-8 bg-gradient-to-b from-transparent to-[#FFF0F0]",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsx("div",{className:"max-w-2xl mx-auto",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-6 top-1/2 transform -translate-y-1/2 text-[#DF4362]",children:a.jsx(d.Z,{className:"w-5 h-5"})}),a.jsx(n.I,{placeholder:"Where do you want to go",className:"pl-14 pr-20 py-4 rounded-full border-0 bg-white text-gray-900 placeholder-gray-400 text-base shadow-xl focus:ring-0 focus:outline-none h-14"}),a.jsx(i.Button,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 bg-[#DF4362] hover:bg-[#C73A56] text-white rounded-full w-10 h-10 p-0 flex items-center justify-center shadow-md",children:a.jsx(d.Z,{className:"h-4 w-4"})})]})})})}),a.jsx("section",{className:"py-16 relative overflow-hidden bg-[#FFF0F0]",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[a.jsx("h2",{className:"text-3xl font-bold text-center mb-12 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] bg-clip-text text-transparent",children:"Popular Countries"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-[#FFF0F0] to-transparent z-10 pointer-events-none"}),a.jsx("div",{className:"absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-[#FFF0F0] to-transparent z-10 pointer-events-none"}),a.jsx(p,{})]})]})}),(0,a.jsxs)("section",{ref:_,className:"py-16 relative min-h-[600px] bg-gray-50 bg-[url('/swag.svg')] bg-contain bg-center bg-no-repeat",children:[a.jsx("div",{className:"absolute inset-0 bg-gray-50/90"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 relative z-10",children:[a.jsx("h2",{className:"text-3xl font-bold text-center mb-4 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E] bg-clip-text text-transparent",children:"All destination countries/regions"}),a.jsx("p",{className:"text-center text-gray-600 mb-12 max-w-3xl mx-auto",children:"The best data plan for every destination — seamless and secure connectivity in over 200 countries and regions."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)(x.Ph,{value:P,onValueChange:k,children:[a.jsx(x.i4,{children:a.jsx(x.ki,{placeholder:"Region Type"})}),(0,a.jsxs)(x.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[a.jsx(x.Ql,{value:"all",children:"All Region Types"}),a.jsx(x.Ql,{value:"single",children:"Local"}),a.jsx(x.Ql,{value:"multi",children:"Regional"})]})]}),(0,a.jsxs)(x.Ph,{value:F||"all",onValueChange:e=>E("all"===e?"":e),children:[a.jsx(x.i4,{children:a.jsx(x.ki,{placeholder:"Country"})}),(0,a.jsxs)(x.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[a.jsx(x.Ql,{value:"all",children:"All Destinations"}),v.map(e=>e.name).map(e=>(0,a.jsxs)(x.Ql,{value:e,children:[e," (",z(e),")"]},e))]})]}),(0,a.jsxs)(x.Ph,{value:C,onValueChange:S,children:[a.jsx(x.i4,{children:a.jsx(x.ki,{placeholder:"Sort By",children:"default"===C?"Sort By":"price-asc"===C?"Price: Low to High":"price-desc"===C?"Price: High to Low":"name-asc"===C?"Name: A to Z":"Name: Z to A"})}),(0,a.jsxs)(x.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[a.jsx(x.Ql,{value:"default",children:"Sort By"}),a.jsx(x.Ql,{value:"price-asc",children:"Price: Low to High"}),a.jsx(x.Ql,{value:"price-desc",children:"Price: High to Low"}),a.jsx(x.Ql,{value:"name-asc",children:"Name: A to Z"}),a.jsx(x.Ql,{value:"name-desc",children:"Name: Z to A"})]})]}),a.jsx(i.Button,{variant:"outline",onClick:()=>{k("all"),E(""),S("default"),w(1),$(""),A("")},children:"Reset Filters"})]}),a.jsx("div",{className:"max-w-6xl mx-auto",children:h?a.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-[#DF4362]"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"Loading products..."})]})}):j?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] text-center",children:[a.jsx("div",{className:"text-red-500 mb-4",children:a.jsx(o.Z,{className:"h-12 w-12"})}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Error Loading Products"}),a.jsx("p",{className:"text-gray-600 mb-4",children:j}),a.jsx(i.Button,{onClick:()=>Z(),children:"Try Again"})]}):0===e.length?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-12 text-center bg-white shadow-md rounded-lg",children:[a.jsx(o.Z,{className:"h-12 w-12 text-gray-400 mb-4"}),a.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Products Found"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Try adjusting your filters to find more products."}),a.jsx(i.Button,{onClick:()=>{k("all"),E(""),S("default"),w(1),$(""),A("")},children:"Clear Filters"})]}):(0,a.jsxs)("div",{children:[a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:e.map(e=>{let t=e.country?e.country.split(/[,;]/)[0].trim():"Global";return(0,a.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 hover:border-gray-300 flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-3 h-3 bg-[#DF4362] rounded-full mr-3 flex-shrink-0"}),a.jsx("span",{className:"text-base font-semibold text-gray-900",children:t})]}),(0,a.jsxs)("div",{className:"flex-1 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:(e=>{if(!e)return"1GB";if(!(e>=1024))return`${e}MB`;{let t=e/1024;return Number.isInteger(t)?`${t}GB`:`${t.toFixed(1)}GB`}})(e.dataSize)}),a.jsx("span",{className:"text-gray-500",children:"/"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx("span",{className:"text-gray-500 text-sm",children:"Natural Day"}),a.jsx(u,{title:"Natural Day",content:"Refers to a calendar day, from 00:00 to 23:59, typically based on a specific time zone (UTC+8)."})]})]}),a.jsx("div",{className:"mb-3",children:(0,a.jsxs)("span",{className:"text-lg font-semibold text-[#DF4362]",children:["From $",e.price.toFixed(1)]})}),(0,a.jsxs)("div",{className:"flex gap-2 mb-4",children:[a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-700 border border-pink-200",children:"5G"}),a.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700 border border-green-200",children:e.country&&e.country.split(/[,;]/).length>1?"Regional":"Local"})]})]}),a.jsx(l.default,{href:`/products/${e.id}`,className:"mt-auto",children:a.jsx(i.Button,{className:"w-full bg-[#DF4362] hover:bg-[#C73A56] text-white font-semibold py-3 rounded-lg transition-all duration-200",children:"View Details"})})]},e.id)})}),s&&s.totalPages>1&&a.jsx("div",{className:"w-full flex justify-center items-center my-8",children:a.jsx(m.tl,{className:"justify-center",children:(0,a.jsxs)(m.ng,{className:"justify-center",children:[a.jsx(m.nt,{children:a.jsx(m.dN,{href:"#",onClick:e=>R(e,Math.max(1,N-1)),"aria-disabled":!s?.hasPrev})}),s.totalPages>5&&N>3&&a.jsx(m.nt,{children:a.jsx(m.kN,{href:"#",onClick:e=>R(e,1),children:"1"})}),s.totalPages>5&&N>3&&a.jsx(m.nt,{children:a.jsx(m.Dj,{})}),Array.from({length:s.totalPages},(e,t)=>t+1).filter(e=>s.totalPages<=5||(N<=3?e<=5:N>=s.totalPages-2?e>s.totalPages-5:1>=Math.abs(e-N))).map(e=>a.jsx(m.nt,{children:a.jsx(m.kN,{href:"#",onClick:t=>R(t,e),isActive:N===e,children:e})},e)),s.totalPages>5&&N<s.totalPages-2&&a.jsx(m.nt,{children:a.jsx(m.Dj,{})}),s.totalPages>5&&N<s.totalPages-2&&a.jsx(m.nt,{children:a.jsx(m.kN,{href:"#",onClick:e=>R(e,s.totalPages),children:s.totalPages})}),a.jsx(m.nt,{children:a.jsx(m.$0,{href:"#",onClick:e=>R(e,Math.min(s.totalPages,N+1)),"aria-disabled":!s?.hasNext})})]})})})]})})]})]}),a.jsx(f,{})]})}},90670:(e,t,s)=>{"use strict";s.d(t,{$0:()=>f,Dj:()=>g,dN:()=>p,kN:()=>h,ng:()=>m,nt:()=>u,tl:()=>x});var a=s(10326),r=s(17577),l=s(11890),i=s(39183),n=s(15919),o=s(77863),c=s(90772),d=s(90434);let x=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,o.cn)("flex w-full flex-col items-center gap-4 sm:flex-row sm:gap-6",e),...t}));x.displayName="Pagination";let m=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,o.cn)("flex flex-wrap items-center gap-1",e),...t}));m.displayName="PaginationContent";let u=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,o.cn)("",e),...t}));u.displayName="PaginationItem";let h=({className:e,isActive:t,size:s="icon",...r})=>a.jsx(d.default,{"aria-current":t?"page":void 0,className:(0,o.cn)((0,c.d)({variant:t?"outline":"ghost",size:s}),e),...r});h.displayName="PaginationLink";let p=({className:e,...t})=>(0,a.jsxs)(h,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",e),...t,children:[a.jsx(l.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Previous"})]});p.displayName="PaginationPrevious";let f=({className:e,...t})=>(0,a.jsxs)(h,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",e),...t,children:[a.jsx("span",{children:"Next"}),a.jsx(i.Z,{className:"h-4 w-4"})]});f.displayName="PaginationNext";let g=({className:e,...t})=>(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",e),...t,children:[a.jsx(n.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"More pages"})]});g.displayName="PaginationEllipsis"},34474:(e,t,s)=>{"use strict";s.d(t,{Bw:()=>m,Ph:()=>c,Ql:()=>u,i4:()=>x,ki:()=>d});var a=s(10326),r=s(17577),l=s(18792),i=s(941),n=s(32933),o=s(77863);let c=l.fC;l.ZA;let d=l.B4,x=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.xz,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:[t,a.jsx(l.JO,{asChild:!0,children:a.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=l.xz.displayName;let m=r.forwardRef(({className:e,children:t,position:s="popper",...r},i)=>a.jsx(l.h_,{children:a.jsx(l.VY,{ref:i,className:(0,o.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:a.jsx(l.l_,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));m.displayName=l.VY.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(l.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=l.__.displayName;let u=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.ck,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(l.wU,{children:a.jsx(n.Z,{className:"h-4 w-4"})})}),a.jsx(l.eT,{children:t})]}));u.displayName=l.ck.displayName,r.forwardRef(({className:e,...t},s)=>a.jsx(l.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=l.Z0.displayName},79256:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62881).Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},15919:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62881).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},18089:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>l,default:()=>n});var a=s(68570);let r=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\yolloo-smart\page.tsx`),{__esModule:l,$$typeof:i}=r;r.default;let n=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\yolloo-smart\page.tsx#default`)},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,8792,4824],()=>s(61761));module.exports=a})();