(()=>{var e={};e.id=7173,e.ids=[7173],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},83528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>f,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(7426),r(85460),r(89090),r(26083),r(35866);var s=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["admin",{children:["affiliates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7426)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\affiliates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\affiliates\\page.tsx"],u="/admin/affiliates/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/affiliates/page",pathname:"/admin/affiliates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},65887:(e,t,r)=>{Promise.resolve().then(r.bind(r,38049))},38049:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ew});var s=r(10326),a=r(17577),n=r(74990),i=r(54432),o=r(90772),l=r(57372),d=r(15940),c=r(567),u=r(85999),f=r(93095),p=r(48051),m=a.forwardRef((e,t)=>{let{children:r,...n}=e,i=a.Children.toArray(r),o=i.find(g);if(o){let e=o.props.children,r=i.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(x,{...n,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,s.jsx)(x,{...n,ref:t,children:r})});m.displayName="Slot";var x=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),n=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(n.ref=t?(0,p.F)(t,e):e),a.cloneElement(r,n)}return a.Children.count(r)>1?a.Children.only(null):null});x.displayName="SlotClone";var h=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function g(e){return a.isValidElement(e)&&e.type===h}var y=r(82561),j=r(52067);r(60962);var b=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=a.forwardRef((e,r)=>{let{asChild:a,...n}=e,i=a?m:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i,{...n,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),v=r(65819),N=a.forwardRef((e,t)=>{let{children:r,...n}=e,i=a.Children.toArray(r),o=i.find(C);if(o){let e=o.props.children,r=i.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(w,{...n,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,r):null})}return(0,s.jsx)(w,{...n,ref:t,children:r})});N.displayName="Slot";var w=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),n=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{n(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(n.ref=t?(0,p.F)(t,e):e),a.cloneElement(r,n)}return a.Children.count(r)>1?a.Children.only(null):null});w.displayName="SlotClone";var R=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function C(e){return a.isValidElement(e)&&e.type===R}var P=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=a.forwardRef((e,r)=>{let{asChild:a,...n}=e,i=a?N:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i,{...n,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),_=r(9815),S=r(88957),A="Collapsible",[E,O]=(0,f.b)(A),[T,q]=E(A),M=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:n,defaultOpen:i,disabled:o,onOpenChange:l,...d}=e,[c=!1,u]=(0,j.T)({prop:n,defaultProp:i,onChange:l});return(0,s.jsx)(T,{scope:r,disabled:o,contentId:(0,S.M)(),open:c,onOpenToggle:a.useCallback(()=>u(e=>!e),[u]),children:(0,s.jsx)(P.div,{"data-state":V(c),"data-disabled":o?"":void 0,...d,ref:t})})});M.displayName=A;var F="CollapsibleTrigger",k=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,...a}=e,n=q(F,r);return(0,s.jsx)(P.button,{type:"button","aria-controls":n.contentId,"aria-expanded":n.open||!1,"data-state":V(n.open),"data-disabled":n.disabled?"":void 0,disabled:n.disabled,...a,ref:t,onClick:(0,y.M)(e.onClick,n.onOpenToggle)})});k.displayName=F;var D="CollapsibleContent",I=a.forwardRef((e,t)=>{let{forceMount:r,...a}=e,n=q(D,e.__scopeCollapsible);return(0,s.jsx)(_.z,{present:r||n.open,children:({present:e})=>(0,s.jsx)($,{...a,ref:t,present:e})})});I.displayName=D;var $=a.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:n,children:i,...o}=e,l=q(D,r),[d,c]=a.useState(n),u=a.useRef(null),f=(0,p.e)(t,u),m=a.useRef(0),x=m.current,h=a.useRef(0),g=h.current,y=l.open||d,j=a.useRef(y),b=a.useRef(void 0);return a.useEffect(()=>{let e=requestAnimationFrame(()=>j.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,v.b)(()=>{let e=u.current;if(e){b.current=b.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,h.current=t.width,j.current||(e.style.transitionDuration=b.current.transitionDuration,e.style.animationName=b.current.animationName),c(n)}},[l.open,n]),(0,s.jsx)(P.div,{"data-state":V(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!y,...o,ref:f,style:{"--radix-collapsible-content-height":x?`${x}px`:void 0,"--radix-collapsible-content-width":g?`${g}px`:void 0,...e.style},children:y&&i})});function V(e){return e?"open":"closed"}var W=r(17124),U="Accordion",H=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[z,B,Z]=function(e){let t=e+"CollectionProvider",[r,n]=(0,f.b)(t),[i,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,n=a.useRef(null),o=a.useRef(new Map).current;return(0,s.jsx)(i,{scope:t,itemMap:o,collectionRef:n,children:r})};l.displayName=t;let d=e+"CollectionSlot",c=a.forwardRef((e,t)=>{let{scope:r,children:a}=e,n=o(d,r),i=(0,p.e)(t,n.collectionRef);return(0,s.jsx)(m,{ref:i,children:a})});c.displayName=d;let u=e+"CollectionItemSlot",x="data-radix-collection-item",h=a.forwardRef((e,t)=>{let{scope:r,children:n,...i}=e,l=a.useRef(null),d=(0,p.e)(t,l),c=o(u,r);return a.useEffect(()=>(c.itemMap.set(l,{ref:l,...i}),()=>void c.itemMap.delete(l))),(0,s.jsx)(m,{[x]:"",ref:d,children:n})});return h.displayName=u,[{Provider:l,Slot:c,ItemSlot:h},function(t){let r=o(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${x}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(U),[G,L]=(0,f.b)(U,[Z,O]),X=O(),J=a.forwardRef((e,t)=>{let{type:r,...a}=e;return(0,s.jsx)(z.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,s.jsx)(er,{...a,ref:t}):(0,s.jsx)(et,{...a,ref:t})})});J.displayName=U;var[K,Y]=G(U),[Q,ee]=G(U,{collapsible:!1}),et=a.forwardRef((e,t)=>{let{value:r,defaultValue:n,onValueChange:i=()=>{},collapsible:o=!1,...l}=e,[d,c]=(0,j.T)({prop:r,defaultProp:n,onChange:i});return(0,s.jsx)(K,{scope:e.__scopeAccordion,value:d?[d]:[],onItemOpen:c,onItemClose:a.useCallback(()=>o&&c(""),[o,c]),children:(0,s.jsx)(Q,{scope:e.__scopeAccordion,collapsible:o,children:(0,s.jsx)(en,{...l,ref:t})})})}),er=a.forwardRef((e,t)=>{let{value:r,defaultValue:n,onValueChange:i=()=>{},...o}=e,[l=[],d]=(0,j.T)({prop:r,defaultProp:n,onChange:i}),c=a.useCallback(e=>d((t=[])=>[...t,e]),[d]),u=a.useCallback(e=>d((t=[])=>t.filter(t=>t!==e)),[d]);return(0,s.jsx)(K,{scope:e.__scopeAccordion,value:l,onItemOpen:c,onItemClose:u,children:(0,s.jsx)(Q,{scope:e.__scopeAccordion,collapsible:!0,children:(0,s.jsx)(en,{...o,ref:t})})})}),[es,ea]=G(U),en=a.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:n,dir:i,orientation:o="vertical",...l}=e,d=a.useRef(null),c=(0,p.e)(d,t),u=B(r),f="ltr"===(0,W.gm)(i),m=(0,y.M)(e.onKeyDown,e=>{if(!H.includes(e.key))return;let t=e.target,r=u().filter(e=>!e.ref.current?.disabled),s=r.findIndex(e=>e.ref.current===t),a=r.length;if(-1===s)return;e.preventDefault();let n=s,i=a-1,l=()=>{(n=s+1)>i&&(n=0)},d=()=>{(n=s-1)<0&&(n=i)};switch(e.key){case"Home":n=0;break;case"End":n=i;break;case"ArrowRight":"horizontal"===o&&(f?l():d());break;case"ArrowDown":"vertical"===o&&l();break;case"ArrowLeft":"horizontal"===o&&(f?d():l());break;case"ArrowUp":"vertical"===o&&d()}let c=n%a;r[c].ref.current?.focus()});return(0,s.jsx)(es,{scope:r,disabled:n,direction:i,orientation:o,children:(0,s.jsx)(z.Slot,{scope:r,children:(0,s.jsx)(b.div,{...l,"data-orientation":o,ref:c,onKeyDown:n?void 0:m})})})}),ei="AccordionItem",[eo,el]=G(ei),ed=a.forwardRef((e,t)=>{let{__scopeAccordion:r,value:a,...n}=e,i=ea(ei,r),o=Y(ei,r),l=X(r),d=(0,S.M)(),c=a&&o.value.includes(a)||!1,u=i.disabled||e.disabled;return(0,s.jsx)(eo,{scope:r,open:c,disabled:u,triggerId:d,children:(0,s.jsx)(M,{"data-orientation":i.orientation,"data-state":eh(c),...l,...n,ref:t,disabled:u,open:c,onOpenChange:e=>{e?o.onItemOpen(a):o.onItemClose(a)}})})});ed.displayName=ei;var ec="AccordionHeader",eu=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,n=ea(U,r),i=el(ec,r);return(0,s.jsx)(b.h3,{"data-orientation":n.orientation,"data-state":eh(i.open),"data-disabled":i.disabled?"":void 0,...a,ref:t})});eu.displayName=ec;var ef="AccordionTrigger",ep=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,n=ea(U,r),i=el(ef,r),o=ee(ef,r),l=X(r);return(0,s.jsx)(z.ItemSlot,{scope:r,children:(0,s.jsx)(k,{"aria-disabled":i.open&&!o.collapsible||void 0,"data-orientation":n.orientation,id:i.triggerId,...l,...a,ref:t})})});ep.displayName=ef;var em="AccordionContent",ex=a.forwardRef((e,t)=>{let{__scopeAccordion:r,...a}=e,n=ea(U,r),i=el(em,r),o=X(r);return(0,s.jsx)(I,{role:"region","aria-labelledby":i.triggerId,"data-orientation":n.orientation,...o,...a,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function eh(e){return e?"open":"closed"}ex.displayName=em;var eg=r(941),ey=r(77863);let ej=a.forwardRef(({className:e,...t},r)=>s.jsx(ed,{ref:r,className:(0,ey.cn)("border-b",e),...t}));ej.displayName="AccordionItem";let eb=a.forwardRef(({className:e,children:t,...r},a)=>s.jsx(eu,{className:"flex",children:(0,s.jsxs)(ep,{ref:a,className:(0,ey.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...r,children:[t,s.jsx(eg.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));eb.displayName=ep.displayName;let ev=a.forwardRef(({className:e,children:t,...r},a)=>s.jsx(ex,{ref:a,className:(0,ey.cn)("overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",e),...r,children:s.jsx("div",{className:"pb-4 pt-0",children:t})}));ev.displayName=ex.displayName;var eN=r(33071);function ew(){let[e,t]=(0,a.useState)([]),[r,f]=(0,a.useState)([]),[p,m]=(0,a.useState)(!0),[x,h]=(0,a.useState)(null),[g,y]=(0,a.useState)(""),[j,b]=(0,a.useState)(1),[v,N]=(0,a.useState)(1),w=async(r,s)=>{if(s<0||s>1){u.A.error("Commission rate must be between 0 and 1");return}h(r);try{if(!(await fetch(`/api/admin/affiliates/${r}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({commissionRate:s})})).ok)throw Error("Failed to update commission rate");t(e.map(e=>e.id===r?{...e,commissionRate:s}:e)),u.A.success("Commission rate updated successfully")}catch(e){console.error("Error updating commission rate:",e),u.A.error("Failed to update commission rate")}finally{h(null)}},R=async(r,s)=>{if(s<0||s>1){u.A.error("Discount rate must be between 0 and 1");return}h(r);try{if(!(await fetch(`/api/admin/affiliates/${r}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({discountRate:s})})).ok)throw Error("Failed to update discount rate");t(e.map(e=>e.id===r?{...e,discountRate:s}:e)),u.A.success("Discount rate updated successfully")}catch(e){console.error("Error updating discount rate:",e),u.A.error("Failed to update discount rate")}finally{h(null)}};return p?s.jsx("div",{className:"container mx-auto py-10 flex items-center justify-center",children:s.jsx(l.P.spinner,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)("div",{className:"container mx-auto py-10",children:[s.jsx("div",{className:"flex justify-between items-center mb-8",children:(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Affiliate Management"}),s.jsx("p",{className:"text-muted-foreground",children:"Manage affiliate partners and their commission rates"})]})}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(i.I,{placeholder:"Search by name, email or code...",value:g,onChange:e=>y(e.target.value),className:"max-w-sm"}),g&&s.jsx(o.Button,{variant:"ghost",onClick:()=>y(""),size:"sm",children:s.jsx(l.P.close,{className:"h-4 w-4"})})]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["Showing ",r.length>0?`${(j-1)*5+1}-${Math.min(5*j,r.length)}`:"0"," of ",r.length," affiliates"]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(()=>{let e=(j-1)*5;return r.slice(e,e+5)})().map(e=>(0,s.jsxs)(eN.Zb,{className:"w-full",children:[s.jsx(eN.Ol,{children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[s.jsx(eN.ll,{children:e.user.name?`${e.user.name} (${e.user.email})`:e.user.email}),(0,s.jsxs)(eN.SZ,{children:["Code: ",e.code]})]}),s.jsx(c.C,{variant:"ACTIVE"===e.status?"success":"secondary",children:e.status})]})}),(0,s.jsxs)(eN.aY,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[s.jsx("p",{className:"text-sm text-muted-foreground",children:"Commission Rate"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(i.I,{type:"number",value:e.commissionRate,onChange:t=>{let r=parseFloat(t.target.value);isNaN(r)||w(e.id,r)},step:"0.01",min:"0",max:"1",className:"w-24"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",(100*e.commissionRate).toFixed(1),"%)"]})]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[s.jsx("p",{className:"text-sm text-muted-foreground",children:"Discount Rate"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx(i.I,{type:"number",value:e.discountRate,onChange:t=>{let r=parseFloat(t.target.value);isNaN(r)||R(e.id,r)},step:"0.01",min:"0",max:"1",className:"w-24"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",(100*e.discountRate).toFixed(1),"%)"]})]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[s.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Earnings"}),(0,s.jsxs)("p",{className:"text-2xl font-bold",children:["$",e.totalEarnings.toFixed(2)]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold mb-2",children:"Presale Statistics"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:["Total Subscriptions: ",e.statistics.presaleSubscriptions]}),(0,s.jsxs)("p",{children:["Converted to Users: ",e.statistics.convertedSubscriptions]})]})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-semibold mb-2",children:"Purchase Statistics"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:["Total Orders: ",e.statistics.totalPurchases]}),(0,s.jsxs)("p",{children:["Total Amount: $",e.statistics.totalPurchaseAmount.toFixed(2)]})]})]})]}),e.statistics.purchases.length>0&&s.jsx(J,{type:"single",collapsible:!0,className:"w-full",children:(0,s.jsxs)(ej,{value:"purchases",children:[(0,s.jsxs)(eb,{children:["Purchase Details (",e.statistics.purchases.length," orders)"]}),s.jsx(ev,{children:s.jsx("div",{className:"mt-4",children:(0,s.jsxs)(d.iA,{children:[s.jsx(d.xD,{children:(0,s.jsxs)(d.SC,{children:[s.jsx(d.ss,{children:"Order Date"}),s.jsx(d.ss,{children:"Customer"}),s.jsx(d.ss,{children:"Products"}),s.jsx(d.ss,{children:"Total"})]})}),s.jsx(d.RM,{children:e.statistics.purchases.map(e=>(0,s.jsxs)(d.SC,{children:[s.jsx(d.pj,{children:(0,n.WU)(new Date(e.orderDate),"PPP")}),s.jsx(d.pj,{children:e.customer.name||e.customer.email}),s.jsx(d.pj,{children:s.jsx("ul",{className:"list-disc list-inside",children:e.items.map((e,t)=>(0,s.jsxs)("li",{children:[e.productName," x",e.quantity," ($",e.price,")"]},t))})}),(0,s.jsxs)(d.pj,{children:["$",e.total.toFixed(2)]})]},e.orderId))})]})})})]})})]})]},e.id)),0===r.length&&s.jsx("div",{className:"text-center py-10 text-muted-foreground",children:"No affiliates found matching your search."})]}),v>1&&(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 py-4",children:[s.jsx(o.Button,{variant:"outline",size:"sm",onClick:()=>b(e=>Math.max(e-1,1)),disabled:1===j,children:"Previous"}),(0,s.jsxs)("div",{className:"text-sm text-muted-foreground mx-4",children:["Page ",j," of ",v]}),s.jsx(o.Button,{variant:"outline",size:"sm",onClick:()=>b(e=>Math.min(e+1,v)),disabled:j===v,children:"Next"})]})]})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var s=r(10326);r(17577);var a=r(79360),n=r(77863);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},33071:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>i,aY:()=>c,eW:()=>u,ll:()=>l});var s=r(10326),a=r(17577),n=r(77863);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));i.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},15940:(e,t,r)=>{"use strict";r.d(t,{RM:()=>l,SC:()=>d,iA:()=>i,pj:()=>u,ss:()=>c,xD:()=>o});var s=r(10326),a=r(17577),n=r(77863);let i=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{className:"relative w-full overflow-auto",children:s.jsx("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));i.displayName="Table";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...t}));o.displayName="TableHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",a.forwardRef(({className:e,...t},r)=>s.jsx("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("th",{ref:r,className:(0,n.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));c.displayName="TableHead";let u=a.forwardRef(({className:e,...t},r)=>s.jsx("td",{ref:r,className:(0,n.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));u.displayName="TableCell",a.forwardRef(({className:e,...t},r)=>s.jsx("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},7426:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var s=r(68570);let a=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\affiliates\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\affiliates\page.tsx#default`)},58585:(e,t,r)=>{"use strict";var s=r(61085);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"redirect")&&r.d(t,{redirect:function(){return s.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return s.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=r(83953),a=r(16399);class n extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new n}delete(){throw new n}set(){throw new n}sort(){throw new n}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return a},notFound:function(){return s}});let r="NEXT_NOT_FOUND";function s(){let e=Error(r);throw e.digest=r,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return s},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return f},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return d}});let a=r(54580),n=r(72934),i=r(8586),o="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let s=Error(o);s.digest=o+";"+t+";"+e+";"+r+";";let n=a.requestAsyncStorage.getStore();return n&&(s.mutableCookies=n.mutableCookies),s}function d(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=n.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,s,a]=e.digest.split(";",4),n=Number(a);return t===o&&("replace"===r||"push"===r)&&"string"==typeof s&&!isNaN(n)&&n in i.RedirectStatusCode}function f(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,4824,7123],()=>r(83528));module.exports=s})();