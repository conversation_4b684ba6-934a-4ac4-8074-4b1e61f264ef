"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebOrdersController = void 0;
const common_1 = require("@nestjs/common");
const web_orders_service_1 = require("./web-orders.service");
let WebOrdersController = class WebOrdersController {
    webOrdersService;
    constructor(webOrdersService) {
        this.webOrdersService = webOrdersService;
    }
    async getOrders(query, res) {
        try {
            const userId = 'temp-user-id';
            const orders = await this.webOrdersService.getOrders(userId, query);
            return res.json(orders);
        }
        catch (error) {
            console.error('[WEB_ORDERS_GET]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch orders',
            });
        }
    }
    async getOrder(orderId, res) {
        try {
            const order = await this.webOrdersService.getOrderById(orderId);
            if (!order) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: 'Order not found',
                });
            }
            return res.json(order);
        }
        catch (error) {
            console.error('[WEB_ORDER_GET]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Internal error',
            });
        }
    }
    async createOrder(createOrderDto, res) {
        try {
            const userId = 'temp-user-id';
            const order = await this.webOrdersService.createOrder(userId, createOrderDto);
            return res.status(common_1.HttpStatus.CREATED).json(order);
        }
        catch (error) {
            console.error('[WEB_ORDER_CREATE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to create order',
            });
        }
    }
    async updateOrder(orderId, updateOrderDto, res) {
        try {
            const order = await this.webOrdersService.updateOrder(orderId, updateOrderDto);
            return res.json(order);
        }
        catch (error) {
            console.error('[WEB_ORDER_UPDATE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to update order',
            });
        }
    }
    async getAvailableEsims(query, res) {
        try {
            const esims = await this.webOrdersService.getAvailableEsims(query);
            return res.json(esims);
        }
        catch (error) {
            console.error('[WEB_ORDERS_AVAILABLE_ESIMS]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch available eSIMs',
            });
        }
    }
};
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebOrdersController.prototype, "getOrders", null);
__decorate([
    (0, common_1.Get)(':orderId'),
    __param(0, (0, common_1.Param)('orderId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebOrdersController.prototype, "getOrder", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebOrdersController.prototype, "createOrder", null);
__decorate([
    (0, common_1.Patch)(':orderId'),
    __param(0, (0, common_1.Param)('orderId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WebOrdersController.prototype, "updateOrder", null);
__decorate([
    (0, common_1.Get)('available-esims'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebOrdersController.prototype, "getAvailableEsims", null);
WebOrdersController = __decorate([
    (0, common_1.Controller)('api/web/orders'),
    __metadata("design:paramtypes", [web_orders_service_1.WebOrdersService])
], WebOrdersController);
exports.WebOrdersController = WebOrdersController;
//# sourceMappingURL=web-orders.controller.js.map