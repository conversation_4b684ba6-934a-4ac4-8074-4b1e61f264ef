"use strict";(()=>{var e={};e.id=8028,e.ids=[8028],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63235:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>w,patchFetch:()=>_,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>y,staticGenerationAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{POST:()=>f,dynamic:()=>u,fetchCache:()=>p,revalidate:()=>d});var n=r(49303),a=r(88716),o=r(60670),s=r(87070),l=r(72331);let u="force-dynamic",p="force-no-store",d=0;async function c(e,t,r,i){try{let n=0,a=null,o=0;if(i.organizationId){let e=await l._.affiliateOrganization.findUnique({where:{id:i.organizationId}});if(e){o=r*e.commissionRate,n=r*i.commissionRate;let t=o-n,s=await l._.organizationCommission.create({data:{organizationId:e.id,commissionAmount:o,status:"PENDING"}});await l._.affiliateOrganization.update({where:{id:e.id},data:{totalEarnings:{increment:t}}}),a=s.id}}else n=r*i.commissionRate;let s=await l._.affiliateReferral.create({data:{affiliateId:i.id,orderId:e,commissionAmount:n,status:"PENDING",...a&&{organizationCommissionId:a}}});return await l._.order.update({where:{id:e},data:{referralCode:t}}),await l._.affiliateProfile.update({where:{id:i.id},data:{totalEarnings:{increment:n}}}),{success:!0,referral:s,commissionGenerated:!0,cardItemsTotal:r,commissionAmount:n,organizationCommissionAmount:o||0,hasOrganization:!!a}}catch(e){throw console.error("[PROCESS_COMMISSION]",e),e}}async function f(e){try{let{orderId:t,referralCode:r}=await e.json();if(!t||!r)return new s.NextResponse("Missing required fields",{status:400});let[i,n]=await Promise.all([l._.order.findUnique({where:{id:t},include:{items:{include:{product:{include:{category:!0}}}}}}),l._.affiliateProfile.findUnique({where:{code:r},include:{organization:!0}})]);if(!i||!n)return new s.NextResponse("Order or affiliate not found",{status:404});if(i.userId===n.userId)return new s.NextResponse("Cannot refer yourself",{status:400});let a=i.items.filter(e=>"card"===e.product.category.name.toLowerCase());if(0===a.length)return await l._.order.update({where:{id:t},data:{referralCode:r}}),s.NextResponse.json({success:!0,message:"Referral code applied, but no commission generated as there are no card products",commissionGenerated:!1});let o=a.reduce((e,t)=>e+t.price*t.quantity,0),u=await c(t,r,o,n);return s.NextResponse.json(u)}catch(e){return console.error("[AFFILIATE_TRACK]",e),new s.NextResponse("Internal error",{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/affiliate/track/route",pathname:"/api/affiliate/track",filename:"route",bundlePath:"app/api/affiliate/track/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\track\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:y}=m,w="/api/affiliate/track/route";function _(){return(0,o.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:g})}},72331:(e,t,r)=>{r.d(t,{_:()=>n});var i=r(53524);let n=global.prisma||new i.PrismaClient({log:["error"]})},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,a={};function o(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function s(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,n],...a]=s(e),{domain:o,expires:l,httponly:d,maxage:c,path:f,samesite:m,secure:h,partitioned:g,priority:y}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(n),domain:o,...l&&{expires:new Date(l)},...d&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:f,...m&&{sameSite:u.includes(t=(t=m).toLowerCase())?t:void 0},...h&&{secure:!0},...y&&{priority:p.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(a,{RequestCookies:()=>d,ResponseCookies:()=>c,parseCookie:()=>s,parseSetCookie:()=>l,stringifyCookie:()=>o}),e.exports=((e,a,o,s)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let o of i(a))n.call(e,o)||void 0===o||t(e,o,{get:()=>a[o],enumerable:!(s=r(a,o))||s.enumerable});return e})(t({},"__esModule",{value:!0}),a);var u=["strict","lax","none"],p=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of s(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>o(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>o(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},c=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let n=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,i,n,a,o=[],s=0;function l(){for(;s<e.length&&/\s/.test(e.charAt(s));)s+=1;return s<e.length}for(;s<e.length;){for(t=s,a=!1;l();)if(","===(r=e.charAt(s))){for(i=s,s+=1,l(),n=s;s<e.length&&"="!==(r=e.charAt(s))&&";"!==r&&","!==r;)s+=1;s<e.length&&"="===e.charAt(s)?(a=!0,s=n,o.push(e.substring(t,i)),t=s):s=i+1}else s+=1;(!a||s>=e.length)&&o.push(e.substring(t,e.length))}return o}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=o(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(o).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return i.RequestCookies},ResponseCookies:function(){return i.ResponseCookies}});let i=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,5972],()=>r(63235));module.exports=i})();