import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { GeographyService } from './geography.service';
import { GeographyQueryDto, ProductsByCountryDto } from './dto/geography-query.dto';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('geography')
export class GeographyController {
  constructor(private readonly geographyService: GeographyService) {}

  /**
   * 获取所有大洲列表
   * GET /geography/continents
   */
  @Public()
  @Get('continents')
  getContinents(@RequestCtx() ctx: RequestContext) {
    return this.geographyService.getContinents(ctx);
  }

  /**
   * 根据大洲获取国家列表
   * GET /geography/continents/:continent/countries
   */
  @Public()
  @Get('continents/:continent/countries')
  getCountriesByContinent(
    @Param('continent') continent: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.geographyService.getCountriesByContinent(continent, ctx);
  }

  /**
   * 根据国家获取商品列表
   * GET /geography/countries/:countryCode/products
   */
  @Public()
  @Get('countries/:countryCode/products')
  getProductsByCountry(
    @Param('countryCode') countryCode: string,
    @Query() query: Omit<ProductsByCountryDto, 'countryCode'>,
    @RequestCtx() ctx: RequestContext,
  ) {
    const dto: ProductsByCountryDto = { ...query, countryCode };
    return this.geographyService.getProductsByCountry(dto, ctx);
  }

  /**
   * 获取所有国家的商品（不限制大洲）
   * GET /geography/products
   */
  @Public()
  @Get('products')
  getAllProducts(
    @Query() query: ProductsByCountryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.geographyService.getProductsByCountry(query, ctx);
  }

  /**
   * 获取商品筛选选项
   * GET /geography/filters
   */
  @Public()
  @Get('filters')
  getProductFilters(@RequestCtx() ctx: RequestContext) {
    return this.geographyService.getProductFilters(ctx);
  }
}
