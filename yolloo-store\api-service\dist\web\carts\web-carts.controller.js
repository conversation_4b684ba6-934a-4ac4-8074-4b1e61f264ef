"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebCartsController = void 0;
const common_1 = require("@nestjs/common");
const web_carts_service_1 = require("./web-carts.service");
let WebCartsController = class WebCartsController {
    webCartsService;
    constructor(webCartsService) {
        this.webCartsService = webCartsService;
    }
    async getCart(res) {
        try {
            const userId = 'temp-user-id';
            const cart = await this.webCartsService.getCart(userId);
            return res.json(cart);
        }
        catch (error) {
            console.error('[WEB_CART_GET]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch cart',
            });
        }
    }
    async addToCart(addToCartDto, res) {
        try {
            const userId = 'temp-user-id';
            const cartItem = await this.webCartsService.addToCart(userId, addToCartDto);
            return res.status(common_1.HttpStatus.CREATED).json(cartItem);
        }
        catch (error) {
            console.error('[WEB_CART_ADD]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to add to cart',
            });
        }
    }
    async updateCartItem(cartItemId, updateCartItemDto, res) {
        try {
            const cartItem = await this.webCartsService.updateCartItem(cartItemId, updateCartItemDto);
            return res.json(cartItem);
        }
        catch (error) {
            console.error('[WEB_CART_UPDATE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to update cart item',
            });
        }
    }
    async removeFromCart(cartItemId, res) {
        try {
            await this.webCartsService.removeFromCart(cartItemId);
            return res.status(common_1.HttpStatus.NO_CONTENT).send();
        }
        catch (error) {
            console.error('[WEB_CART_REMOVE]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to remove from cart',
            });
        }
    }
};
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebCartsController.prototype, "getCart", null);
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebCartsController.prototype, "addToCart", null);
__decorate([
    (0, common_1.Patch)(':cartItemId'),
    __param(0, (0, common_1.Param)('cartItemId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WebCartsController.prototype, "updateCartItem", null);
__decorate([
    (0, common_1.Delete)(':cartItemId'),
    __param(0, (0, common_1.Param)('cartItemId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WebCartsController.prototype, "removeFromCart", null);
WebCartsController = __decorate([
    (0, common_1.Controller)('api/web/cart'),
    __metadata("design:paramtypes", [web_carts_service_1.WebCartsService])
], WebCartsController);
exports.WebCartsController = WebCartsController;
//# sourceMappingURL=web-carts.controller.js.map