"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebProductsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
let WebProductsService = class WebProductsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getProducts(query) {
        const { category, country, search, status = 'ACTIVE' } = query;
        const whereCondition = {
            status,
            off_shelve: false,
        };
        if (category) {
            whereCondition.categoryId = category;
        }
        if (country) {
            whereCondition.OR = [
                { country: { contains: country, mode: 'insensitive' } },
                { countryCode: { contains: country, mode: 'insensitive' } },
            ];
        }
        if (search) {
            whereCondition.name = { contains: search, mode: 'insensitive' };
        }
        return await this.prisma.product.findMany({
            where: whereCondition,
            select: {
                id: true,
                name: true,
                description: true,
                websiteDescription: true,
                price: true,
                off_shelve: true,
                parameters: true,
                category: true,
                country: true,
                countryCode: true,
                dataSize: true,
                planType: true,
            },
            orderBy: {
                createdAt: 'asc',
            },
        });
    }
    async getProductById(productId) {
        return await this.prisma.product.findUnique({
            where: { id: productId },
            include: {
                category: true,
                variants: true,
                parameters: true,
                reviews: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                image: true,
                            },
                        },
                    },
                    orderBy: {
                        createdAt: 'desc',
                    },
                    take: 10,
                },
            },
        });
    }
    async getProductCountries() {
        const products = await this.prisma.product.findMany({
            where: {
                status: 'ACTIVE',
                off_shelve: false,
                country: { not: null },
            },
            select: {
                country: true,
                countryCode: true,
            },
            distinct: ['country'],
        });
        const countries = new Set();
        products.forEach(product => {
            if (product.country) {
                const countryList = product.country.split(/[,;]/).map(c => c.trim());
                countryList.forEach(country => {
                    if (country) {
                        countries.add(country);
                    }
                });
            }
        });
        return Array.from(countries).sort();
    }
    async getProductsBatch(productIds) {
        return await this.prisma.product.findMany({
            where: {
                id: { in: productIds },
                status: 'ACTIVE',
                off_shelve: false,
            },
            include: {
                category: true,
                variants: true,
            },
        });
    }
    async getProductByCode(code) {
        return await this.prisma.product.findFirst({
            where: {
                sku: code,
                status: 'ACTIVE',
                off_shelve: false,
            },
            include: {
                category: true,
                variants: true,
                parameters: true,
            },
        });
    }
    async getProductExternalData(productId) {
        return {
            productId,
            externalData: {},
            message: 'External data feature not implemented yet',
        };
    }
    async getProductCardLinks(productId, query) {
        return {
            productId,
            cardLinks: [],
            message: 'Card links feature not implemented yet',
        };
    }
    async getPaginatedProducts(query) {
        const { page = 1, limit = 20, ...filters } = query;
        const skip = (page - 1) * limit;
        const whereCondition = {
            status: 'ACTIVE',
            off_shelve: false,
            ...filters,
        };
        const [products, total] = await Promise.all([
            this.prisma.product.findMany({
                where: whereCondition,
                skip,
                take: parseInt(limit),
                include: {
                    category: true,
                    variants: true,
                },
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.product.count({
                where: whereCondition,
            }),
        ]);
        return {
            products,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getEnhancedProducts(query) {
        const products = await this.getProducts(query);
        return {
            products,
            enhanced: true,
            message: 'Enhanced products feature partially implemented',
        };
    }
    async getCacheFirstProducts(query) {
        return await this.getProducts(query);
    }
};
WebProductsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WebProductsService);
exports.WebProductsService = WebProductsService;
//# sourceMappingURL=web-products.service.js.map