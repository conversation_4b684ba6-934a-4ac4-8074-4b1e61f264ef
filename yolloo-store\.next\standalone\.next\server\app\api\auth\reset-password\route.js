"use strict";(()=>{var e={};e.id=9436,e.ids=[9436],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},76002:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>O,patchFetch:()=>T,requestAsyncStorage:()=>D,routeModule:()=>x,serverHooks:()=>_,staticGenerationAsyncStorage:()=>A});var n={};r.r(n),r.d(n,{POST:()=>w,PUT:()=>v,dynamic:()=>m,fetchCache:()=>h,revalidate:()=>f});var i=r(49303),s=r(88716),a=r(60670),o=r(87070),u=r(72331),l=r(7410),d=r(6113),p=r.n(d),c=r(50650);let m="force-dynamic",h="force-no-store",f=0,g=l.z.object({email:l.z.string().email("Invalid email address")}),y=l.z.object({token:l.z.string(),password:l.z.string().min(8,"Password must be at least 8 characters")});async function w(e){try{let t=await e.json(),r=g.safeParse(t);if(!r.success)return o.NextResponse.json({error:r.error.errors},{status:400});let{email:n}=r.data,i=await u._.user.findUnique({where:{email:n}});if(!i)return o.NextResponse.json({success:!0});let s=p().randomBytes(32).toString("hex"),a=c.ED.addHours(new Date,24);return await u._.passwordResetToken.upsert({where:{userId:i.id},update:{token:s,expiresAt:a},create:{userId:i.id,token:s,expiresAt:a}}),o.NextResponse.json({success:!0,token:s,resetLink:`http://localhost:8000/auth/reset-password?token=${s}`})}catch(e){return console.error("Error requesting password reset:",e),o.NextResponse.json({error:"Failed to process password reset request"},{status:500})}}async function v(e){try{let t=await e.json(),n=y.safeParse(t);if(!n.success)return o.NextResponse.json({error:n.error.errors},{status:400});let{token:i,password:s}=n.data,a=await u._.passwordResetToken.findFirst({where:{token:i,expiresAt:{gt:new Date}},include:{user:!0}});if(!a)return o.NextResponse.json({error:"Invalid or expired token"},{status:400});let l=r(42023),d=await l.hash(s,10);return await u._.user.update({where:{id:a.userId},data:{hashedPassword:d}}),await u._.passwordResetToken.delete({where:{id:a.id}}),o.NextResponse.json({success:!0})}catch(e){return console.error("Error resetting password:",e),o.NextResponse.json({error:"Failed to reset password"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\auth\\reset-password\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:D,staticGenerationAsyncStorage:A,serverHooks:_}=x,O="/api/auth/reset-password/route";function T(){return(0,a.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:A})}},72331:(e,t,r)=>{r.d(t,{_:()=>i});var n=r(53524);let i=global.prisma||new n.PrismaClient({log:["error"]})},50650:(e,t,r)=>{r.d(t,{CN:()=>h,ED:()=>f,QG:()=>y,T4:()=>d,cn:()=>l,eP:()=>v,mo:()=>w,vI:()=>g});var n=r(55761),i=r(62386),s=r(6180),a=r(4284),o=r(35772),u=r(21740);function l(...e){return(0,i.m6)((0,n.W)(e))}function d(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let p={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function c(){return p.TIMEZONE}function m(e){if(!e)return null;try{let t;if(e instanceof Date)t=e;else if("string"==typeof e)t=e.includes("T")||e.includes("Z")?(0,s.D)(e):new Date(e);else{if("number"!=typeof e)return null;t=new Date(e)}return(0,a.J)(t)?t:null}catch(t){return console.warn("Date parsing error:",t,"Input:",e),null}}let h={short:(e,t="Invalid Date")=>{let r=m(e);return r?(0,o.WU)(r,p.DATE_FORMAT):t},full:(e,t="Invalid Date")=>{let r=m(e);return r?(0,o.WU)(r,p.DATETIME_FORMAT):t},long:(e,t="Invalid Date")=>{let r=m(e);return r?r.toLocaleDateString(p.LOCALE,{year:"numeric",month:"long",day:"numeric"}):t},relative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,u.Q)(r,{addSuffix:!0}):t},time:(e,t="Invalid Time")=>{let r=m(e);return r?(0,o.WU)(r,"HH:mm:ss"):t},timeShort:(e,t="Invalid Time")=>{let r=m(e);return r?(0,o.WU)(r,"HH:mm"):t},withTimezone:(e,t,r="Invalid Date")=>{let n=m(e);return n?new Intl.DateTimeFormat(p.LOCALE,{timeZone:t||p.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(n):r},forUser:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=c();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserShort:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=c();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit"}).format(r)},forUserSafe:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=p.TIMEZONE;return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserRelative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,u.Q)(r,{addSuffix:!0}):t},iso:(e,t="")=>{let r=m(e);return r?r.toISOString():t},custom:(e,t,r="Invalid Date")=>{let n=m(e);return n?(0,o.WU)(n,t):r}},f={addDays:(e,t)=>new Date((m(e)||new Date).getTime()+864e5*t),addHours:(e,t)=>new Date((m(e)||new Date).getTime()+36e5*t),addMinutes:(e,t)=>new Date((m(e)||new Date).getTime()+6e4*t),daysBetween:(e,t)=>{let r=m(e),n=m(t);return r&&n?Math.floor((n.getTime()-r.getTime())/864e5):0},isExpired:e=>{let t=m(e);return!!t&&t.getTime()<Date.now()}};function g(e){return e?e.replace(/\D/g,""):""}function y(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function w(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function v(e){let t=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return t?{badge:t[1].toUpperCase(),name:t[2]}:{name:e}}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[n,i],...s]=o(e),{domain:a,expires:u,httponly:p,maxage:c,path:m,samesite:h,secure:f,partitioned:g,priority:y}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(i),domain:a,...u&&{expires:new Date(u)},...p&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:m,...h&&{sameSite:l.includes(t=(t=h).toLowerCase())?t:void 0},...f&&{secure:!0},...y&&{priority:d.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>p,ResponseCookies:()=>c,parseCookie:()=>o,parseSetCookie:()=>u,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let a of n(s))i.call(e,a)||void 0===a||t(e,a,{get:()=>s[a],enumerable:!(o=r(s,a))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var l=["strict","lax","none"],d=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},c=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,a=[],o=0;function u(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;u();)if(","===(r=e.charAt(o))){for(n=o,o+=1,u(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,5972,2023,5772,7624,7410],()=>r(76002));module.exports=n})();