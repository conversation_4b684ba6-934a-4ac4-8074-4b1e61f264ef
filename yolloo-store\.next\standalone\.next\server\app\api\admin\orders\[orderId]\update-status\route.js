"use strict";(()=>{var e={};e.id=2995,e.ids=[2995],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},58087:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{POST:()=>l});var o=r(49303),i=r(88716),a=r(60670),n=r(87070),d=r(21822),u=r(72331);async function l(e,{params:t}){try{let e=await u._.odooOrderStatus.findMany({where:{orderId:t.orderId},select:{variantCode:!0,uid:!0}}),r=[];if(e.length>0)for(let s of e){let e=s.variantCode||"default",o=s.uid||"no-uid",i=`${t.orderId}-${e}:::${o}`;r.push(i)}else r.push(t.orderId);console.log("[UPDATE_ODOO_STATUS] Querying Odoo with customer_order_refs:",r);let s=await d.h.queryOrderStatusMultiple(r);if(console.log(`[UPDATE_ODOO_STATUS] Response for order ${t.orderId}:`,JSON.stringify(s,null,2)),s?.status!=="success"&&s?.status!=="ok")return n.NextResponse.json({success:!1,message:`Failed to get order status: ${s?.result?.message||"Unknown error"}`},{status:400});let o=s.result?.data;if(!o||0===o.length)return n.NextResponse.json({success:!1,message:"No order data returned from Odoo"},{status:400});let i=[];for(let e of o){let r;if(!e.order_lines||0===e.order_lines.length){console.log(`[UPDATE_ODOO_STATUS] No order lines for order detail: ${e.customer_order_ref}`);continue}let s="default",o=null;if(e.customer_order_ref.includes(":::")){let t=e.customer_order_ref.split("-");if(r=t[0],t.length>1){let[e,r]=t.slice(1).join("-").split(":::");s=e||"default",o="no-uid"===r?null:r}}else{let t=e.customer_order_ref.split("-");r=t[0],t.length>1&&(s=t[1],t.length>2&&(o="no-uid"===t[2]?null:t[2]))}if(r!==t.orderId){console.log(`[UPDATE_ODOO_STATUS] Skipping order ${e.customer_order_ref} as it doesn't match requested ID ${t.orderId}`);continue}for(let r of e.order_lines){let e,a=[],n=o;!n&&r.data&&(Array.isArray(r.data)?(a=r.data.filter(e=>e&&e.uid).map(e=>e.uid)).length>0&&(n=a.join(",")):"object"==typeof r.data&&r.data.uid&&(n=r.data.uid));let d=s||"default",l=n?n.replace(/[^0-9,]/g,""):null;console.log(`[UPDATE_ODOO_STATUS] Processing order ${t.orderId}, variant ${d}, uid=${l||"none"}`);let p=await u._.odooOrderStatus.findFirst({where:{orderId:t.orderId,variantCode:d,uid:l}});e=p?await u._.odooOrderStatus.update({where:{id:p.id},data:{status:r.status,description:r.description,productName:r.product_name,isDigital:r.is_digital||!1,deliveredQty:r.delivered_qty||0,trackingNumber:r.tracking_number,planState:r.data?.[0]?.plan_state,lastCheckedAt:new Date}}):await u._.odooOrderStatus.create({data:{orderId:t.orderId,variantCode:d,status:r.status,description:r.description,productName:r.product_name,isDigital:r.is_digital||!1,deliveredQty:r.delivered_qty||0,trackingNumber:r.tracking_number,planState:r.data?.[0]?.plan_state,uid:l,lastCheckedAt:new Date}}),i.push(e),"delivered"===r.status&&await u._.order.update({where:{id:t.orderId},data:{status:"DELIVERED"}})}}return n.NextResponse.json({success:!0,message:`Order status updated successfully. Updated ${i.length} status records.`,odooStatuses:i})}catch(e){return console.error("[UPDATE_ODOO_STATUS]",e),n.NextResponse.json({success:!1,message:`Error updating order status: ${e instanceof Error?e.message:"Unknown error"}`},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/orders/[orderId]/update-status/route",pathname:"/api/admin/orders/[orderId]/update-status",filename:"route",bundlePath:"app/api/admin/orders/[orderId]/update-status/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\orders\\[orderId]\\update-status\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:c,staticGenerationAsyncStorage:f,serverHooks:m}=p,h="/api/admin/orders/[orderId]/update-status/route";function g(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:f})}},21822:(e,t,r)=>{r.d(t,{h:()=>s.h2});var s=r(10835)},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,i={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),s=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?s:`${s}; ${r.join("; ")}`}function n(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[s,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(s,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function d(e){var t,r;if(!e)return;let[[s,o],...i]=n(e),{domain:a,expires:d,httponly:p,maxage:c,path:f,samesite:m,secure:h,partitioned:g,priority:_}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:s,value:decodeURIComponent(o),domain:a,...d&&{expires:new Date(d)},...p&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:f,...m&&{sameSite:u.includes(t=(t=m).toLowerCase())?t:void 0},...h&&{secure:!0},..._&&{priority:l.includes(r=(r=_).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var s in r)t(e,s,{get:r[s],enumerable:!0})})(i,{RequestCookies:()=>p,ResponseCookies:()=>c,parseCookie:()=>n,parseSetCookie:()=>d,stringifyCookie:()=>a}),e.exports=((e,i,a,n)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let a of s(i))o.call(e,a)||void 0===a||t(e,a,{get:()=>i[a],enumerable:!(n=r(i,a))||n.enumerable});return e})(t({},"__esModule",{value:!0}),i);var u=["strict","lax","none"],l=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of n(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===s).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,s=this._parsed;return s.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(s).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},c=class{constructor(e){var t,r,s;this._parsed=new Map,this._headers=e;let o=null!=(s=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?s:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var t,r,s,o,i,a=[],n=0;function d(){for(;n<e.length&&/\s/.test(e.charAt(n));)n+=1;return n<e.length}for(;n<e.length;){for(t=n,i=!1;d();)if(","===(r=e.charAt(n))){for(s=n,n+=1,d(),o=n;n<e.length&&"="!==(r=e.charAt(n))&&";"!==r&&","!==r;)n+=1;n<e.length&&"="===e.charAt(n)?(i=!0,n=o,a.push(e.substring(t,s)),t=n):n=s+1}else n+=1;(!i||n>=e.length)&&a.push(e.substring(t,e.length))}return a}(o)){let t=d(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===s)}has(e){return this._parsed.has(e)}set(...e){let[t,r,s]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...s})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,s]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:s,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return s.RequestCookies},ResponseCookies:function(){return s.ResponseCookies}});let s=r(79925)}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,9092,5972,9712,835],()=>r(58087));module.exports=s})();