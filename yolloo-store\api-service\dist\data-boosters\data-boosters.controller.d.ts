import { DataBoostersService } from './data-boosters.service';
import { DataBoostersQueryDto, DataBoosterOrderDto } from './dto/data-boosters-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class DataBoostersController {
    private readonly dataBoostersService;
    constructor(dataBoostersService: DataBoostersService);
    getDataBoosters(query: DataBoostersQueryDto, ctx: RequestContext): Promise<{
        boosters: {
            id: any;
            name: any;
            description: any;
            boosterType: string;
            dataSize: string;
            price: number;
            originalPrice: number;
            currency: any;
            validity: string;
            activationType: string;
            features: string[];
            imageUrl: any;
            isPopular: boolean;
            rating: number;
            reviewCount: any;
        }[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        filters: {
            boosterTypes: {
                value: string;
                label: string;
            }[];
            dataSizes: {
                value: string;
                label: string;
            }[];
            activationTypes: {
                value: string;
                label: string;
            }[];
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getBoosterById(boosterId: string, ctx: RequestContext): Promise<{
        id: string;
        name: string;
        description: string;
        boosterType: string;
        dataSize: string;
        price: number;
        originalPrice: number;
        currency: string;
        validity: string;
        activationType: string;
        features: string[];
        imageUrl: string;
        isPopular: boolean;
        rating: number;
        reviewCount: number;
        detailedInfo: {
            activation: string;
            usage: string;
            speed: string;
            compatibility: string;
        };
        usageScenarios: string[];
        restrictions: string[];
    } | {
        detailedInfo: {
            activation: string;
            usage: string;
            speed: string;
            compatibility: string;
        };
        usageScenarios: string[];
        restrictions: string[];
        variants: any;
        reviews: any;
        id: any;
        name: any;
        description: any;
        boosterType: string;
        dataSize: string;
        price: number;
        originalPrice: number;
        currency: any;
        validity: string;
        activationType: string;
        features: string[];
        imageUrl: any;
        isPopular: boolean;
        rating: number;
        reviewCount: any;
    }>;
    createBoosterOrder(orderData: DataBoosterOrderDto, ctx: RequestContext): Promise<{
        order: {
            id: string;
            boosterId: string;
            activationTime: string;
            targetNumber: string | undefined;
            status: string;
            statusText: string;
            createdAt: string;
            estimatedActivationTime: string;
        };
        message: string;
    }>;
    getBoosterHistory(userId: string, query: DataBoostersQueryDto, ctx: RequestContext): Promise<{
        history: ({
            id: string;
            name: string;
            dataSize: string;
            price: number;
            currency: string;
            status: string;
            statusText: string;
            purchasedAt: string;
            activatedAt: string;
            expiredAt: string;
            usagePercentage: number;
            remainingData?: undefined;
        } | {
            id: string;
            name: string;
            dataSize: string;
            price: number;
            currency: string;
            status: string;
            statusText: string;
            purchasedAt: string;
            activatedAt: string;
            expiredAt: string;
            usagePercentage: number;
            remainingData: string;
        })[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        summary: {
            totalPurchased: number;
            totalSpent: number;
            activeCount: number;
        };
    }>;
}
