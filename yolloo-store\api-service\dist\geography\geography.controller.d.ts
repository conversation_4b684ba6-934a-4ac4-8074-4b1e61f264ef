import { GeographyService } from './geography.service';
import { ProductsByCountryDto } from './dto/geography-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class GeographyController {
    private readonly geographyService;
    constructor(geographyService: GeographyService);
    getContinents(ctx: RequestContext): Promise<{
        continents: {
            id: string;
            name: any;
            nameEn: any;
            nameZh: any;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getCountriesByContinent(continent: string, ctx: RequestContext): Promise<{
        continent: {
            id: string;
            name: any;
        };
        countries: any;
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getProductsByCountry(countryCode: string, query: Omit<ProductsByCountryDto, 'countryCode'>, ctx: RequestContext): Promise<{
        products: {
            id: string;
            name: string;
            description: string;
            price: number;
            currency: string;
            imageUrl: string;
            dataSize: number;
            planType: string;
            duration: number;
            countries: string[];
            countryCode: string | undefined;
            rating: number;
            reviewCount: number;
            ratingDistribution: {
                1: number;
                2: number;
                3: number;
                4: number;
                5: number;
            };
            category: {
                id: string;
                name: string;
            };
            variants: {
                id: string;
                price: number;
                currency: string;
            }[];
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            countryCode: string | undefined;
            planType: "Total" | "Daily" | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
    } | {
        products: any[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            countryCode: string | undefined;
            planType: "Total" | "Daily" | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error: string;
    }>;
    getAllProducts(query: ProductsByCountryDto, ctx: RequestContext): Promise<{
        products: {
            id: string;
            name: string;
            description: string;
            price: number;
            currency: string;
            imageUrl: string;
            dataSize: number;
            planType: string;
            duration: number;
            countries: string[];
            countryCode: string | undefined;
            rating: number;
            reviewCount: number;
            ratingDistribution: {
                1: number;
                2: number;
                3: number;
                4: number;
                5: number;
            };
            category: {
                id: string;
                name: string;
            };
            variants: {
                id: string;
                price: number;
                currency: string;
            }[];
        }[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            countryCode: string | undefined;
            planType: "Total" | "Daily" | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
    } | {
        products: any[];
        pagination: {
            page: number;
            pageSize: number;
            total: number;
            totalPages: number;
        };
        filters: {
            countryCode: string | undefined;
            planType: "Total" | "Daily" | undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error: string;
    }>;
    getProductFilters(ctx: RequestContext): Promise<{
        planTypes: {
            value: string;
            label: string;
            description: string;
        }[];
        dataSizes: {
            value: string;
            label: string;
        }[];
        priceRange: {
            min: number;
            max: number;
        };
        sortOptions: {
            value: string;
            label: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error?: undefined;
    } | {
        planTypes: {
            value: string;
            label: string;
            description: string;
        }[];
        dataSizes: {
            value: string;
            label: string;
        }[];
        priceRange: {
            min: number;
            max: number;
        };
        sortOptions: {
            value: string;
            label: string;
        }[];
        context: {
            language: string;
            theme: string;
            currency: string;
        };
        error: string;
    }>;
}
