"use strict";(()=>{var e={};e.id=4541,e.ids=[4541],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},50852:e=>{e.exports=require("async_hooks")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},84492:e=>{e.exports=require("node:stream")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},99777:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>O,patchFetch:()=>R,requestAsyncStorage:()=>b,routeModule:()=>_,serverHooks:()=>E,staticGenerationAsyncStorage:()=>q});var i={};r.r(i),r.d(i,{POST:()=>x,dynamic:()=>h,fetchCache:()=>g,revalidate:()=>w});var a=r(49303),n=r(88716),o=r(60670),s=r(87070),l=r(75571),c=r(7410),u=r(90455),d=r(72331),p=r(60682),m=r(72322),f=r(45045);let h="force-dynamic",g="force-no-store",w=0;async function y(e,t){let r=await d._.user.findUnique({where:{id:e},select:{role:!0,affiliate:{select:{id:!0,organizationId:!0,isAdmin:!0}}}});return!!r&&("ADMIN"===r.role||r.affiliate?.organizationId===t&&!!r.affiliate.isAdmin)}let v=c.z.object({emails:c.z.array(c.z.string().email("Invalid email address")),commissionRate:c.z.number().min(0).max(1).default(.5),isAdmin:c.z.boolean().default(!1)});async function x(e,{params:t}){try{let r=await (0,l.getServerSession)(u.L);if(!r?.user)return s.NextResponse.json({error:"You must be logged in to perform this action"},{status:401});if(!await y(r.user.id,t.id))return s.NextResponse.json({error:"You do not have admin access to this organization"},{status:403});let i=await e.json(),a=v.safeParse(i);if(!a.success)return s.NextResponse.json({error:"Invalid request data",details:a.error.format()},{status:400});let{emails:n,commissionRate:o,isAdmin:c}=a.data,h=await d._.affiliateOrganization.findUnique({where:{id:t.id},include:{members:{where:{userId:r.user.id},select:{id:!0,user:{select:{name:!0}}}}}});if(!h)return s.NextResponse.json({error:"Organization not found"},{status:404});let g=h.members[0]?.user?.name||r.user.name||"A team member",w=[];for(let e of n)try{let r=await d._.organizationInvite.findFirst({where:{organizationId:t.id,email:e,status:"PENDING",expiresAt:{gt:new Date}}});if(r){await (0,p.Z_)(e,h.name,`http://localhost:8000/invite/${r.inviteCode}`,r.expiresAt||new Date,g,r.isAdmin),w.push({email:e,success:!0,message:"Invitation re-sent"});continue}let i=function(e,t,r){let{years:i=0,months:a=0,weeks:n=0,days:o=0,hours:s=0,minutes:l=0,seconds:c=0}=t,u=(0,f.Q)(e,void 0),d=a||i?function(e,t,r){let i=(0,f.Q)(e,void 0);if(isNaN(t))return(0,m.L)(e,NaN);if(!t)return i;let a=i.getDate(),n=(0,m.L)((void 0)||e,i.getTime());return(n.setMonth(i.getMonth()+t+1,0),a>=n.getDate())?n:(i.setFullYear(n.getFullYear(),n.getMonth(),a),i)}(u,a+12*i):u,p=o||n?function(e,t,r){let i=(0,f.Q)(e,void 0);return isNaN(t)?(0,m.L)(e,NaN):(t&&i.setDate(i.getDate()+t),i)}(d,o+7*n):d;return(0,m.L)(e,+p+1e3*(c+60*(l+60*s)))}(new Date,{days:7}),a=`${Math.random().toString(36).substring(2,10)}-${Math.random().toString(36).substring(2,10)}`;await d._.organizationInvite.create({data:{organizationId:t.id,affiliateId:h.members[0]?.id,email:e,status:"PENDING",inviteCode:a,expiresAt:i,commissionRate:o,isAdmin:c}}),await (0,p.Z_)(e,h.name,`http://localhost:8000/invite/${a}`,i,g,c),w.push({email:e,success:!0,message:"Invitation sent"})}catch(t){console.error(`Error processing invite for ${e}:`,t),w.push({email:e,success:!1,error:"Failed to send invitation"})}return s.NextResponse.json({results:w,successCount:w.filter(e=>e.success).length,totalCount:n.length})}catch(e){return console.error("Error processing batch invites:",e),s.NextResponse.json({error:"Failed to process batch invites"},{status:500})}}let _=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/invites/batch/route",pathname:"/api/affiliate/organizations/[id]/invites/batch",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/invites/batch/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\invites\\batch\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:b,staticGenerationAsyncStorage:q,serverHooks:E}=_,O="/api/affiliate/organizations/[id]/invites/batch/route";function R(){return(0,o.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:q})}},90455:(e,t,r)=>{r.d(t,{L:()=>u});var i=r(7585),a=r(72331),n=r(77234),o=r(53797),s=r(42023),l=r.n(s),c=r(93475);let u={adapter:{...(0,i.N)(a._),getUser:async e=>{let t=await a._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await a._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await a._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await l().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,c.Ak)(e.email);if(!t||t!==e.code)return null;await (0,c.qc)(e.email);let r=await a._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await a._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await a._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:i,request:n}){try{if(r&&r.id){let t=n?.headers||new Headers,o=t.get("user-agent")||"",s=t.get("x-forwarded-for"),l=s?s.split(/, /)[0]:t.get("REMOTE_ADDR")||"",c="unknown";i?c=i.code&&!i.password?"email_code":"password":e&&(c=e.provider),await a._.userLoginHistory.create({data:{userId:r.id,ipAddress:l||null,userAgent:o||null,loginMethod:c,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],i=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,a=new URL(i).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let i=new URL(e);if(r.some(e=>i.hostname===e||i.hostname.includes(e)||i.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(i);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return i}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:i}){if("update"===r&&i)return{...e,...i.user};let n=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,t,r)=>{r.d(t,{_:()=>a});var i=r(53524);let a=global.prisma||new i.PrismaClient({log:["error"]})},93475:(e,t,r)=>{r.d(t,{AL:()=>s,Ak:()=>l,qc:()=>c,yz:()=>u});var i=r(62197),a=r.n(i);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,t,r=300){try{let i=o(),a=`verification_code:${e}`;return await i.setex(a,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let t=o(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function c(e){try{let t=o(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function u(e,t,r){try{let i=o(),a=`rate_limit:${e}`,n=await i.get(a),s=n?parseInt(n):0;if(s>=t)return!1;return 0===s?await i.setex(a,r,"1"):await i.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var a=r(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(void 0);if(r&&r.has(e))return r.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=a?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(i,n,s):i[n]=e[n]}return i.default=e,r&&r.set(e,i),i}(r(45609));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7410,5637,682],()=>r(99777));module.exports=i})();