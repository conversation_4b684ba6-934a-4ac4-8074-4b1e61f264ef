"use strict";(()=>{var e={};e.id=9203,e.ids=[9203],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},89341:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>E,requestAsyncStorage:()=>y,routeModule:()=>h,serverHooks:()=>g,staticGenerationAsyncStorage:()=>w});var a={};t.r(a),t.d(a,{POST:()=>f,dynamic:()=>u,fetchCache:()=>p,revalidate:()=>m});var o=t(49303),i=t(88716),n=t(60670),s=t(87070),l=t(75571),c=t(90455),d=t(72331);let u="force-dynamic",p="force-no-store",m=0;async function f(e){try{let e=await (0,l.getServerSession)(c.L);if(!e||"ADMIN"!==e.user.role)return new s.NextResponse("Unauthorized",{status:401});let r=await d._.product.findMany({include:{category:!0,variants:!0,parameters:!0}});if(0===r.length)return s.NextResponse.json({message:"No products found to delete",deletedCount:0,skippedCount:0});let t=[];console.log(`[DELETE_ALL_PRODUCTS] Products to delete: ${r.length}`);let a=0;for(let e of r)try{await d._.$transaction(async r=>{let t=await r.orderItem.findMany({where:{productCode:e.sku},include:{order:!0}});t.length>0&&console.log(`[DELETE_ALL_PRODUCTS] Product ${e.id} has ${t.length} associated order items, preserving productCode`),await r.cartItem.deleteMany({where:{productId:e.id}}),await r.wishlistItem.deleteMany({where:{productId:e.id}}),await r.review.deleteMany({where:{productId:e.id}}),await r.productParameter.deleteMany({where:{productId:e.id}}),await r.productVariant.deleteMany({where:{productId:e.id}});let a=await r.esim.findMany({where:{productId:e.id}});if(a.length>0){console.log(`[DELETE_ALL_PRODUCTS] Product ${e.id} has ${a.length} associated eSIMs`);let t=await r.product.create({data:{name:e.name+" (Placeholder for eSIM)",description:e.description,websiteDescription:e.websiteDescription,price:e.price,images:e.images,categoryId:e.categoryId,stock:0,sku:e.sku+"_placeholder_esim_"+Date.now()+"_"+Math.floor(1e4*Math.random()),status:"DELETED",mcc:e.mcc,dataSize:e.dataSize,planType:e.planType,country:e.country,countryCode:e.countryCode}});await r.esim.updateMany({where:{productId:e.id},data:{productId:t.id}})}await r.product.delete({where:{id:e.id}})}),a++,console.log(`[DELETE_ALL_PRODUCTS] Successfully deleted product: ${e.name} (${e.id})`)}catch(r){console.error(`[DELETE_ALL_PRODUCTS] Error deleting product ${e.id}:`,r)}return s.NextResponse.json({message:"Products deletion completed",totalProducts:r.length,deletedCount:a,skippedCount:t.length,skippedProducts:t.map(e=>({id:e.id,name:e.name}))})}catch(e){if(console.error("[DELETE_ALL_PRODUCTS]",e),e instanceof Error)return new s.NextResponse(`Error deleting products: ${e.message}`,{status:500});return new s.NextResponse("Internal error",{status:500})}}let h=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/products/delete-all/route",pathname:"/api/admin/products/delete-all",filename:"route",bundlePath:"app/api/admin/products/delete-all/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\products\\delete-all\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:y,staticGenerationAsyncStorage:w,serverHooks:g}=h,_="/api/admin/products/delete-all/route";function E(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:w})}},90455:(e,r,t)=>{t.d(r,{L:()=>d});var a=t(7585),o=t(72331),i=t(77234),n=t(53797),s=t(42023),l=t.n(s),c=t(93475);let d={adapter:{...(0,a.N)(o._),getUser:async e=>{let r=await o._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await o._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await o._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,n.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await o._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,c.Ak)(e.email);if(!r||r!==e.code)return null;await (0,c.qc)(e.email);let t=await o._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await o._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await o._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:i}){try{if(t&&t.id){let r=i?.headers||new Headers,n=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",c="unknown";a?c=a.code&&!a.password?"email_code":"password":e&&(c=e.provider),await o._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:n||null,loginMethod:c,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,o=new URL(a).searchParams.get("callbackUrl");if(o){let e=decodeURIComponent(o);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let i=await o._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return i?{id:i.id,name:i.name,email:i.email,picture:i.image,role:i.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>o});var a=t(53524);let o=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>c,yz:()=>d});var a=t(62197),o=t.n(a);let i=null;function n(){if(!i){let e=process.env.REDIS_URL||"redis://localhost:6379";(i=new(o())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),i.on("connect",()=>{console.log("Successfully connected to Redis")})}return i}async function s(e,r,t=300){try{let a=n(),o=`verification_code:${e}`;return await a.setex(o,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=n(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function c(e){try{let r=n(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function d(e,r,t){try{let a=n(),o=`rate_limit:${e}`,i=await a.get(o),s=i?parseInt(i):0;if(s>=r)return!1;return 0===s?await a.setex(o,t,"1"):await a.incr(o),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var o=t(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=o?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(a,i,s):a[i]=e[i]}return a.default=e,t&&t.set(e,a),a}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>t(89341));module.exports=a})();