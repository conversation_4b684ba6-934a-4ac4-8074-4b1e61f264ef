import { <PERSON>, Get, Param, Res, NotFoundException } from '@nestjs/common';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';

@Controller('images')
export class StaticController {
  private readonly staticDir = path.join(process.cwd(), 'static');

  constructor() {
    this.ensureStaticDirectories();
  }

  private ensureStaticDirectories() {
    const dirs = [
      'static',
      'static/images',
      'static/images/defaults',
      'static/images/products',
      'static/images/categories',
      'static/images/rewards',
    ];

    dirs.forEach(dir => {
      const dirPath = path.join(process.cwd(), dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    });

    // 创建默认占位符图片（如果不存在）
    this.createDefaultPlaceholders();
  }

  private createDefaultPlaceholders() {
    const placeholders = [
      'defaults/product-placeholder.jpg',
      'defaults/travel-package-placeholder.jpg',
      'defaults/author-avatar.jpg',
      'defaults/user-avatar.jpg',
    ];

    placeholders.forEach(placeholder => {
      const filePath = path.join(this.staticDir, 'images', placeholder);
      if (!fs.existsSync(filePath)) {
        // 创建一个简单的SVG占位符
        const svgContent = this.generatePlaceholderSVG(placeholder);
        fs.writeFileSync(filePath.replace('.jpg', '.svg'), svgContent);
      }
    });
  }

  private generatePlaceholderSVG(type: string): string {
    const isAvatar = type.includes('avatar');
    const width = isAvatar ? 100 : 300;
    const height = isAvatar ? 100 : 200;
    
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="#999" text-anchor="middle" dy=".3em">
    ${isAvatar ? 'Avatar' : 'Image'}
  </text>
</svg>`;
  }

  @Get('defaults/:filename')
  async serveDefaultImage(@Param('filename') filename: string, @Res() res: Response) {
    return this.serveStaticFile('defaults', filename, res);
  }

  @Get('products/:filename')
  async serveProductImage(@Param('filename') filename: string, @Res() res: Response) {
    return this.serveStaticFile('products', filename, res);
  }

  @Get('categories/:filename')
  async serveCategoryImage(@Param('filename') filename: string, @Res() res: Response) {
    return this.serveStaticFile('categories', filename, res);
  }

  @Get('rewards/:filename')
  async serveRewardImage(@Param('filename') filename: string, @Res() res: Response) {
    return this.serveStaticFile('rewards', filename, res);
  }

  private async serveStaticFile(category: string, filename: string, res: Response) {
    try {
      // 安全检查：防止路径遍历攻击
      if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new NotFoundException('Invalid filename');
      }

      const filePath = path.join(this.staticDir, 'images', category, filename);
      
      // 如果请求的是 .jpg 但文件不存在，尝试 .svg
      if (!fs.existsSync(filePath) && filename.endsWith('.jpg')) {
        const svgPath = filePath.replace('.jpg', '.svg');
        if (fs.existsSync(svgPath)) {
          res.setHeader('Content-Type', 'image/svg+xml');
          res.setHeader('Cache-Control', 'public, max-age=86400'); // 1天缓存
          const fileStream = fs.createReadStream(svgPath);
          return fileStream.pipe(res);
        }
      }

      if (!fs.existsSync(filePath)) {
        throw new NotFoundException('Image not found');
      }

      // 设置适当的 Content-Type
      const ext = path.extname(filename).toLowerCase();
      const mimeTypes: { [key: string]: string } = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
      };

      const mimeType = mimeTypes[ext] || 'application/octet-stream';
      res.setHeader('Content-Type', mimeType);
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 1天缓存

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

    } catch (error) {
      throw new NotFoundException('Image not found');
    }
  }
}
