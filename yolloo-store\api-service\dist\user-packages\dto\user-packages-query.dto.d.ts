export declare class UserPackagesQueryDto {
    status?: 'activating' | 'to_be_activated' | 'expired' | 'all';
    packageType?: 'domestic' | 'roaming' | 'local';
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class ActivatePackageDto {
    packageId: string;
}
export declare class UsageStatsQueryDto {
    period?: 'daily' | 'weekly' | 'monthly';
    startDate?: string;
    endDate?: string;
}
export declare class UsageHistoryQueryDto {
    page?: number;
    pageSize?: number;
    period?: 'daily' | 'weekly' | 'monthly';
    startDate?: string;
    endDate?: string;
}
