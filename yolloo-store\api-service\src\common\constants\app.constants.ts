// Language constants
export const LANGUAGES = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US',
} as const;

// Theme constants
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
} as const;

// Currency constants
export const CURRENCIES = {
  CNY: 'CNY',
  USD: 'USD',
} as const;

// Default pagination
export const DEFAULT_PAGINATION = {
  PAGE: 1,
  PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 50,
} as const;

// Default sort
export const DEFAULT_SORT = {
  BY: 'price',
  ORDER: 'asc',
} as const;

// Package types
export const PACKAGE_TYPES = {
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  UNLIMITED: 'unlimited',
  DAILY: 'daily',
  WEEKLY: 'weekly',
} as const;

// Account types
export const ACCOUNT_TYPES = {
  PREPAID: 'prepaid',
  POSTPAID: 'postpaid',
} as const;

// Operators
export const OPERATORS = {
  CHINA_MOBILE: 'china-mobile',
  CHINA_UNICOM: 'china-unicom',
  CHINA_TELECOM: 'china-telecom',
} as const;

// Regions/Continents
export const CONTINENTS = {
  ASIA: 'asia',
  EUROPE: 'europe',
  AMERICA: 'america',
  OCEANIA: 'oceania',
  AFRICA: 'africa',
} as const;

// Legacy alias for backward compatibility
export const REGIONS = CONTINENTS;

// Continent and Country mapping
export const CONTINENT_COUNTRIES = {
  [CONTINENTS.ASIA]: [
    { code: 'JP', name: '日本', nameEn: 'Japan' },
    { code: 'KR', name: '韩国', nameEn: 'South Korea' },
    { code: 'TH', name: '泰国', nameEn: 'Thailand' },
    { code: 'SG', name: '新加坡', nameEn: 'Singapore' },
    { code: 'MY', name: '马来西亚', nameEn: 'Malaysia' },
    { code: 'VN', name: '越南', nameEn: 'Vietnam' },
    { code: 'CN', name: '中国', nameEn: 'China' },
    { code: 'IN', name: '印度', nameEn: 'India' },
    { code: 'ID', name: '印度尼西亚', nameEn: 'Indonesia' },
    { code: 'PH', name: '菲律宾', nameEn: 'Philippines' },
  ],
  [CONTINENTS.EUROPE]: [
    { code: 'GB', name: '英国', nameEn: 'United Kingdom' },
    { code: 'FR', name: '法国', nameEn: 'France' },
    { code: 'DE', name: '德国', nameEn: 'Germany' },
    { code: 'IT', name: '意大利', nameEn: 'Italy' },
    { code: 'ES', name: '西班牙', nameEn: 'Spain' },
    { code: 'NL', name: '荷兰', nameEn: 'Netherlands' },
    { code: 'CH', name: '瑞士', nameEn: 'Switzerland' },
    { code: 'AT', name: '奥地利', nameEn: 'Austria' },
    { code: 'BE', name: '比利时', nameEn: 'Belgium' },
    { code: 'SE', name: '瑞典', nameEn: 'Sweden' },
  ],
  [CONTINENTS.AMERICA]: [
    { code: 'US', name: '美国', nameEn: 'United States' },
    { code: 'CA', name: '加拿大', nameEn: 'Canada' },
    { code: 'MX', name: '墨西哥', nameEn: 'Mexico' },
    { code: 'BR', name: '巴西', nameEn: 'Brazil' },
    { code: 'AR', name: '阿根廷', nameEn: 'Argentina' },
    { code: 'CL', name: '智利', nameEn: 'Chile' },
    { code: 'CO', name: '哥伦比亚', nameEn: 'Colombia' },
    { code: 'PE', name: '秘鲁', nameEn: 'Peru' },
  ],
  [CONTINENTS.OCEANIA]: [
    { code: 'AU', name: '澳大利亚', nameEn: 'Australia' },
    { code: 'NZ', name: '新西兰', nameEn: 'New Zealand' },
    { code: 'FJ', name: '斐济', nameEn: 'Fiji' },
    { code: 'PG', name: '巴布亚新几内亚', nameEn: 'Papua New Guinea' },
  ],
  [CONTINENTS.AFRICA]: [
    { code: 'ZA', name: '南非', nameEn: 'South Africa' },
    { code: 'EG', name: '埃及', nameEn: 'Egypt' },
    { code: 'MA', name: '摩洛哥', nameEn: 'Morocco' },
    { code: 'KE', name: '肯尼亚', nameEn: 'Kenya' },
    { code: 'NG', name: '尼日利亚', nameEn: 'Nigeria' },
    { code: 'GH', name: '加纳', nameEn: 'Ghana' },
  ],
} as const;

// Continent display names
export const CONTINENT_NAMES = {
  [CONTINENTS.ASIA]: { zh: '亚洲', en: 'Asia' },
  [CONTINENTS.EUROPE]: { zh: '欧洲', en: 'Europe' },
  [CONTINENTS.AMERICA]: { zh: '美洲', en: 'America' },
  [CONTINENTS.OCEANIA]: { zh: '大洋洲', en: 'Oceania' },
  [CONTINENTS.AFRICA]: { zh: '非洲', en: 'Africa' },
} as const;

// Data sizes
export const DATA_SIZES = {
  MB_100: '100MB',
  MB_500: '500MB',
  GB_1: '1GB',
  GB_3: '3GB',
  GB_5: '5GB',
  GB_10: '10GB',
  GB_20: '20GB',
  UNLIMITED: 'unlimited',
} as const;

// Booster types
export const BOOSTER_TYPES = {
  EMERGENCY: 'emergency',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
} as const;

// Activation types
export const ACTIVATION_TYPES = {
  INSTANT: 'instant',
  SCHEDULED: 'scheduled',
} as const;

// Order status
export const ORDER_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

// Network types
export const NETWORK_TYPES = {
  FOUR_G: '4G',
  FIVE_G: '5G',
  FOUR_G_FIVE_G: '4G/5G',
} as const;

// Colors for grid buttons
export const GRID_COLORS = {
  BLUE: '#007AFF',
  GREEN: '#34C759',
  ORANGE: '#FF9500',
  PURPLE: '#5856D6',
  PINK: '#FF2D92',
  RED: '#FF3B30',
  MINT: '#30D158',
  CYAN: '#64D2FF',
  GRAY: '#8E8E93',
} as const;

// Card status
export const CARD_STATUS = {
  PENDING_ACTIVATION: 'pending_activation', // 待激活
  ACTIVE: 'active',                        // 已激活
  INACTIVE: 'inactive',                    // 未激活
  EXPIRED: 'expired',                      // 已过期
} as const;

// Card status display names
export const CARD_STATUS_NAMES = {
  [CARD_STATUS.ACTIVE]: {
    zh: '已激活',
    en: 'Active',
  },
  [CARD_STATUS.INACTIVE]: {
    zh: '未激活',
    en: 'Inactive',
  },
  [CARD_STATUS.EXPIRED]: {
    zh: '已过期',
    en: 'Expired',
  },
} as const;
