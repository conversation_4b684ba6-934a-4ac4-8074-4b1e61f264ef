(()=>{var e={};e.id=477,e.ids=[477],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},80544:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>h,pages:()=>c,routeModule:()=>d,tree:()=>f}),r(35429),r(85460),r(89090),r(26083),r(35866);var n=r(23191),a=r(88716),s=r(37922),i=r.n(s),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let f=["",{children:["admin",{children:["cards",{children:["import",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35429)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\cards\\import\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\cards\\import\\page.tsx"],h="/admin/cards/import/page",u={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/cards/import/page",pathname:"/admin/cards/import",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:f}})},8519:(e,t,r)=>{Promise.resolve().then(r.bind(r,30038))},30038:(e,t,r)=>{"use strict";let n;r.d(t,{ImportForm:()=>sH});var a,s,i,o,l=r(10326),f=r(17577),c=r(35047),h=r(90772),u=r(54432),d=r(31048),p=r(57372),m=r(85999),g={};g.version="0.18.5";var v=1200,T=1252,E=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],b={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},w=function(e){-1!=E.indexOf(e)&&(T=b[0]=e)},S=function(e){v=e,w(e)};function A(e){for(var t=[],r=0,n=e.length;r<n;++r)t[r]=e.charCodeAt(r);return t}function _(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var y=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}(e.slice(2)):254==t&&255==r?_(e.slice(2)):65279==t?e.slice(1):e},x=function(e){return String.fromCharCode(e)},O=function(e){return String.fromCharCode(e)},C="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function R(e){for(var t="",r=0,n=0,a=0,s=0,i=0,o=0,l=0,f=0;f<e.length;)s=(r=e.charCodeAt(f++))>>2,i=(3&r)<<4|(n=e.charCodeAt(f++))>>4,o=(15&n)<<2|(a=e.charCodeAt(f++))>>6,l=63&a,isNaN(n)?o=l=64:isNaN(a)&&(l=64),t+=C.charAt(s)+C.charAt(i)+C.charAt(o)+C.charAt(l);return t}function N(e){var t="",r=0,n=0,a=0,s=0,i=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)t+=String.fromCharCode(C.indexOf(e.charAt(l++))<<2|(s=C.indexOf(e.charAt(l++)))>>4),r=(15&s)<<4|(i=C.indexOf(e.charAt(l++)))>>2,64!==i&&(t+=String.fromCharCode(r)),n=(3&i)<<6|(o=C.indexOf(e.charAt(l++))),64!==o&&(t+=String.fromCharCode(n));return t}var I="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,k=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function P(e){return I?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function D(e){return I?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var L=function(e){return I?k(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function M(e){if("undefined"==typeof ArrayBuffer)return L(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function F(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var U=I?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:k(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else if("string"==typeof e[t])throw"wtf";else n.set(new Uint8Array(e[t]),r);return n}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},B=/\u0000/g,W=/[\u0001-\u0006]/g;function H(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function G(e,t){var r=""+e;return r.length>=t?r:e$("0",t-r.length)+r}function j(e,t){var r=""+e;return r.length>=t?r:e$(" ",t-r.length)+r}function V(e,t){var r=""+e;return r.length>=t?r:r+e$(" ",t-r.length)}function z(e,t){var r,n;return e>4294967296||e<-4294967296?(r=""+Math.round(e)).length>=t?r:e$("0",t-r.length)+r:(n=""+Math.round(e)).length>=t?n:e$("0",t-n.length)+n}function Y(e,t){return t=t||0,e.length>=7+t&&(32|e.charCodeAt(t))==103&&(32|e.charCodeAt(t+1))==101&&(32|e.charCodeAt(t+2))==110&&(32|e.charCodeAt(t+3))==101&&(32|e.charCodeAt(t+4))==114&&(32|e.charCodeAt(t+5))==97&&(32|e.charCodeAt(t+6))==108}var K=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],X=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],q={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},J={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Z={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Q(e,t,r){for(var n=e<0?-1:1,a=e*n,s=0,i=1,o=0,l=1,f=0,c=0,h=Math.floor(a);f<t&&(o=(h=Math.floor(a))*i+s,c=h*f+l,!(a-h<5e-8));)a=1/(a-h),s=i,i=o,l=f,f=c;if(c>t&&(f>t?(c=l,o=s):(c=f,o=i)),!r)return[0,n*o,c];var u=Math.floor(n*o/c);return[u,n*o-u*c,c]}function ee(e,t,r){if(e>2958465||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),s=0,i=[],o={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(o.u)&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++a&&(o.T=a=0,++n,++o.D)),60===n)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===n)i=r?[1317,8,29]:[1900,1,0],s=6;else{n>60&&--n;var l,f,c=new Date(1900,0,1);c.setDate(c.getDate()+n-1),i=[c.getFullYear(),c.getMonth()+1,c.getDate()],s=c.getDay(),n<60&&(s=(s+6)%7),r&&(l=i,l[0]-=581,f=c.getDay(),c<60&&(f=(f+6)%7),s=f)}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=a%60,a=Math.floor(a/60),o.M=a%60,a=Math.floor(a/60),o.H=a,o.q=s,o}var et=new Date(1899,11,31,0,0,0),er=et.getTime(),en=new Date(1900,2,1,0,0,0);function ea(e,t){var r=e.getTime();return t?r-=1262304e5:e>=en&&(r+=864e5),(r-(er+(e.getTimezoneOffset()-et.getTimezoneOffset())*6e4))/864e5}function es(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function ei(e){var t,r,n,a,s,i=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return i>=-4&&i<=-1?s=e.toPrecision(10+i):9>=Math.abs(i)?(t=e<0?12:11,s=(r=es(e.toFixed(12))).length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)):s=10===i?e.toFixed(10).substr(0,12):(n=es(e.toFixed(11))).length>(e<0?12:11)||"0"===n||"-0"===n?e.toPrecision(6):n,es(-1==(a=s.toUpperCase()).indexOf("E")?a:a.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function eo(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):ei(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return eS(14,ea(e,t&&t.date1904),t)}throw Error("unsupported value in General format: "+e)}function el(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var ef=/%/g,ec=/# (\?+)( ?)\/( ?)(\d+)/,eh=/^#*0*\.([0#]+)/,eu=/\).*[0#]/,ed=/\(###\) ###\\?-####/;function ep(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function em(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function eg(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function ev(e,t,r){return(0|r)===r?function e(t,r,n){if(40===t.charCodeAt(0)&&!r.match(eu)){var a,s=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return n>=0?e("n",s,n):"("+e("n",s,-n)+")"}if(44===r.charCodeAt(r.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return ev(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(t,r,n);if(-1!==r.indexOf("%"))return o=(i=r).replace(ef,""),l=i.length-o.length,ev(t,o,n*Math.pow(10,2*l))+e$("%",l);if(-1!==r.indexOf("E"))return function e(t,r){var n,a=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),!(n=(r/Math.pow(10,i)).toPrecision(a+1+(s+i)%s)).match(/[Ee]/)){var o=Math.floor(Math.log(r)*Math.LOG10E);-1===n.indexOf(".")?n=n.charAt(0)+"."+n.substr(1)+"E+"+(o-n.length+i):n+="E+"+(o-i),n=n.replace(/\+-/,"-")}n=n.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,n){return t+r+n.substr(0,(s+i)%s)+"."+n.substr(i)+"E"})}else n=r.toExponential(a);return t.match(/E\+00$/)&&n.match(/e[+-]\d$/)&&(n=n.substr(0,n.length-1)+"0"+n.charAt(n.length-1)),t.match(/E\-/)&&n.match(/e\+/)&&(n=n.replace(/e\+/,"e")),n.replace("e","E")}(r,n);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),n);var i,o,l,f,c,h,u,d=Math.abs(n),p=n<0?"-":"";if(r.match(/^00+$/))return p+G(d,r.length);if(r.match(/^[#?]+$/))return f=""+n,0===n&&(f=""),f.length>r.length?f:ep(r.substr(0,r.length-f.length))+f;if(c=r.match(ec))return p+(0===d?"":""+d)+e$(" ",(a=c)[1].length+2+a[4].length);if(r.match(/^#+0+$/))return p+G(d,r.length-r.indexOf("0"));if(c=r.match(eh))return f=(f=(""+n).replace(/^([^\.]+)$/,"$1."+ep(c[1])).replace(/\.$/,"."+ep(c[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+e$("0",ep(c[1]).length-t.length)}),-1!==r.indexOf("0.")?f:f.replace(/^0\./,".");if(c=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return p+(""+d).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,c[1].length?"0.":".");if(c=r.match(/^#{1,3},##0(\.?)$/))return p+el(""+d);if(c=r.match(/^#,##0\.([#0]*0)$/))return n<0?"-"+e(t,r,-n):el(""+n)+"."+e$("0",c[1].length);if(c=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),n);if(c=r.match(/^([0#]+)(\\?-([0#]+))+$/))return f=H(e(t,r.replace(/[\\-]/g,""),n)),h=0,H(H(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return h<f.length?f.charAt(h++):"0"===e?"0":""}));if(r.match(ed))return"("+(f=e(t,"##########",n)).substr(0,3)+") "+f.substr(3,3)+"-"+f.substr(6);var m="";if(c=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return u=Q(d,Math.pow(10,h=Math.min(c[4].length,7))-1,!1),f=""+p," "==(m=ev("n",c[1],u[1])).charAt(m.length-1)&&(m=m.substr(0,m.length-1)+"0"),f+=m+c[2]+"/"+c[3],(m=V(u[2],h)).length<c[4].length&&(m=ep(c[4].substr(c[4].length-m.length))+m),f+=m;if(c=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return p+((u=Q(d,Math.pow(10,h=Math.min(Math.max(c[1].length,c[4].length),7))-1,!0))[0]||(u[1]?"":"0"))+" "+(u[1]?j(u[1],h)+c[2]+"/"+c[3]+V(u[2],h):e$(" ",2*h+1+c[2].length+c[3].length));if(c=r.match(/^[#0?]+$/))return(f=""+n,r.length<=f.length)?f:ep(r.substr(0,r.length-f.length))+f;if(c=r.match(/^([#0]+)\.([#0]+)$/)){h=(f=""+n.toFixed(Math.min(c[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var g=r.indexOf(".")-h,v=r.length-f.length-g;return ep(r.substr(0,g)+f+r.substr(r.length-v))}if(c=r.match(/^00,000\.([#0]*0)$/))return n<0?"-"+e(t,r,-n):el(""+n).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?G(0,3-e.length):"")+e})+"."+G(0,c[1].length);switch(r){case"###,###":case"##,###":case"#,###":var T=el(""+d);return"0"!==T?p+T:"";default:if(r.match(/\.[0#?]*$/))return e(t,r.slice(0,r.lastIndexOf(".")),n)+ep(r.slice(r.lastIndexOf(".")))}throw Error("unsupported format |"+r+"|")}(e,t,r):function e(t,r,n){if(40===t.charCodeAt(0)&&!r.match(eu)){var a,s,i,o,l,f,c=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return n>=0?e("n",c,n):"("+e("n",c,-n)+")"}if(44===r.charCodeAt(r.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return ev(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(t,r,n);if(-1!==r.indexOf("%"))return u=(h=r).replace(ef,""),d=h.length-u.length,ev(t,u,n*Math.pow(10,2*d))+e$("%",d);if(-1!==r.indexOf("E"))return function e(t,r){var n,a=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(n=(r/Math.pow(10,i)).toPrecision(a+1+(s+i)%s)).indexOf("e")){var o=Math.floor(Math.log(r)*Math.LOG10E);for(-1===n.indexOf(".")?n=n.charAt(0)+"."+n.substr(1)+"E+"+(o-n.length+i):n+="E+"+(o-i);"0."===n.substr(0,2);)n=(n=n.charAt(0)+n.substr(2,s)+"."+n.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");n=n.replace(/\+-/,"-")}n=n.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,n){return t+r+n.substr(0,(s+i)%s)+"."+n.substr(i)+"E"})}else n=r.toExponential(a);return t.match(/E\+00$/)&&n.match(/e[+-]\d$/)&&(n=n.substr(0,n.length-1)+"0"+n.charAt(n.length-1)),t.match(/E\-/)&&n.match(/e\+/)&&(n=n.replace(/e\+/,"e")),n.replace("e","E")}(r,n);if(36===r.charCodeAt(0))return"$"+e(t,r.substr(" "==r.charAt(1)?2:1),n);var h,u,d,p,m,g,v,T=Math.abs(n),E=n<0?"-":"";if(r.match(/^00+$/))return E+z(T,r.length);if(r.match(/^[#?]+$/))return"0"===(p=z(n,0))&&(p=""),p.length>r.length?p:ep(r.substr(0,r.length-p.length))+p;if(m=r.match(ec))return o=Math.floor((i=Math.round(T*(s=parseInt((a=m)[4],10))))/s),l=i-o*s,E+(0===o?"":""+o)+" "+(0===l?e$(" ",a[1].length+1+a[4].length):j(l,a[1].length)+a[2]+"/"+a[3]+G(s,a[4].length));if(r.match(/^#+0+$/))return E+z(T,r.length-r.indexOf("0"));if(m=r.match(eh))return p=em(n,m[1].length).replace(/^([^\.]+)$/,"$1."+ep(m[1])).replace(/\.$/,"."+ep(m[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+e$("0",ep(m[1]).length-t.length)}),-1!==r.indexOf("0.")?p:p.replace(/^0\./,".");if(m=(r=r.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return E+em(T,m[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,m[1].length?"0.":".");if(m=r.match(/^#{1,3},##0(\.?)$/))return E+el(z(T,0));if(m=r.match(/^#,##0\.([#0]*0)$/))return n<0?"-"+e(t,r,-n):el(""+(Math.floor(n)+((f=m[1].length)<(""+Math.round((n-Math.floor(n))*Math.pow(10,f))).length?1:0)))+"."+G(eg(n,m[1].length),m[1].length);if(m=r.match(/^#,#*,#0/))return e(t,r.replace(/^#,#*,/,""),n);if(m=r.match(/^([0#]+)(\\?-([0#]+))+$/))return p=H(e(t,r.replace(/[\\-]/g,""),n)),g=0,H(H(r.replace(/\\/g,"")).replace(/[0#]/g,function(e){return g<p.length?p.charAt(g++):"0"===e?"0":""}));if(r.match(ed))return"("+(p=e(t,"##########",n)).substr(0,3)+") "+p.substr(3,3)+"-"+p.substr(6);var b="";if(m=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return v=Q(T,Math.pow(10,g=Math.min(m[4].length,7))-1,!1),p=""+E," "==(b=ev("n",m[1],v[1])).charAt(b.length-1)&&(b=b.substr(0,b.length-1)+"0"),p+=b+m[2]+"/"+m[3],(b=V(v[2],g)).length<m[4].length&&(b=ep(m[4].substr(m[4].length-b.length))+b),p+=b;if(m=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return E+((v=Q(T,Math.pow(10,g=Math.min(Math.max(m[1].length,m[4].length),7))-1,!0))[0]||(v[1]?"":"0"))+" "+(v[1]?j(v[1],g)+m[2]+"/"+m[3]+V(v[2],g):e$(" ",2*g+1+m[2].length+m[3].length));if(m=r.match(/^[#0?]+$/))return(p=z(n,0),r.length<=p.length)?p:ep(r.substr(0,r.length-p.length))+p;if(m=r.match(/^([#0?]+)\.([#0]+)$/)){g=(p=""+n.toFixed(Math.min(m[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var w=r.indexOf(".")-g,S=r.length-p.length-w;return ep(r.substr(0,w)+p+r.substr(r.length-S))}if(m=r.match(/^00,000\.([#0]*0)$/))return g=eg(n,m[1].length),n<0?"-"+e(t,r,-n):el(n<2147483647&&n>-2147483648?""+(n>=0?0|n:n-1|0):""+Math.floor(n)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?G(0,3-e.length):"")+e})+"."+G(g,m[1].length);switch(r){case"###,##0.00":return e(t,"#,##0.00",n);case"###,###":case"##,###":case"#,###":var A=el(z(T,0));return"0"!==A?E+A:"";case"###,###.00":return e(t,"###,##0.00",n).replace(/^0\./,".");case"#,###.00":return e(t,"#,##0.00",n).replace(/^0\./,".")}throw Error("unsupported format |"+r+"|")}(e,t,r)}var eT=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function eE(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":Y(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase()||"AM/PM"===e.substr(t,5).toUpperCase()||"上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(n=r;"]"!==e.charAt(t++)&&t<e.length;)n+=e.charAt(t);if(n.match(eT))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(" "==e.charAt(t)||"*"==e.charAt(t))&&++t;break;case"(":case")":case" ":default:++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);}return!1}var eb=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ew(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function eS(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:q)[e])&&(n=r.table&&r.table[J[e]]||q[J[e]]),null==n&&(n=Z[e]||"General")}if(Y(n,0))return eo(t,r);t instanceof Date&&(t=ea(t,r.date1904));var a=function(e,t){var r=function(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw Error("Format |"+e+"| unterminated string ");return t}(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(eb),o=r[1].match(eb);return ew(t,i)?[n,r[0]]:ew(t,o)?[n,r[1]]:[n,r[null!=i&&null!=o?2:1]]}return[n,s]}(n,t);if(Y(a[1]))return eo(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,n){for(var a,s,i,o=[],l="",f=0,c="",h="t",u="H";f<e.length;)switch(c=e.charAt(f)){case"G":if(!Y(e,f))throw Error("unrecognized character "+c+" in "+e);o[o.length]={t:"G",v:"General"},f+=7;break;case'"':for(l="";34!==(i=e.charCodeAt(++f))&&f<e.length;)l+=String.fromCharCode(i);o[o.length]={t:"t",v:l},++f;break;case"\\":var d=e.charAt(++f),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++f;break;case"_":o[o.length]={t:"t",v:" "},f+=2;break;case"@":o[o.length]={t:"T",v:t},++f;break;case"B":case"b":if("1"===e.charAt(f+1)||"2"===e.charAt(f+1)){if(null==a&&null==(a=ee(t,r,"2"===e.charAt(f+1))))return"";o[o.length]={t:"X",v:e.substr(f,2)},h=c,f+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||null==a&&null==(a=ee(t,r)))return"";for(l=c;++f<e.length&&e.charAt(f).toLowerCase()===c;)l+=c;"m"===c&&"h"===h.toLowerCase()&&(c="M"),"h"===c&&(c=u),o[o.length]={t:c,v:l},h=c;break;case"A":case"a":case"上":var m={t:c,v:c};if(null==a&&(a=ee(t,r)),"A/P"===e.substr(f,3).toUpperCase()?(null!=a&&(m.v=a.H>=12?"P":"A"),m.t="T",u="h",f+=3):"AM/PM"===e.substr(f,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"PM":"AM"),m.t="T",f+=5,u="h"):"上午/下午"===e.substr(f,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"下午":"上午"),m.t="T",f+=5,u="h"):(m.t="t",++f),null==a&&"T"===m.t)return"";o[o.length]=m,h=c;break;case"[":for(l=c;"]"!==e.charAt(f++)&&f<e.length;)l+=e.charAt(f);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(eT)){if(null==a&&null==(a=ee(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else l.indexOf("$")>-1&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",eE(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=a){for(l=c;++f<e.length&&"0"===(c=e.charAt(f));)l+=c;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=c;++f<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(f))>-1;)l+=c;o[o.length]={t:"n",v:l};break;case"?":for(l=c;e.charAt(++f)===c;)l+=c;o[o.length]={t:c,v:l},h=c;break;case"*":++f,(" "==e.charAt(f)||"*"==e.charAt(f))&&++f;break;case"(":case")":o[o.length]={t:1===n?"t":c,v:c},++f;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=c;f<e.length&&"0123456789".indexOf(e.charAt(++f))>-1;)l+=e.charAt(f);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:c,v:c},++f;break;case"$":o[o.length]={t:"t",v:"$"},++f;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c))throw Error("unrecognized character "+c+" in "+e);o[o.length]={t:"t",v:c},++f}var g,v=0,T=0;for(f=o.length-1,h="t";f>=0;--f)switch(o[f].t){case"h":case"H":o[f].t=u,h="h",v<1&&(v=1);break;case"s":(g=o[f].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[f].t;break;case"m":"s"===h&&(o[f].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[f].v.match(/[Hh]/)&&(v=1),v<2&&o[f].v.match(/[Mm]/)&&(v=2),v<3&&o[f].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M),a.M>=60&&(a.M=0,++a.H);break;case 2:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M)}var E,b="";for(f=0;f<o.length;++f)switch(o[f].t){case"t":case"T":case" ":case"D":break;case"X":o[f].v="",o[f].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[f].v=function(e,t,r,n){var a,s="",i=0,o=0,l=r.y,f=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:a=l%100,f=2;break;default:a=l%1e4,f=4}break;case 109:switch(t.length){case 1:case 2:a=r.m,f=t.length;break;case 3:return X[r.m-1][1];case 5:return X[r.m-1][0];default:return X[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,f=t.length;break;case 3:return K[r.q][0];default:return K[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,f=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,f=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,f=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;if(0===r.u&&("s"==t||"ss"==t))return G(r.S,t.length);if((i=Math.round((o=n>=2?3===n?1e3:100:1===n?10:1)*(r.S+r.u)))>=60*o&&(i=0),"s"===t)return 0===i?"0":""+i/o;if(s=G(i,2+n),"ss"===t)return s.substr(0,2);return"."+s.substr(2,t.length-1);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=(24*r.D+r.H)*60+r.M;break;case"[s]":case"[ss]":a=((24*r.D+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}f=3===t.length?1:2;break;case 101:a=l,f=1}return f>0?G(a,f):""}(o[f].t.charCodeAt(0),o[f].v,a,T),o[f].t="t";break;case"n":case"?":for(E=f+1;null!=o[E]&&("?"===(c=o[E].t)||"D"===c||(" "===c||"t"===c)&&null!=o[E+1]&&("?"===o[E+1].t||"t"===o[E+1].t&&"/"===o[E+1].v)||"("===o[f].t&&(" "===c||"n"===c||")"===c)||"t"===c&&("/"===o[E].v||" "===o[E].v&&null!=o[E+1]&&"?"==o[E+1].t));)o[f].v+=o[E].v,o[E]={v:"",t:";"},++E;b+=o[f].v,f=E-1;break;case"G":o[f].t="t",o[f].v=eo(t,r)}var w,S,A="";if(b.length>0){40==b.charCodeAt(0)?(w=t<0&&45===b.charCodeAt(0)?-t:t,S=ev("n",b,w)):(S=ev("n",b,w=t<0&&n>1?-t:t),w<0&&o[0]&&"t"==o[0].t&&(S=S.substr(1),o[0].v="-"+o[0].v)),E=S.length-1;var _=o.length;for(f=0;f<o.length;++f)if(null!=o[f]&&"t"!=o[f].t&&o[f].v.indexOf(".")>-1){_=f;break}var y=o.length;if(_===o.length&&-1===S.indexOf("E")){for(f=o.length-1;f>=0;--f)null!=o[f]&&-1!=="n?".indexOf(o[f].t)&&(E>=o[f].v.length-1?(E-=o[f].v.length,o[f].v=S.substr(E+1,o[f].v.length)):E<0?o[f].v="":(o[f].v=S.substr(0,E+1),E=-1),o[f].t="t",y=f);E>=0&&y<o.length&&(o[y].v=S.substr(0,E+1)+o[y].v)}else if(_!==o.length&&-1===S.indexOf("E")){for(E=S.indexOf(".")-1,f=_;f>=0;--f)if(null!=o[f]&&-1!=="n?".indexOf(o[f].t)){for(s=o[f].v.indexOf(".")>-1&&f===_?o[f].v.indexOf(".")-1:o[f].v.length-1,A=o[f].v.substr(s+1);s>=0;--s)E>=0&&("0"===o[f].v.charAt(s)||"#"===o[f].v.charAt(s))&&(A=S.charAt(E--)+A);o[f].v=A,o[f].t="t",y=f}for(E>=0&&y<o.length&&(o[y].v=S.substr(0,E+1)+o[y].v),E=S.indexOf(".")+1,f=_;f<o.length;++f)if(null!=o[f]&&(-1!=="n?(".indexOf(o[f].t)||f===_)){for(s=o[f].v.indexOf(".")>-1&&f===_?o[f].v.indexOf(".")+1:0,A=o[f].v.substr(0,s);s<o[f].v.length;++s)E<S.length&&(A+=S.charAt(E++));o[f].v=A,o[f].t="t",y=f}}}for(f=0;f<o.length;++f)null!=o[f]&&"n?".indexOf(o[f].t)>-1&&(w=n>1&&t<0&&f>0&&"-"===o[f-1].v?-t:t,o[f].v=ev(o[f].t,o[f].v,w),o[f].t="t");var x="";for(f=0;f!==o.length;++f)null!=o[f]&&(x+=o[f].v);return x}(a[1],t,r,a[0])}function eA(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r){if(void 0==q[r]){t<0&&(t=r);continue}if(q[r]==e){t=r;break}}t<0&&(t=391)}return q[t]=e,t}function e_(e){for(var t=0;392!=t;++t)void 0!==e[t]&&eA(e[t],t)}function ey(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',q=e}var ex=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,eO=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,n=0,a="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(n=0;256!=n;++n)a[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=a[t]=r>>>8^e[255&r];var s=[];for(n=1;16!=n;++n)s[n-1]="undefined"!=typeof Int32Array?a.subarray(256*n,256*n+256):a.slice(256*n,256*n+256);return s}(t),n=r[0],a=r[1],s=r[2],i=r[3],o=r[4],l=r[5],f=r[6],c=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var n=-1^r,a=0,s=e.length;a<s;)n=n>>>8^t[(n^e.charCodeAt(a++))&255];return~n},e.buf=function(e,r){for(var T=-1^r,E=e.length-15,b=0;b<E;)T=v[e[b++]^255&T]^g[e[b++]^T>>8&255]^m[e[b++]^T>>16&255]^p[e[b++]^T>>>24]^d[e[b++]]^u[e[b++]]^h[e[b++]]^c[e[b++]]^f[e[b++]]^l[e[b++]]^o[e[b++]]^i[e[b++]]^s[e[b++]]^a[e[b++]]^n[e[b++]]^t[e[b++]];for(E+=15;b<E;)T=T>>>8^t[(T^e[b++])&255];return~T},e.str=function(e,r){for(var n=-1^r,a=0,s=e.length,i=0,o=0;a<s;)(i=e.charCodeAt(a++))<128?n=n>>>8^t[(n^i)&255]:i<2048?n=(n=n>>>8^t[(n^(192|i>>6&31))&255])>>>8^t[(n^(128|63&i))&255]:i>=55296&&i<57344?(i=(1023&i)+64,o=1023&e.charCodeAt(a++),n=(n=(n=(n=n>>>8^t[(n^(240|i>>8&7))&255])>>>8^t[(n^(128|i>>2&63))&255])>>>8^t[(n^(128|o>>6&15|(3&i)<<4))&255])>>>8^t[(n^(128|63&o))&255]):n=(n=(n=n>>>8^t[(n^(224|i>>12&15))&255])>>>8^t[(n^(128|i>>6&63))&255])>>>8^t[(n^(128|63&i))&255];return~n},e}(),eC=function(){var e,t,r={};function n(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:n(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return -1===t?e:e.slice(t+1)}function s(e){tZ(e,0);for(var t={},r=0;e.l<=e.length-4;){var n=e.read_shift(2),a=e.read_shift(2),s=e.l+a,i={};21589===n&&(1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),a>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,t[n]=i}return t}function i(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return ea(e,t);if((32|e[0])==109&&(32|e[1])==105)return function(e,t){if("mime-version:"!=T(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var r=t&&t.root||"",n=(I&&Buffer.isBuffer(e)?e.toString("binary"):T(e)).split("\r\n"),a=0,s="";for(a=0;a<n.length;++a)if(s=n[a],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(n[1]||"").match(/boundary="(.*?)"/);if(!i)throw Error("MAD cannot find boundary");var o="--"+(i[1]||""),l={FileIndex:[],FullPaths:[]};f(l);var c,h=0;for(a=0;a<n.length;++a){var u=n[a];(u===o||u===o+"--")&&(h++&&function(e,t,r){for(var n,a="",s="",i="",o=0;o<10;++o){var l=t[o];if(!l||l.match(/^\s*$/))break;var f=l.match(/^(.*?):\s*([^\s].*)$/);if(f)switch(f[1].toLowerCase()){case"content-location":a=f[2].trim();break;case"content-type":i=f[2].trim();break;case"content-transfer-encoding":s=f[2].trim()}}switch(++o,s.toLowerCase()){case"base64":n=L(N(t.slice(o).join("")));break;case"quoted-printable":n=function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r];r<=e.length&&"="==n.charAt(n.length-1);)n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var a=0;a<t.length;++a)t[a]=t[a].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return L(t.join("\r\n"))}(t.slice(o));break;default:throw Error("Unsupported Content-Transfer-Encoding "+s)}var c=ei(e,a.slice(r.length),n,{unsafe:!0});i&&(c.ctype=i)}(l,n.slice(c,a),r),c=a)}return l}(e,t);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var r=3,n=512,a=0,s=0,i=0,o=0,c=0,h=[],m=e.slice(0,512);tZ(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(p,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:n=512;break;case 4:n=4096;break;case 0:if(0==g[1])return ea(e,t);default:throw Error("Major Version: Expected 3 or 4 saw "+r)}512!==n&&tZ(m=e.slice(0,n),28);var v=e.slice(0,n);(function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw Error("Sector Shift: Expected 12 saw "+r);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")})(m,r);var E=m.read_shift(4,"i");if(3===r&&0!==E)throw Error("# Directory Sectors: Expected 0 saw "+E);m.l+=4,i=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),o=m.read_shift(4,"i"),a=m.read_shift(4,"i"),c=m.read_shift(4,"i"),s=m.read_shift(4,"i");for(var b=-1,w=0;w<109&&!((b=m.read_shift(4,"i"))<0);++w)h[w]=b;var S=function(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}(e,n);(function e(t,r,n,a,s){var i=d;if(t===d){if(0!==r)throw Error("DIFAT chain shorter than expected")}else if(-1!==t){var o=n[t],l=(a>>>2)-1;if(!o)return;for(var f=0;f<l&&(i=tz(o,4*f))!==d;++f)s.push(i);e(tz(o,a-4),r-1,n,a,s)}})(c,s,S,n,h);var A=function(e,t,r,n){var a=e.length,s=[],i=[],o=[],l=[],f=n-1,c=0,h=0,u=0,d=0;for(c=0;c<a;++c)if(o=[],(u=c+t)>=a&&(u-=a),!i[u]){l=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,l.push(e[h]);var m=r[Math.floor(4*h/n)];if(n<4+(d=4*h&f))throw Error("FAT boundary crossed: "+h+" 4 "+n);if(!e[m]||p[h=tz(e[m],d)])break}s[u]={nodes:o,data:tw([l])}}return s}(S,i,h,n);A[i].name="!Directory",a>0&&o!==d&&(A[o].name="!MiniFAT"),A[h[0]].name="!FAT",A.fat_addrs=h,A.ssz=n;var _=[],y=[],x=[];(function(e,t,r,n,a,s,i,o){for(var f,c=0,h=n.length?2:0,p=t[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);tZ(v,64),g=v.read_shift(2),f=tA(v,0,g-h),n.push(f);var T={name:f,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.ct=l(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.mt=l(v,v.l-8)),T.start=v.read_shift(4,"i"),T.size=v.read_shift(4,"i"),T.size<0&&T.start<0&&(T.size=T.type=0,T.start=d,T.name=""),5===T.type?(c=T.start,a>0&&c!==d&&(t[c].name="!StreamData")):T.size>=4096?(T.storage="fat",void 0===t[T.start]&&(t[T.start]=function(e,t,r,n,a){var s=[],i=[];a||(a=[]);var o=n-1,l=0,f=0;for(l=t;l>=0;){a[l]=!0,s[s.length]=l,i.push(e[l]);var c=r[Math.floor(4*l/n)];if(n<4+(f=4*l&o))throw Error("FAT boundary crossed: "+l+" 4 "+n);if(!e[c])break;l=tz(e[c],f)}return{nodes:s,data:tw([i])}}(r,T.start,t.fat_addrs,t.ssz)),t[T.start].name=T.name,T.content=t[T.start].data.slice(0,T.size)):(T.storage="minifat",T.size<0?T.size=0:c!==d&&T.start!==d&&t[c]&&(T.content=function(e,t,r){for(var n=e.start,a=e.size,s=[],i=n;r&&a>0&&i>=0;)s.push(t.slice(i*u,i*u+u)),a-=u,i=tz(r,4*i);return 0===s.length?t1(0):U(s).slice(0,e.size)}(T,t[c].data,(t[o]||{}).data))),T.content&&tZ(T.content,0),s[f]=T,i.push(T)}})(i,A,S,_,a,{},y,o),function(e,t,r){for(var n=0,a=0,s=0,i=0,o=0,l=r.length,f=[],c=[];n<l;++n)f[n]=c[n]=n,t[n]=r[n];for(;o<c.length;++o)a=e[n=c[o]].L,s=e[n].R,i=e[n].C,f[n]===n&&(-1!==a&&f[a]!==a&&(f[n]=f[a]),-1!==s&&f[s]!==s&&(f[n]=f[s])),-1!==i&&(f[i]=n),-1!==a&&n!=f[n]&&(f[a]=f[n],c.lastIndexOf(a)<o&&c.push(a)),-1!==s&&n!=f[n]&&(f[s]=f[n],c.lastIndexOf(s)<o&&c.push(s));for(n=1;n<l;++n)f[n]===n&&(-1!==s&&f[s]!==s?f[n]=f[s]:-1!==a&&f[a]!==a&&(f[n]=f[a]));for(n=1;n<l;++n)if(0!==e[n].type){if((o=n)!=f[o])do o=f[o],t[n]=t[o]+"/"+t[n];while(0!==o&&-1!==f[o]&&o!=f[o]);f[n]=-1}for(t[0]+="/",n=1;n<l;++n)2!==e[n].type&&(t[n]+="/")}(y,x,_),_.shift();var O={FileIndex:y,FullPaths:x};return t&&t.raw&&(O.raw={header:v,sectors:S}),O}function l(e,t){return new Date((tV(e,t+4)/1e7*4294967296+tV(e,t)/1e7-11644473600)*1e3)}function f(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(!eC.find(e,"/"+t)){var r=t1(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),c(e)}}(e)}function c(e,t){f(e);for(var r=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(r=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(r=!0);break;default:r=!0}}if(r||t){var l=new Date(1987,1,19),c=0,h=Object.create?Object.create(null):{},u=[];for(i=0;i<e.FullPaths.length;++i)h[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&u.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<u.length;++i){var d=n(u[i][0]);(s=h[d])||(u.push([d,{name:a(d).replace("/",""),type:1,clsid:g,ct:l,mt:l,content:null}]),h[d]=!0)}for(u.sort(function(e,t){return function(e,t){for(var r=e.split("/"),n=t.split("/"),a=0,s=0,i=Math.min(r.length,n.length);a<i;++a){if(s=r[a].length-n[a].length)return s;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],i=0;i<u.length;++i)e.FullPaths[i]=u[i][0],e.FileIndex[i]=u[i][1];for(i=0;i<u.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||g,0===i)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(c=i+1;c<u.length&&n(e.FullPaths[c])!=m;++c);for(p.C=c>=u.length?-1:c,c=i+1;c<u.length&&n(e.FullPaths[c])!=n(m);++c);p.R=c>=u.length?-1:c,p.type=1}else n(e.FullPaths[i+1]||"")==n(m)&&(p.R=i+1),p.type=2}}}function h(e,r){var n=r||{};if("mad"==n.fileType)return function(e,t){for(var r=t||{},n=r.boundary||"SheetJS",a=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(n="------="+n).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],l=1;l<e.FullPaths.length;++l)if(i=e.FullPaths[l].slice(s.length),(o=e.FileIndex[l]).size&&o.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var f=o.content,c=I&&Buffer.isBuffer(f)?f.toString("binary"):T(f),h=0,u=Math.min(1024,c.length),d=0,p=0;p<=u;++p)(d=c.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;a.push(n),a.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),a.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),a.push("Content-Type: "+function(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&es[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/))&&es[n[1]]?es[n[1]]:"application/octet-stream"}(o,i)),a.push(""),a.push(m?function(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)});"\n"==(t=t.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(t="=0D"+t.slice(1)),t=t.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var r=[],n=t.split("\r\n"),a=0;a<n.length;++a){var s=n[a];if(0==s.length){r.push("");continue}for(var i=0;i<s.length;){var o=76,l=s.slice(i,i+o);"="==l.charAt(o-1)?o--:"="==l.charAt(o-2)?o-=2:"="==l.charAt(o-3)&&(o-=3),l=s.slice(i,i+o),(i+=o)<s.length&&(l+="="),r.push(l)}}return r.join("\r\n")}(c):function(e){for(var t=R(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}(c))}return a.push(n+"--\r\n"),a.join("\r\n")}(e,n);if(c(e),"zip"===n.fileType)return function(e,r){var n=[],a=[],s=t1(1),i=(r||{}).compression?8:0,o=0,l=0,f=0,c=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(o=1;o<e.FullPaths.length;++o)if(u=e.FullPaths[o].slice(h.length),(d=e.FileIndex[o]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=f,T=t1(u.length);for(l=0;l<u.length;++l)T.write_shift(1,127&u.charCodeAt(l));T=T.slice(0,T.l),p[c]=eO.buf(d.content,0);var E=d.content;8==i&&(g=E,E=t?t.deflateRawSync(g):q(g)),(s=t1(30)).write_shift(4,67324752),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),d.mt?function(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=(n=n<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,n)}(s,d.mt):s.write_shift(4,0),s.write_shift(-4,p[c]),s.write_shift(4,E.length),s.write_shift(4,d.content.length),s.write_shift(2,T.length),s.write_shift(2,0),f+=s.length,n.push(s),f+=T.length,n.push(T),f+=E.length,n.push(E),(s=t1(46)).write_shift(4,33639248),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(-4,p[c]),s.write_shift(4,E.length),s.write_shift(4,d.content.length),s.write_shift(2,T.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,v),m+=s.l,a.push(s),m+=T.length,a.push(T),++c}return(s=t1(22)).write_shift(4,101010256),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,c),s.write_shift(2,c),s.write_shift(4,m),s.write_shift(4,f),s.write_shift(2,0),U([U(n),U(a),s])}(e,n);var a=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];if(a.content){var s=a.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+7>>3,l=t+127>>7,f=o+r+i+l,c=f+127>>7,h=c<=109?0:Math.ceil((c-109)/127);f+c+h+127>>7>c;)h=++c<=109?0:Math.ceil((c-109)/127);var u=[1,h,c,l,i,r,t,0];return e.FileIndex[0].size=t<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),s=t1(a[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,m[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,a[2]),s.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:d),s.write_shift(4,a[3]),s.write_shift(-4,a[1]?a[0]-1:d),s.write_shift(4,a[1]),i=0;i<109;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);if(a[1])for(o=0;o<a[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);s.write_shift(-4,o===a[1]-1?d:o+1)}var l=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,d))};for(o=(i=0)+a[1];i<o;++i)s.write_shift(-4,v.DIFSECT);for(o+=a[2];i<o;++i)s.write_shift(-4,v.FATSECT);l(a[3]),l(a[4]);for(var f=0,h=0,u=e.FileIndex[0];f<e.FileIndex.length;++f)(u=e.FileIndex[f]).content&&((h=u.content.length)<4096||(u.start=o,l(h+511>>9)));for(l(a[6]+7>>3);511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(f=0,o=i=0;f<e.FileIndex.length;++f)(u=e.FileIndex[f]).content&&(h=u.content.length)&&!(h>=4096)&&(u.start=o,l(h+63>>6));for(;511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var p=e.FullPaths[i];if(!p||0===p.length){for(f=0;f<17;++f)s.write_shift(4,0);for(f=0;f<3;++f)s.write_shift(4,-1);for(f=0;f<12;++f)s.write_shift(4,0);continue}u=e.FileIndex[i],0===i&&(u.start=u.size?u.start-1:d);var g=0===i&&n.root||u.name;if(h=2*(g.length+1),s.write_shift(64,g,"utf16le"),s.write_shift(2,h),s.write_shift(1,u.type),s.write_shift(1,u.color),s.write_shift(-4,u.L),s.write_shift(-4,u.R),s.write_shift(-4,u.C),u.clsid)s.write_shift(16,u.clsid,"hex");else for(f=0;f<4;++f)s.write_shift(4,0);s.write_shift(4,u.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,u.start),s.write_shift(4,u.size),s.write_shift(4,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>=4096){if(s.l=u.start+1<<9,I&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+511&-512;else{for(f=0;f<u.size;++f)s.write_shift(1,u.content[f]);for(;511&f;++f)s.write_shift(1,0)}}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>0&&u.size<4096){if(I&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+63&-64;else{for(f=0;f<u.size;++f)s.write_shift(1,u.content[f]);for(;63&f;++f)s.write_shift(1,0)}}if(I)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}r.version="1.2.1";var u=64,d=-2,p="d0cf11e0a1b11ae1",m=[208,207,17,224,161,177,26,225],g="00000000000000000000000000000000",v={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:p,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:g,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function T(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var E=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],b=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],w=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],S="undefined"!=typeof Uint8Array,A=S?new Uint8Array(256):[],_=0;_<256;++_)A[_]=function(e){var t=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(t>>16|t>>8|t)&255}(_);function y(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function x(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function O(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function C(e,t,r){var n=7&t,a=t>>>3,s=(1<<r)-1,i=e[a]>>>n;return r<8-n?i&s:(i|=e[a+1]<<8-n,r<16-n)?i&s:(i|=e[a+2]<<16-n,r<24-n)?i&s:(i|=e[a+3]<<24-n)&s}function M(e,t,r){var n=7&t,a=t>>>3;return n<=5?e[a]|=(7&r)<<n:(e[a]|=r<<n&255,e[a+1]=(7&r)>>8-n),t+3}function F(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=r,t+8}function H(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=255&r,e[n+2]=r>>>8,t+16}function G(e,t){var r=e.length,n=2*r>t?2*r:t+5,a=0;if(r>=t)return e;if(I){var s=D(n);if(e.copy)e.copy(s);else for(;a<e.length;++a)s[a]=e[a];return s}if(S){var i=new Uint8Array(n);if(i.set)i.set(e);else for(;a<r;++a)i[a]=e[a];return i}return e.length=n,e}function j(e){for(var t=Array(e),r=0;r<e;++r)t[r]=0;return t}function V(e,t,r){var n=1,a=0,s=0,i=0,o=0,l=e.length,f=S?new Uint16Array(32):j(32);for(s=0;s<32;++s)f[s]=0;for(s=l;s<r;++s)e[s]=0;l=e.length;var c=S?new Uint16Array(l):j(l);for(s=0;s<l;++s)f[a=e[s]]++,n<a&&(n=a),c[s]=0;for(s=1,f[0]=0;s<=n;++s)f[s+16]=o=o+f[s-1]<<1;for(s=0;s<l;++s)0!=(o=e[s])&&(c[s]=f[o+16]++);var h=0;for(s=0;s<l;++s)if(0!=(h=e[s]))for(o=function(e,t){var r=A[255&e];return t<=8?r>>>8-t:(r=r<<8|A[e>>8&255],t<=16)?r>>>16-t:(r=r<<8|A[e>>16&255])>>>24-t}(c[s],n)>>n-h,i=(1<<n+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return n}var z=S?new Uint16Array(512):j(512),Y=S?new Uint16Array(32):j(32);if(!S){for(var K=0;K<512;++K)z[K]=0;for(K=0;K<32;++K)Y[K]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);V(e,Y,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);V(r,z,288)}();var X=function(){for(var e=S?new Uint8Array(32768):[],t=0,r=0;t<w.length-1;++t)for(;r<w[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=S?new Uint8Array(259):[];for(t=0,r=0;t<b.length-1;++t)for(;r<b[t+1];++r)n[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var n=Math.min(65535,e.length-r),a=r+n==e.length;for(t.write_shift(1,+a),t.write_shift(2,n),t.write_shift(2,65535&~n);n-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var a=0,s=0,i=S?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(a=M(r,a,+!(s+o!=t.length)))&&(a+=8-(7&a)),r.l=a/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];a=8*r.l;continue}a=M(r,a,+!(s+o!=t.length)+2);for(var l=0;o-- >0;){var f,c,h=t[s],u=-1,d=0;if((u=i[l=(l<<5^h)&32767])&&((u|=-32768&s)>s&&(u-=32768),u<s))for(;t[u+d]==t[s+d]&&d<250;)++d;if(d>2){(h=n[d])<=22?a=F(r,a,A[h+1]>>1)-1:(F(r,a,3),F(r,a+=5,A[h-23]>>5),a+=3);var p=h<8?0:h-4>>2;p>0&&(H(r,a,d-b[h]),a+=p),a=F(r,a,A[h=e[s-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(H(r,a,s-u-w[h]),a+=m);for(var g=0;g<d;++g)i[l]=32767&s,l=(l<<5^t[s])&32767,++s;o-=d-1}else h<=143?h+=48:(c=(1&(c=1))<<(7&(f=a)),r[f>>>3]|=c,a=f+1),a=F(r,a,A[h]),i[l]=32767&s,++s}a=F(r,a,0)-1}return r.l=(a+7)/8|0,r.l}(t,r)}}();function q(e){var t=t1(50+Math.floor(1.1*e.length)),r=X(e,t);return t.slice(0,r)}var J=S?new Uint16Array(32768):j(32768),Z=S?new Uint16Array(32768):j(32768),Q=S?new Uint16Array(128):j(128),ee=1,et=1;function er(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[P(t),2];for(var r=0,n=0,a=D(t||262144),s=0,i=a.length>>>0,o=0,l=0;(1&n)==0;){if(n=y(e,r),r+=3,n>>>1==0){7&r&&(r+=8-(7&r));var f=e[r>>>3]|e[(r>>>3)+1]<<8;if(r+=32,f>0)for(!t&&i<s+f&&(i=(a=G(a,s+f)).length);f-- >0;)a[s++]=e[r>>>3],r+=8;continue}for(n>>1==1?(o=9,l=5):(r=function(e,t){var r,n,a,s=x(e,t)+257,i=x(e,t+=5)+1;t+=5;var o=(n=7&(r=t),((e[a=r>>>3]|(n<=4?0:e[a+1]<<8))>>>n&15)+4);t+=4;for(var l=0,f=S?new Uint8Array(19):j(19),c=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=S?new Uint8Array(8):j(8),d=S?new Uint8Array(8):j(8),p=f.length,m=0;m<o;++m)f[E[m]]=l=y(e,t),h<l&&(h=l),u[l]++,t+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=f[m])&&(c[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=f[m])){g=A[c[m]]>>8-v;for(var T=(1<<7-v)-1;T>=0;--T)Q[g|T<<v]=7&v|m<<3}var b=[];for(h=1;b.length<s+i;)switch(g=Q[O(e,t)],t+=7&g,g>>>=3){case 16:for(l=3+function(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}(e,t),t+=2,g=b[b.length-1];l-- >0;)b.push(g);break;case 17:for(l=3+y(e,t),t+=3;l-- >0;)b.push(0);break;case 18:for(l=11+O(e,t),t+=7;l-- >0;)b.push(0);break;default:b.push(g),h<g&&(h=g)}var w=b.slice(0,s),_=b.slice(s);for(m=s;m<286;++m)w[m]=0;for(m=i;m<30;++m)_[m]=0;return ee=V(w,J,286),et=V(_,Z,30),t}(e,r),o=ee,l=et);;){!t&&i<s+32767&&(i=(a=G(a,s+32767)).length);var c=C(e,r,o),h=n>>>1==1?z[c]:J[c];if(r+=15&h,((h>>>=4)>>>8&255)==0)a[s++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=s+b[h];u>0&&(d+=C(e,r,u),r+=u),c=C(e,r,l),r+=15&(h=n>>>1==1?Y[c]:Z[c]);var p=(h>>>=4)<4?0:h-2>>1,m=w[h];for(p>0&&(m+=C(e,r,p),r+=p),!t&&i<d&&(i=(a=G(a,d+100)).length);s<d;)a[s]=a[s-m],++s}}}return t?[a,r+7>>>3]:[a.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function en(e,t){if(e)"undefined"!=typeof console&&console.error(t);else throw Error(t)}function ea(e,r){tZ(e,0);var n={FileIndex:[],FullPaths:[]};f(n,{root:r.root});for(var a=e.length-4;(80!=e[a]||75!=e[a+1]||5!=e[a+2]||6!=e[a+3])&&a>=0;)--a;e.l=a+4,e.l+=4;var i=e.read_shift(2);e.l+=6;var o=e.read_shift(4);for(a=0,e.l=o;a<i;++a){e.l+=20;var l=e.read_shift(4),c=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=s(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,r,n,a,i){e.l+=2;var o,l,f,c,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(o=65535&e.read_shift(2),l=65535&e.read_shift(2),f=new Date,c=31&l,h=15&(l>>>=5),l>>>=4,f.setMilliseconds(0),f.setFullYear(l+1980),f.setMonth(h-1),f.setDate(c),u=31&o,d=63&(o>>>=5),o>>>=6,f.setHours(o),f.setMinutes(d),f.setSeconds(u<<1),f);if(8257&p)throw Error("Unsupported ZIP encryption");e.read_shift(4);for(var v=e.read_shift(4),T=e.read_shift(4),E=e.read_shift(2),b=e.read_shift(2),w="",S=0;S<E;++S)w+=String.fromCharCode(e[e.l++]);if(b){var A=s(e.slice(e.l,e.l+b));(A[21589]||{}).mt&&(g=A[21589].mt),((i||{})[21589]||{}).mt&&(g=i[21589].mt)}e.l+=b;var _=e.slice(e.l,e.l+v);switch(m){case 8:_=function(e,r){if(!t)return er(e,r);var n=new t.InflateRaw,a=n._processChunk(e.slice(e.l),n._finishFlushFlag);return e.l+=n.bytesRead,a}(e,T);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var y=!1;8&p&&(134695760==e.read_shift(4)&&(e.read_shift(4),y=!0),v=e.read_shift(4),T=e.read_shift(4)),v!=r&&en(y,"Bad compressed size: "+r+" != "+v),T!=n&&en(y,"Bad uncompressed size: "+n+" != "+T),ei(a,w,_,{unsafe:!0,mt:g})}(e,l,c,n,m),e.l=g}return n}var es={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ei(e,t,r,n){var s=n&&n.unsafe;s||f(e);var i=!s&&eC.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:a(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||eC.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return r.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),n=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===a?r.indexOf(s):n.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(W);for(s=s.replace(B,""),o&&(s=s.replace(W,"!")),i=0;i<r.length;++i)if((o?r[i].replace(W,"!"):r[i]).replace(B,"")==s||(o?n[i].replace(W,"!"):n[i]).replace(B,"")==s)return e.FileIndex[i];return null},r.read=function(t,r){var n=r&&r.type;switch(!n&&I&&Buffer.isBuffer(t)&&(n="buffer"),n||"base64"){case"file":return i(),o(e.readFileSync(t),r);case"base64":return o(L(N(t)),r);case"binary":return o(L(t),r)}return o(t,r)},r.parse=o,r.write=function(t,r){var n=h(t,r);switch(r&&r.type||"buffer"){case"file":i(),e.writeFileSync(r.filename,n);break;case"binary":return"string"==typeof n?n:T(n);case"base64":return R("string"==typeof n?n:T(n));case"buffer":if(I)return Buffer.isBuffer(n)?n:k(n);case"array":return"string"==typeof n?L(n):n}return n},r.writeFile=function(t,r,n){i();var a=h(t,n);e.writeFileSync(r,a)},r.utils={cfb_new:function(e){var t={};return f(t,e),t},cfb_add:ei,cfb_del:function(e,t){f(e);var r=eC.find(e,t);if(r){for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0}return!1},cfb_mov:function(e,t,r){f(e);var n=eC.find(e,t);if(n){for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==n)return e.FileIndex[s].name=a(r),e.FullPaths[s]=r,!0}return!1},cfb_gc:function(e){c(e,!0)},ReadShift:t$,CheckField:tJ,prep_blob:tZ,bconcat:U,use_zlib:function(e){try{var r=new e.InflateRaw;if(r._processChunk(new Uint8Array([3,0]),r._finishFlushFlag),r.bytesRead)t=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:q,_inflateRaw:er,consts:v},r}();function eR(e,t,r){if(void 0!==n&&n.writeFileSync)return r?n.writeFileSync(e,t,r):n.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=M(t);break;default:throw Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a="utf8"==r?to(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(a,e);if("undefined"!=typeof Blob){var s=new Blob(["string"==typeof a?M(a):Array.isArray(a)?function(e){if("undefined"==typeof Uint8Array)throw Error("Unsupported");return new Uint8Array(e)}(a):a],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(s,e);if("undefined"!=typeof saveAs)return saveAs(s,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(s);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var o=document.createElement("a");if(null!=o.download)return o.download=e,o.href=i,document.body.appendChild(o),o.click(),document.body.removeChild(o),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(i)},6e4),i}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var l=File(e);return l.open("w"),l.encoding="binary",Array.isArray(t)&&(t=F(t)),l.write(t),l.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("cannot save file "+e)}function eN(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function eI(e,t){for(var r=[],n=eN(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function ek(e){for(var t=[],r=eN(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function eP(e){for(var t=[],r=eN(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}var eD=new Date(1899,11,30,0,0,0);function eL(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(eD.getTime()+(e.getTimezoneOffset()-eD.getTimezoneOffset())*6e4))/864e5}var eM=new Date,eF=eD.getTime()+(eM.getTimezoneOffset()-eD.getTimezoneOffset())*6e4,eU=eM.getTimezoneOffset();function eB(e){var t=new Date;return t.setTime(864e5*e+eF),t.getTimezoneOffset()!==eU&&t.setTime(t.getTime()+(t.getTimezoneOffset()-eU)*6e4),t}var eW=new Date("2017-02-19T19:06:09.000Z"),eH=isNaN(eW.getFullYear())?new Date("2/19/17"):eW,eG=2017==eH.getFullYear();function ej(e,t){var r=new Date(e);if(eG)return t>0?r.setTime(r.getTime()+6e4*r.getTimezoneOffset()):t<0&&r.setTime(r.getTime()-6e4*r.getTimezoneOffset()),r;if(e instanceof Date)return e;if(1917==eH.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-6e4*s.getTimezoneOffset())),s}function eV(e,t){if(I&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return to(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return to(_(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return to(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return to(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"\x80","‚":"\x82",ƒ:"\x83","„":"\x84","…":"\x85","†":"\x86","‡":"\x87",ˆ:"\x88","‰":"\x89",Š:"\x8a","‹":"\x8b",Œ:"\x8c",Ž:"\x8e","‘":"\x91","’":"\x92","“":"\x93","”":"\x94","•":"\x95","–":"\x96","—":"\x97","˜":"\x98","™":"\x99",š:"\x9a","›":"\x9b",œ:"\x9c",ž:"\x9e",Ÿ:"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return r[e]||e})}catch(e){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function ez(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=ez(e[r]));return t}function e$(e,t){for(var r="";r.length<t;)r+=e;return r}function eY(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(n))&&isNaN(t=Number(n=n.replace(/[(](.*)[)]/,function(e,t){return r=-r,t})))?t:t/r}var eK=["january","february","march","april","may","june","july","august","september","october","november","december"];function eX(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==eK.indexOf(i))return r}else if(i.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||s>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function eq(e){return e?e.content&&e.type?eV(e.content,!0):e.data?y(e.data):e.asNodeBuffer&&I?y(e.asNodeBuffer().toString("binary")):e.asBinary?y(e.asBinary()):e._data&&e._data.getContent?y(eV(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function eJ(e,t){var r=function(e,t){for(var r=e.FullPaths||eN(e.files),n=t.toLowerCase().replace(/[\/]/g,"\\"),a=n.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(n==i||a==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}(e,t);if(null==r)throw Error("Cannot find file "+t+" in zip");return r}function eZ(e,t,r){if(e.FullPaths){if("string"==typeof r){var n;return n=I?k(r):function(e){for(var t=[],r=0,n=e.length+250,a=P(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)a[r++]=i;else if(i<2048)a[r++]=192|i>>6&31,a[r++]=128|63&i;else if(i>=55296&&i<57344){i=(1023&i)+64;var o=1023&e.charCodeAt(++s);a[r++]=240|i>>8&7,a[r++]=128|i>>2&63,a[r++]=128|o>>6&15|(3&i)<<4,a[r++]=128|63&o}else a[r++]=224|i>>12&15,a[r++]=128|i>>6&63,a[r++]=128|63&i;r>n&&(t.push(a.slice(0,r)),r=0,a=P(65535),n=65530)}return t.push(a.slice(0,r)),U(t)}(r),eC.utils.cfb_add(e,t,n)}eC.utils.cfb_add(e,t,r)}else e.file(t,r)}function eQ(){return eC.utils.cfb_new()}var e1='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',e0=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,e2=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,e4=e1.match(e2)?e2:/<[^>]*>/g;function e3(e,t,r){for(var n={},a=0,s=0;a!==e.length&&32!==(s=e.charCodeAt(a))&&10!==s&&13!==s;++a);if(t||(n[0]=e.slice(0,a)),a===e.length)return n;var i=e.match(e0),o=0,l="",f=0,c="",h="",u=1;if(i)for(f=0;f!=i.length;++f){for(s=0,h=i[f];s!=h.length&&61!==h.charCodeAt(s);++s);for(c=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(o=0,u=34==(a=h.charCodeAt(s+1))||39==a?1:0,l=h.slice(s+1+u,h.length-u);o!=c.length&&58!==c.charCodeAt(o);++o);if(o===c.length)c.indexOf("_")>0&&(c=c.slice(0,c.indexOf("_"))),n[c]=l,r||(n[c.toLowerCase()]=l);else{var d=(5===o&&"xmlns"===c.slice(0,5)?"xmlns":"")+c.slice(o+1);if(n[d]&&"ext"==c.slice(o-3,o))continue;n[d]=l,r||(n[d.toLowerCase()]=l)}}return n}var e5=ek({"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"}),e6=/[&<>'"]/g,e8=/[\u0000-\u0008\u000b-\u001f]/g;function e7(e){return(e+"").replace(e6,function(e){return e5[e]}).replace(e8,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function e9(e){return e7(e).replace(/ /g,"_x0020_")}var te=/[\u0000-\u001f]/g;function tt(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function tr(e){for(var t="",r=0,n=0,a=0,s=0,i=0;r<e.length;){if((n=e.charCodeAt(r++))<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){t+=String.fromCharCode((31&n)<<6|63&a);continue}if(s=e.charCodeAt(r++),n<240){t+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&s);continue}t+=String.fromCharCode(55296+((i=((7&n)<<18|(63&a)<<12|(63&s)<<6|63&e.charCodeAt(r++))-65536)>>>10&1023))+String.fromCharCode(56320+(1023&i))}return t}function tn(e){var t,r,n,a=P(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=(31&n)*64+(63&e.charCodeAt(r+1)),s=2):n<240?(t=(15&n)*4096+(63&e.charCodeAt(r+1))*64+(63&e.charCodeAt(r+2)),s=3):(s=4,o=55296+((t=(7&n)*262144+(63&e.charCodeAt(r+1))*4096+(63&e.charCodeAt(r+2))*64+(63&e.charCodeAt(r+3))-65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(a[i++]=255&o,a[i++]=o>>>8,o=0),a[i++]=t%256,a[i++]=t>>>8;return a.slice(0,i).toString("ucs2")}function ta(e){return k(e,"binary").toString("utf8")}var ts="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",ti=I&&(ta(ts)==tr(ts)&&ta||tn(ts)==tr(ts)&&tn)||tr,to=I?function(e){return k(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,t.push(String.fromCharCode(240+((a=e.charCodeAt(r++)-56320+(n<<10))>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},tl=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}(),tf=/<\/?(?:vt:)?variant>/g,tc=/<(?:vt:)([^>]*)>([\s\S]*)</,th=/(^\s|\s$|\n)/;function tu(e,t){return"<"+e+(t.match(th)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function td(e){return eN(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function tp(e,t,r){return"<"+e+(null!=r?td(r):"")+(null!=t?(t.match(th)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function tm(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function tg(e){if(I&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return ti(F(function e(t){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(t instanceof ArrayBuffer)return e(new Uint8Array(t));for(var r=Array(t.length),n=0;n<t.length;++n)r[n]=t[n];return r}(e)));throw Error("Bad input format: expected Buffer or string")}var tv=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,tT={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},tE={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},tb=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var n=0,a=e[0][r].length;n<a;n+=10240)t.push.apply(t,e[0][r].slice(n,n+10240));return t},tw=I?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:k(e)})):tb(e)}:tb,tS=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(tG(e,a)));return n.join("").replace(B,"")},tA=I?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(B,""):tS(e,t,r)}:tS,t_=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},ty=I?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):t_(e,t,r)}:t_,tx=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(tH(e,a)));return n.join("")},tO=I?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):tx(e,t,r)}:tx,tC=function(e,t){var r=tV(e,t);return r>0?tO(e,t+4,t+4+r-1):""},tR=tC,tN=function(e,t){var r=tV(e,t);return r>0?tO(e,t+4,t+4+r-1):""},tI=tN,tk=function(e,t){var r=2*tV(e,t);return r>0?tO(e,t+4,t+4+r-1):""},tP=tk,tD=function(e,t){var r=tV(e,t);return r>0?tA(e,t+4,t+4+r):""},tL=tD,tM=function(e,t){var r=tV(e,t);return r>0?tO(e,t+4,t+4+r):""},tF=tM,tU=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],s=5;s>=0;--s)a=256*a+e[t+s];return 2047==n?0==a?1/0*r:NaN:(0==n?n=-1022:(n-=1023,a+=4503599627370496),r*Math.pow(2,n-52)*a)}(e,t)},tB=tU,tW=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};I&&(tR=function(e,t){if(!Buffer.isBuffer(e))return tC(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tI=function(e,t){if(!Buffer.isBuffer(e))return tN(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},tP=function(e,t){if(!Buffer.isBuffer(e))return tk(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},tL=function(e,t){if(!Buffer.isBuffer(e))return tD(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},tF=function(e,t){if(!Buffer.isBuffer(e))return tM(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},tB=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):tU(e,t)},tW=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==a&&(tA=function(e,t,r){return a.utils.decode(1200,e.slice(t,r)).replace(B,"")},tO=function(e,t,r){return a.utils.decode(65001,e.slice(t,r))},tR=function(e,t){var r=tV(e,t);return r>0?a.utils.decode(T,e.slice(t+4,t+4+r-1)):""},tI=function(e,t){var r=tV(e,t);return r>0?a.utils.decode(v,e.slice(t+4,t+4+r-1)):""},tP=function(e,t){var r=2*tV(e,t);return r>0?a.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},tL=function(e,t){var r=tV(e,t);return r>0?a.utils.decode(1200,e.slice(t+4,t+4+r)):""},tF=function(e,t){var r=tV(e,t);return r>0?a.utils.decode(65001,e.slice(t+4,t+4+r)):""});var tH=function(e,t){return e[t]},tG=function(e,t){return 256*e[t+1]+e[t]},tj=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-((65535-r+1)*1)},tV=function(e,t){return 16777216*e[t+3]+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},tz=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]};function t$(e,t){var r,n,s,i,o,l,f="",c=[];switch(t){case"dbcs":if(l=this.l,I&&Buffer.isBuffer(this))f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)f+=String.fromCharCode(tG(this,l)),l+=2;e*=2;break;case"utf8":f=tO(this,this.l,this.l+e);break;case"utf16le":e*=2,f=tA(this,this.l,this.l+e);break;case"wstr":if(void 0===a)return t$.call(this,e,"dbcs");f=a.utils.decode(v,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=tR(this,this.l),e=4+tV(this,this.l);break;case"lpstr-cp":f=tI(this,this.l),e=4+tV(this,this.l);break;case"lpwstr":f=tP(this,this.l),e=4+2*tV(this,this.l);break;case"lpp4":e=4+tV(this,this.l),f=tL(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+tV(this,this.l),f=tF(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(s=tH(this,this.l+e++));)c.push(x(s));f=c.join("");break;case"_wstr":for(e=0,f="";0!==(s=tG(this,this.l+e));)c.push(x(s)),e+=2;e+=2,f=c.join("");break;case"dbcs-cont":for(o=0,f="",l=this.l;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return s=tH(this,l),this.l=l+1,i=t$.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),c.join("")+i;c.push(x(tG(this,l))),l+=2}f=c.join(""),e*=2;break;case"cpstr":if(void 0!==a){f=a.utils.decode(v,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(o=0,f="",l=this.l;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(l))return s=tH(this,l),this.l=l+1,i=t$.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),c.join("")+i;c.push(x(tH(this,l))),l+=1}f=c.join("");break;default:switch(e){case 1:return r=tH(this,this.l),this.l++,r;case 2:return r=("i"===t?tj:tG)(this,this.l),this.l+=2,r;case 4:case -4:if("i"===t||(128&this[this.l+3])==0)return r=(e>0?tz:function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]})(this,this.l),this.l+=4,r;return n=tV(this,this.l),this.l+=4,n;case 8:case -8:if("f"===t)return n=8==e?tB(this,this.l):tB([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:f=ty(this,this.l,e)}}return this.l+=e,f}var tY=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},tK=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},tX=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function tq(e,t,r){var n=0,s=0;if("dbcs"===r){for(s=0;s!=t.length;++s)tX(this,t.charCodeAt(s),this.l+2*s);n=2*t.length}else if("sbcs"===r){if(void 0!==a&&874==T)for(s=0;s!=t.length;++s){var i=a.utils.encode(T,t.charAt(s));this[this.l+s]=i[0]}else for(s=0,t=t.replace(/[^\x00-\x7F]/g,"_");s!=t.length;++s)this[this.l+s]=255&t.charCodeAt(s);n=t.length}else if("hex"===r){for(;s<e;++s)this[this.l++]=parseInt(t.slice(2*s,2*s+2),16)||0;return this}else if("utf16le"===r){var o=Math.min(this.l+e,this.length);for(s=0;s<Math.min(t.length,e);++s){var l=t.charCodeAt(s);this[this.l++]=255&l,this[this.l++]=l>>8}for(;this.l<o;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,tY(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,s=0,i=n?-t:t;isFinite(i)?0==i?a=s=0:(a=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-a),a<=-1023&&(!isFinite(s)||s<4503599627370496)?a=-1022:(s-=4503599627370496,a+=1023)):(a=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&a)<<4|15&s,e[r+7]=a>>4|n}(this,t,this.l);break}case 16:break;case -4:n=4,tK(this,t,this.l)}return this.l+=n,this}function tJ(e,t){var r=ty(this,this.l,e.length>>1);if(r!==e)throw Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function tZ(e,t){e.l=t,e.read_shift=t$,e.chk=tJ,e.write_shift=tq}function tQ(e,t){e.l+=t}function t1(e){var t=P(e);return tZ(t,0),t}function t0(){var e=[],t=I?256:2048,r=function(e){var t=t1(e);return tZ(t,0),t},n=r(t),a=function(){n&&(n.length>n.l&&((n=n.slice(0,n.l)).l=n.length),n.length>0&&e.push(n),n=null)},s=function(e){return n&&e<n.length-n.l?n:(a(),n=r(Math.max(e+1,t)))};return{next:s,push:function(e){a(),null==(n=e).l&&(n.l=n.length),s(t)},end:function(){return a(),U(e)},_bufs:e}}function t2(e,t,r,n){var a,s=+t;if(!isNaN(s)){n||(n=si[s].p||(r||[]).length||0),a=1+(s>=128?1:0)+1,n>=128&&++a,n>=16384&&++a,n>=2097152&&++a;var i=e.next(a);s<=127?i.write_shift(1,s):(i.write_shift(1,(127&s)+128),i.write_shift(1,s>>7));for(var o=0;4!=o;++o)if(n>=128)i.write_shift(1,(127&n)+128),n>>=7;else{i.write_shift(1,n);break}n>0&&tW(r)&&e.push(r)}}function t4(e,t,r){var n=ez(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function t3(e,t,r){var n=ez(e);return n.s=t4(n.s,t.s,r),n.e=t4(n.e,t.s,r),n}function t5(e,t){if(e.cRel&&e.c<0)for(e=ez(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=ez(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=rr(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function t6(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?t5(e.s,t.biff)+":"+t5(e.e,t.biff):(e.s.rRel?"":"$")+t7(e.s.r)+":"+(e.e.rRel?"":"$")+t7(e.e.r):(e.s.cRel?"":"$")+re(e.s.c)+":"+(e.e.cRel?"":"$")+re(e.e.c)}function t8(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function t7(e){return""+(e+1)}function t9(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function re(e){if(e<0)throw Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function rt(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function rr(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function rn(e){var t=e.indexOf(":");return -1==t?{s:rt(e),e:rt(e)}:{s:rt(e.slice(0,t)),e:rt(e.slice(t+1))}}function ra(e,t){return void 0===t||"number"==typeof t?ra(e.s,e.e):("string"!=typeof e&&(e=rr(e)),"string"!=typeof t&&(t=rr(t)),e==t?e:e+":"+t)}function rs(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,s=e.length;for(r=0;n<s&&!((a=e.charCodeAt(n)-64)<1)&&!(a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<s&&!((a=e.charCodeAt(n)-48)<0)&&!(a>9);++n)r=10*r+a;if(t.s.r=--r,n===s||10!=a)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=s&&!((a=e.charCodeAt(n)-64)<1)&&!(a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=s&&!((a=e.charCodeAt(n)-48)<0)&&!(a>9);++n)r=10*r+a;return t.e.r=--r,t}function ri(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=eS(e.z,r?eL(t):t)}catch(e){}try{return e.w=eS((e.XF||{}).numFmtId||(r?14:0),r?eL(t):t)}catch(e){return""+t}}function ro(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t)?rP[e.v]||e.v:void 0==t?ri(e,e.v):ri(e,t)}function rl(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function rf(e,t){return function(e,t,r){var n=r||{},a=n.dense,s=a?[]:{},i=0,o=0;if(s&&null!=n.origin){if("number"==typeof n.origin)i=n.origin;else{var l="string"==typeof n.origin?rt(n.origin):n.origin;i=l.r,o=l.c}s["!ref"]||(s["!ref"]="A1:A1")}var f={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var c=rs(s["!ref"]);f.s.c=c.s.c,f.s.r=c.s.r,f.e.c=Math.max(f.e.c,c.e.c),f.e.r=Math.max(f.e.r,c.e.r),-1==i&&(f.e.r=i=c.e.r+1)}for(var h=0;h!=t.length;++h)if(t[h]){if(!Array.isArray(t[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=t[h].length;++u)if(void 0!==t[h][u]){var d={v:t[h][u]},p=i+h,m=o+u;if(f.s.r>p&&(f.s.r=p),f.s.c>m&&(f.s.c=m),f.e.r<p&&(f.e.r=p),f.e.c<m&&(f.e.c=m),!t[h][u]||"object"!=typeof t[h][u]||Array.isArray(t[h][u])||t[h][u]instanceof Date){if(Array.isArray(d.v)&&(d.f=t[h][u][1],d.v=d.v[0]),null===d.v){if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else{if(!n.sheetStubs)continue;d.t="z"}}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=n.dateNF||q[14],n.cellDates?(d.t="d",d.w=eS(d.z,eL(d.v))):(d.t="n",d.v=eL(d.v),d.w=eS(d.z,d.v))):d.t="s"}else d=t[h][u];if(a)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=rr({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return f.s.c<1e7&&(s["!ref"]=ra(f)),s}(null,e,t)}function rc(e,t){return t||(t=t1(4)),t.write_shift(4,e),t}function rh(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function ru(e,t){var r=!1;return null==t&&(r=!0,t=t1(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rd(e,t){var r=e.l,n=e.read_shift(1),a=rh(e),s=[],i={t:a,h:a};if((1&n)!=0){for(var o=e.read_shift(4),l=0;l!=o;++l)s.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}function rp(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function rm(e,t){return null==t&&(t=t1(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rg(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function rv(e,t){return null==t&&(t=t1(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function rT(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function rE(e,t){var r=!1;return null==t&&(r=!0,t=t1(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function rb(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var a=0===n?tB([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):tz(t,0)>>2;return r?a/100:a}function rw(e,t){null==t&&(t=t1(4));var r=0,n=0,a=100*e;if(e==(0|e)&&e>=-536870912&&e<536870912?n=1:a==(0|a)&&a>=-536870912&&a<536870912&&(n=1,r=1),n)t.write_shift(-4,((r?a:e)<<2)+(r+2));else throw Error("unsupported RkNumber "+e)}function rS(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var rA=function(e,t){return t||(t=t1(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function r_(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function ry(e,t){return(t||t1(8)).write_shift(8,e,"f")}function rx(e,t){if(t||(t=t1(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}var rO=[80,81],rC={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},rR={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},rN={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},rI=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],rk=ez([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),rP={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rD={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},rL={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},rM={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function rF(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function rU(e,t){var r,n=function(e){for(var t=[],r=eN(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}(rL),a=[];a[a.length]=e1,a[a.length]=tp("Types",null,{xmlns:tT.CT,"xmlns:xsd":tT.xsd,"xmlns:xsi":tT.xsi}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map(function(e){return tp("Default",null,{Extension:e[0],ContentType:e[1]})}));var s=function(n){e[n]&&e[n].length>0&&(r=e[n][0],a[a.length]=tp("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:rM[n][t.bookType]||rM[n].xlsx}))},i=function(r){(e[r]||[]).forEach(function(e){a[a.length]=tp("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:rM[r][t.bookType]||rM[r].xlsx})})},o=function(t){(e[t]||[]).forEach(function(e){a[a.length]=tp("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})})};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var rB={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function rW(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function rH(e){var t=[e1,tp("Relationships",null,{xmlns:tT.RELS})];return eN(e["!id"]).forEach(function(r){t[t.length]=tp("Relationship",null,e["!id"][r])}),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function rG(e,t,r,n,a,s){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,s?a.TargetMode=s:[rB.HLINK,rB.XPATH,rB.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function rj(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function rV(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+g.version+"</meta:generator></office:meta></office:document-meta>"}var rz=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function r$(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,t=e7(t),n[n.length]=r?tp(e,t,r):tu(e,t))}function rY(e,t){var r=t||{},n=[e1,tp("cp:coreProperties",null,{"xmlns:cp":tT.CORE_PROPS,"xmlns:dc":tT.dc,"xmlns:dcterms":tT.dcterms,"xmlns:dcmitype":tT.dcmitype,"xmlns:xsi":tT.xsi})],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&r$("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:tm(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&r$("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:tm(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var s=0;s!=rz.length;++s){var i=rz[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&r$(i[0],o,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var rK=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],rX=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function rq(e){var t=[];return e||(e={}),e.Application="SheetJS",t[t.length]=e1,t[t.length]=tp("Properties",null,{xmlns:tT.EXT_PROPS,"xmlns:vt":tT.vt}),rK.forEach(function(r){var n;if(void 0!==e[r[1]]){switch(r[2]){case"string":n=e7(String(e[r[1]]));break;case"bool":n=e[r[1]]?"true":"false"}void 0!==n&&(t[t.length]=tp(r[0],n))}}),t[t.length]=tp("HeadingPairs",tp("vt:vector",tp("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+tp("vt:variant",tp("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=tp("TitlesOfParts",tp("vt:vector",e.SheetNames.map(function(e){return"<vt:lpstr>"+e7(e)+"</vt:lpstr>"}).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function rJ(e){var t=[e1,tp("Properties",null,{xmlns:tT.CUST_PROPS,"xmlns:vt":tT.vt})];if(!e)return t.join("");var r=1;return eN(e).forEach(function(n){++r,t[t.length]=tp("property",function(e,t){switch(typeof e){case"string":var r=tp("vt:lpwstr",e7(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return tp((0|e)==e?"vt:i4":"vt:r8",e7(String(e)));case"boolean":return tp("vt:bool",e?"true":"false")}if(e instanceof Date)return tp("vt:filetime",tm(e));throw Error("Unable to serialize "+e)}(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:e7(n)})}),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var rZ={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function rQ(e){var t=e.read_shift(4);return new Date((e.read_shift(4)/1e7*4294967296+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function r1(e,t,r){var n=e.l,a=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-n&3;)++e.l;return a}function r0(e,t,r){var n=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(n.length+1&3)&3),n}function r2(e,t,r){return 31===t?r0(e):r1(e,t,r)}function r4(e,t,r){return r2(e,t,!1===r?0:4)}function r3(e,t){for(var r=e.read_shift(4),n={},a=0;a!=r;++a){var s=e.read_shift(4),i=e.read_shift(4);n[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(B,"").replace(W,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),n}function r5(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function r6(e,t,r){var n,a,s=e.read_shift(2),i=r||{};if(e.l+=2,12!==t&&s!==t&&-1===rO.indexOf(t)&&!((65534&t)==4126&&(65534&s)==4126))throw Error("Expected type "+t+" saw "+s);switch(12===t?s:t){case 2:return a=e.read_shift(2,"i"),i.raw||(e.l+=2),a;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return r1(e,s,4).replace(B,"");case 31:return r0(e);case 64:return rQ(e);case 65:return r5(e);case 71:return(n={}).Size=e.read_shift(4),e.l+=n.Size+3-(n.Size-1)%4,n;case 80:return r4(e,s,!i.raw).replace(B,"");case 81:return(function(e,t){if(!t)throw Error("VtUnalignedString must have positive length");return r2(e,t,0)})(e,s).replace(B,"");case 4108:return function(e){for(var t=e.read_shift(4),r=[],n=0;n<t/2;++n)r.push(function(e){var t=e.l,r=r6(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,r6(e,3)]}(e));return r}(e);case 4126:case 4127:return 4127==s?function(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n){var a=e.l;r[n]=e.read_shift(0,"lpwstr").replace(B,""),e.l-a&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n)r[n]=e.read_shift(0,"lpstr-cp").replace(B,"");return r}(e);default:throw Error("TypedPropertyValue unrecognized type "+t+" "+s)}}function r8(e,t){var r,n,a,s,i,o=t1(4),l=t1(4);switch(o.write_shift(4,80==e?31:e),e){case 3:l.write_shift(-4,t);break;case 5:(l=t1(8)).write_shift(8,t,"f");break;case 11:l.write_shift(4,t?1:0);break;case 64:n=(r=("string"==typeof t?new Date(Date.parse(t)):t).getTime()/1e3+11644473600)%4294967296,a=(r-n)/4294967296*1e7,(s=(n*=1e7)/4294967296|0)>0&&(n%=4294967296,a+=s),(i=t1(8)).write_shift(4,n),i.write_shift(4,a),l=i;break;case 31:case 80:for((l=t1(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),l.write_shift(0,t,"dbcs");l.l!=l.length;)l.write_shift(1,0);break;default:throw Error("TypedPropertyValue unrecognized type "+e+" "+t)}return U([o,l])}function r7(e,t){var r=e.l,n=e.read_shift(4),a=e.read_shift(4),s=[],i=0,o=0,l=-1,f={};for(i=0;i!=a;++i){var c=e.read_shift(4),h=e.read_shift(4);s[i]=[c,h+r]}s.sort(function(e,t){return e[1]-t[1]});var u={};for(i=0;i!=a;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var p=t[s[i][0]];if(u[p.n]=r6(e,p.t,{raw:!0}),"version"===p.p&&(u[p.n]=String(u[p.n]>>16)+"."+("0000"+String(65535&u[p.n])).slice(-4)),"CodePage"==p.n)switch(u[p.n]){case 0:u[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:S(o=u[p.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[p.n])}}else if(1===s[i][0]){if(S(o=u.CodePage=r6(e,2)),-1!==l){var m=e.l;e.l=s[l][1],f=r3(e,o),e.l=m}}else if(0===s[i][0]){if(0===o){l=i,e.l=s[i+1][1];continue}f=r3(e,o)}else{var g,v=f[s[i][0]];switch(e[e.l]){case 65:e.l+=4,g=r5(e);break;case 30:case 31:e.l+=4,g=r4(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,g=e.read_shift(4,"i");break;case 19:e.l+=4,g=e.read_shift(4);break;case 5:e.l+=4,g=e.read_shift(8,"f");break;case 11:e.l+=4,g=nn(e,4);break;case 64:e.l+=4,g=ej(rQ(e));break;default:throw Error("unparsed value: "+e[e.l])}u[v]=g}}return e.l=r+n,u}var r9=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function ne(e,t,r){var n=t1(8),a=[],s=[],i=8,o=0,l=t1(8),f=t1(8);if(l.write_shift(4,2),l.write_shift(4,1200),f.write_shift(4,1),s.push(l),a.push(f),i+=8+l.length,!t){(f=t1(8)).write_shift(4,0),a.unshift(f);var c=[t1(4)];for(c[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((l=t1(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),l.write_shift(4,h.length+1),l.write_shift(0,h,"dbcs");l.l!=l.length;)l.write_shift(1,0);c.push(l)}l=U(c),s.unshift(l),i+=8+l.length}for(o=0;o<e.length;++o)if(!(t&&!t[e[o][0]]||r9.indexOf(e[o][0])>-1||rX.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}l=r8(p.t,u)}else{var g=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return -1}(u);-1==g&&(g=31,u=String(u)),l=r8(g,u)}s.push(l),(f=t1(8)).write_shift(4,t?d:2+o),a.push(f),i+=8+l.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)a[o].write_shift(4,v),v+=s[o].length;return n.write_shift(4,i),n.write_shift(4,s.length),U([n].concat(a).concat(s))}function nt(e,t,r,n,a,s){var i=t1(a?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,eC.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,a?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,a?68:48);var l=ne(e,r,n);if(o.push(l),a){var f=ne(a,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+l.length),o.push(f)}return U(o)}function nr(e,t){return e.read_shift(t),null}function nn(e,t){return 1===e.read_shift(t)}function na(e,t){return t||(t=t1(2)),t.write_shift(2,+!!e),t}function ns(e){return e.read_shift(2,"u")}function ni(e,t){return t||(t=t1(2)),t.write_shift(2,e),t}function no(e,t){return function(e,t,r){for(var n=[],a=e.l+t;e.l<a;)n.push(r(e,a-e.l));if(a!==e.l)throw Error("Slurp error");return n}(e,t,ns)}function nl(e,t,r){return r||(r=t1(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function nf(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont",s=v;r&&r.biff>=8&&(v=1200),r&&8!=r.biff?12==r.biff&&(a="wstr"):e.read_shift(1)&&(a="dbcs-cont"),r.biff>=2&&r.biff<=5&&(a="cpstr");var i=n?e.read_shift(n,a):"";return v=s,i}function nc(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function nh(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):nc(e,n,r)}function nu(e,t,r){if(r.biff>5)return nh(e,t,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function nd(e,t,r){return r||(r=t1(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function np(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(B,""):""}function nm(e,t){t||(t=t1(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function ng(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function nv(e,t){var r=ng(e,t);return r[3]=0,r}function nT(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function nE(e,t,r,n){return n||(n=t1(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function nb(e){return[e.read_shift(2),rb(e)]}function nw(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function nS(e,t){return t||(t=t1(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function nA(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}function n_(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return e.l+=12,[r,t,n]}function ny(e){e.l+=2,e.l+=e.read_shift(2)}var nx={0:ny,4:ny,5:ny,6:ny,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:ny,9:ny,10:ny,11:ny,12:ny,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:ny,15:ny,16:ny,17:ny,18:ny,19:ny,20:ny,21:n_};function nO(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function nC(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;default:throw Error("unsupported BIFF version")}var s=t1(a);return s.write_shift(2,n),s.write_shift(2,t),a>4&&s.write_shift(2,29282),a>6&&s.write_shift(2,1997),a>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function nR(e,t,r){var n=0;r&&2==r.biff||(n=e.read_shift(2));var a=e.read_shift(2);return r&&2==r.biff&&(n=1-(a>>15),a&=32767),[{Unsynced:1&n,DyZero:(2&n)>>1,ExAsc:(4&n)>>2,ExDsc:(8&n)>>3},a]}function nN(e,t,r){var n=e.l+t,a=8!=r.biff&&r.biff?2:4,s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(2),l=e.read_shift(2);return e.l=n,{s:{r:s,c:o},e:{r:i,c:l}}}function nI(e,t,r,n){var a=r&&5==r.biff;n||(n=t1(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&a&&(s|=1024),n.write_shift(4,s),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function nk(e,t,r){var n,a=nT(e,6);(2==r.biff||9==t)&&++e.l;var s=(n=e.read_shift(1),1===e.read_shift(1)?n:1===n);return a.val=s,a.t=!0===s||!1===s?"b":"e",a}var nP=function(e,t,r){return 0===t?"":nu(e,t,r)};function nD(e,t,r){var n,a=e.read_shift(2),s={fBuiltIn:1&a,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return 14849===r.sbcch&&(n=function(e,t,r){e.l+=4,t-=4;var n=e.l+t,a=nf(e,t,r),s=e.read_shift(2);if(s!==(n-=e.l))throw Error("Malformed AddinUdf: padding = "+n+" != "+s);return e.l+=s,a}(e,t-2,r)),s.body=n||e.read_shift(t-2),"string"==typeof n&&(s.Name=n),s}var nL=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function nM(e,t,r){var n,a,s,i,o=e.l+t,l=e.read_shift(2),f=e.read_shift(1),c=e.read_shift(1),h=e.read_shift(r&&2==r.biff?1:2),u=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),u=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var d=nc(e,c,r);32&l&&(d=nL[d.charCodeAt(0)]);var p=o-e.l;return r&&2==r.biff&&--p,{chKey:f,Name:d,itab:u,rgce:o!=e.l&&0!==h&&p>0?(n=p,s=e.l+n,i=aW(e,h,r),s!==e.l&&(a=aB(e,s-e.l,i,r)),[i,a]):[]}}function nF(e,t,r){if(r.biff<8){var n;return 3==e[e.l+1]&&e[e.l]++,3==(n=nf(e,t,r)).charCodeAt(0)?n.slice(1):n}for(var a=[],s=e.l+t,i=e.read_shift(r.biff>8?4:2);0!=i--;)a.push(function(e,t,r){var n=r.biff>8?4:2;return[e.read_shift(n),e.read_shift(n,"i"),e.read_shift(n,"i")]}(e,r.biff,r));if(e.l!=s)throw Error("Bad ExternSheet: "+e.l+" != "+s);return a}function nU(e,t,r){var n=nA(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[n,function(e,t,r){var n,a,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(n=t-2,void(e.l+=n))];var l=aW(e,o,r);return t!==o+i&&(a=aB(e,t-o-i,l,r)),e.l=s,[l,a]}(e,t,r,n)]}var nB={8:function(e,t){var r=e.l+t;e.l+=10;var n=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var a=e.read_shift(1);return e.l+=a,e.l=r,{fmt:n}}};function nW(e,t,r){if(!r.cellStyles)return void(e.l+=t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(n),l=e.read_shift(2);2==n&&(e.l+=2);var f={s:a,e:s,w:i,ixfe:o,flags:l};return(r.biff>=5||!r.biff)&&(f.level=l>>8&7),f}var nH=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=ek({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=r||{};n.dateNF||(n.dateNF="yyyymmdd");var s=rf(function(t,r){var n=[],s=P(1);switch(r.type){case"base64":s=L(N(t));break;case"binary":s=L(t);break;case"buffer":case"array":s=t}tZ(s,0);var i=s.read_shift(1),o=!!(136&i),l=!1,f=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:l=!0,o=!0;break;case 140:f=!0;break;default:throw Error("DBF Unsupported Version: "+i.toString(16))}var c=0,h=521;2==i&&(c=s.read_shift(2)),s.l+=3,2!=i&&(c=s.read_shift(4)),c>1048576&&(c=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),f&&(s.l+=36);for(var p=[],m={},g=Math.min(s.length,2==i?521:h-10-(l?264:0)),v=f?32:11;s.l<g&&13!=s[s.l];)switch((m={}).name=a.utils.decode(d,s.slice(s.l,s.l+v)).replace(/[\u0000\r\n].*$/g,""),s.l+=v,m.type=String.fromCharCode(s.read_shift(1)),2==i||f||(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=f?13:14),m.type){case"B":(!l||8!=m.len)&&r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var T=0,E=0;for(E=0,n[0]=[];E!=p.length;++E)n[0][E]=p[E].name;for(;c-- >0;){if(42===s[s.l]){s.l+=u;continue}for(++s.l,n[++T]=[],E=0,E=0;E!=p.length;++E){var b=s.slice(s.l,s.l+p[E].len);s.l+=p[E].len,tZ(b,0);var w=a.utils.decode(d,b);switch(p[E].type){case"C":w.trim().length&&(n[T][E]=w.replace(/\s+$/,""));break;case"D":8===w.length?n[T][E]=new Date(+w.slice(0,4),+w.slice(4,6)-1,+w.slice(6,8)):n[T][E]=w;break;case"F":n[T][E]=parseFloat(w.trim());break;case"+":case"I":n[T][E]=f?2147483648^b.read_shift(-4,"i"):b.read_shift(4,"i");break;case"L":switch(w.trim().toUpperCase()){case"Y":case"T":n[T][E]=!0;break;case"N":case"F":n[T][E]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+w+"|")}break;case"M":if(!o)throw Error("DBF Unexpected MEMO for type "+i.toString(16));n[T][E]="##MEMO##"+(f?parseInt(w.trim(),10):b.read_shift(4));break;case"N":(w=w.replace(/\u0000/g,"").trim())&&"."!=w&&(n[T][E]=+w||0);break;case"@":n[T][E]=new Date(b.read_shift(-8,"f")-621356832e5);break;case"T":n[T][E]=new Date((b.read_shift(4)-2440588)*864e5+b.read_shift(4));break;case"Y":n[T][E]=b.read_shift(4,"i")/1e4+b.read_shift(4,"i")/1e4*4294967296;break;case"O":n[T][E]=-b.read_shift(-8,"f");break;case"B":if(l&&8==p[E].len){n[T][E]=b.read_shift(8,"f");break}case"G":case"P":b.l+=p[E].len;break;case"0":if("_NullFlags"===p[E].name)break;default:throw Error("DBF Unsupported data type "+p[E].type)}}}if(2!=i&&s.l<s.length&&26!=s[s.l++])throw Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=p,n}(t,n),n);return s["!cols"]=n.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete n.DBF,s}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return rl(r(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var a=r||{};if(+a.codepage>=0&&S(+a.codepage),"string"==a.type)throw Error("Cannot write DBF to JS string");var s=t0(),i=sL(e,{header:1,raw:!0,cellDates:!0}),o=i[0],l=i.slice(1),f=e["!cols"]||[],c=0,h=0,u=0,d=1;for(c=0;c<o.length;++c){if(((f[c]||{}).DBF||{}).name){o[c]=f[c].DBF.name,++u;continue}if(null!=o[c]){if(++u,"number"==typeof o[c]&&(o[c]=o[c].toString(10)),"string"!=typeof o[c])throw Error("DBF Invalid column name "+o[c]+" |"+typeof o[c]+"|");if(o.indexOf(o[c])!==c){for(h=0;h<1024;++h)if(-1==o.indexOf(o[c]+"_"+h)){o[c]+="_"+h;break}}}}var p=rs(e["!ref"]),m=[],g=[],v=[];for(c=0;c<=p.e.c-p.s.c;++c){var E="",b="",w=0,A=[];for(h=0;h<l.length;++h)null!=l[h][c]&&A.push(l[h][c]);if(0==A.length||null==o[c]){m[c]="?";continue}for(h=0;h<A.length;++h){switch(typeof A[h]){case"number":b="B";break;case"string":default:b="C";break;case"boolean":b="L";break;case"object":b=A[h]instanceof Date?"D":"C"}w=Math.max(w,String(A[h]).length),E=E&&E!=b?"C":b}w>250&&(w=250),"C"==(b=((f[c]||{}).DBF||{}).type)&&f[c].DBF.len>w&&(w=f[c].DBF.len),"B"==E&&"N"==b&&(E="N",v[c]=f[c].DBF.dec,w=f[c].DBF.len),g[c]="C"==E||"N"==b?w:n[E]||0,d+=g[c],m[c]=E}var _=s.next(32);for(_.write_shift(4,318902576),_.write_shift(4,l.length),_.write_shift(2,296+32*u),_.write_shift(2,d),c=0;c<4;++c)_.write_shift(4,0);for(_.write_shift(4,0|(+t[T]||3)<<8),c=0,h=0;c<o.length;++c)if(null!=o[c]){var y=s.next(32),x=(o[c].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);y.write_shift(1,x,"sbcs"),y.write_shift(1,"?"==m[c]?"C":m[c],"sbcs"),y.write_shift(4,h),y.write_shift(1,g[c]||n[m[c]]||0),y.write_shift(1,v[c]||0),y.write_shift(1,2),y.write_shift(4,0),y.write_shift(1,0),y.write_shift(4,0),y.write_shift(4,0),h+=g[c]||n[m[c]]||0}var O=s.next(264);for(O.write_shift(4,13),c=0;c<65;++c)O.write_shift(4,0);for(c=0;c<l.length;++c){var C=s.next(d);for(C.write_shift(1,0),h=0;h<o.length;++h)if(null!=o[h])switch(m[h]){case"L":C.write_shift(1,null==l[c][h]?63:l[c][h]?84:70);break;case"B":C.write_shift(8,l[c][h]||0,"f");break;case"N":var R="0";for("number"==typeof l[c][h]&&(R=l[c][h].toFixed(v[h]||0)),u=0;u<g[h]-R.length;++u)C.write_shift(1,32);C.write_shift(1,R,"sbcs");break;case"D":l[c][h]?(C.write_shift(4,("0000"+l[c][h].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(l[c][h].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+l[c][h].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var N=String(null!=l[c][h]?l[c][h]:"").slice(0,g[h]);for(C.write_shift(1,N,"sbcs"),u=0;u<g[h]-N.length;++u)C.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),nG=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"œ",a:"\xc6",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=RegExp("\x1bN("+eN(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?O(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:O(n)};function s(e,s){var i,o=e.split(/[\n\r]+/),l=-1,f=-1,c=0,h=0,u=[],d=[],p=null,m={},g=[],v=[],T=[],E=0;for(+s.codepage>=0&&S(+s.codepage);c!==o.length;++c){E=0;var b,w=o[c].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),A=w.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),_=A[0];if(w.length>0)switch(_){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==A[1].charAt(0)&&d.push(w.slice(3).replace(/;;/g,";"));break;case"C":var y=!1,x=!1,O=!1,C=!1,R=-1,N=-1;for(h=1;h<A.length;++h)switch(A[h].charAt(0)){case"A":case"G":break;case"X":f=parseInt(A[h].slice(1))-1,x=!0;break;case"Y":for(l=parseInt(A[h].slice(1))-1,x||(f=0),i=u.length;i<=l;++i)u[i]=[];break;case"K":'"'===(b=A[h].slice(1)).charAt(0)?b=b.slice(1,b.length-1):"TRUE"===b?b=!0:"FALSE"===b?b=!1:isNaN(eY(b))?isNaN(eX(b).getDate())||(b=ej(b)):(b=eY(b),null!==p&&eE(p)&&(b=eB(b))),void 0!==a&&"string"==typeof b&&"string"!=(s||{}).type&&(s||{}).codepage&&(b=a.utils.decode(s.codepage,b)),y=!0;break;case"E":C=!0;var I=aw(A[h].slice(1),{r:l,c:f});u[l][f]=[u[l][f],I];break;case"S":O=!0,u[l][f]=[u[l][f],"S5S"];break;case"R":R=parseInt(A[h].slice(1))-1;break;case"C":N=parseInt(A[h].slice(1))-1;break;default:if(s&&s.WTF)throw Error("SYLK bad record "+w)}if(y&&(u[l][f]&&2==u[l][f].length?u[l][f][0]=b:u[l][f]=b,p=null),O){if(C)throw Error("SYLK shared formula cannot have own formula");var k=R>-1&&u[R][N];if(!k||!k[1])throw Error("SYLK shared formula cannot find base");u[l][f][1]=function(e,t){return e.replace(aS,function(e,r,n,a,s,i){return r+("$"==n?n+a:re(t9(a)+t.c))+("$"==s?s+i:t7(t8(i)+t.r))})}(k[1],{r:l-R,c:f-N})}break;case"F":var P=0;for(h=1;h<A.length;++h)switch(A[h].charAt(0)){case"X":f=parseInt(A[h].slice(1))-1,++P;break;case"Y":for(l=parseInt(A[h].slice(1))-1,i=u.length;i<=l;++i)u[i]=[];break;case"M":E=parseInt(A[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(A[h].slice(1))];break;case"W":for(i=parseInt((T=A[h].slice(1).split(" "))[0],10);i<=parseInt(T[1],10);++i)E=parseInt(T[2],10),v[i-1]=0===E?{hidden:!0}:{wch:E},n7(v[i-1]);break;case"C":v[f=parseInt(A[h].slice(1))-1]||(v[f]={});break;case"R":g[l=parseInt(A[h].slice(1))-1]||(g[l]={}),E>0?(g[l].hpt=E,g[l].hpx=function(e){return 96*e/96}(E)):0===E&&(g[l].hidden=!0);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+w)}P<1&&(p=null);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+w)}}return g.length>0&&(m["!rows"]=g),v.length>0&&(m["!cols"]=v),s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),[u,m]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return s(N(e),t);case"binary":return s(e,t);case"buffer":return s(I&&Buffer.isBuffer(e)?e.toString("binary"):F(e),t);case"array":return s(eV(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),n=r[0],a=r[1],i=rf(n,t);return eN(a).forEach(function(e){i[e]=a[e]}),i}return e["|"]=254,{to_workbook:function(e,t){return rl(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,n=["ID;PWXL;N;E"],a=[],s=rs(e["!ref"]),i=Array.isArray(e);n.push("P;PGeneral"),n.push("F;P0;DG0G8;M255"),e["!cols"]&&function(e,t){t.forEach(function(t,r){var n="F;W"+(r+1)+" "+(r+1)+" ";t.hidden?n+="0":("number"!=typeof t.width||t.wpx||(t.wpx=n5(t.width)),"number"!=typeof t.wpx||t.wch||(t.wch=n6(t.wpx)),"number"==typeof t.wch&&(n+=Math.round(t.wch)))," "!=n.charAt(n.length-1)&&e.push(n)})}(n,e["!cols"]),e["!rows"]&&function(e,t){t.forEach(function(t,r){var n="F;";t.hidden?n+="M0;":t.hpt?n+="M"+20*t.hpt+";":t.hpx&&(n+="M"+20*n9(t.hpx)+";"),n.length>2&&e.push(n+"R"+(r+1))})}(n,e["!rows"]),n.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var o=s.s.r;o<=s.e.r;++o)for(var l=s.s.c;l<=s.e.c;++l){var f=rr({r:o,c:l});(r=i?(e[o]||[])[l]:e[f])&&(null!=r.v||r.f&&!r.F)&&a.push(function(e,t,r,n){var a="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":a+=e.v||0,e.f&&!e.F&&(a+=";E"+aA(e.f,{r:r,c:n}));break;case"b":a+=e.v?"TRUE":"FALSE";break;case"e":a+=e.w||e.v;break;case"d":a+='"'+(e.w||e.v)+'"';break;case"s":a+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return a}(r,0,o,l,t))}return n.join("\r\n")+"\r\n"+a.join("\r\n")+"\r\nE\r\n"}}}(),nj=function(){var e,t;function r(e,t){for(var r=e.split("\n"),n=-1,a=-1,s=0,i=[];s!==r.length;++s){if("BOT"===r[s].trim()){i[++n]=[],a=0;continue}if(!(n<0)){for(var o=r[s].trim().split(","),l=o[0],f=o[1],c=r[++s]||"";1&(c.match(/["]/g)||[]).length&&s<r.length-1;)c+="\n"+r[++s];switch(c=c.trim(),+l){case -1:if("BOT"===c){i[++n]=[],a=0;continue}if("EOD"!==c)throw Error("Unrecognized DIF special command "+c);break;case 0:"TRUE"===c?i[n][a]=!0:"FALSE"===c?i[n][a]=!1:isNaN(eY(f))?isNaN(eX(f).getDate())?i[n][a]=f:i[n][a]=ej(f):i[n][a]=eY(f),++a;break;case 1:(c=(c=c.slice(1,c.length-1)).replace(/""/g,'"'))&&c.match(/^=".*"$/)&&(c=c.slice(2,-1)),i[n][a++]=""!==c?c:null}if("EOD"===c)break}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function n(e,t){return rf(function(e,t){switch(t.type){case"base64":return r(N(e),t);case"binary":return r(e,t);case"buffer":return r(I&&Buffer.isBuffer(e)?e.toString("binary"):F(e),t);case"array":return r(eV(e),t)}throw Error("Unrecognized type "+t.type)}(e,t),t)}return{to_workbook:function(e,t){return rl(n(e,t),t)},to_sheet:n,from_sheet:(e=function(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)},function(r){var n,a=[],s=rs(r["!ref"]),i=Array.isArray(r);e(a,"TABLE",0,1,"sheetjs"),e(a,"VECTORS",0,s.e.r-s.s.r+1,""),e(a,"TUPLES",0,s.e.c-s.s.c+1,""),e(a,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){t(a,-1,0,"BOT");for(var l=s.s.c;l<=s.e.c;++l){var f=rr({r:o,c:l});if(!(n=i?(r[o]||[])[l]:r[f])){t(a,1,0,"");continue}switch(n.t){case"n":var c=n.w;c||null==n.v||(c=n.v),null==c?!n.f||n.F?t(a,1,0,""):t(a,1,0,"="+n.f):t(a,0,c,"V");break;case"b":t(a,0,n.v?1:0,n.v?"TRUE":"FALSE");break;case"s":t(a,1,0,isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=eS(n.z||q[14],eL(ej(n.v)))),t(a,0,n.w,"V");break;default:t(a,1,0,"")}}}return t(a,-1,0,"EOD"),a.join("\r\n")})}}(),nV=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return rf(function(e,t){for(var r=e.split("\n"),n=-1,a=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var l=rt(o[1]);if(i.length<=l.r)for(n=i.length;n<=l.r;++n)i[n]||(i[n]=[]);switch(n=l.r,a=l.c,o[2]){case"t":i[n][a]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[n][a]=+o[3];break;case"vtf":var f=o[o.length-1];case"vtc":"nl"===o[3]?i[n][a]=!!+o[4]:i[n][a]=+o[4],"vtf"==o[2]&&(i[n][a]=[i[n][a],f])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,r){return rl(t(e,r),r)},to_sheet:t,from_sheet:function(t){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",r,"# SocialCalc Spreadsheet Control Save\npart:sheet",r,function(t){if(!t||!t["!ref"])return"";for(var r,n=[],a=[],s="",i=rn(t["!ref"]),o=Array.isArray(t),l=i.s.r;l<=i.e.r;++l)for(var f=i.s.c;f<=i.e.c;++f)if(s=rr({r:l,c:f}),(r=o?(t[l]||[])[f]:t[s])&&null!=r.v&&"z"!==r.t){switch(a=["cell",s,"t"],r.t){case"s":case"str":a.push(e(r.v));break;case"n":r.f?(a[2]="vtf",a[3]="n",a[4]=r.v,a[5]=e(r.f)):(a[2]="v",a[3]=r.v);break;case"b":a[2]="vt"+(r.f?"f":"c"),a[3]="nl",a[4]=r.v?"1":"0",a[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var c=eL(ej(r.v));a[2]="vtc",a[3]="nd",a[4]=""+c,a[5]=r.w||eS(r.z||q[14],c);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}(t),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),nz=function(){function e(e,t,r,n,a){a.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(eY(e))?isNaN(eX(e).getDate())?t[r][n]=e:t[r][n]=ej(e):t[r][n]=eY(e))}var t={44:",",9:"	",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function n(e){for(var n={},a=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?a=!a:!a&&i in t&&(n[i]=(n[i]||0)+1);for(s in i=[],n)Object.prototype.hasOwnProperty.call(n,s)&&i.push([n[s],s]);if(!i.length)for(s in n=r)Object.prototype.hasOwnProperty.call(n,s)&&i.push([n[s],s]);return i.sort(function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]}),t[i.pop()[1]]||44}function s(t,r){var s,i="",o="string"==r.type?[0,0,0,0]:function(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=N(e.slice(0,12));break;case"binary":r=e;break;default:throw Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}(t,r);switch(r.type){case"base64":i=N(t);break;case"binary":case"string":i=t;break;case"buffer":i=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==a?a.utils.decode(r.codepage,t):I&&Buffer.isBuffer(t)?t.toString("binary"):F(t);break;case"array":i=eV(t);break;default:throw Error("Unrecognized type "+r.type)}return(239==o[0]&&187==o[1]&&191==o[2]?i=ti(i.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?i=ti(i):"binary"==r.type&&void 0!==a&&r.codepage&&(i=a.utils.decode(r.codepage,a.utils.encode(28591,i))),"socialcalc:version:"==i.slice(0,19))?nV.to_sheet("string"==r.type?i:ti(i),r):(s=i,!(r&&r.PRN)||r.FS||"sep="==s.slice(0,4)||s.indexOf("	")>=0||s.indexOf(",")>=0||s.indexOf(";")>=0?function(e,t){var r,a=t||{},s="",i=a.dense?[]:{},o={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(s=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(s=e.charAt(4),e=e.slice(6)):s=n(e.slice(0,1024)):s=a&&a.FS?a.FS:n(e.slice(0,1024));var l=0,f=0,c=0,h=0,u=0,d=s.charCodeAt(0),p=!1,m=0,g=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var v=null!=a.dateNF?RegExp("^"+("number"==typeof(r=a.dateNF)?q[r]:r).replace(ex,"(\\d+)")+"$"):null;function T(){var t=e.slice(h,u),r={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)r.t="z";else if(a.raw)r.t="s",r.v=t;else if(0===t.trim().length)r.t="s",r.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t);else if("TRUE"==t)r.t="b",r.v=!0;else if("FALSE"==t)r.t="b",r.v=!1;else if(isNaN(c=eY(t))){if(!isNaN(eX(t).getDate())||v&&t.match(v)){r.z=a.dateNF||q[14];var n,s,p,T,E,b,w,S,A,_,y=0;v&&t.match(v)&&(n=a.dateNF,s=t.match(v)||[],p=-1,T=-1,E=-1,b=-1,w=-1,S=-1,(n.match(ex)||[]).forEach(function(e,t){var r=parseInt(s[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":p=r;break;case"d":E=r;break;case"h":b=r;break;case"s":S=r;break;case"m":b>=0?w=r:T=r}}),S>=0&&-1==w&&T>=0&&(w=T,T=-1),7==(A=(""+(p>=0?p:new Date().getFullYear())).slice(-4)+"-"+("00"+(T>=1?T:1)).slice(-2)+"-"+("00"+(E>=1?E:1)).slice(-2)).length&&(A="0"+A),8==A.length&&(A="20"+A),_=("00"+(b>=0?b:0)).slice(-2)+":"+("00"+(w>=0?w:0)).slice(-2)+":"+("00"+(S>=0?S:0)).slice(-2),t=-1==b&&-1==w&&-1==S?A:-1==p&&-1==T&&-1==E?_:A+"T"+_,y=1),a.cellDates?(r.t="d",r.v=ej(t,y)):(r.t="n",r.v=eL(ej(t,y))),!1!==a.cellText&&(r.w=eS(r.z,r.v instanceof Date?eL(r.v):r.v)),a.cellNF||delete r.z}else r.t="s",r.v=t}else r.t="n",!1!==a.cellText&&(r.w=t),r.v=c;if("z"==r.t||(a.dense?(i[l]||(i[l]=[]),i[l][f]=r):i[rr({c:f,r:l})]=r),h=u+1,g=e.charCodeAt(h),o.e.c<f&&(o.e.c=f),o.e.r<l&&(o.e.r=l),m==d)++f;else if(f=0,++l,a.sheetRows&&a.sheetRows<=l)return!0}e:for(;u<e.length;++u)switch(m=e.charCodeAt(u)){case 34:34===g&&(p=!p);break;case d:case 10:case 13:if(!p&&T())break e}return u-h>0&&T(),i["!ref"]=ra(o),i}(s,r):rf(function(t,r){var n=r||{},a=[];if(!t||0===t.length)return a;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,l=0,f=0;f<=i;++f)-1==(l=s[f].indexOf(" "))?l=s[f].length:l++,o=Math.max(o,l);for(f=0;f<=i;++f){a[f]=[];var c=0;for(e(s[f].slice(0,o).trim(),a,f,c,n),c=1;c<=(s[f].length-o)/10+1;++c)e(s[f].slice(o+(c-1)*10,o+10*c).trim(),a,f,c,n)}return n.sheetRows&&(a=a.slice(0,n.sheetRows)),a}(s,r),r))}return{to_workbook:function(e,t){return rl(s(e,t),t)},to_sheet:s,from_sheet:function(e){for(var t,r=[],n=rs(e["!ref"]),a=Array.isArray(e),s=n.s.r;s<=n.e.r;++s){for(var i=[],o=n.s.c;o<=n.e.c;++o){var l=rr({r:s,c:o});if(!(t=a?(e[s]||[])[o]:e[l])||null==t.v){i.push("          ");continue}for(var f=(t.w||(ro(t),t.w)||"").slice(0,10);f.length<10;)f+=" ";i.push(f+(0===o?" ":""))}r.push(i.join(""))}return r.join("\n")}}}(),n$=function(){function e(e,t,r){if(e){tZ(e,e.l||0);for(var n=r.Enum||h;e.l<e.length;){var a=e.read_shift(2),s=n[a]||n[65535],i=e.read_shift(2),o=e.l+i,l=s.f&&s.f(e,i,r);if(e.l=o,t(l,s,a))return}}}function t(t,r){if(!t)return t;var n=r||{},a=n.dense?[]:{},s="Sheet1",i="",o=0,l={},f=[],c=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=h,e(t,function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:d=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||q[14],n.cellDates&&(e[1].t="d",e[1].v=eB(e[1].v))),n.qpro&&e[3]>o&&(a["!ref"]=ra(d),l[s]=a,f.push(s),a=n.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var c=n.dense?(a[e[0].r]||[])[e[0].c]:a[rr(e[0])];if(c){c.t=e[1].t,c.v=e[1].v,null!=e[1].z&&(c.z=e[1].z),null!=e[1].f&&(c.f=e[1].f);break}n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[rr(e[0])]=e[1]}},n);else if(26==t[2]||14==t[2])n.Enum=u,14==t[2]&&(n.qpro=!0,t.l=0),e(t,function(e,t,r){switch(r){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(a["!ref"]=ra(d),l[s]=a,f.push(s),a=n.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},s="Sheet"+((o=e[3])+1)),p>0&&e[0].r>=p)break;n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[rr(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(c[e[14e3][0]]=e[14e3][1]);break;case 1537:c[e[0]]=e[1],e[0]==o&&(s=e[1])}},n);else throw Error("Unrecognized LOTUS BOF "+t[2]);if(a["!ref"]=ra(d),l[i||s]=a,f.push(i||s),!c.length)return{SheetNames:f,Sheets:l};for(var m={},g=[],v=0;v<c.length;++v)l[f[v]]?(g.push(c[v]||f[v]),m[c[v]]=l[c[v]]||l[f[v]]):(g.push(c[v]),m[c[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function r(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function n(e,t,n){var a=e.l+t,s=r(e,t,n);if(s[1].t="s",20768==n.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return n.qpro&&e.l++,s[1].v=e.read_shift(a-e.l,"cstr"),s}function a(e,t,r){var n=32768&t;return t&=-32769,t=(n?e:0)+(t>=8192?t-16384:t),(n?"":"$")+(r?re(t):t7(t))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},i=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function o(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function l(e,t){var r=o(e,t),n=e.read_shift(4),a=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===n&&3221225472===a?(r[1].t="e",r[1].v=15):0===n&&3489660928===a?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(a*Math.pow(2,s+32)+n*Math.pow(2,s)),r}function f(e,t){var r=o(e,t),n=e.read_shift(8,"f");return r[1].v=n,r}function c(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}var h={0:{n:"BOF",f:ns},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2)):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0)),n}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,n){var a=r(e,t,n);return a[1].v=e.read_shift(2,"i"),a}},14:{n:"NUMBER",f:function(e,t,n){var a=r(e,t,n);return a[1].v=e.read_shift(8,"f"),a}},15:{n:"LABEL",f:n},16:{n:"FORMULA",f:function(e,t,n){var o=e.l+t,l=r(e,t,n);if(l[1].v=e.read_shift(8,"f"),n.qpro)e.l=o;else{var f=e.read_shift(2);(function(e,t){tZ(e,0);for(var r=[],n=0,o="",l="",f="",c="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:r.push(e.read_shift(8,"f"));break;case 1:l=a(t[0].c,e.read_shift(2),!0),o=a(t[0].r,e.read_shift(2),!1),r.push(l+o);break;case 2:var u=a(t[0].c,e.read_shift(2),!0),d=a(t[0].r,e.read_shift(2),!1);l=a(t[0].c,e.read_shift(2),!0),o=a(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+l+o);break;case 3:if(e.l<e.length){console.error("WK1 premature formula end");return}break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:c=r.pop(),f=r.pop(),r.push(["AND","OR"][h-20]+"("+f+","+c+")");break;default:if(h<32&&i[h])c=r.pop(),f=r.pop(),r.push(f+i[h]+c);else if(s[h]){if(69==(n=s[h][1])&&(n=e[e.l++]),n>r.length){console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+r.join("|")+"|");return}var m=r.slice(-n);r.length-=n,r.push(s[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")})(e.slice(e.l,e.l+f),l),e.l+=f}return l}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:n},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:c},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var n="";n.length<r;)n+=String.fromCharCode(e[e.l++]);return n}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=o(e,t);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:l},24:{n:"NUMBER18",f:function(e,t){var r=o(e,t);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=(n>>3)*5e3;break;case 1:n=(n>>3)*500;break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64}return r[1].v=n,r}},25:{n:"FORMULA19",f:function(e,t){var r=l(e,14);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},n=e.l+t;e.l<n;){var a=e.read_shift(2);if(14e3==a){for(r[a]=[0,""],r[a][0]=e.read_shift(2);e[e.l];)r[a][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=o(e,t),n=e.read_shift(4);return r[1].v=n>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:f},40:{n:"FORMULA28",f:function(e,t){var r=f(e,14);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:c},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[n,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r,n,a=t||{};if(+a.codepage>=0&&S(+a.codepage),"string"==a.type)throw Error("Cannot write WK1 to JS string");var s=t0(),i=rs(e["!ref"]),o=Array.isArray(e),l=[];so(s,0,((r=t1(2)).write_shift(2,1030),r)),so(s,6,((n=t1(8)).write_shift(2,i.s.c),n.write_shift(2,i.s.r),n.write_shift(2,i.e.c),n.write_shift(2,i.e.r),n));for(var f=Math.min(i.e.r,8191),c=i.s.r;c<=f;++c)for(var h=t7(c),u=i.s.c;u<=i.e.c;++u){c===i.s.r&&(l[u]=re(u));var d=l[u]+h,p=o?(e[c]||[])[u]:e[d];p&&"z"!=p.t&&("n"==p.t?(0|p.v)==p.v&&p.v>=-32768&&p.v<=32767?so(s,13,function(e,t,r){var n=t1(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}(c,u,p.v)):so(s,14,function(e,t,r){var n=t1(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}(c,u,p.v)):so(s,15,function(e,t,r){var n=t1(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var a=0;a<n.length;++a){var s=r.charCodeAt(a);n.write_shift(1,s>=128?95:s)}return n.write_shift(1,0),n}(c,u,ro(p).slice(0,239))))}return so(s,1),s.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&S(+r.codepage),"string"==r.type)throw Error("Cannot write WK3 to JS string");var n=t0();so(n,0,function(e){var t=t1(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,a=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++a;var l=rn(o["!ref"]);r<l.e.r&&(r=l.e.r),n<l.e.c&&(n=l.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,a),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var a=0,s=0;a<e.SheetNames.length;++a)(e.Sheets[e.SheetNames[a]]||{})["!ref"]&&so(n,27,function(e,t){var r=t1(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var a=e.charCodeAt(n);r[r.l++]=a>127?95:a}return r[r.l++]=0,r}(e.SheetNames[a],s++));var i=0;for(a=0;a<e.SheetNames.length;++a){var o=e.Sheets[e.SheetNames[a]];if(o&&o["!ref"]){for(var l=rs(o["!ref"]),f=Array.isArray(o),c=[],h=Math.min(l.e.r,8191),u=l.s.r;u<=h;++u)for(var d=t7(u),p=l.s.c;p<=l.e.c;++p){u===l.s.r&&(c[p]=re(p));var m=c[p]+d,g=f?(o[u]||[])[p]:o[m];g&&"z"!=g.t&&("n"==g.t?so(n,23,function(e,t,r,n){var a=t1(14);if(a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),0==n)return a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,65535),a;var s=0,i=0,o=0,l=0;return n<0&&(s=1,n=-n),i=0|Math.log2(n),n/=Math.pow(2,i-31),(2147483648&(l=n>>>0))==0&&(n/=2,++i,l=n>>>0),n-=l,l|=2147483648,l>>>=0,n*=4294967296,o=n>>>0,a.write_shift(4,o),a.write_shift(4,l),i+=16383+(s?32768:0),a.write_shift(2,i),a}(u,p,i,g.v)):so(n,22,function(e,t,r,n){var a=t1(6+n.length);a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),a.write_shift(1,39);for(var s=0;s<n.length;++s){var i=n.charCodeAt(s);a.write_shift(1,i>=128?95:i)}return a.write_shift(1,0),a}(u,p,i,ro(g).slice(0,239))))}++i}}return so(n,1),n.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(L(N(e)),r);case"binary":return t(L(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}(),nY=/^\s|\s$|[\t\n\r]/;function nK(e,t){if(!t.bookSST)return"";var r=[e1];r[r.length]=tp("sst",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main",count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var a=e[n],s="<si>";a.r?s+=a.r:(s+="<t",a.t||(a.t=""),a.t.match(nY)&&(s+=' xml:space="preserve"'),s+=">"+e7(a.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var nX=function(e,t){var r=!1;return null==t&&(r=!0,t=t1(15+4*e.t.length)),t.write_shift(1,0),ru(e.t,t),r?t.slice(0,t.l):t};function nq(e){if(void 0!==a)return a.utils.encode(T,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function nJ(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function nZ(e){var t,r,n=0,a=nq(e),s=a.length+1;for(r=1,(t=P(s))[0]=a.length;r!=s;++r)t[r]=a[r-1];for(r=s-1;r>=0;--r)n=(((16384&n)==0?0:1)|n<<1&32767)^t[r];return 52811^n}var nQ=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],n=function(e,t){var r;return((r=e^t)/2|128*r)&255},a=function(e){for(var n=t[e.length-1],a=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(n^=r[a]),i*=2,--a;return n};return function(t){for(var r,s,i,o=nq(t),l=a(o),f=o.length,c=P(16),h=0;16!=h;++h)c[h]=0;for((1&f)==1&&(r=l>>8,c[f]=n(187,r),--f,r=255&l,s=o[o.length-1],c[f]=n(s,r));f>0;)--f,r=l>>8,c[f]=n(o[f],r),--f,r=255&l,c[f]=n(o[f],r);for(f=15,i=15-o.length;i>0;)r=l>>8,c[f]=n(e[i],r),--f,--i,r=255&l,c[f]=n(o[f],r),--f,--i;return c}}(),n1=function(e,t,r,n,a){var s,i;for(a||(a=t),n||(n=nQ(e)),s=0;s!=t.length;++s)i=((i=t[s]^n[r])>>5|i<<3)&255,a[s]=i,++r;return[a,r,n]},n0=function(e){var t=0,r=nQ(e);return function(e){var n=n1("",e,t,r);return t=n[1],n[0]}},n2=function(){function e(e,r){switch(r.type){case"base64":return t(N(e),r);case"binary":return t(e,r);case"buffer":return t(I&&Buffer.isBuffer(e)?e.toString("binary"):F(e),r);case"array":return t(eV(e),r)}throw Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw Error("RTF missing table");var a={s:{c:0,r:0},e:{c:0,r:n.length-1}};return n.forEach(function(e,t){Array.isArray(r)&&(r[t]=[]);for(var n,s=/\\\w+\b/g,i=0,o=-1;n=s.exec(e);){if("\\cell"===n[0]){var l=e.slice(i,s.lastIndex-n[0].length);if(" "==l[0]&&(l=l.slice(1)),++o,l.length){var f={v:l,t:"s"};Array.isArray(r)?r[t][o]=f:r[rr({r:t,c:o})]=f}}i=s.lastIndex}o>a.e.c&&(a.e.c=o)}),r["!ref"]=ra(a),r}return{to_workbook:function(t,r){return rl(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],n=rs(e["!ref"]),a=Array.isArray(e),s=n.s.r;s<=n.e.r;++s){r.push("\\trowd\\trautofit1");for(var i=n.s.c;i<=n.e.c;++i)r.push("\\cellx"+(i+1));for(r.push("\\pard\\intbl"),i=n.s.c;i<=n.e.c;++i){var o=rr({r:s,c:i});(t=a?(e[s]||[])[i]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(ro(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function n4(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var n3=6;function n5(e){return Math.floor((e+Math.round(128/n3)/256)*n3)}function n6(e){return Math.floor((e-5)/n3*100+.5)/100}function n8(e){return Math.round((e*n3+5)/n3*256)/256}function n7(e){e.width?(e.wpx=n5(e.width),e.wch=n6(e.wpx),e.MDW=n3):e.wpx?(e.wch=n6(e.wpx),e.width=n8(e.wch),e.MDW=n3):"number"==typeof e.wch&&(e.width=n8(e.wch),e.wpx=n5(e.width),e.MDW=n3),e.customWidth&&delete e.customWidth}function n9(e){return 96*e/96}function ae(e){return 96*e/96}var at={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function ar(e,t){var r,n,a,s,i,o=[e1,tp("styleSheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:vt":tT.vt})];return e.SSF&&null!=(r=e.SSF,n=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=r[t]&&(n[n.length]=tp("numFmt",null,{numFmtId:t,formatCode:e7(r[t])}))}),i=1===n.length?"":(n[n.length]="</numFmts>",n[0]=tp("numFmts",null,{count:n.length-2}).replace("/>",">"),n.join("")))&&(o[o.length]=i),o[o.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',o[o.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',o[o.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',o[o.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',a=t.cellXfs,(s=[])[s.length]=tp("cellXfs",null),a.forEach(function(e){s[s.length]=tp("xf",null,e)}),s[s.length]="</cellXfs>",(i=2===s.length?"":(s[0]=tp("cellXfs",null,{count:s.length-2}).replace("/>",">"),s.join("")))&&(o[o.length]=i),o[o.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',o[o.length]='<dxfs count="0"/>',o[o.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',o.length>2&&(o[o.length]="</styleSheet>",o[1]=o[1].replace("/>",">")),o.join("")}var an=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function aa(e,t){t||(t=t1(84)),i||(i=ek(an));var r=i[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(rx({auto:1},t),rx({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function as(e,t,r){return r||(r=t1(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function ai(e,t){return t||(t=t1(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var ao=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function al(e,t,r){t.themeElements.clrScheme=[];var n={};(e[0].match(e4)||[]).forEach(function(e){var a=e3(e);switch(a[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":n.rgb=a.val;break;case"<a:sysClr":n.rgb=a.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===a[0].charAt(1)?(t.themeElements.clrScheme[ao.indexOf(a[0])]=n,n={}):n.name=a[0].slice(3,a[0].length-1);break;default:if(r&&r.WTF)throw Error("Unrecognized "+a[0]+" in clrScheme")}})}function af(){}function ac(){}var ah=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,au=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,ad=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,ap=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function am(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[e1];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function ag(){var e=[e1];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var av=1024;function aT(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[tp("xml",null,{"xmlns:v":tE.v,"xmlns:o":tE.o,"xmlns:x":tE.x,"xmlns:mv":tE.mv}).replace(/\/>/,">"),tp("o:shapelayout",tp("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),tp("v:shapetype",[tp("v:stroke",null,{joinstyle:"miter"}),tp("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];av<1e3*e;)av+=1e3;return t.forEach(function(e){var t=rt(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?tp("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=tp("v:fill",n,r);++av,a=a.concat(["<v:shape"+td({id:"_x0000_s"+av,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,tp("v:shadow",null,{on:"t",obscured:"t"}),tp("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",tu("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),tu("x:AutoFill","False"),tu("x:Row",String(t.r)),tu("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}function aE(e){var t=[e1,tp("comments",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main"})],r=[];return t.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){var n=e7(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))})}),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach(function(e){var n=0,a=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach(function(e){e.a&&(n=r.indexOf(e7(e.a))),a.push(e.t||"")}),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),a.length<=1)t.push(tu("t",e7(a[0]||"")));else{for(var s="Comment:\n    "+a[0]+"\n",i=1;i<a.length;++i)s+="Reply:\n    "+a[i]+"\n";t.push(tu("t",e7(s)))}t.push("</text></comment>")}),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}var ab=["xlsb","xlsm","xlam","biff8","xla"],aw=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,a){var s=!1,i=!1;0==n.length?i=!0:"["==n.charAt(0)&&(i=!0,n=n.slice(1,-1)),0==a.length?s=!0:"["==a.charAt(0)&&(s=!0,a=a.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,l=a.length>0?0|parseInt(a,10):0;return s?l+=t.c:--l,i?o+=t.r:--o,r+(s?"":"$")+re(l)+(i?"":"$")+t7(o)}return function(n,a){return t=a,n.replace(e,r)}}(),aS=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,aA=function(e,t){return e.replace(aS,function(e,r,n,a,s,i){var o=t9(a)-(n?0:t.c),l=t8(i)-(s?0:t.r);return r+"R"+(0==l?"":s?l+1:"["+l+"]")+"C"+(0==o?"":n?o+1:"["+o+"]")})};function a_(e){e.l+=1}function ay(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function ax(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return aO(e,t,r);12==r.biff&&(n=4)}var a=e.read_shift(n),s=e.read_shift(n),i=ay(e,2),o=ay(e,2);return{s:{r:a,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function aO(e){var t=ay(e,2),r=ay(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function aC(e,t,r){if(r&&r.biff>=2&&r.biff<=5){var n,a;return n=ay(e,2),a=e.read_shift(1),{r:n[0],c:a,cRel:n[1],rRel:n[2]}}var s=e.read_shift(r&&12==r.biff?4:2),i=ay(e,2);return{r:s,c:i[0],cRel:i[1],rRel:i[2]}}function aR(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function aN(e){return[e.read_shift(1),e.read_shift(1)]}function aI(e,t,r){var n;return e.l+=2,[{r:e.read_shift(2),c:255&(n=e.read_shift(2)),fQuoted:!!(16384&n),cRel:n>>15,rRel:n>>15}]}function ak(e){return e.l+=6,[]}function aP(e){return e.l+=2,[ns(e),1&e.read_shift(2)]}var aD=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],aL={1:{n:"PtgExp",f:function(e,t,r){return(e.l++,r&&12==r.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:tQ},3:{n:"PtgAdd",f:a_},4:{n:"PtgSub",f:a_},5:{n:"PtgMul",f:a_},6:{n:"PtgDiv",f:a_},7:{n:"PtgPower",f:a_},8:{n:"PtgConcat",f:a_},9:{n:"PtgLt",f:a_},10:{n:"PtgLe",f:a_},11:{n:"PtgEq",f:a_},12:{n:"PtgGe",f:a_},13:{n:"PtgGt",f:a_},14:{n:"PtgNe",f:a_},15:{n:"PtgIsect",f:a_},16:{n:"PtgUnion",f:a_},17:{n:"PtgRange",f:a_},18:{n:"PtgUplus",f:a_},19:{n:"PtgUminus",f:a_},20:{n:"PtgPercent",f:a_},21:{n:"PtgParen",f:a_},22:{n:"PtgMissArg",f:a_},23:{n:"PtgStr",f:function(e,t,r){return e.l++,nf(e,t-1,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,rP[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,r_(e,8)}},32:{n:"PtgArray",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}},33:{n:"PtgFunc",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[aX[a],aK[a],n]}},34:{n:"PtgFuncVar",f:function(e,t,r){var n=e[e.l++],a=e.read_shift(1),s=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:[e[e.l+1]>>7,32767&e.read_shift(2)];return[a,(0===s[0]?aK:aY)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,s=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[n,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,aC(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,ax(e,r.biff>=2&&r.biff<=5?6:8,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[n,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:tQ},40:{n:"PtgMemNoMem",f:tQ},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}},43:{n:"PtgAreaErr",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}},44:{n:"PtgRefN",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,function(e,t,r){var n,a,s,i,o=r&&r.biff?r.biff:8;if(o>=2&&o<=5)return n=e.read_shift(2),a=e.read_shift(1),s=(32768&n)>>15,i=(16384&n)>>14,n&=16383,1==s&&n>=8192&&(n-=16384),1==i&&a>=128&&(a-=256),{r:n,c:a,cRel:i,rRel:s};var l=e.read_shift(o>=12?4:2),f=e.read_shift(2),c=(16384&f)>>14,h=(32768&f)>>15;if(f&=16383,1==h)for(;l>524287;)l-=1048576;if(1==c)for(;f>8191;)f-=16384;return{r:l,c:f,cRel:c,rRel:h}}(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t,r){if(r.biff<8)return aO(e,t,r);var n=e.read_shift(12==r.biff?4:2),a=e.read_shift(12==r.biff?4:2),s=ay(e,2),i=ay(e,2);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:a,c:i[0],cRel:i[1],rRel:i[2]}}}(e,t-1,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){var n,a,s;return 5==r.biff?(n=e.read_shift(1)>>>5&3,a=e.read_shift(2,"i"),e.l+=8,s=e.read_shift(2),e.l+=12,[n,a,s]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[n,a,aC(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return[n,a,ax(e,s,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[n,a]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[n,a]}},255:{}},aM={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},aF={1:{n:"PtgElfLel",f:aP},2:{n:"PtgElfRw",f:aI},3:{n:"PtgElfCol",f:aI},6:{n:"PtgElfRwV",f:aI},7:{n:"PtgElfColV",f:aI},10:{n:"PtgElfRadical",f:aI},11:{n:"PtgElfRadicalS",f:ak},13:{n:"PtgElfColS",f:ak},15:{n:"PtgElfColSV",f:ak},16:{n:"PtgElfRadicalLel",f:aP},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),s=e.read_shift(2),i=aD[r>>2&31];return{ixti:t,coltype:3&r,rt:i,idx:n,c:a,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},aU={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}},2:{n:"PtgAttrIf",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],s=0;s<=n;++s)a.push(e.read_shift(r&&2==r.biff?1:2));return a}},8:{n:"PtgAttrGoto",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:aR},33:{n:"PtgAttrBaxcel",f:aR},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),aN(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),aN(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function aB(e,t,r,n){if(n.biff<8)return a=t,void(e.l+=a);for(var a,s,i=e.l+t,o=[],l=0;l!==r.length;++l)switch(r[l][0]){case"PtgArray":r[l][1]=function(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--a&&(a=256));for(var s=0,i=[];s!=n&&(i[s]=[]);++s)for(var o=0;o!=a;++o)i[s][o]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=nn(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=rP[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=r_(e,8);break;case 2:r[1]=nu(e,0,{biff:t>0&&t<8?2:t});break;default:throw Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return i}(e,0,n),o.push(r[l][1]);break;case"PtgMemArea":r[l][2]=function(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),a=[],s=0;s!=n;++s)a.push((12==r.biff?rS:nw)(e,8));return a}(e,r[l][1],n),o.push(r[l][2]);break;case"PtgExp":n&&12==n.biff&&(r[l][1][1]=e.read_shift(4),o.push(r[l][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[l][0]}return 0!=(t=i-e.l)&&o.push((s=t,void(e.l+=s))),o}function aW(e,t,r){for(var n,a,s,i=e.l+t,o=[];i!=e.l;)(t=i-e.l,a=aL[s=e[e.l]]||aL[aM[s]],(24===s||25===s)&&(a=(24===s?aF:aU)[e[e.l+1]]),a&&a.f)?o.push([a.n,a.f(e,t,r)]):(n=t,e.l+=n);return o}var aH={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function aG(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:if(null!=r.SID)return e.SheetNames[r.SID];return"SH33TJSSAME"+e[n[0]][0];default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[n[0]][0][3])return"SH33TJSERR2";return a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]}}function aj(e,t,r){var n=aG(e,t,r);return"#REF"==n?n:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(n,r)}function aV(e,t,r,n,a){var s,i,o,l,f=a&&a.biff||8,c={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var E=e[0][v];switch(E[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=e$(" ",e[0][m][1][1]);break;case 1:g=e$("\r",e[0][m][1][1]);break;default:if(g="",a.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+aH[E[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=t4(E[1][1],c,a),h.push(t5(o,f));break;case"PtgRefN":o=r?t4(E[1][1],r,a):E[1][1],h.push(t5(o,f));break;case"PtgRef3d":u=E[1][1],o=t4(E[1][2],c,a),p=aj(n,u,a),h.push(p+"!"+t5(o,f));break;case"PtgFunc":case"PtgFuncVar":var b=E[1][0],w=E[1][1];b||(b=0);var S=0==(b&=127)?[]:h.slice(-b);h.length-=b,"User"===w&&(w=S.shift()),h.push(w+"("+S.join(",")+")");break;case"PtgBool":h.push(E[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(E[1]);break;case"PtgNum":h.push(String(E[1]));break;case"PtgStr":h.push('"'+E[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":l=t3(E[1][1],r?{s:r}:c,a),h.push(t6(l,a));break;case"PtgArea":l=t3(E[1][1],c,a),h.push(t6(l,a));break;case"PtgArea3d":u=E[1][1],l=E[1][2],p=aj(n,u,a),h.push(p+"!"+t6(l,a));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=E[1][2];var A=(n.names||[])[d-1]||(n[0]||[])[d],_=A?A.Name:"SH33TJSNAME"+String(d);_&&"_xlfn."==_.slice(0,6)&&!a.xlfn&&(_=_.slice(6)),h.push(_);break;case"PtgNameX":var y,x=E[1][1];if(d=E[1][2],a.biff<=5)x<0&&(x=-x),n[x]&&(y=n[x][d]);else{var O="";if(14849==((n[x]||[])[0]||[])[0]||(1025==((n[x]||[])[0]||[])[0]?n[x][d]&&n[x][d].itab>0&&(O=n.SheetNames[n[x][d].itab-1]+"!"):O=n.SheetNames[d-1]+"!"),n[x]&&n[x][d])O+=n[x][d].Name;else if(n[0]&&n[0][d])O+=n[0][d].Name;else{var C=(aG(n,x,a)||"").split(";;");C[d-1]?O=C[d-1]:O+="SH33TJSERRX"}h.push(O);break}y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var R="(",N=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:R=e$(" ",e[0][m][1][1])+R;break;case 3:R=e$("\r",e[0][m][1][1])+R;break;case 4:N=e$(" ",e[0][m][1][1])+N;break;case 5:N=e$("\r",e[0][m][1][1])+N;break;default:if(a.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(R+h.pop()+N);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:E[1][1],r:E[1][0]};var I={c:r.c,r:r.r};if(n.sharedf[rr(o)]){var k=n.sharedf[rr(o)];h.push(aV(k,c,I,n,a))}else{var P=!1;for(s=0;s!=n.arrayf.length;++s)if(i=n.arrayf[s],!(o.c<i[0].s.c)&&!(o.c>i[0].e.c)&&!(o.r<i[0].s.r)&&!(o.r>i[0].e.r)){h.push(aV(i[1],c,I,n,a)),P=!0;break}P||h.push(E[1])}break;case"PtgArray":h.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],s=0;s<n.length;++s){var i=n[s];i?2===i[0]?a.push('"'+i[1].replace(/"/g,'""')+'"'):a.push(i[1]):a.push("")}t.push(a.join(","))}return t.join(";")}(E[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+E[1].idx+"[#"+E[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(E))}var D=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=a.biff&&m>=0&&-1==D.indexOf(e[0][v][0])){E=e[0][m];var L=!0;switch(E[1][0]){case 4:L=!1;case 0:g=e$(" ",E[1][1]);break;case 5:L=!1;case 1:g=e$("\r",E[1][1]);break;default:if(g="",a.WTF)throw Error("Unexpected PtgAttrSpaceType "+E[1][0])}h.push((L?g:"")+h.pop()+(L?"":g)),m=-1}}if(h.length>1&&a.WTF)throw Error("bad formula stack");return h[0]}function az(e,t,r){var n=e.l+t,a=nT(e,6);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==tG(e,e.l+6))return[r_(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var n,a,s=e.l+t,i=2==r.biff?1:2,o=e.read_shift(i);if(65535==o)return[[],(n=t-2,void(e.l+=n))];var l=aW(e,o,r);return t!==o+i&&(a=aB(e,t-o-i,l,r)),e.l=s,[l,a]}(e,n-e.l,r);return{cell:a,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function a$(e,t,r){var n=e.read_shift(4),a=aW(e,n,r),s=e.read_shift(4),i=s>0?aB(e,s,a,r):null;return[a,i]}var aY={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},aK={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},aX={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function aq(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function aJ(e){var t=e.split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var aZ="undefined"!=typeof Map;function aQ(e,t,r){var n=0,a=e.length;if(r){if(aZ?r.has(t):Object.prototype.hasOwnProperty.call(r,t)){for(var s=aZ?r.get(t):r[t];n<s.length;++n)if(e[s[n]].t===t)return e.Count++,s[n]}}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(aZ?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function a1(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(n3=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=n6(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=n8(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function a0(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function a2(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],a=60,s=e.length;if(null==n&&r.ssf){for(;a<392;++a)if(null==r.ssf[a]){eA(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}}for(a=0;a!=s;++a)if(e[a].numFmtId===n)return a;return e[s]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}var a4=["objects","scenarios","selectLockedCells","selectUnlockedCells"],a3=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function a5(e,t,r,n){var a,s=[e1,tp("worksheet",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tT.r})],i=r.SheetNames[e],o=0,l="",f=r.Sheets[i];null==f&&(f={});var c=f["!ref"]||"A1",h=rs(c);if(h.e.c>16383||h.e.r>1048575){if(t.WTF)throw Error("Range "+c+" exceeds format limit A1:XFD1048576");h.e.c=Math.min(h.e.c,16383),h.e.r=Math.min(h.e.c,1048575),c=ra(h)}n||(n={}),f["!comments"]=[];var u=[];(function(e,t,r,n,a){var s=!1,i={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var l=t.SheetNames[r];try{t.Workbook&&(l=t.Workbook.Sheets[r].CodeName||l)}catch(e){}s=!0,i.codeName=to(e7(l))}if(e&&e["!outline"]){var f={summaryBelow:1,summaryRight:1};e["!outline"].above&&(f.summaryBelow=0),e["!outline"].left&&(f.summaryRight=0),o=(o||"")+tp("outlinePr",null,f)}(s||o)&&(a[a.length]=tp("sheetPr",o,i))})(f,r,e,t,s),s[s.length]=tp("dimension",null,{ref:c}),s[s.length]=(d={workbookViewId:"0"},(((r||{}).Workbook||{}).Views||[])[0]&&(d.rightToLeft=r.Workbook.Views[0].RTL?"1":"0"),tp("sheetViews",tp("sheetView",null,d),{})),t.sheetFormat&&(s[s.length]=tp("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=f["!cols"]&&f["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,n=["<cols>"],a=0;a!=t.length;++a)(r=t[a])&&(n[n.length]=tp("col",null,a1(a,r)));return n[n.length]="</cols>",n.join("")}(0,f["!cols"])),s[o=s.length]="<sheetData/>",f["!links"]=[],null!=f["!ref"]&&(l=function(e,t,r,n){var a,s,i=[],o=[],l=rs(e["!ref"]),f="",c="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),g={r:c},v=-1;for(d=l.s.c;d<=l.e.c;++d)h[d]=re(d);for(u=l.s.r;u<=l.e.r;++u){for(o=[],c=t7(u),d=l.s.c;d<=l.e.c;++d){a=h[d]+c;var T=m?(e[u]||[])[d]:e[a];void 0!==T&&null!=(f=function(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!=typeof e.f||"z"===e.t&&!e.f)return"";var a="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=rP[e.v];break;case"d":n&&n.cellDates?a=ej(e.v,-1).toISOString():((e=ez(e)).t="n",a=""+(e.v=eL(ej(e.v)))),void 0===e.z&&(e.z=q[14]);break;default:a=e.v}var o=tu("v",e7(a)),l={r:t},f=a2(n.cellXfs,e,n);switch(0!==f&&(l.s=f),e.t){case"n":case"z":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=tu("v",""+aQ(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str"}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var c=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=tp("f",e7(e.f),c)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(l.cm=1),tp("c",o,l)}(T,a,e,t,r,n))&&o.push(f)}(o.length>0||p&&p[u])&&(g={r:c},p&&p[u]&&((s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=n9(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level)),i[i.length]=tp("row",o.join(""),g))}if(p)for(;u<p.length;++u)p&&p[u]&&(g={r:u+1},(s=p[u]).hidden&&(g.hidden=1),v=-1,s.hpx?v=n9(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(g.ht=v,g.customHeight=1),s.level&&(g.outlineLevel=s.level),i[i.length]=tp("row","",g));return i.join("")}(f,t,e,r,n)).length>0&&(s[s.length]=l),s.length>o+1&&(s[s.length]="</sheetData>",s[o]=s[o].replace("/>",">")),f["!protect"]&&(s[s.length]=(p=f["!protect"],m={sheet:1},a4.forEach(function(e){null!=p[e]&&p[e]&&(m[e]="1")}),a3.forEach(function(e){null==p[e]||p[e]||(m[e]="0")}),p.password&&(m.password=nZ(p.password).toString(16).toUpperCase()),tp("sheetProtection",null,m))),null!=f["!autofilter"]&&(s[s.length]=function(e,t,r,n){var a="string"==typeof e.ref?e.ref:ra(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=rn(a);i.s.r==i.e.r&&(i.e.r=rn(t["!ref"]).e.r,a=ra(i));for(var o=0;o<s.length;++o){var l=s[o];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==n){l.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),tp("autoFilter",null,{ref:a})}(f["!autofilter"],f,r,e)),null!=f["!merges"]&&f["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+ra(e[r])+'"/>';return t+"</mergeCells>"}(f["!merges"]));var d,p,m,g,v=-1,T=-1;return f["!links"].length>0&&(s[s.length]="<hyperlinks>",f["!links"].forEach(function(e){e[1].Target&&(g={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(T=rG(n,-1,e7(e[1].Target).replace(/#.*$/,""),rB.HLINK),g["r:id"]="rId"+T),(v=e[1].Target.indexOf("#"))>-1&&(g.location=e7(e[1].Target.slice(v+1))),e[1].Tooltip&&(g.tooltip=e7(e[1].Tooltip)),s[s.length]=tp("hyperlink",null,g))}),s[s.length]="</hyperlinks>"),delete f["!links"],null!=f["!margins"]&&(s[s.length]=(a0(a=f["!margins"]),tp("pageMargins",null,a))),(!t||t.ignoreEC||void 0==t.ignoreEC)&&(s[s.length]=tu("ignoredErrors",tp("ignoredError",null,{numberStoredAsText:1,sqref:c}))),u.length>0&&(T=rG(n,-1,"../drawings/drawing"+(e+1)+".xml",rB.DRAW),s[s.length]=tp("drawing",null,{"r:id":"rId"+T}),f["!drawing"]=u),f["!comments"].length>0&&(T=rG(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",rB.VML),s[s.length]=tp("legacyDrawing",null,{"r:id":"rId"+T}),f["!legacy"]=T),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}var a6=["left","right","top","bottom","header","footer"],a8=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],a7="][*?/\\".split("");function a9(e,t){if(e.length>31){if(t)return!1;throw Error("Sheet names cannot exceed 31 chars")}var r=!0;return a7.forEach(function(n){if(-1!=e.indexOf(n)){if(!t)throw Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}}),r}function se(e){var t=[e1];t[t.length]=tp("workbook",null,{xmlns:"http://schemas.openxmlformats.org/spreadsheetml/2006/main","xmlns:r":tT.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(a8.forEach(function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])}),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=tp("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],s=0;if(a&&a[0]&&a[0].Hidden){for(s=0,t[t.length]="<bookViews>";s!=e.SheetNames.length&&a[s]&&a[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(s=0,t[t.length]="<sheets>";s!=e.SheetNames.length;++s){var i={name:e7(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),a[s])switch(a[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=tp("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach(function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=tp("definedName",e7(e.Ref),r))}),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}var st=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,sr=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function sn(e,t){var r=e.split(/\s+/),n=[];if(t||(n[0]=r[0]),1===r.length)return n;var a,s,i,o=e.match(st);if(o)for(i=0;i!=o.length;++i)-1===(s=(a=o[i].match(sr))[1].indexOf(":"))?n[a[1]]=a[2].slice(1,a[2].length-1):n["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(s+1)]=a[2].slice(1,a[2].length-1);return n}function sa(e){return tp("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+aA(e.Ref,{r:0,c:0})})}var ss={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"},si={0:{f:function(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=n,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=a/20),r}},1:{f:function(e){return[rp(e)]}},2:{f:function(e){return[rp(e),rb(e),"n"]}},3:{f:function(e){return[rp(e),e.read_shift(1),"e"]}},4:{f:function(e){return[rp(e),e.read_shift(1),"b"]}},5:{f:function(e){return[rp(e),r_(e),"n"]}},6:{f:function(e){return[rp(e),rh(e),"str"]}},7:{f:function(e){return[rp(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var n=e.l+t,a=rp(e);a.r=r["!row"];var s=[a,rh(e),"str"];if(r.cellFormula){e.l+=2;var i=a$(e,n-e.l,r);s[3]=aV(i,null,a,r.supbooks,r)}else e.l=n;return s}},9:{f:function(e,t,r){var n=e.l+t,a=rp(e);a.r=r["!row"];var s=[a,r_(e),"n"];if(r.cellFormula){e.l+=2;var i=a$(e,n-e.l,r);s[3]=aV(i,null,a,r.supbooks,r)}else e.l=n;return s}},10:{f:function(e,t,r){var n=e.l+t,a=rp(e);a.r=r["!row"];var s=[a,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=a$(e,n-e.l,r);s[3]=aV(i,null,a,r.supbooks,r)}else e.l=n;return s}},11:{f:function(e,t,r){var n=e.l+t,a=rp(e);a.r=r["!row"];var s=[a,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=a$(e,n-e.l,r);s[3]=aV(i,null,a,r.supbooks,r)}else e.l=n;return s}},12:{f:function(e){return[rg(e)]}},13:{f:function(e){return[rg(e),rb(e),"n"]}},14:{f:function(e){return[rg(e),e.read_shift(1),"e"]}},15:{f:function(e){return[rg(e),e.read_shift(1),"b"]}},16:{f:function(e){return[rg(e),r_(e),"n"]}},17:{f:function(e){return[rg(e),rh(e),"str"]}},18:{f:function(e){return[rg(e),e.read_shift(4),"s"]}},19:{f:rd},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),s=rh(e),i=a$(e,0,r),o=rT(e);e.l=n;var l={Name:s,Ptg:i};return a<268435455&&(l.Sheet=a),o&&(l.Comment=o),l}},40:{},42:{},43:{f:function(e,t,r){var n,a={};a.sz=e.read_shift(2)/20;var s=(n=e.read_shift(1),e.l++,{fBold:1&n,fItalic:2&n,fUnderline:4&n,fStrikeout:8&n,fOutline:16&n,fShadow:32&n,fCondense:64&n,fExtend:128&n});switch(s.fItalic&&(a.italic=1),s.fCondense&&(a.condense=1),s.fExtend&&(a.extend=1),s.fShadow&&(a.shadow=1),s.fOutline&&(a.outline=1),s.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(a.underline=i);var o=e.read_shift(1);o>0&&(a.family=o);var l=e.read_shift(1);switch(l>0&&(a.charset=l),e.l++,a.color=function(e){var t={},r=e.read_shift(1),n=e.read_shift(1),a=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r>>>1){case 0:t.auto=1;break;case 1:t.index=n;var l=rk[n];l&&(t.rgb=n4(l));break;case 2:t.rgb=n4([s,i,o]);break;case 3:t.theme=n}return 0!=a&&(t.tint=a>0?a/32767:a/32768),t}(e,8),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=rh(e,t-21),a}},44:{f:function(e,t){return[e.read_shift(2),rh(e,t-2)]}},45:{f:tQ},46:{f:tQ},47:{f:function(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:nW},62:{f:function(e){return[rp(e),rd(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=rr(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:tQ,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=rh(e,t-19),r}},148:{f:rS,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?rh(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=rT(e,t-8),r.name=rh(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:rS},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:rS},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:rh(e,t-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:rT},357:{},358:{},359:{},360:{T:1},361:{},362:{f:nF},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var n=e.l+t,a=rS(e,16),s=e.read_shift(1),i=[a];if(i[2]=s,r.cellFormula){var o=a$(e,n-e.l,r);i[1]=o}else e.l=n;return i}},427:{f:function(e,t,r){var n=e.l+t,a=[rS(e,16)];if(r.cellFormula){var s=a$(e,n-e.l,r);a[1]=s,e.l=n}else e.l=n;return a}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return a6.forEach(function(r){t[r]=r_(e,8)}),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,n=rS(e,16),a=rT(e),s=rh(e),i=rh(e),o=rh(e);e.l=r;var l={rfx:n,relId:a,loc:s,display:o};return i&&(l.Tooltip=i),l}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:rT},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:rh},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=rS(e,16);return t.rfx=r.s,t.ref=rr(r.s),e.l+=16,t}},636:{T:-1},637:{f:rd},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:rh(e,t-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function so(e,t,r,n){if(!isNaN(t)){var a=n||(r||[]).length||0,s=e.next(4);s.write_shift(2,t),s.write_shift(2,a),a>0&&tW(r)&&e.push(r)}}function sl(e,t,r){return e||(e=t1(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function sf(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];n&&n["!ref"]&&rn(n["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var a=t||{};switch(a.biff||2){case 8:case 5:return function(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=ez(q)),e&&e.SSF&&(ey(),e_(e.SSF),r.revssf=eP(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,sI(r),r.cellXfs=[],a2(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=function(e,t,r){var n,a,s,i,o,l,f,c,h,u=t0(),d=r.SheetNames[e],p=r.Sheets[d]||{},m=(r||{}).Workbook||{},g=(m.Sheets||[])[e]||{},v=Array.isArray(p),T=8==t.biff,E="",b=[],w=rs(p["!ref"]||"A1"),S=T?65536:16384;if(w.e.c>255||w.e.r>=S){if(t.WTF)throw Error("Range "+(p["!ref"]||"A1")+" exceeds format limit A1:IV16384");w.e.c=Math.min(w.e.c,255),w.e.r=Math.min(w.e.c,S-1)}so(u,2057,nC(r,16,t)),so(u,13,ni(1)),so(u,12,ni(100)),so(u,15,na(!0)),so(u,17,na(!1)),so(u,16,ry(.001)),so(u,95,na(!0)),so(u,42,na(!1)),so(u,43,na(!1)),so(u,130,ni(1)),so(u,128,((a=t1(8)).write_shift(4,0),a.write_shift(2,0),a.write_shift(2,0),a)),so(u,131,na(!1)),so(u,132,na(!1)),T&&function(e,t){if(t){var r=0;t.forEach(function(t,n){if(++r<=256&&t){var a,s,i;so(e,125,(a=a1(n,t),(s=t1(12)).write_shift(2,n),s.write_shift(2,n),s.write_shift(2,256*a.width),s.write_shift(2,0),i=0,a.hidden&&(i|=1),s.write_shift(1,i),i=a.level||0,s.write_shift(1,i),s.write_shift(2,0),s))}})}}(u,p["!cols"]),so(u,512,((i=t1(2*(s=8!=t.biff&&t.biff?2:4)+6)).write_shift(s,w.s.r),i.write_shift(s,w.e.r+1),i.write_shift(2,w.s.c),i.write_shift(2,w.e.c+1),i.write_shift(2,0),i)),T&&(p["!links"]=[]);for(var A=w.s.r;A<=w.e.r;++A){E=t7(A);for(var _=w.s.c;_<=w.e.c;++_){A===w.s.r&&(b[_]=re(_)),h=b[_]+E;var y=v?(p[A]||[])[_]:p[h];y&&(function(e,t,r,n,a){var s=16+a2(a.cellXfs,t,a);if(null==t.v&&!t.bf){so(e,513,nE(r,n,s));return}if(t.bf)so(e,6,function(e,t,r,n,a){var s=nE(t,r,a),i=function(e){if(null==e){var t=t1(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return"number"==typeof e?ry(e):ry(0)}(e.v),o=t1(6);o.write_shift(2,33),o.write_shift(4,0);for(var l=t1(e.bf.length),f=0;f<e.bf.length;++f)l[f]=e.bf[f];return U([s,i,o,l])}(t,r,n,0,s));else switch(t.t){case"d":case"n":var i,o="d"==t.t?eL(ej(t.v)):t.v;so(e,515,(nE(r,n,s,i=t1(14)),ry(o,i),i));break;case"b":case"e":so(e,517,(l=t.v,f=t.t,nE(r,n,s,c=t1(8)),nl(l,f,c),c));break;case"s":case"str":if(a.bookSST){var l,f,c,h,u,d,p,m=aQ(a.Strings,t.v,a.revStrings);so(e,253,(nE(r,n,s,p=t1(10)),p.write_shift(4,m),p))}else so(e,516,(h=(t.v||"").slice(0,255),nE(r,n,s,d=t1(8+ +(u=!a||8==a.biff)+(1+u)*h.length)),d.write_shift(2,h.length),u&&d.write_shift(1,1),d.write_shift((1+u)*h.length,h,u?"utf16le":"sbcs"),d));break;default:so(e,513,nE(r,n,s))}}(u,y,A,_,t),T&&y.l&&p["!links"].push([h,y.l]))}}var x=g.CodeName||g.name||d;return T&&so(u,574,(o=(m.Views||[])[0],l=t1(18),f=1718,o&&o.RTL&&(f|=64),l.write_shift(2,f),l.write_shift(4,0),l.write_shift(4,64),l.write_shift(4,0),l.write_shift(4,0),l)),T&&(p["!merges"]||[]).length&&so(u,229,function(e){var t=t1(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)nS(e[r],t);return t}(p["!merges"])),T&&function(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];so(e,440,function(e){var t=t1(24),r=rt(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return U([t,function(e){var t=t1(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var a=n.indexOf("#"),s=a>-1?31:23;switch(n.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)nm(n=n.slice(1),t);else if(2&s){for(r=0,i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&nm(a>-1?n.slice(a+1):"",t)}else{for(r=0,i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" ");r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var l=0;"../"==n.slice(3*l,3*l+3)||"..\\"==n.slice(3*l,3*l+3);)++l;for(t.write_shift(2,l),t.write_shift(4,n.length-3*l+1),r=0;r<n.length-3*l;++r)t.write_shift(1,255&n.charCodeAt(r+3*l));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}(n)),n[1].Tooltip&&so(e,2048,function(e){var t=e[1].Tooltip,r=t1(10+2*(t.length+1));r.write_shift(2,2048);var n=rt(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}(n))}delete t["!links"]}(u,p),so(u,442,nd(x,t)),T&&((c=t1(19)).write_shift(4,2151),c.write_shift(4,0),c.write_shift(4,0),c.write_shift(2,3),c.write_shift(1,1),c.write_shift(4,0),so(u,2151,c),(c=t1(39)).write_shift(4,2152),c.write_shift(4,0),c.write_shift(4,0),c.write_shift(2,3),c.write_shift(1,0),c.write_shift(4,0),c.write_shift(2,1),c.write_shift(4,4),c.write_shift(2,0),nS(rs(p["!ref"]||"A1"),c),c.write_shift(4,4),so(u,2152,c)),so(u,10),u.end()}(a,r,e);return n.unshift(function(e,t,r){var n,a,s,i,o,l,f,c=t0(),h=(e||{}).Workbook||{},u=h.Sheets||[],d=h.WBProps||{},p=8==r.biff,m=5==r.biff;so(c,2057,nC(e,5,r)),"xla"==r.bookType&&so(c,135),so(c,225,p?ni(1200):null),so(c,193,function(e,t){t||(t=t1(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}(0)),m&&so(c,191),m&&so(c,192),so(c,226),so(c,92,function(e,t){var r=!t||8==t.biff,n=t1(r?112:54);for(n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}(0,r)),so(c,66,ni(p?1200:1252)),p&&so(c,353,ni(0)),p&&so(c,448),so(c,317,function(e){for(var t=t1(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),p&&e.vbaraw&&so(c,211),p&&e.vbaraw&&so(c,442,nd(d.CodeName||"ThisWorkbook",r)),so(c,156,ni(17)),so(c,25,na(!1)),so(c,18,na(!1)),so(c,19,ni(0)),p&&so(c,431,na(!1)),p&&so(c,444,ni(0)),so(c,61,((n=t1(18)).write_shift(2,0),n.write_shift(2,0),n.write_shift(2,29280),n.write_shift(2,17600),n.write_shift(2,56),n.write_shift(2,0),n.write_shift(2,0),n.write_shift(2,1),n.write_shift(2,500),n)),so(c,64,na(!1)),so(c,141,ni(0)),so(c,34,na("true"==(e.Workbook&&e.Workbook.WBProps&&tt(e.Workbook.WBProps.date1904)?"true":"false"))),so(c,14,na(!0)),p&&so(c,439,na(!1)),so(c,218,ni(0)),so(c,49,(s=(a={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(o=t1((i=r&&5==r.biff)?15+s.length:16+2*s.length)).write_shift(2,20*(a.sz||12)),o.write_shift(4,0),o.write_shift(2,400),o.write_shift(4,0),o.write_shift(2,0),o.write_shift(1,s.length),i||o.write_shift(1,1),o.write_shift((i?1:2)*s.length,s,i?"sbcs":"utf16le"),o)),(l=e.SSF)&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=l[t]&&so(c,1054,function(e,t,r,n){var a=r&&5==r.biff;n||(n=t1(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var s=n.length>n.l?n.slice(0,n.l):n;return null==s.l&&(s.l=s.length),s}(t,l[t],r))}),function(e,t){for(var r=0;r<16;++r)so(e,224,nI({numFmtId:0,style:!0},0,t));t.cellXfs.forEach(function(r){so(e,224,nI(r,0,t))})}(c,r),p&&so(c,352,na(!1));var g=c.end(),v=t0();p&&so(v,140,(f||(f=t1(4)),f.write_shift(2,1),f.write_shift(2,1),f)),p&&r.Strings&&function(e,t,r,n){var a=(r||[]).length||0;if(a<=8224)return so(e,252,r,a);if(!isNaN(252)){for(var s=r.parts||[],i=0,o=0,l=0;l+(s[i]||8224)<=8224;)l+=s[i]||8224,i++;var f=e.next(4);for(f.write_shift(2,252),f.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l;o<a;){for((f=e.next(4)).write_shift(2,60),l=0;l+(s[i]||8224)<=8224;)l+=s[i]||8224,i++;f.write_shift(2,l),e.push(r.slice(o,o+l)),o+=l}}}(v,252,function(e,t){var r=t1(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var n=[],a=0;a<e.length;++a)n[a]=function(e){var t=e.t||"",r=t1(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=t1(2*t.length);return n.write_shift(2*t.length,t,"utf16le"),U([r,n])}(e[a],t);var s=U([r].concat(n));return s.parts=[r.length].concat(n.map(function(e){return e.length})),s}(r.Strings,r)),so(v,10);var T=v.end(),E=t0(),b=0,w=0;for(w=0;w<e.SheetNames.length;++w)b+=(p?12:11)+(p?2:1)*e.SheetNames[w].length;var S=g.length+b+T.length;for(w=0;w<e.SheetNames.length;++w)so(E,133,function(e,t){var r=!t||t.biff>=8?2:1,n=t1(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}({pos:S,hs:(u[w]||{}).Hidden||0,dt:0,name:e.SheetNames[w]},r)),S+=t[w].length;var A=E.end();if(b!=A.length)throw Error("BS8 "+b+" != "+A.length);var _=[];return g.length&&_.push(g),A.length&&_.push(A),T.length&&_.push(T),U(_)}(e,n,r)),U(n)}(e,t);case 4:case 3:case 2:return function(e,t){for(var r=t||{},n=t0(),a=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(a=s);if(0==a&&r.sheet&&e.SheetNames[0]!=r.sheet)throw Error("Sheet not found: "+r.sheet);return so(n,4==r.biff?1033:3==r.biff?521:9,nC(e,16,r)),function(e,t,r,n){var a,s=Array.isArray(t),i=rs(t["!ref"]||"A1"),o="",l=[];if(i.e.c>255||i.e.r>16383){if(n.WTF)throw Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),a=ra(i)}for(var f=i.s.r;f<=i.e.r;++f){o=t7(f);for(var c=i.s.c;c<=i.e.c;++c){f===i.s.r&&(l[c]=re(c)),a=l[c]+o;var h=s?(t[f]||[])[c]:t[a];h&&function(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a,s,i,o,l,f,c,h="d"==t.t?eL(ej(t.v)):t.v;h==(0|h)&&h>=0&&h<65536?so(e,2,(sl(f=t1(9),r,n),f.write_shift(2,h),f)):so(e,3,(sl(c=t1(15),r,n),c.write_shift(8,h,"f"),c));return;case"b":case"e":so(e,5,(a=t.v,s=t.t,sl(i=t1(9),r,n),nl(a,s||"b",i),i));return;case"s":case"str":so(e,4,(sl(l=t1(8+2*(o=(t.v||"").slice(0,255)).length),r,n),l.write_shift(1,o.length),l.write_shift(o.length,o,"sbcs"),l.l<l.length?l.slice(0,l.l):l));return}so(e,1,sl(null,r,n))}(e,h,f,c,n)}}}(n,e.Sheets[e.SheetNames[a]],0,r,e),so(n,10),n.end()}(e,t)}throw Error("invalid type "+a.bookType+" for BIFF")}function sc(e,t){var r=t||{},n=r.dense?[]:{},a=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!a)throw Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=a.index,o=s&&s.index||e.length,l=(null)(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),f=-1,c=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<l.length;++i){var m=l[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++f,r.sheetRows&&r.sheetRows<=f){--f;break}c=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var T=v[o].trim();if(T.match(/<t[dh]/i)){for(var E=T,b=0;"<"==E.charAt(0)&&(b=E.indexOf(">"))>-1;)E=E.slice(b+1);for(var w=0;w<p.length;++w){var S=p[w];S.s.c==c&&S.s.r<f&&f<=S.e.r&&(c=S.e.c+1,w=-1)}var A=e3(T.slice(0,T.indexOf(">")));u=A.colspan?+A.colspan:1,((h=+A.rowspan)>1||u>1)&&p.push({s:{r:f,c:c},e:{r:f+(h||1)-1,c:c+u-1}});var _=A.t||A["data-t"]||"";if(!E.length||(E=tl(E),d.s.r>f&&(d.s.r=f),d.e.r<f&&(d.e.r=f),d.s.c>c&&(d.s.c=c),d.e.c<c&&(d.e.c=c),!E.length)){c+=u;continue}var y={t:"s",v:E};!r.raw&&E.trim().length&&"s"!=_&&("TRUE"===E?y={t:"b",v:!0}:"FALSE"===E?y={t:"b",v:!1}:isNaN(eY(E))?isNaN(eX(E).getDate())||(y={t:"d",v:ej(E)},r.cellDates||(y={t:"n",v:eL(y.v)}),y.z=r.dateNF||q[14]):y={t:"n",v:eY(E)}),r.dense?(n[f]||(n[f]=[]),n[f][c]=y):n[rr({r:f,c:c})]=y,c+=u}}}}return n["!ref"]=ra(d),p.length&&(n["!merges"]=p),n}var sh={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']},su=function(){var e="<office:document-styles "+td({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+'><office:master-styles><style:master-page style:name="mp1" style:page-layout-name="mp1"><style:header/><style:header-left style:display="false"/><style:footer/><style:footer-left style:display="false"/></style:master-page></office:master-styles></office:document-styles>';return function(){return e1+e}}(),sd=function(){var e="          <table:table-cell />\n",t=function(t,r,n){var a=[];a.push('      <table:table table:name="'+e7(r.SheetNames[n])+'" table:style-name="ta1">\n');var s=0,i=0,o=rn(t["!ref"]||"A1"),l=t["!merges"]||[],f=0,c=Array.isArray(t);if(t["!cols"])for(i=0;i<=o.e.c;++i)a.push("        <table:table-column"+(t["!cols"][i]?' table:style-name="co'+t["!cols"][i].ods+'"':"")+"></table:table-column>\n");var h="",u=t["!rows"]||[];for(s=0;s<o.s.r;++s)h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",a.push("        <table:table-row"+h+"></table:table-row>\n");for(;s<=o.e.r;++s){for(h=u[s]?' table:style-name="ro'+u[s].ods+'"':"",a.push("        <table:table-row"+h+">\n"),i=0;i<o.s.c;++i)a.push(e);for(;i<=o.e.c;++i){var d=!1,p={},m="";for(f=0;f!=l.length;++f)if(!(l[f].s.c>i)&&!(l[f].s.r>s)&&!(l[f].e.c<i)&&!(l[f].e.r<s)){(l[f].s.c!=i||l[f].s.r!=s)&&(d=!0),p["table:number-columns-spanned"]=l[f].e.c-l[f].s.c+1,p["table:number-rows-spanned"]=l[f].e.r-l[f].s.r+1;break}if(d){a.push("          <table:covered-table-cell/>\n");continue}var g=rr({r:s,c:i}),v=c?(t[s]||[])[i]:t[g];if(v&&v.f&&(p["table:formula"]=e7(("of:="+v.f.replace(aS,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var T=rn(v.F);p["table:number-matrix-columns-spanned"]=T.e.c-T.s.c+1,p["table:number-matrix-rows-spanned"]=T.e.r-T.s.r+1}if(!v){a.push(e);continue}switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||ej(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=ej(v.v).toISOString(),p["table:style-name"]="ce1";break;default:a.push(e);continue}var E=e7(m).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var b=v.l.Target;"#"==(b="#"==b.charAt(0)?"#"+b.slice(1).replace(/\./,"!"):b).charAt(0)||b.match(/^\w+:/)||(b="../"+b),E=tp("text:a",E,{"xlink:href":b.replace(/&/g,"&amp;")})}a.push("          "+tp("table:table-cell",tp("text:p",E,{}),p)+"\n")}a.push("        </table:table-row>\n")}return a.push("      </table:table>\n"),a.join("")},r=function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!cols"]){for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var a=t["!cols"][n];if(null==a.width&&null==a.wpx&&null==a.wch)continue;n7(a),a.ods=r;var s=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}});var n=0;t.SheetNames.map(function(e){return t.Sheets[e]}).forEach(function(t){if(t&&t["!rows"]){for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var a=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+a+'"/>\n'),e.push("  </style:style>\n"),++n}}}),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,n){var a=[e1],s=td({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=td({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==n.bookType?(a.push("<office:document"+s+i+">\n"),a.push(rV().replace(/office:document-meta/g,"office:meta"))):a.push("<office:document-content"+s+">\n"),r(a,e),a.push("  <office:body>\n"),a.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)a.push(t(e.Sheets[e.SheetNames[o]],e,o,n));return a.push("    </office:spreadsheet>\n"),a.push("  </office:body>\n"),"fods"==n.bookType?a.push("</office:document>"):a.push("</office:document-content>"),a.join("")}}();function sp(e,t){if("fods"==t.bookType)return sd(e,t);var r=eQ(),n="",a=[],s=[];return eZ(r,n="mimetype","application/vnd.oasis.opendocument.spreadsheet"),eZ(r,n="content.xml",sd(e,t)),a.push([n,"text/xml"]),s.push([n,"ContentFile"]),eZ(r,n="styles.xml",su(e,t)),a.push([n,"text/xml"]),s.push([n,"StylesFile"]),eZ(r,n="meta.xml",e1+rV()),a.push([n,"text/xml"]),s.push([n,"MetadataFile"]),eZ(r,n="manifest.rdf",function(e){var t=[e1];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(rj(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(rj("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(s)),a.push([n,"application/rdf+xml"]),eZ(r,n="META-INF/manifest.xml",function(e){var t=[e1];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(a)),r}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function sm(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function sg(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):ti(F(e))}function sv(e){var t=new Uint8Array(e.reduce(function(e,t){return e+t.length},0)),r=0;return e.forEach(function(e){t.set(e,r),r+=e.length}),t}function sT(e,t){var r=t?t[0]:0,n=127&e[r];t:if(e[r++]>=128&&(n|=(127&e[r])<<7,e[r++]<128||(n|=(127&e[r])<<14,e[r++]<128)||(n|=(127&e[r])<<21,e[r++]<128)||(n+=(127&e[r])*268435456,++r,e[r++]<128)||(n+=(127&e[r])*34359738368,++r,e[r++]<128)||(n+=(127&e[r])*4398046511104,++r,e[r++]<128)))break t;return t&&(t[0]=r),n}function sE(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;r:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383||(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)||(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)||(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)||(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103))break r;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function sb(e){var t=0,r=127&e[0];t:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128||(r|=(127&e[t])<<14,e[t++]<128)||(r|=(127&e[t])<<21,e[t++]<128))break t;r|=(127&e[t])<<28}return r}function sw(e){for(var t=[],r=[0];r[0]<e.length;){var n,a=r[0],s=sT(e,r),i=7&s,o=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var l=r[0];e[r[0]++]>=128;);n=e.slice(l,r[0]);break;case 5:o=4,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=sT(e,r),n=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(a))}var f={data:n,type:i};null==t[s]?t[s]=[f]:t[s].push(f)}return t}function sS(e){var t=[];return e.forEach(function(e,r){e.forEach(function(e){e.data&&(t.push(sE(8*r+e.type)),2==e.type&&t.push(sE(e.data.length)),t.push(e.data))})}),sv(t)}function sA(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=sT(e,n),s=sw(e.slice(n[0],n[0]+a));n[0]+=a;var i={id:sb(s[1][0].data),messages:[]};s[2].forEach(function(t){var r=sw(t.data),a=sb(r[3][0].data);i.messages.push({meta:r,data:e.slice(n[0],n[0]+a)}),n[0]+=a}),(null==(t=s[3])?void 0:t[0])&&(i.merge=sb(s[3][0].data)>>>0>0),r.push(i)}return r}function s_(e){var t=[];return e.forEach(function(e){var r=[];r[1]=[{data:sE(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:sE(+!!e.merge),type:0}]);var n=[];e.messages.forEach(function(e){n.push(e.data),e.meta[3]=[{type:0,data:sE(e.data.length)}],r[2].push({data:sS(e.meta),type:2})});var a=sS(r);t.push(sE(a.length)),t.push(a),n.forEach(function(e){return t.push(e)})}),sv(t)}function sy(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(function(e,t){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=sT(t,r),a=[];r[0]<t.length;){var s=3&t[r[0]];if(0==s){var i=t[r[0]++]>>2;if(i<60)++i;else{var o=i-59;i=t[r[0]],o>1&&(i|=t[r[0]+1]<<8),o>2&&(i|=t[r[0]+2]<<16),o>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=o}a.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}var l=0,f=0;if(1==s?(f=(t[r[0]]>>2&7)+4,l=(224&t[r[0]++])<<3|t[r[0]++]):(f=(t[r[0]++]>>2)+1,2==s?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[sv(a)],0==l)throw Error("Invalid offset 0");if(l>a[0].length)throw Error("Invalid offset beyond length");if(f>=l)for(a.push(a[0].slice(-l)),f-=l;f>=a[a.length-1].length;)a.push(a[a.length-1]),f-=a[a.length-1].length;a.push(a[0].slice(-l,-l+f))}var c=sv(a);if(c.length!=n)throw Error("Unexpected length: ".concat(c.length," != ").concat(n));return c}(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw Error("data is not a valid framed stream!");return sv(t)}function sx(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var s=sE(n),i=s.length;t.push(s),n<=60?(i++,t.push(new Uint8Array([n-1<<2]))):n<=256?(i+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(i+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(i+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(i+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),i+=n,a[0]=0,a[1]=255&i,a[2]=i>>8&255,a[3]=i>>16&255,r+=n}return sv(t)}function sO(e,t){var r=new Uint8Array(32),n=sm(r),a=12,s=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var s=0;a>=1;++s,a/=256)e[t+s]=255&a;e[t+15]|=r>=0?0:128}(r,a,e.v),s|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),s|=2,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),s|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,s,!0),r.slice(0,a)}function sC(e,t){var r=new Uint8Array(32),n=sm(r),a=12,s=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),s|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),s|=32,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),s|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,s,!0),r.slice(0,a)}function sR(e){return sT(sw(e)[1][0].data)}function sN(e){return function(t){for(var r=0;r!=e.length;++r){var n=e[r];void 0===t[n[0]]&&(t[n[0]]=n[1]),"n"===n[2]&&(t[n[0]]=Number(t[n[0]]))}}}function sI(e){sN([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function sk(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return eR(t.file,eC.write(e,{type:I?"buffer":""}));case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");default:throw Error("Unrecognized type "+t.type)}return eC.write(e,t)}function sP(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return R(to(n));case"binary":return to(n);case"string":return e;case"file":return eR(t.file,n,"utf8");case"buffer":if(I)return k(n,"utf8");if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(n);return sP(n,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}function sD(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?R(r):"string"==t.type?ti(r):r;case"file":return eR(t.file,e);case"buffer":return e;default:throw Error("Unrecognized type "+t.type)}}function sL(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,a=1,s=[],i=0,o="",l={s:{r:0,c:0},e:{r:0,c:0}},f=t||{},c=null!=f.range?f.range:e["!ref"];switch(1===f.header?n=1:"A"===f.header?n=2:Array.isArray(f.header)?n=3:null==f.header&&(n=0),typeof c){case"string":l=rs(c);break;case"number":(l=rs(e["!ref"])).s.r=c;break;default:l=c}n>0&&(a=0);var h=t7(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=l.s.r,T=0,E={};g&&!e[v]&&(e[v]=[]);var b=f.skipHidden&&e["!cols"]||[],w=f.skipHidden&&e["!rows"]||[];for(T=l.s.c;T<=l.e.c;++T)if(!(b[T]||{}).hidden)switch(u[T]=re(T),r=g?e[v][T]:e[u[T]+h],n){case 1:s[T]=T-l.s.c;break;case 2:s[T]=u[T];break;case 3:s[T]=f.header[T-l.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=ro(r,null,f),m=E[i]||0){do o=i+"_"+m++;while(E[o]);E[i]=m,E[o]=1}else E[i]=1;s[T]=o}for(v=l.s.r+a;v<=l.e.r;++v)if(!(w[v]||{}).hidden){var S=function(e,t,r,n,a,s,i,o){var l=t7(r),f=o.defval,c=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===a?[]:{};if(1!==a){if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r}if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[n[d]+l];if(void 0===p||void 0===p.t){if(void 0===f)continue;null!=s[d]&&(u[s[d]]=f);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m){if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==f)u[s[d]]=f;else{if(!c||null!==m)continue;u[s[d]]=null}}else u[s[d]]=c&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:ro(p,m,o);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,l,v,u,n,s,g,f);(!1===S.isempty||(1===n?!1!==f.blankrows:f.blankrows))&&(d[p++]=S.row)}return d.length=p,d}var sM=/"/g;function sF(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=rs(e["!ref"]),s=void 0!==n.FS?n.FS:",",i=s.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),f=RegExp(("|"==s?"\\|":s)+"+$"),c="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(u[p]||{}).hidden||(h[p]=re(p));for(var m=0,g=a.s.r;g<=a.e.r;++g)if(!(d[g]||{}).hidden){if(null==(c=function(e,t,r,n,a,s,i,o){for(var l=!0,f=[],c="",h=t7(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var d=o.dense?(e[r]||[])[u]:e[n[u]+h];if(null==d)c="";else if(null!=d.v){l=!1,c=""+(o.rawNumbers&&"n"==d.t?d.v:ro(d,null,o));for(var p=0,m=0;p!==c.length;++p)if((m=c.charCodeAt(p))===a||m===s||34===m||o.forceQuotes){c='"'+c.replace(sM,'""')+'"';break}"ID"==c&&(c='"ID"')}else null==d.f||d.F?c="":(l=!1,(c="="+d.f).indexOf(",")>=0&&(c='"'+c.replace(sM,'""')+'"'));f.push(c)}return!1===o.blankrows&&l?null:f.join(i)}(e,a,g,h,i,l,s,n)))continue;n.strip&&(c=c.replace(f,"")),(c||!1!==n.blankrows)&&r.push((m++?o:"")+c)}return delete n.dense,r.join("")}function sU(){return{SheetNames:[],Sheets:{}}}function sB(e,t,r,n){var a=1;if(!r)for(;a<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+a);++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);a=s&&+s[2]||0;var i=s&&s[1]||r;for(++a;a<=65535&&-1!=e.SheetNames.indexOf(r=i+a);++a);}if(a9(r),e.SheetNames.indexOf(r)>=0)throw Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}var sW={aoa_to_sheet:rf,book_new:sU,book_append_sheet:sB};function sH(){let e=(0,c.useRouter)(),[t,r]=(0,f.useState)(!1),[n,s]=(0,f.useState)(null);async function i(t){if(t.preventDefault(),n){r(!0);try{let t=new FormData;t.append("file",n);let r=await fetch("/api/admin/cards/import",{method:"POST",body:t});if(!r.ok){let e=await r.text();throw Error(e||"Failed to import cards")}let a=await r.json();m.A.success(`Import completed:
        • Total records: ${a.total}
        • Successfully created: ${a.created}
        • Duplicates in input: ${a.duplicatesInInput}
        • Already in database: ${a.existingInDB}`,{duration:5e3}),e.refresh(),e.push("/admin/cards")}catch(e){console.error(e),m.A.error(e instanceof Error?e.message:"Something went wrong")}finally{r(!1)}}}return(0,l.jsxs)("form",{onSubmit:i,className:"space-y-8",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx(d._,{htmlFor:"file",children:"Import File"}),l.jsx(u.I,{id:"file",type:"file",accept:".csv,.xlsx,.xls",onChange:e=>s(e.target.files?.[0]||null),required:!0}),l.jsx("p",{className:"text-sm text-muted-foreground",children:'Support CSV or Excel file. The file should have a header row with "UID" column. Duplicate UIDs and existing UIDs will be skipped automatically.'})]}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsxs)(h.Button,{type:"submit",disabled:t,children:[t&&l.jsx(p.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Import Cards"]}),(0,l.jsxs)(h.Button,{type:"button",variant:"outline",onClick:()=>{let e=new Blob(["UID\n"],{type:"text/csv"}),t=window.URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download="cards-template.csv",r.click(),window.URL.revokeObjectURL(t)},children:[l.jsx(p.P.download,{className:"mr-2 h-4 w-4"}),"Download CSV Template"]}),(0,l.jsxs)(h.Button,{type:"button",variant:"outline",onClick:()=>{var e,t;let r=sW.book_new(),n=sW.aoa_to_sheet([["UID"]]);sW.book_append_sheet(r,n,"Cards"),e="cards-template.xlsx",(t={}).type="file",t.file=e,function(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType=({xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"})[e.bookType]||e.bookType}}(t),function e(t,r){S(1200),w(1252),function(e){if(!e||!e.SheetNames||!e.Sheets)throw Error("Invalid Workbook");if(!e.SheetNames.length)throw Error("Workbook is empty");var t=e.Workbook&&e.Workbook.Sheets||[];!function(e,t,r){e.forEach(function(n,a){a9(n);for(var s=0;s<a;++s)if(n==e[s])throw Error("Duplicate Sheet Name: "+n);if(r){var i=t&&t[a]&&t[a].CodeName||n;if(95==i.charCodeAt(0)&&i.length>22)throw Error("Bad Code Name: Worksheet"+i)}})}(e.SheetNames,t,!!e.vbaraw);for(var r=0;r<e.SheetNames.length;++r)!function(e,t,r){if(e&&e["!ref"]){var n=rs(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw Error("Bad range ("+r+"): "+e["!ref"])}}(e.Sheets[e.SheetNames[r]],e.SheetNames[r],r)}(t);var n,s,i=ez(r||{});if(i.cellStyles&&(i.cellNF=!0,i.sheetStubs=!0),"array"==i.type){i.type="binary";var o=e(t,i);return i.type="array",M(o)}var l=0;if(i.sheet&&(l="number"==typeof i.sheet?i.sheet:t.SheetNames.indexOf(i.sheet),!t.SheetNames[l]))throw Error("Sheet not found: "+i.sheet+" : "+typeof i.sheet);switch(i.bookType||"xlsb"){case"xml":case"xlml":return sP(function(e,t){t||(t={}),e.SSF||(e.SSF=ez(q)),e.SSF&&(ey(),e_(e.SSF),t.revssf=eP(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],a2(t.cellXfs,{},{revssf:{General:0}}));var r,n,a,s,i,o,l,f,c,h,u,d=[];d.push((r=e,n=t,h=[],r.Props&&h.push((a=r.Props,s=[],eN(rZ).map(function(e){for(var t=0;t<rz.length;++t)if(rz[t][1]==e)return rz[t];for(t=0;t<rK.length;++t)if(rK[t][1]==e)return rK[t];throw e}).forEach(function(e){if(null!=a[e[1]]){var t=n&&n.Props&&null!=n.Props[e[1]]?n.Props[e[1]]:a[e[1]];"date"===e[2]&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof t?t=String(t):!0===t||!1===t?t=t?"1":"0":t instanceof Date&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"")),s.push(tu(rZ[e[1]]||e[1],t))}}),tp("DocumentProperties",s.join(""),{xmlns:tE.o}))),r.Custprops&&h.push((i=r.Props,o=r.Custprops,l=["Worksheets","SheetNames"],f="CustomDocumentProperties",c=[],i&&eN(i).forEach(function(e){if(Object.prototype.hasOwnProperty.call(i,e)){for(var t=0;t<rz.length;++t)if(e==rz[t][1])return;for(t=0;t<rK.length;++t)if(e==rK[t][1])return;for(t=0;t<l.length;++t)if(e==l[t])return;var r=i[e],n="string";"number"==typeof r?(n="float",r=String(r)):!0===r||!1===r?(n="boolean",r=r?"1":"0"):r=String(r),c.push(tp(e9(e),r,{"dt:dt":n}))}}),o&&eN(o).forEach(function(e){if(Object.prototype.hasOwnProperty.call(o,e)&&!(i&&Object.prototype.hasOwnProperty.call(i,e))){var t=o[e],r="string";"number"==typeof t?(r="float",t=String(t)):!0===t||!1===t?(r="boolean",t=t?"1":"0"):t instanceof Date?(r="dateTime.tz",t=t.toISOString()):t=String(t),c.push(tp(e9(e),t,{"dt:dt":r}))}}),"<"+f+' xmlns="'+tE.o+'">'+c.join("")+"</"+f+">")),h.join(""))),d.push(""),d.push(""),d.push("");for(var p=0;p<e.SheetNames.length;++p)d.push(tp("Worksheet",function(e,t,r){var n=[],a=r.SheetNames[e],s=r.Sheets[a],i=s?function(e,t,r,n){if(!e||!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,s=[],i=0;i<a.length;++i){var o=a[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(sa(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&n.push("<Names>"+i+"</Names>"),(i=s?function(e,t,r,n){if(!e["!ref"])return"";var a=rs(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach(function(e,t){n7(e);var r=!!e.width,n=a1(t,e),a={"ss:Index":t+1};r&&(a["ss:Width"]=n5(n.width)),e.hidden&&(a["ss:Hidden"]="1"),o.push(tp("Column",null,a))});for(var l=Array.isArray(e),f=a.s.r;f<=a.e.r;++f){for(var c=[function(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=96*t.hpt/96),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}(f,(e["!rows"]||[])[f])],h=a.s.c;h<=a.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h)&&!(s[i].s.r>f)&&!(s[i].e.c<h)&&!(s[i].e.r<f)){(s[i].s.c!=h||s[i].s.r!=f)&&(u=!0);break}if(!u){var d={r:f,c:h},p=rr(d),m=l?(e[f]||[])[h]:e[p];c.push(function(e,t,r,n,a,s,i){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+e7(aA(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var l=rt(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(l.r==i.r?"":"["+(l.r-i.r)+"]")+"C"+(l.c==i.c?"":"["+(l.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=e7(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=e7(e.l.Tooltip))),r["!merges"])for(var f=r["!merges"],c=0;c!=f.length;++c)f[c].s.c==i.c&&f[c].s.r==i.r&&(f[c].e.c>f[c].s.c&&(o["ss:MergeAcross"]=f[c].e.c-f[c].s.c),f[c].e.r>f[c].s.r&&(o["ss:MergeDown"]=f[c].e.r-f[c].s.r));var h="",u="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=rP[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||q[14]);break;case"s":h="String",u=((e.v||"")+"").replace(e6,function(e){return e5[e]}).replace(te,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}var d=a2(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map(function(e){var t=tp("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return tp("Comment",t,{"ss:Author":e.a})}).join("")),tp("Cell",m,o)}(m,p,e,t,0,0,d))}}c.push("</Row>"),c.length>2&&o.push(c.join(""))}return o.join("")}(s,t,0,0):"").length>0&&n.push("<Table>"+i+"</Table>"),n.push(function(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(tp("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(tp("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(tp("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r]){if(n.Workbook.Sheets[r].Hidden)a.push(tp("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!n.Workbook.Sheets[s]||n.Workbook.Sheets[s].Hidden);++s);s==r&&a.push("<Selected/>")}}return(((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(tu("ProtectContents","True")),e["!protect"].objects&&a.push(tu("ProtectObjects","True")),e["!protect"].scenarios&&a.push(tu("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||a.push(tu("EnableSelection","UnlockedCells")):a.push(tu("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(t){e["!protect"][t[0]]&&a.push("<"+t[1]+"/>")})),0==a.length)?"":tp("WorksheetOptions",a.join(""),{xmlns:tE.x})}(s,0,e,r)),n.join("")}(p,t,e),{"ss:Name":e7(e.SheetNames[p])}));return d[2]=(u=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],t.cellXfs.forEach(function(e,t){var r=[];r.push(tp("NumberFormat",null,{"ss:Format":e7(q[e.numFmtId])})),u.push(tp("Style",r.join(""),{"ss:ID":"s"+(21+t)}))}),tp("Styles",u.join(""))),d[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(sa(a)))}return tp("Names",r.join(""))}(e,t),e1+tp("Workbook",d.join(""),{xmlns:tE.ss,"xmlns:o":tE.o,"xmlns:x":tE.x,"xmlns:ss":tE.ss,"xmlns:dt":tE.dt,"xmlns:html":tE.html})}(t,i),i);case"slk":case"sylk":return sP(nG.from_sheet(t.Sheets[t.SheetNames[l]],i),i);case"htm":case"html":return sP(function(e,t){var r=t||{},n=null!=r.header?r.header:'<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',a=null!=r.footer?r.footer:"</body></html>",s=[n],i=rn(e["!ref"]);r.dense=Array.isArray(e),s.push("<table"+(r&&r.id?' id="'+r.id+'"':"")+">");for(var o=i.s.r;o<=i.e.r;++o)s.push(function(e,t,r,n){for(var a=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o=0,l=0,f=0;f<a.length;++f)if(!(a[f].s.r>r)&&!(a[f].s.c>i)&&!(a[f].e.r<r)&&!(a[f].e.c<i)){if(a[f].s.r<r||a[f].s.c<i){o=-1;break}o=a[f].e.r-a[f].s.r+1,l=a[f].e.c-a[f].s.c+1;break}if(!(o<0)){var c=rr({r:r,c:i}),h=n.dense?(e[r]||[])[i]:e[c],u=h&&null!=h.v&&(h.h||((h.w||(ro(h),h.w)||"")+"").replace(e6,function(e){return e5[e]}).replace(/\n/g,"<br/>").replace(te,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))||"",d={};o>1&&(d.rowspan=o),l>1&&(d.colspan=l),n.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(n.id||"sjs")+"-"+c,s.push(tp("td",u,d))}}return"<tr>"+s.join("")+"</tr>"}(e,i,o,r));return s.push("</table>"+a),s.join("")}(t.Sheets[t.SheetNames[l]],i),i);case"txt":return function(e,t){switch(t.type){case"base64":return R(e);case"binary":case"string":return e;case"file":return eR(t.file,e,"binary");case"buffer":if(I)return k(e,"binary");return e.split("").map(function(e){return e.charCodeAt(0)})}throw Error("Unrecognized type "+t.type)}(function(e,t){t||(t={}),t.FS="	",t.RS="\n";var r=sF(e,t);if(void 0===a||"string"==t.type)return r;var n=a.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}(t.Sheets[t.SheetNames[l]],i),i);case"csv":return sP(sF(t.Sheets[t.SheetNames[l]],i),i,"\uFEFF");case"dif":return sP(nj.from_sheet(t.Sheets[t.SheetNames[l]],i),i);case"dbf":return sD(nH.from_sheet(t.Sheets[t.SheetNames[l]],i),i);case"prn":return sP(nz.from_sheet(t.Sheets[t.SheetNames[l]],i),i);case"rtf":return sP(n2.from_sheet(t.Sheets[t.SheetNames[l]],i),i);case"eth":return sP(nV.from_sheet(t.Sheets[t.SheetNames[l]],i),i);case"fods":return sP(sp(t,i),i);case"wk1":return sD(n$.sheet_to_wk1(t.Sheets[t.SheetNames[l]],i),i);case"wk3":return sD(n$.book_to_wk3(t,i),i);case"biff2":i.biff||(i.biff=2);case"biff3":i.biff||(i.biff=3);case"biff4":return i.biff||(i.biff=4),sD(sf(t,i),i);case"biff5":i.biff||(i.biff=5);case"biff8":case"xla":case"xls":return i.biff||(i.biff=8),sk(function(e,t){var r=t||{},n=eC.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw Error("invalid type "+r.bookType+" for XLS CFB")}return eC.utils.cfb_add(n,a,sf(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,n=[],a=[],s=[],i=0,o=eI(rC,"n"),l=eI(rR,"n");if(e.Props)for(i=0,r=eN(e.Props);i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?n:Object.prototype.hasOwnProperty.call(l,r[i])?a:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(i=0,r=eN(e.Custprops);i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?n:Object.prototype.hasOwnProperty.call(l,r[i])?a:s).push([r[i],e.Custprops[r[i]]]);var f=[];for(i=0;i<s.length;++i)r9.indexOf(s[i][0])>-1||rX.indexOf(s[i][0])>-1||null==s[i][1]||f.push(s[i]);a.length&&eC.utils.cfb_add(t,"/\x05SummaryInformation",nt(a,ss.SI,l,rR)),(n.length||f.length)&&eC.utils.cfb_add(t,"/\x05DocumentSummaryInformation",nt(n,ss.DSI,o,rC,f.length?f:null,ss.UDI))}(e,n),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach(function(r,n){if(0!=n){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&eC.utils.cfb_add(e,a,t.FileIndex[n].content)}})}(n,eC.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),n}(t,n=i||{}),n);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r={},n=I?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw Error("Unrecognized type "+t.type)}var a=e.FullPaths?eC.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof a){if("binary"==t.type||"base64"==t.type)return a;a=new Uint8Array(M(a))}return t.password&&"undefined"!=typeof encrypt_agile?sk(encrypt_agile(a,t.password),t):"file"===t.type?eR(t.file,a):"string"==t.type?ti(a):a}("ods"==(s=ez(i||{})).bookType?sp(t,s):"numbers"==s.bookType?function(e,t){if(!t||!t.numbers)throw Error("Must pass a `numbers` option -- check the README");var r,n=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=rn(n["!ref"]);a.s.r=a.s.c=0;var s=!1;a.e.c>9&&(s=!0,a.e.c=9),a.e.r>49&&(s=!0,a.e.r=49),s&&console.error("The Numbers writer is currently limited to ".concat(ra(a)));var i=sL(n,{range:a,header:1}),o=["~Sh33tJ5~"];i.forEach(function(e){return e.forEach(function(e){"string"==typeof e&&o.push(e)})});var l={},f=[],c=eC.read(t.numbers,{type:"base64"});c.FileIndex.map(function(e,t){return[e,c.FullPaths[t]]}).forEach(function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&sA(sy(t.content)).forEach(function(e){f.push(e.id),l[e.id]={deps:[],location:r,type:sb(e.messages[0].meta[1][0].data)}})}),f.sort(function(e,t){return e-t});var h=f.filter(function(e){return e>1}).map(function(e){return[e,sE(e)]});c.FileIndex.map(function(e,t){return[e,c.FullPaths[t]]}).forEach(function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&sA(sy(t.content)).forEach(function(e){e.messages.forEach(function(t){h.forEach(function(t){e.messages.some(function(e){return 11006!=sb(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}(e.data,t[1])})&&l[t[0]].deps.push(e.id)})})})});for(var u=eC.find(c,l[1].location),d=sA(sy(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(r=m)}var g=sR(sw(r.messages[0].data)[1][0].data);for(p=0,d=sA(sy((u=eC.find(c,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=sR(sw(r.messages[0].data)[2][0].data),d=sA(sy((u=eC.find(c,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);for(p=0,g=sR(sw(r.messages[0].data)[2][0].data),d=sA(sy((u=eC.find(c,l[g].location)).content));p<d.length;++p)(m=d[p]).id==g&&(r=m);var v=sw(r.messages[0].data);v[6][0].data=sE(a.e.r+1),v[7][0].data=sE(a.e.c+1);for(var T=sR(v[46][0].data),E=eC.find(c,l[T].location),b=sA(sy(E.content)),w=0;w<b.length&&b[w].id!=T;++w);if(b[w].id!=T)throw"Bad ColumnRowUIDMapArchive";var S=sw(b[w].messages[0].data);S[1]=[],S[2]=[],S[3]=[];for(var A=0;A<=a.e.c;++A){var _=[];_[1]=_[2]=[{type:0,data:sE(A+420690)}],S[1].push({type:2,data:sS(_)}),S[2].push({type:0,data:sE(A)}),S[3].push({type:0,data:sE(A)})}S[4]=[],S[5]=[],S[6]=[];for(var y=0;y<=a.e.r;++y)(_=[])[1]=_[2]=[{type:0,data:sE(y+726270)}],S[4].push({type:2,data:sS(_)}),S[5].push({type:0,data:sE(y)}),S[6].push({type:0,data:sE(y)});b[w].messages[0].data=sS(S),E.content=sx(s_(b)),E.size=E.content.length,delete v[46];var x=sw(v[4][0].data);x[7][0].data=sE(a.e.r+1);var O=sR(sw(x[1][0].data)[2][0].data);if((b=sA(sy((E=eC.find(c,l[O].location)).content)))[0].id!=O)throw"Bad HeaderStorageBucket";var C=sw(b[0].messages[0].data);for(y=0;y<i.length;++y){var R=sw(C[2][0].data);R[1][0].data=sE(y),R[4][0].data=sE(i[y].length),C[2][y]={type:C[2][0].type,data:sS(R)}}b[0].messages[0].data=sS(C),E.content=sx(s_(b)),E.size=E.content.length;var N=sR(x[2][0].data);if((b=sA(sy((E=eC.find(c,l[N].location)).content)))[0].id!=N)throw"Bad HeaderStorageBucket";for(A=0,C=sw(b[0].messages[0].data);A<=a.e.c;++A)(R=sw(C[2][0].data))[1][0].data=sE(A),R[4][0].data=sE(a.e.r+1),C[2][A]={type:C[2][0].type,data:sS(R)};b[0].messages[0].data=sS(C),E.content=sx(s_(b)),E.size=E.content.length;var I=sR(x[4][0].data);!function(){for(var e,t=eC.find(c,l[I].location),r=sA(sy(t.content)),n=0;n<r.length;++n){var a=r[n];a.id==I&&(e=a)}var s=sw(e.messages[0].data);s[3]=[];var i=[];o.forEach(function(e,t){i[1]=[{type:0,data:sE(t)}],i[2]=[{type:0,data:sE(1)}],i[3]=[{type:2,data:"undefined"!=typeof TextEncoder?new TextEncoder().encode(e):L(to(e))}],s[3].push({type:2,data:sS(i)})}),e.messages[0].data=sS(s);var f=sx(s_(r));t.content=f,t.size=t.content.length}();var k=sw(x[3][0].data),P=k[1][0];delete k[2];var D=sw(P.data),M=sR(D[2][0].data);(function(){for(var e,t=eC.find(c,l[M].location),r=sA(sy(t.content)),n=0;n<r.length;++n){var s=r[n];s.id==M&&(e=s)}var f=sw(e.messages[0].data);delete f[6],delete k[7];var h=new Uint8Array(f[5][0].data);f[5]=[];for(var u=0,d=0;d<=a.e.r;++d){var p=sw(h);u+=function(e,t,r){if(!(null==(n=e[6])?void 0:n[0])||!(null==(a=e[7])?void 0:a[0]))throw"Mutation only works on post-BNC storages!";if((null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&sb(e[8][0].data)>0)throw"Math only works with normal offsets";for(var n,a,s,i,o,l,f=0,c=sm(e[7][0].data),h=0,u=[],d=sm(e[4][0].data),p=0,m=[],g=0;g<t.length;++g){if(null==t[g]){c.setUint16(2*g,65535,!0),d.setUint16(2*g,65535);continue}switch(c.setUint16(2*g,h,!0),d.setUint16(2*g,p,!0),typeof t[g]){case"string":o=sO({t:"s",v:t[g]},r),l=sC({t:"s",v:t[g]},r);break;case"number":o=sO({t:"n",v:t[g]},r),l=sC({t:"n",v:t[g]},r);break;case"boolean":o=sO({t:"b",v:t[g]},r),l=sC({t:"b",v:t[g]},r);break;default:throw Error("Unsupported value "+t[g])}u.push(o),h+=o.length,m.push(l),p+=l.length,++f}for(e[2][0].data=sE(f);g<e[7][0].data.length/2;++g)c.setUint16(2*g,65535,!0),d.setUint16(2*g,65535,!0);return e[6][0].data=sv(u),e[3][0].data=sv(m),f}(p,i[d],o),p[1][0].data=sE(d),f[5].push({data:sS(p),type:2})}f[1]=[{type:0,data:sE(a.e.c+1)}],f[2]=[{type:0,data:sE(a.e.r+1)}],f[3]=[{type:0,data:sE(u)}],f[4]=[{type:0,data:sE(a.e.r+1)}],e.messages[0].data=sS(f);var m=sx(s_(r));t.content=m,t.size=t.content.length})(),P.data=sS(D),x[3][0].data=sS(k),v[4][0].data=sS(x),r.messages[0].data=sS(v);var F=sx(s_(d));return u.content=F,u.size=u.content.length,c}(t,s):"xlsb"==s.bookType?function(e,t){av=1024,e&&!e.SSF&&(e.SSF=ez(q)),e&&e.SSF&&(ey(),e_(e.SSF),t.revssf=eP(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,aZ?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,n,a,s,i,o,l,f="xlsb"==t.bookType?"bin":"xml",c=ab.indexOf(t.bookType)>-1,h=rF();sI(t=t||{});var u=eQ(),d="",p=0;if(t.cellXfs=[],a2(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eZ(u,d="docProps/core.xml",rY(e.Props,t)),h.coreprops.push(d),rG(t.rels,2,d,rB.CORE_PROPS),d="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var m=[],v=0;v<e.SheetNames.length;++v)2!=(e.Workbook.Sheets[v]||{}).Hidden&&m.push(e.SheetNames[v]);e.Props.SheetNames=m}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,eZ(u,d,rq(e.Props,t)),h.extprops.push(d),rG(t.rels,3,d,rB.EXT_PROPS),e.Custprops!==e.Props&&eN(e.Custprops||{}).length>0&&(eZ(u,d="docProps/custom.xml",rJ(e.Custprops,t)),h.custprops.push(d),rG(t.rels,4,d,rB.CUST_PROPS)),p=1;p<=e.SheetNames.length;++p){var T={"!id":{}},E=e.Sheets[e.SheetNames[p-1]];if((E||{})["!type"],eZ(u,d="xl/worksheets/sheet"+p+"."+f,(b=p-1,w=d,S=t,(".bin"===w.slice(-4)?function(e,t,r,n){var a,s,i,o,l,f=t0(),c=r.SheetNames[e],h=r.Sheets[c]||{},u=c;try{r&&r.Workbook&&(u=r.Workbook.Sheets[e].CodeName||u)}catch(e){}var d=rs(h["!ref"]||"A1");if(d.e.c>16383||d.e.r>1048575){if(t.WTF)throw Error("Range "+(h["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");d.e.c=Math.min(d.e.c,16383),d.e.r=Math.min(d.e.c,1048575)}return h["!links"]=[],h["!comments"]=[],t2(f,129),(r.vbaraw||h["!outline"])&&t2(f,147,function(e,t,r){null==r&&(r=t1(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return rx({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),ru(e,r),r.slice(0,r.l)}(u,h["!outline"])),t2(f,148,rA(d)),a=r.Workbook,t2(f,133),t2(f,137,(null==s&&(s=t1(30)),i=924,(((a||{}).Views||[])[0]||{}).RTL&&(i|=32),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(1,0),s.write_shift(1,0),s.write_shift(2,0),s.write_shift(2,100),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s)),t2(f,138),t2(f,134),h&&h["!cols"]&&(t2(f,390),h["!cols"].forEach(function(e,t){if(e){var r,n,a;t2(f,60,(null==r&&(r=t1(18)),n=a1(t,e),r.write_shift(-4,t),r.write_shift(-4,t),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0),a=0,e.hidden&&(a|=1),"number"==typeof n.width&&(a|=2),e.level&&(a|=e.level<<8),r.write_shift(2,a),r))}}),t2(f,391)),function(e,t,r,n){var a,s=rs(t["!ref"]||"A1"),i="",o=[];t2(e,145);var l=Array.isArray(t),f=s.e.r;t["!rows"]&&(f=Math.max(s.e.r,t["!rows"].length-1));for(var c=s.s.r;c<=f;++c){i=t7(c),function(e,t,r,n){var a=function(e,t,r){var n=t1(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var s=320;a.hpx?s=20*n9(a.hpx):a.hpt&&(s=20*a.hpt),n.write_shift(2,s),n.write_shift(1,0);var i=0;a.level&&(i|=a.level),a.hidden&&(i|=16),(a.hpx||a.hpt)&&(i|=32),n.write_shift(1,i),n.write_shift(1,0);var o=0,l=n.l;n.l+=4;for(var f={r:e,c:0},c=0;c<16;++c)if(!(t.s.c>c+1<<10)&&!(t.e.c<c<<10)){for(var h=-1,u=-1,d=c<<10;d<c+1<<10;++d)f.c=d,(Array.isArray(r)?(r[f.r]||[])[f.c]:r[rr(f)])&&(h<0&&(h=d),u=d);h<0||(++o,n.write_shift(4,h),n.write_shift(4,u))}var p=n.l;return n.l=l,n.write_shift(4,o),n.l=p,n.length>n.l?n.slice(0,n.l):n}(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&t2(e,0,a)}(e,t,s,c);var h=!1;if(c<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){c===s.s.r&&(o[u]=re(u)),a=o[u]+i;var d=l?(t[c]||[])[u]:t[a];if(!d){h=!1;continue}h=function(e,t,r,n,a,s,i){if(void 0===t.v)return!1;var o,l,f,c,h,u,d,p,m,g,v,T,E,b,w,S,A,_,y,x,O,C,R,N,I="";switch(t.t){case"b":I=t.v?"1":"0";break;case"d":(t=ez(t)).z=t.z||q[14],t.v=eL(ej(t.v)),t.t="n";break;case"n":case"e":I=""+t.v;break;default:I=t.v}var k={r:r,c:n};switch(k.s=a2(a.cellXfs,t,a),t.l&&s["!links"].push([rr(k),t.l]),t.c&&s["!comments"].push([rr(k),t.c]),t.t){case"s":case"str":return a.bookSST?(I=aQ(a.Strings,t.v,a.revStrings),k.t="s",k.v=I,i)?t2(e,18,(null==o&&(o=t1(8)),rv(k,o),o.write_shift(4,k.v),o)):t2(e,7,(null==l&&(l=t1(12)),rm(k,l),l.write_shift(4,k.v),l)):(k.t="str",i)?t2(e,17,(f=t,null==c&&(c=t1(8+4*f.v.length)),rv(k,c),ru(f.v,c),c.length>c.l?c.slice(0,c.l):c)):t2(e,6,(h=t,null==u&&(u=t1(12+4*h.v.length)),rm(k,u),ru(h.v,u),u.length>u.l?u.slice(0,u.l):u)),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?t2(e,13,(d=t,null==p&&(p=t1(8)),rv(k,p),rw(d.v,p),p)):t2(e,2,(m=t,null==g&&(g=t1(12)),rm(k,g),rw(m.v,g),g)):i?t2(e,16,(v=t,null==T&&(T=t1(12)),rv(k,T),ry(v.v,T),T)):t2(e,5,(E=t,null==b&&(b=t1(16)),rm(k,b),ry(E.v,b),b)),!0;case"b":return(k.t="b",i)?t2(e,15,(w=t,null==S&&(S=t1(5)),rv(k,S),S.write_shift(1,w.v?1:0),S)):t2(e,4,(A=t,null==_&&(_=t1(9)),rm(k,_),_.write_shift(1,A.v?1:0),_)),!0;case"e":return(k.t="e",i)?t2(e,14,(y=t,null==x&&(x=t1(8)),rv(k,x),x.write_shift(1,y.v),x.write_shift(2,0),x.write_shift(1,0),x)):t2(e,3,(O=t,null==C&&(C=t1(9)),rm(k,C),C.write_shift(1,O.v),C)),!0}return i?t2(e,12,(null==R&&(R=t1(4)),rv(k,R))):t2(e,1,(null==N&&(N=t1(8)),rm(k,N))),!0}(e,d,c,u,n,t,h)}}t2(e,146)}(f,h,0,t,r),function(e,t){if(t["!protect"]){var r,n;t2(e,535,(r=t["!protect"],null==n&&(n=t1(66)),n.write_shift(2,r.password?nZ(r.password):0),n.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(e){e[1]?n.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):n.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)}),n))}}(f,h),function(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],s="string"==typeof a.ref?a.ref:ra(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=rn(s);o.s.r==o.e.r&&(o.e.r=rn(t["!ref"]).e.r,s=ra(o));for(var l=0;l<i.length;++l){var f=i[l];if("_xlnm._FilterDatabase"==f.Name&&f.Sheet==n){f.Ref="'"+r.SheetNames[n]+"'!"+s;break}}l==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+s}),t2(e,161,rA(rs(s))),t2(e,162)}}(f,h,r,e),function(e,t){if(t&&t["!merges"]){var r,n;t2(e,177,(r=t["!merges"].length,null==n&&(n=t1(4)),n.write_shift(4,r),n)),t["!merges"].forEach(function(t){t2(e,176,rA(t))}),t2(e,178)}}(f,h),h["!links"].forEach(function(e){if(e[1].Target){var t,r,a=rG(n,-1,e[1].Target.replace(/#.*$/,""),rB.HLINK);t2(f,494,(t=t1(50+4*(e[1].Target.length+(e[1].Tooltip||"").length)),rA({s:rt(e[0]),e:rt(e[0])},t),rE("rId"+a,t),ru((-1==(r=e[1].Target.indexOf("#"))?"":e[1].Target.slice(r+1))||"",t),ru(e[1].Tooltip||"",t),ru("",t),t.slice(0,t.l)))}}),delete h["!links"],h["!margins"]&&t2(f,476,(o=h["!margins"],null==l&&(l=t1(48)),a0(o),a6.forEach(function(e){ry(o[e],l)}),l)),(!t||t.ignoreEC||void 0==t.ignoreEC)&&function(e,t){if(t&&t["!ref"]){var r,n;t2(e,648),t2(e,649,(r=rs(t["!ref"]),(n=t1(24)).write_shift(4,4),n.write_shift(4,1),rA(r,n),n)),t2(e,650)}}(f,h),function(e,t,r,n){if(t["!comments"].length>0){var a=rG(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",rB.VML);t2(e,551,rE("rId"+a)),t["!legacy"]=a}}(f,h,e,n),t2(f,130),f.end()}:a5)(b,S,e,T))),h.sheets.push(d),rG(t.wbrels,-1,"worksheets/sheet"+p+"."+f,rB.WS[0]),E){var b,w,S,A,_,y=E["!comments"],x=!1,O="";y&&y.length>0&&(eZ(u,O="xl/comments"+p+"."+f,(A=O,_=t,(".bin"===A.slice(-4)?function(e){var t=t0(),r=[];return t2(t,628),t2(t,630),e.forEach(function(e){e[1].forEach(function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),t2(t,632,ru(e.a.slice(0,54))))})}),t2(t,631),t2(t,633),e.forEach(function(e){e[1].forEach(function(n){var a,s,i,o,l,f;n.iauthor=r.indexOf(n.a),t2(t,635,(a=[{s:rt(e[0]),e:rt(e[0])},n],null==s&&(s=t1(36)),s.write_shift(4,a[1].iauthor),rA(a[0],s),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s)),n.t&&n.t.length>0&&t2(t,637,(o=!1,null==i&&(o=!0,i=t1(23+4*n.t.length)),i.write_shift(1,1),ru(n.t,i),i.write_shift(4,1),l={ich:0,ifnt:0},(f=i)||(f=t1(4)),f.write_shift(2,l.ich||0),f.write_shift(2,l.ifnt||0),o?i.slice(0,i.l):i)),t2(t,636),delete n.iauthor})}),t2(t,634),t2(t,629),t.end()}:aE)(y,_))),h.comments.push(O),rG(T,-1,"../comments"+p+"."+f,rB.CMNT),x=!0),E["!legacy"]&&x&&eZ(u,"xl/drawings/vmlDrawing"+p+".vml",aT(p,E["!comments"])),delete E["!comments"],delete E["!legacy"]}T["!id"].rId1&&eZ(u,rW(d),rH(T))}return null!=t.Strings&&t.Strings.length>0&&(eZ(u,d="xl/sharedStrings."+f,(r=t.Strings,n=d,a=t,(".bin"===n.slice(-4)?function(e){var t,r=t0();t2(r,159,(t||(t=t1(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t));for(var n=0;n<e.length;++n)t2(r,19,nX(e[n]));return t2(r,160),r.end()}:nK)(r,a))),h.strs.push(d),rG(t.wbrels,-1,"sharedStrings."+f,rB.SST)),eZ(u,d="xl/workbook."+f,(s=d,i=t,(".bin"===s.slice(-4)?function(e,t){var r,n,a,s=t0();return t2(s,131),t2(s,128,function(e,t){t||(t=t1(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return ru("SheetJS",t),ru(g.version,t),ru(g.version,t),ru("7262",t),t.length>t.l?t.slice(0,t.l):t}()),t2(s,153,(r=e.Workbook&&e.Workbook.WBProps||null,n||(n=t1(72)),a=0,r&&r.filterPrivacy&&(a|=8),n.write_shift(4,a),n.write_shift(4,0),ru(r&&r.CodeName||"ThisWorkbook",n),n.slice(0,n.l))),function(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,n,a=t.Workbook.Sheets,s=0,i=-1,o=-1;s<a.length;++s)a[s]&&(a[s].Hidden||-1!=i)?1==a[s].Hidden&&-1==o&&(o=s):i=s;o>i||(t2(e,135),t2(e,158,(r=i,n||(n=t1(29)),n.write_shift(-4,0),n.write_shift(-4,460),n.write_shift(4,28800),n.write_shift(4,17600),n.write_shift(4,500),n.write_shift(4,r),n.write_shift(4,r),n.write_shift(1,120),n.length>n.l?n.slice(0,n.l):n)),t2(e,136))}}(s,e,t),function(e,t){t2(e,143);for(var r=0;r!=t.SheetNames.length;++r){var n={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},a=void 0;t2(e,156,(a||(a=t1(127)),a.write_shift(4,n.Hidden),a.write_shift(4,n.iTabID),rE(n.strRelID,a),ru(n.name.slice(0,31),a),a.length>a.l?a.slice(0,a.l):a))}t2(e,144)}(s,e,t),t2(s,132),s.end()}:se)(e,i))),h.workbooks.push(d),rG(t.rels,1,d,rB.WB),eZ(u,d="xl/theme/theme1.xml",am(e.Themes,t)),h.themes.push(d),rG(t.wbrels,-1,"theme/theme1.xml",rB.THEME),eZ(u,d="xl/styles."+f,(o=d,l=t,(".bin"===o.slice(-4)?function(e,t){var r,n,a,s,i,o,l,f,c,h,u,d,p,m,g,v=t0();return t2(v,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r}),0!=r&&(t2(e,615,rc(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&t2(e,44,function(e,t,r){r||(r=t1(6+4*t.length)),r.write_shift(2,e),ru(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}(n,t[n]))}),t2(e,616))}}(v,e.SSF),t2(r=v,611,rc(1)),t2(r,43,(n={sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"},a||(a=t1(153)),a.write_shift(2,20*n.sz),(s=a)||(s=t1(2)),i=(n.italic?2:0)|(n.strike?8:0)|(n.outline?16:0)|(n.shadow?32:0)|(n.condense?64:0)|(n.extend?128:0),s.write_shift(1,i),s.write_shift(1,0),a.write_shift(2,n.bold?700:400),o=0,"superscript"==n.vertAlign?o=1:"subscript"==n.vertAlign&&(o=2),a.write_shift(2,o),a.write_shift(1,n.underline||0),a.write_shift(1,n.family||0),a.write_shift(1,n.charset||0),a.write_shift(1,0),rx(n.color,a),l=0,"major"==n.scheme&&(l=1),"minor"==n.scheme&&(l=2),a.write_shift(1,l),ru(n.name,a),a.length>a.l?a.slice(0,a.l):a)),t2(r,612),t2(v,603,rc(2)),t2(v,45,aa({patternType:"none"})),t2(v,45,aa({patternType:"gray125"})),t2(v,604),t2(f=v,613,rc(1)),t2(f,46,(c||(c=t1(51)),c.write_shift(1,0),ai(null,c),ai(null,c),ai(null,c),ai(null,c),ai(null,c),c.length>c.l?c.slice(0,c.l):c)),t2(f,614),t2(v,626,rc(1)),t2(v,47,as({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),t2(v,627),t2(v,617,rc((h=t.cellXfs).length)),h.forEach(function(e){t2(v,47,as(e,0))}),t2(v,618),t2(v,619,rc(1)),t2(v,48,(u={xfId:0,builtinId:0,name:"Normal"},d||(d=t1(52)),d.write_shift(4,u.xfId),d.write_shift(2,1),d.write_shift(1,+u.builtinId),d.write_shift(1,0),rE(u.name||"",d),d.length>d.l?d.slice(0,d.l):d)),t2(v,620),t2(v,505,rc(0)),t2(v,506),t2(v,508,(p="TableStyleMedium9",m="PivotStyleMedium4",(g=t1(2052)).write_shift(4,0),rE(p,g),rE(m,g),g.length>g.l?g.slice(0,g.l):g)),t2(v,509),t2(v,279),v.end()}:ar)(e,l))),h.styles.push(d),rG(t.wbrels,-1,"styles."+f,rB.STY),e.vbaraw&&c&&(eZ(u,d="xl/vbaProject.bin",e.vbaraw),h.vba.push(d),rG(t.wbrels,-1,"vbaProject.bin",rB.VBA)),eZ(u,d="xl/metadata."+f,(".bin"===d.slice(-4)?function(){var e,t,r,n,a,s=t0();return t2(s,332),t2(s,334,rc(1)),t2(s,335,((t=t1(12+2*(e={name:"XLDAPR",version:12e4,flags:3496657072}).name.length)).write_shift(4,e.flags),t.write_shift(4,e.version),ru(e.name,t),t.slice(0,t.l))),t2(s,336),t2(s,339,((n=t1(8+2*(r="XLDAPR").length)).write_shift(4,1),ru(r,n),n.slice(0,n.l))),t2(s,52),t2(s,35,rc(514)),t2(s,4096,rc(0)),t2(s,4097,ni(1)),t2(s,36),t2(s,53),t2(s,340),t2(s,337,((a=t1(8)).write_shift(4,1),a.write_shift(4,1),a)),t2(s,51,function(e){var t=t1(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),t2(s,338),t2(s,333),s.end()}:ag)()),h.metadata.push(d),rG(t.wbrels,-1,"metadata."+f,rB.XLMETA),eZ(u,"[Content_Types].xml",rU(h,t)),eZ(u,"_rels/.rels",rH(t.rels)),eZ(u,"xl/_rels/workbook."+f+".rels",rH(t.wbrels)),delete t.revssf,delete t.ssf,u}(t,s):function(e,t){av=1024,e&&!e.SSF&&(e.SSF=ez(q)),e&&e.SSF&&(ey(),e_(e.SSF),t.revssf=eP(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,aZ?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r,n=ab.indexOf(t.bookType)>-1,a=rF();sI(t=t||{});var s=eQ(),i="",o=0;if(t.cellXfs=[],a2(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),eZ(s,i="docProps/core.xml",rY(e.Props,t)),a.coreprops.push(i),rG(t.rels,2,i,rB.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var l=[],f=0;f<e.SheetNames.length;++f)2!=(e.Workbook.Sheets[f]||{}).Hidden&&l.push(e.SheetNames[f]);e.Props.SheetNames=l}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,eZ(s,i,rq(e.Props,t)),a.extprops.push(i),rG(t.rels,3,i,rB.EXT_PROPS),e.Custprops!==e.Props&&eN(e.Custprops||{}).length>0&&(eZ(s,i="docProps/custom.xml",rJ(e.Custprops,t)),a.custprops.push(i),rG(t.rels,4,i,rB.CUST_PROPS));var c=["SheetJ5"];for(o=1,t.tcid=0;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]];if((u||{})["!type"],eZ(s,i="xl/worksheets/sheet"+o+".xml",a5(o-1,t,e,h)),a.sheets.push(i),rG(t.wbrels,-1,"worksheets/sheet"+o+".xml",rB.WS[0]),u){var d=u["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach(function(e){e[1].forEach(function(e){!0==e.T&&(g=!0)})}),g&&(eZ(s,m="xl/threadedComments/threadedComment"+o+".xml",function(e,t,r){var n=[e1,tp("ThreadedComments",null,{xmlns:tT.TCMNT}).replace(/[\/]>/,">")];return e.forEach(function(e){var a="";(e[1]||[]).forEach(function(s,i){if(!s.T){delete s.ID;return}s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?a=o.id:o.parentId=a,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),n.push(tp("threadedComment",tu("text",s.t||""),o))})}),n.push("</ThreadedComments>"),n.join("")}(d,c,t)),a.threadedcomments.push(m),rG(h,-1,"../threadedComments/threadedComment"+o+".xml",rB.TCMNT)),eZ(s,m="xl/comments"+o+".xml",aE(d,t)),a.comments.push(m),rG(h,-1,"../comments"+o+".xml",rB.CMNT),p=!0}u["!legacy"]&&p&&eZ(s,"xl/drawings/vmlDrawing"+o+".vml",aT(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&eZ(s,rW(i),rH(h))}return null!=t.Strings&&t.Strings.length>0&&(eZ(s,i="xl/sharedStrings.xml",nK(t.Strings,t)),a.strs.push(i),rG(t.wbrels,-1,"sharedStrings.xml",rB.SST)),eZ(s,i="xl/workbook.xml",se(e,t)),a.workbooks.push(i),rG(t.rels,1,i,rB.WB),eZ(s,i="xl/theme/theme1.xml",am(e.Themes,t)),a.themes.push(i),rG(t.wbrels,-1,"theme/theme1.xml",rB.THEME),eZ(s,i="xl/styles.xml",ar(e,t)),a.styles.push(i),rG(t.wbrels,-1,"styles.xml",rB.STY),e.vbaraw&&n&&(eZ(s,i="xl/vbaProject.bin",e.vbaraw),a.vba.push(i),rG(t.wbrels,-1,"vbaProject.bin",rB.VBA)),eZ(s,i="xl/metadata.xml",ag()),a.metadata.push(i),rG(t.wbrels,-1,"metadata.xml",rB.XLMETA),c.length>1&&(eZ(s,i="xl/persons/person.xml",(r=[e1,tp("personList",null,{xmlns:tT.TCMNT,"xmlns:x":"http://schemas.openxmlformats.org/spreadsheetml/2006/main"}).replace(/[\/]>/,">")],c.forEach(function(e,t){r.push(tp("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))}),r.push("</personList>"),r.join(""))),a.people.push(i),rG(t.wbrels,-1,"persons/person.xml",rB.PEOPLE)),eZ(s,"[Content_Types].xml",rU(a,t)),eZ(s,"_rels/.rels",rH(t.rels)),eZ(s,"xl/_rels/workbook.xml.rels",rH(t.wbrels)),delete t.revssf,delete t.ssf,s}(t,s),s);default:throw Error("Unrecognized bookType |"+i.bookType+"|")}}(r,t)},children:[l.jsx(p.P.download,{className:"mr-2 h-4 w-4"}),"Download Excel Template"]})]})]})}g.version},31048:(e,t,r)=>{"use strict";r.d(t,{_:()=>f});var n=r(10326),a=r(17577),s=r(34478),i=r(79360),o=r(77863);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),f=a.forwardRef(({className:e,...t},r)=>n.jsx(s.f,{ref:r,className:(0,o.cn)(l(),e),...t}));f.displayName=s.f.displayName},35429:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>u});var n=r(19510),a=r(75571),s=r(58585),i=r(90455),o=r(68570);let l=(0,o.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\cards\import\import-form.tsx`),{__esModule:f,$$typeof:c}=l;l.default;let h=(0,o.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\cards\import\import-form.tsx#ImportForm`),u={title:"Import Cards",description:"Import multiple Yolloo Cards"};async function d(){let e=await (0,a.getServerSession)(i.L);return e&&"ADMIN"===e.user.role||(0,s.redirect)("/auth/signin"),(0,n.jsxs)("div",{className:"flex flex-col gap-4 p-8",children:[(0,n.jsxs)("div",{children:[n.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Import Cards"}),n.jsx("p",{className:"text-muted-foreground",children:"Import multiple Yolloo Cards from a CSV file"})]}),n.jsx(h,{})]})}},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return n.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),a=r(16399);class s extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new s}delete(){throw new s}set(){throw new s}sort(){throw new s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return a},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},isRedirectError:function(){return h},permanentRedirect:function(){return c},redirect:function(){return f}});let a=r(54580),s=r(72934),i=r(8586),o="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Error(o);n.digest=o+";"+t+";"+e+";"+r+";";let s=a.requestAsyncStorage.getStore();return s&&(n.mutableCookies=s.mutableCookies),n}function f(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function h(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,a]=e.digest.split(";",4),s=Number(a);return t===o&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(s)&&s in i.RedirectStatusCode}function u(e){return h(e)?e.digest.split(";",3)[2]:null}function d(e){if(!h(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!h(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>o});var n=r(17577),a=r(45226),s=r(10326),i=n.forwardRef((e,t)=>(0,s.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,4824,7123],()=>r(80544));module.exports=n})();