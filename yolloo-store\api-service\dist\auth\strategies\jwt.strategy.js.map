{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,yDAAqD;AAErD,IACa,WAAW,GADxB,MACa,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAE/C;IACA;IAFV,YACU,aAA4B,EAC5B,MAAqB;QAE7B,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,uBAAuB,CAAC,IAAI,iBAAiB;SACrF,CAAC,CAAC;QAPK,kBAAa,GAAb,aAAa,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAe;IAO/B,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,8BAAqB,EAAE,CAAC;SACnC;QAED,OAAO;YACL,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;IACJ,CAAC;CACF,CAAA;AA3BY,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGc,sBAAa;QACpB,8BAAa;GAHpB,WAAW,CA2BvB;AA3BY,kCAAW"}