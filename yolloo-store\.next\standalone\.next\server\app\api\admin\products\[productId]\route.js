"use strict";(()=>{var e={};e.id=5133,e.ids=[5133],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},22182:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>D,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>E,staticGenerationAsyncStorage:()=>v});var o={};t.r(o),t.d(o,{DELETE:()=>y,PATCH:()=>w,dynamic:()=>p,fetchCache:()=>m,revalidate:()=>f});var n=t(49303),a=t(88716),i=t(60670),s=t(87070),l=t(75571),u=t(72331),c=t(90455),d=t(71615);let p="force-dynamic",m="force-no-store",f=0;async function w(e,{params:r}){try{(0,d.headers)();let t=await (0,l.getServerSession)(c.L);if(!t||"ADMIN"!==t.user.role)return new s.NextResponse("Unauthorized",{status:401});let o=await e.json(),{productId:n}=r,a=await u._.product.findUnique({where:{id:n},include:{variants:!0}});if(!a)return new s.NextResponse("Product not found",{status:404});let i=await u._.product.update({where:{id:n},data:{name:null===o.name?void 0:o.name,description:null===o.description?void 0:o.description,websiteDescription:null===o.websiteDescription?void 0:o.websiteDescription,price:void 0!==o.price&&null!==o.price?isNaN(parseFloat(o.price))?void 0:parseFloat(o.price):void 0,images:null===o.images?void 0:o.images,category:o.categoryId?{connect:{id:o.categoryId}}:void 0,stock:void 0!==o.stock&&null!==o.stock?isNaN(parseInt(o.stock,10))?void 0:parseInt(o.stock,10):void 0,sku:null===o.sku?void 0:o.sku,specifications:null===o.specifications?void 0:o.specifications,mcc:null===o.mcc?null:o.mcc,requiredUID:null===o.requiredUID?void 0:o.requiredUID,off_shelve:null===o.off_shelve?void 0:o.off_shelve,status:null===o.status?void 0:o.status,dataSize:null===o.dataSize?null:o.dataSize,planType:null===o.planType?null:o.planType,country:null===o.country?null:o.country,countryCode:null===o.countryCode?null:o.countryCode},include:{variants:{orderBy:[{durationType:"asc"},{duration:"asc"}]},category:!0}});if(o.variants&&Array.isArray(o.variants)){let e=a.variants.map(e=>e.id),r=o.variants.filter(e=>e&&e.id).map(e=>e.id),t=e.filter(e=>!r.includes(e));for(let e of(t.length>0&&await u._.productVariant.deleteMany({where:{id:{in:t}}}),o.variants))e&&(e.id?await u._.productVariant.update({where:{id:e.id},data:{price:null!==e.price&&void 0!==e.price?isNaN(Number(e.price))?0:Number(e.price):0,currency:null===e.currency?void 0:e.currency||"USD",attributes:null===e.attributes?{}:e.attributes||{},duration:null===e.duration?null:void 0!==e.duration?isNaN(Number(e.duration))?null:Number(e.duration):null,durationType:null===e.durationType?null:e.durationType||null,variantCode:null===e.variantCode?null:e.variantCode||null}}):await u._.productVariant.create({data:{price:null!==e.price&&void 0!==e.price?isNaN(Number(e.price))?0:Number(e.price):0,currency:null===e.currency?"USD":e.currency||"USD",attributes:null===e.attributes?{}:e.attributes||{},productId:n,duration:null===e.duration?null:void 0!==e.duration?isNaN(Number(e.duration))?null:Number(e.duration):null,durationType:null===e.durationType?null:e.durationType||null,variantCode:null===e.variantCode?null:e.variantCode||null}}))}return s.NextResponse.json(i)}catch(e){return console.error("[PRODUCT_PATCH]",e),new s.NextResponse("Internal error",{status:500})}}async function y(e,{params:r}){try{let e=await (0,l.getServerSession)(c.L);if(!e||"ADMIN"!==e.user.role)return new s.NextResponse("Unauthorized",{status:401});let t=await u._.product.findUnique({where:{id:r.productId},select:{id:!0,name:!0}});if(!t)return new s.NextResponse("Product not found",{status:404});console.log(`[PRODUCT_DELETE] Starting deletion process for product ID: ${r.productId} (${t.name})`);let o=await u._.product.findUnique({where:{id:r.productId},select:{sku:!0}});if(!o)return new s.NextResponse("Product details not found",{status:404});let n=await u._.orderItem.count({where:{productCode:o.sku}});if(n>0)return new s.NextResponse(`Cannot delete product: "${t.name}" because it is used in ${n} order(s). Please first delete those orders.`,{status:400});let a=await u._.esim.count({where:{productId:r.productId}});if(a>0)return new s.NextResponse(`Cannot delete product: "${t.name}" because it is linked to ${a} eSIM(s). Please first update those eSIMs to use a different product.`,{status:400});return await u._.$transaction(async e=>{try{console.log(`[PRODUCT_DELETE] Deleting cart items for product: ${r.productId}`);let t=await e.cartItem.deleteMany({where:{productId:r.productId}});console.log(`[PRODUCT_DELETE] Deleted ${t.count} cart items`),console.log(`[PRODUCT_DELETE] Deleting wishlist items for product: ${r.productId}`);let o=await e.wishlistItem.deleteMany({where:{productId:r.productId}});console.log(`[PRODUCT_DELETE] Deleted ${o.count} wishlist items`),console.log(`[PRODUCT_DELETE] Deleting reviews for product: ${r.productId}`);let n=await e.review.deleteMany({where:{productId:r.productId}});console.log(`[PRODUCT_DELETE] Deleted ${n.count} reviews`),console.log(`[PRODUCT_DELETE] Deleting product parameters for product: ${r.productId}`);let a=await e.productParameter.deleteMany({where:{productId:r.productId}});console.log(`[PRODUCT_DELETE] Deleted ${a.count} product parameters`),console.log(`[PRODUCT_DELETE] Deleting product variants for product: ${r.productId}`);let i=await e.productVariant.deleteMany({where:{productId:r.productId}});console.log(`[PRODUCT_DELETE] Deleted ${i.count} product variants`),console.log(`[PRODUCT_DELETE] Deleting product: ${r.productId}`),await e.product.delete({where:{id:r.productId}}),console.log(`[PRODUCT_DELETE] Successfully deleted product: ${r.productId}`)}catch(e){throw console.error("[PRODUCT_DELETE] Transaction error:",e),e}}),new s.NextResponse(null,{status:204})}catch(e){if(console.error("[PRODUCT_DELETE]",e),e instanceof Error){if(e.message.includes("foreign key constraint"))return new s.NextResponse("Cannot delete this product because it is referenced by other records in the database",{status:400});return new s.NextResponse(`Error deleting product: ${e.message}`,{status:500})}return new s.NextResponse("Internal error",{status:500})}}let h=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/products/[productId]/route",pathname:"/api/admin/products/[productId]",filename:"route",bundlePath:"app/api/admin/products/[productId]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\products\\[productId]\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:v,serverHooks:E}=h,_="/api/admin/products/[productId]/route";function D(){return(0,i.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:v})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var o=t(7585),n=t(72331),a=t(77234),i=t(53797),s=t(42023),l=t.n(s),u=t(93475);let c={adapter:{...(0,o.N)(n._),getUser:async e=>{let r=await n._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await n._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await n._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,i.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await n._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,i.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await n._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await n._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await n._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:o,request:a}){try{if(t&&t.id){let r=a?.headers||new Headers,i=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";o?u=o.code&&!o.password?"email_code":"password":e&&(u=e.provider),await n._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:i||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],o=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,n=new URL(o).searchParams.get("callbackUrl");if(n){let e=decodeURIComponent(n);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let o=new URL(e);if(t.some(e=>o.hostname===e||o.hostname.includes(e)||o.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(o);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return o}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:o}){if("update"===t&&o)return{...e,...o.user};let a=await n._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return a?{id:a.id,name:a.name,email:a.email,picture:a.image,role:a.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>n});var o=t(53524);let n=global.prisma||new o.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var o=t(62197),n=t.n(o);let a=null;function i(){if(!a){let e=process.env.REDIS_URL||"redis://localhost:6379";(a=new(n())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),a.on("connect",()=>{console.log("Successfully connected to Redis")})}return a}async function s(e,r,t=300){try{let o=i(),n=`verification_code:${e}`;return await o.setex(n,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=i(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=i(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let o=i(),n=`rate_limit:${e}`,a=await o.get(n),s=a?parseInt(a):0;if(s>=r)return!1;return 0===s?await o.setex(n,t,"1"):await o.incr(n),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var a=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var s=n?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=e[a]}return o.default=e,t&&t.set(e,o),o}(t(45609));function i(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(i=function(e){return e?t:r})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>t(22182));module.exports=o})();