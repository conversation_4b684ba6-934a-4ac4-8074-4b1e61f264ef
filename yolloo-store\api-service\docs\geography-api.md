# 地理位置商品API文档

## 概述

地理位置商品API提供了按大洲和国家筛选商品的功能，支持总量卡(Total)和天卡(Daily)两种商品类型。

## 接口列表

### 1. 获取所有大洲列表

**接口地址：** `GET /geography/continents`

**请求参数：** 无

**响应示例：**
```json
{
  "continents": [
    {
      "id": "asia",
      "name": "亚洲",
      "nameEn": "Asia",
      "nameZh": "亚洲"
    },
    {
      "id": "europe", 
      "name": "欧洲",
      "nameEn": "Europe",
      "nameZh": "欧洲"
    },
    {
      "id": "america",
      "name": "美洲", 
      "nameEn": "America",
      "nameZh": "美洲"
    },
    {
      "id": "oceania",
      "name": "大洋洲",
      "nameEn": "Oceania", 
      "nameZh": "大洋洲"
    },
    {
      "id": "africa",
      "name": "非洲",
      "nameEn": "Africa",
      "nameZh": "非洲"
    }
  ],
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

### 2. 根据大洲获取国家列表

**接口地址：** `GET /geography/continents/{continent}/countries`

**路径参数：**
- `continent`: 大洲ID (asia, europe, america, oceania, africa)

**响应示例：**
```json
{
  "continent": {
    "id": "asia",
    "name": "亚洲"
  },
  "countries": [
    {
      "code": "JP",
      "name": "日本",
      "nameEn": "Japan",
      "nameZh": "日本"
    },
    {
      "code": "KR", 
      "name": "韩国",
      "nameEn": "South Korea",
      "nameZh": "韩国"
    },
    {
      "code": "TH",
      "name": "泰国", 
      "nameEn": "Thailand",
      "nameZh": "泰国"
    }
  ],
  "context": {
    "language": "zh-CN",
    "theme": "light", 
    "currency": "USD"
  }
}
```

### 3. 根据国家获取商品列表

**接口地址：** `GET /geography/countries/{countryCode}/products`

**路径参数：**
- `countryCode`: 国家代码 (如: JP, KR, TH等)

**查询参数：**
- `planType`: 商品类型 (Total/Daily，可选)
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)

**响应示例：**
```json
{
  "products": [
    {
      "id": "jp-total-500mb",
      "name": "500MB 流量卡",
      "description": "流量 | 全日本", 
      "price": 39.9,
      "currency": "USD",
      "imageUrl": "https://example.com/jp-500mb.jpg",
      "dataSize": 500,
      "planType": "Total",
      "duration": 30,
      "countries": ["JP"],
      "countryCode": "JP",
      "rating": 4.8,
      "reviewCount": 1250
    },
    {
      "id": "jp-total-1gb",
      "name": "1GB 流量卡",
      "description": "流量 | 全日本",
      "price": 39.9, 
      "currency": "USD",
      "imageUrl": "https://example.com/jp-1gb.jpg",
      "dataSize": 1024,
      "planType": "Total",
      "duration": 30,
      "countries": ["JP"],
      "countryCode": "JP", 
      "rating": 4.7,
      "reviewCount": 980
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 10,
    "total": 5,
    "totalPages": 1
  },
  "filters": {
    "countryCode": "JP",
    "planType": "Total"
  },
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

### 4. 获取所有商品（不限制国家）

**接口地址：** `GET /geography/products`

**查询参数：**
- `planType`: 商品类型 (Total/Daily，可选)
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)

**响应格式：** 与接口3相同

### 5. 获取商品筛选选项

**接口地址：** `GET /geography/filters`

**响应示例：**
```json
{
  "planTypes": [
    {
      "value": "Total",
      "label": "总量卡",
      "description": "一次性流量包，用完为止"
    },
    {
      "value": "Daily", 
      "label": "天卡",
      "description": "每日固定流量，按天计费"
    }
  ],
  "dataSizes": [
    { "value": "500", "label": "500MB" },
    { "value": "1024", "label": "1GB" },
    { "value": "3072", "label": "3GB" },
    { "value": "10240", "label": "10GB" }
  ],
  "sortOptions": [
    { "value": "price", "label": "价格" },
    { "value": "dataSize", "label": "流量大小" },
    { "value": "rating", "label": "评分" }
  ],
  "context": {
    "language": "zh-CN",
    "theme": "light",
    "currency": "USD"
  }
}
```

## 使用说明

### 关于大洲和国家数据

1. **数据来源**: 大洲和国家数据是预定义的常量，不需要从数据库动态获取
2. **建议**: App可以将大洲和国家数据写死在客户端，只在需要更新时调用API
3. **优势**: 减少网络请求，提高用户体验

### 商品类型说明

- **Total (总量卡)**: 一次性流量包，有效期内用完为止
- **Daily (天卡)**: 每日固定流量额度，按天计费

### 多语言支持

API根据请求头中的语言设置返回对应语言的内容：
- `zh-CN`: 中文
- `en-US`: 英文

### 货币支持

API根据请求上下文返回对应货币的价格信息。
