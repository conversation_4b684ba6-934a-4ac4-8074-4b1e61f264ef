import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma.service';
import { SocialAuthService } from '../social-auth/social-auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { LoginCodeDto } from './dto/login-code.dto';
import { SocialLoginDto } from './dto/social-login.dto';
export declare class AuthService {
    private prisma;
    private jwtService;
    private socialAuthService;
    private readonly logger;
    constructor(prisma: PrismaService, jwtService: JwtService, socialAuthService: SocialAuthService);
    register(registerDto: RegisterDto): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        token: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        image: string | null;
        token: string;
    }>;
    loginWithCode(loginCodeDto: LoginCodeDto): Promise<{
        status: string;
        message: string;
        id?: undefined;
        email?: undefined;
        name?: undefined;
        token?: undefined;
    } | {
        id: string;
        email: string | null;
        name: string | null;
        token: string;
        status?: undefined;
        message?: undefined;
    }>;
    socialLogin(socialLoginDto: SocialLoginDto): Promise<{
        id: any;
        email: any;
        name: any;
        image: any;
        token: string;
    }>;
    private generateToken;
    private generateTokens;
    webLogin(loginDto: LoginDto): Promise<{
        user: {
            id: string;
            email: string | null;
            name: string | null;
            image: string | null;
            role: import(".prisma/client").UserRole;
        };
        accessToken: string;
        refreshToken: string;
    }>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
}
