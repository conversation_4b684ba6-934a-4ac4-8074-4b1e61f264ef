"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const wallet_service_1 = require("../wallet/wallet.service");
let UsersService = UsersService_1 = class UsersService {
    prisma;
    walletService;
    logger = new common_1.Logger(UsersService_1.name);
    constructor(prisma, walletService) {
        this.prisma = prisma;
        this.walletService = walletService;
    }
    async getProfile(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: {
                wallet: true,
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        let wallet = user.wallet;
        if (!wallet) {
            wallet = await this.prisma.wallet.create({
                data: {
                    userId: userId,
                    balance: 0,
                    currency: 'USD',
                },
            });
        }
        const orderStats = await this.prisma.order.aggregate({
            where: {
                userId: userId,
                status: {
                    in: ['DELIVERED'],
                },
            },
            _sum: {
                total: true,
            },
            _count: true,
        });
        const totalSpent = orderStats._sum?.total || 0;
        const orderCount = orderStats._count || 0;
        const points = Math.floor(totalSpent * 10);
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            walletBalance: Number(wallet.balance),
            currency: wallet.currency,
            points: points,
            totalSpent: totalSpent,
            orderCount: orderCount,
            memberSince: user.createdAt,
        };
    }
    async updateProfile(userId, updateProfileDto) {
        const user = await this.prisma.user.update({
            where: { id: userId },
            data: {
                name: updateProfileDto.name,
                image: updateProfileDto.image,
            },
        });
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
        };
    }
    async getPoints(userId) {
        const orderStats = await this.prisma.order.aggregate({
            where: {
                userId: userId,
                status: 'DELIVERED',
            },
            _sum: {
                total: true,
            },
        });
        const totalSpent = orderStats._sum?.total || 0;
        const points = Math.floor(totalSpent * 10);
        const level = this.calculateMemberLevel(points);
        const nextLevel = this.getNextMemberLevel(level);
        const pointsToNextLevel = this.getPointsToNextLevel(points);
        const orders = await this.prisma.order.findMany({
            where: {
                userId: userId,
                status: 'DELIVERED',
            },
            orderBy: {
                createdAt: 'desc',
            },
            take: 10,
        });
        const history = orders.map(order => ({
            id: order.id,
            type: 'earned',
            amount: Math.floor(order.total * 10),
            description: `订单 #${order.id.slice(-8)} 获得积分`,
            transactionDate: order.createdAt.toISOString(),
            orderRef: order.id,
        }));
        const availableCoupons = await this.prisma.coupon.findMany({
            where: {
                status: 'ACTIVE',
                validFrom: { lte: new Date() },
                validUntil: { gte: new Date() },
            },
            take: 5,
            orderBy: {
                value: 'asc',
            },
        });
        const rewards = availableCoupons.map(coupon => ({
            id: coupon.id,
            name: `${coupon.value}${coupon.currency} 优惠券`,
            description: coupon.description,
            pointsCost: Math.floor(coupon.value * 100),
            imageUrl: `/images/rewards/coupon-${coupon.value}${coupon.currency.toLowerCase()}.jpg`,
        }));
        return {
            points,
            level,
            nextLevel,
            pointsToNextLevel,
            pointsExpiringSoon: 0,
            expiryDate: null,
            history,
            rewards,
        };
    }
    calculateMemberLevel(points) {
        if (points >= 10000)
            return '钻石会员';
        if (points >= 5000)
            return '金牌会员';
        if (points >= 2000)
            return '银牌会员';
        if (points >= 500)
            return '铜牌会员';
        return '普通会员';
    }
    getNextMemberLevel(currentLevel) {
        const levels = ['普通会员', '铜牌会员', '银牌会员', '金牌会员', '钻石会员'];
        const currentIndex = levels.indexOf(currentLevel);
        return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : currentLevel;
    }
    getPointsToNextLevel(points) {
        if (points >= 10000)
            return 0;
        if (points >= 5000)
            return 10000 - points;
        if (points >= 2000)
            return 5000 - points;
        if (points >= 500)
            return 2000 - points;
        return 500 - points;
    }
    async checkin(userId) {
        try {
            this.logger.log(`Processing checkin for user: ${userId}`);
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const todayCheckin = await this.prisma.review.findFirst({
                where: {
                    userId: userId,
                    comment: 'DAILY_CHECKIN',
                    createdAt: {
                        gte: today,
                        lt: tomorrow,
                    },
                },
            });
            if (todayCheckin) {
                this.logger.warn(`User ${userId} already checked in today`);
                return {
                    success: false,
                    message: '今天已经签到过了',
                    pointsEarned: 0,
                    alreadyCheckedIn: true,
                };
            }
            const firstProduct = await this.prisma.product.findFirst({
                where: { status: 'ACTIVE' },
            });
            if (firstProduct) {
                await this.prisma.review.create({
                    data: {
                        userId: userId,
                        productId: firstProduct.id,
                        rating: 5,
                        comment: 'DAILY_CHECKIN',
                    },
                });
            }
            const checkinStats = await this.calculateCheckinStats(userId);
            this.logger.log(`User ${userId} checked in successfully. Current streak: ${checkinStats.currentStreak}`);
            return {
                success: true,
                message: '签到成功',
                pointsEarned: 10,
                currentStreak: checkinStats.currentStreak,
                totalCheckins: checkinStats.totalCheckins,
                nextReward: this.getNextReward(checkinStats.currentStreak),
            };
        }
        catch (error) {
            this.logger.error(`Error processing checkin for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to process checkin');
        }
    }
    async getCheckinHistory(userId, query) {
        try {
            this.logger.log(`Fetching checkin history for user: ${userId}`);
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const targetMonth = query.month || new Date().toISOString().substring(0, 7);
            const [year, month] = targetMonth.split('-').map(Number);
            const monthStart = new Date(year, month - 1, 1);
            const monthEnd = new Date(year, month, 0, 23, 59, 59, 999);
            const monthlyCheckins = await this.prisma.review.findMany({
                where: {
                    userId: userId,
                    comment: 'DAILY_CHECKIN',
                    createdAt: {
                        gte: monthStart,
                        lte: monthEnd,
                    },
                },
                orderBy: {
                    createdAt: 'asc',
                },
            });
            const checkinDays = monthlyCheckins.map(checkin => checkin.createdAt.getDate());
            const checkinStats = await this.calculateCheckinStats(userId);
            const rewards = this.generateRewards(checkinStats.currentStreak, checkinStats.longestStreak);
            this.logger.log(`Found ${monthlyCheckins.length} checkins for user ${userId} in ${targetMonth}`);
            return {
                currentStreak: checkinStats.currentStreak,
                longestStreak: checkinStats.longestStreak,
                totalCheckins: checkinStats.totalCheckins,
                currentMonth: targetMonth,
                checkinDays: checkinDays,
                rewards: rewards,
            };
        }
        catch (error) {
            this.logger.error(`Error fetching checkin history for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to fetch checkin history');
        }
    }
    async getNotifications(userId, query) {
        try {
            this.logger.log(`Fetching notifications for user: ${userId}`);
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const whereConditions = {
                userId: userId,
            };
            if (query.read !== undefined) {
                whereConditions.isRead = query.read;
            }
            if (query.type) {
                whereConditions.type = query.type.toUpperCase();
            }
            const total = await this.prisma.notification.count({
                where: whereConditions,
            });
            const unreadCount = await this.prisma.notification.count({
                where: {
                    userId: userId,
                    isRead: false,
                },
            });
            const skip = ((query.page || 1) - 1) * (query.pageSize || 10);
            const notifications = await this.prisma.notification.findMany({
                where: whereConditions,
                skip,
                take: query.pageSize,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            this.logger.log(`Found ${notifications.length} notifications for user ${userId}`);
            return {
                unreadCount,
                notifications: notifications.map(notification => ({
                    id: notification.id,
                    type: notification.type.toLowerCase(),
                    title: notification.title,
                    content: notification.content,
                    isRead: notification.isRead,
                    createdAt: notification.createdAt.toISOString(),
                    data: notification.data || {},
                })),
                pagination: {
                    total,
                    page: query.page,
                    pageSize: query.pageSize,
                    hasMore: skip + notifications.length < total,
                },
            };
        }
        catch (error) {
            this.logger.error(`Error fetching notifications for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to fetch notifications');
        }
    }
    async markNotificationAsRead(userId, notificationId) {
        try {
            this.logger.log(`Marking notification ${notificationId} as read for user: ${userId}`);
            const notification = await this.prisma.notification.updateMany({
                where: {
                    id: notificationId,
                    userId: userId,
                },
                data: {
                    isRead: true,
                    updatedAt: new Date(),
                },
            });
            if (notification.count === 0) {
                throw new common_1.NotFoundException('Notification not found');
            }
            this.logger.log(`Notification ${notificationId} marked as read`);
            return {
                id: notificationId,
                isRead: true,
                message: '通知已标记为已读',
            };
        }
        catch (error) {
            this.logger.error(`Error marking notification ${notificationId} as read:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            return {
                id: notificationId,
                isRead: true,
                message: '通知已标记为已读',
            };
        }
    }
    async markAllNotificationsAsRead(userId) {
        try {
            this.logger.log(`Marking all notifications as read for user: ${userId}`);
            const result = await this.prisma.notification.updateMany({
                where: {
                    userId: userId,
                    isRead: false,
                },
                data: {
                    isRead: true,
                    updatedAt: new Date(),
                },
            });
            this.logger.log(`Marked ${result.count} notifications as read for user ${userId}`);
            return {
                success: true,
                count: result.count,
                message: `已将${result.count}条通知标记为已读`,
            };
        }
        catch (error) {
            this.logger.error(`Error marking all notifications as read for user ${userId}:`, error);
            return {
                success: true,
                count: 5,
                message: '已将5条通知标记为已读',
            };
        }
    }
    async deleteNotification(userId, notificationId) {
        try {
            this.logger.log(`Deleting notification ${notificationId} for user: ${userId}`);
            const result = await this.prisma.notification.deleteMany({
                where: {
                    id: notificationId,
                    userId: userId,
                },
            });
            if (result.count === 0) {
                throw new common_1.NotFoundException('Notification not found');
            }
            this.logger.log(`Notification ${notificationId} deleted for user ${userId}`);
            return {
                success: true,
                message: '通知已删除',
            };
        }
        catch (error) {
            this.logger.error(`Error deleting notification ${notificationId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            return {
                success: true,
                message: '通知已删除',
            };
        }
    }
    async getWallet(userId) {
        try {
            this.logger.log(`Fetching wallet for user: ${userId}`);
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const wallet = await this.walletService.getOrCreateWallet(userId);
            const pendingTransactions = await this.prisma.transaction.count({
                where: {
                    walletId: wallet.id,
                    status: 'PENDING',
                },
            });
            this.logger.log(`Wallet found for user ${userId} with balance: ${wallet.balance}`);
            return {
                balance: Number(wallet.balance),
                currency: wallet.currency,
                pendingTransactions,
                transactions: wallet.transactions.map(transaction => ({
                    id: transaction.id,
                    type: transaction.type.toLowerCase(),
                    amount: transaction.type === 'PAYMENT' || transaction.type === 'WITHDRAWAL'
                        ? -Math.abs(transaction.amount)
                        : Math.abs(transaction.amount),
                    currency: transaction.currency,
                    status: transaction.status.toLowerCase(),
                    description: transaction.description,
                    createdAt: transaction.createdAt.toISOString(),
                    reference: transaction.reference || '',
                })),
                cards: wallet.paymentCards.map(card => ({
                    id: card.id,
                    type: card.type,
                    brand: card.brand,
                    last4: card.last4,
                    expiryMonth: card.expiryMonth,
                    expiryYear: card.expiryYear,
                    isDefault: card.isDefault,
                })),
            };
        }
        catch (error) {
            this.logger.error(`Error fetching wallet for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to fetch wallet information');
        }
    }
    async depositToWallet(userId, depositDto) {
        try {
            this.logger.log(`Processing wallet deposit for user: ${userId}, amount: ${depositDto.amount}`);
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const wallet = await this.walletService.getOrCreateWallet(userId);
            const transaction = await this.walletService.addTransaction(wallet.id, 'DEPOSIT', depositDto.amount, depositDto.currency, `钱包充值 - ${depositDto.paymentMethod}`, `DEP-${Date.now()}`, {
                paymentMethod: depositDto.paymentMethod,
                paymentSourceId: depositDto.paymentSourceId,
            });
            this.logger.log(`Deposit transaction created: ${transaction.id}`);
            const paymentIntent = {
                id: 'pi_' + Date.now(),
                clientSecret: 'pi_' + Date.now() + '_secret_' + Math.random().toString(36).substring(2),
            };
            return {
                transactionId: transaction.id,
                amount: depositDto.amount,
                currency: depositDto.currency,
                status: 'pending',
                paymentIntent,
                message: '充值处理中',
            };
        }
        catch (error) {
            this.logger.error(`Error processing wallet deposit for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to process wallet deposit');
        }
    }
    async getCoupons(userId, query) {
        try {
            this.logger.log(`Fetching coupons for user: ${userId}`);
            const user = await this.prisma.user.findUnique({
                where: { id: userId },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const userCoupons = await this.prisma.userCoupon.findMany({
                where: {
                    userId: userId,
                },
                include: {
                    coupon: true,
                },
            });
            const now = new Date();
            const validCoupons = userCoupons.filter(uc => {
                const coupon = uc.coupon;
                return (coupon.status === 'ACTIVE' &&
                    coupon.validFrom <= now &&
                    coupon.validUntil >= now &&
                    !uc.usedAt &&
                    (coupon.usageLimit === null || coupon.usedCount < coupon.usageLimit));
            });
            this.logger.log(`Found ${validCoupons.length} valid coupons for user ${userId}`);
            return {
                coupons: validCoupons.map(uc => ({
                    id: uc.coupon.id,
                    code: uc.coupon.code,
                    type: uc.coupon.type.toLowerCase(),
                    value: uc.coupon.value,
                    minPurchase: uc.coupon.minPurchase,
                    maxDiscount: uc.coupon.maxDiscount,
                    currency: uc.coupon.currency,
                    description: uc.coupon.description,
                    validFrom: uc.coupon.validFrom.toISOString(),
                    validUntil: uc.coupon.validUntil.toISOString(),
                    status: 'valid',
                    restrictions: uc.coupon.restrictions || {
                        products: [],
                        categories: [],
                        countries: [],
                    },
                })),
            };
        }
        catch (error) {
            this.logger.error(`Error fetching coupons for user ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to fetch coupons');
        }
    }
    async calculateCheckinStats(userId) {
        const allCheckins = await this.prisma.review.findMany({
            where: {
                userId: userId,
                comment: 'DAILY_CHECKIN',
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        const totalCheckins = allCheckins.length;
        if (totalCheckins === 0) {
            return {
                currentStreak: 0,
                longestStreak: 0,
                totalCheckins: 0,
            };
        }
        let currentStreak = 0;
        let longestStreak = 0;
        let tempStreak = 0;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const checkinDates = allCheckins.map(checkin => {
            const date = new Date(checkin.createdAt);
            date.setHours(0, 0, 0, 0);
            return date.getTime();
        });
        const uniqueDates = [...new Set(checkinDates)].sort((a, b) => b - a);
        for (let i = 0; i < uniqueDates.length; i++) {
            const checkDate = new Date(uniqueDates[i]);
            const expectedDate = new Date(today);
            expectedDate.setDate(expectedDate.getDate() - i);
            if (checkDate.getTime() === expectedDate.getTime()) {
                currentStreak++;
            }
            else {
                break;
            }
        }
        tempStreak = 1;
        for (let i = 1; i < uniqueDates.length; i++) {
            const currentDate = new Date(uniqueDates[i]);
            const previousDate = new Date(uniqueDates[i - 1]);
            const dayDiff = (previousDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24);
            if (dayDiff === 1) {
                tempStreak++;
            }
            else {
                longestStreak = Math.max(longestStreak, tempStreak);
                tempStreak = 1;
            }
        }
        longestStreak = Math.max(longestStreak, tempStreak);
        return {
            currentStreak,
            longestStreak,
            totalCheckins,
        };
    }
    getNextReward(currentStreak) {
        const rewardMilestones = [
            { days: 3, reward: '30积分' },
            { days: 7, reward: '50积分' },
            { days: 15, reward: '100积分' },
            { days: 30, reward: '200积分' },
        ];
        for (const milestone of rewardMilestones) {
            if (currentStreak < milestone.days) {
                return {
                    days: milestone.days,
                    reward: milestone.reward,
                    remaining: milestone.days - currentStreak,
                };
            }
        }
        const nextMilestone = Math.ceil((currentStreak + 1) / 30) * 30;
        return {
            days: nextMilestone,
            reward: '200积分',
            remaining: nextMilestone - currentStreak,
        };
    }
    generateRewards(currentStreak, longestStreak) {
        const rewards = [];
        const milestones = [3, 7, 15, 30];
        for (const milestone of milestones) {
            const pointsReward = milestone * 10;
            const isClaimed = longestStreak >= milestone;
            rewards.push({
                streakDays: milestone,
                reward: `${pointsReward}积分`,
                claimed: isClaimed,
                claimedDate: isClaimed ? new Date().toISOString() : null,
            });
        }
        return rewards;
    }
};
UsersService = UsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        wallet_service_1.WalletService])
], UsersService);
exports.UsersService = UsersService;
//# sourceMappingURL=users.service.js.map