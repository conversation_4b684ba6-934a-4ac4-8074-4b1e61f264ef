import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma.service';
import * as bcrypt from 'bcrypt';

@Injectable()
export class WebAdminService {
  constructor(private prisma: PrismaService) {}

  /**
   * 获取仪表板数据
   */
  async getDashboardData() {
    const [
      totalUsers,
      totalOrders,
      totalProducts,
      recentOrders,
      userStats,
      orderStats,
    ] = await Promise.all([
      // 总用户数
      this.prisma.user.count(),
      
      // 总订单数
      this.prisma.order.count(),
      
      // 总产品数
      this.prisma.product.count(),
      
      // 最近订单
      this.prisma.order.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: true,
        },
      }),
      
      // 用户统计
      this.prisma.user.groupBy({
        by: ['role'],
        _count: true,
      }),
      
      // 订单统计
      this.prisma.order.groupBy({
        by: ['status'],
        _count: true,
      }),
    ]);

    return {
      stats: {
        totalUsers,
        totalOrders,
        totalProducts,
      },
      recentOrders,
      userStats,
      orderStats,
    };
  }

  /**
   * 获取用户列表
   */
  async getUsers(query: any) {
    const { page = 1, limit = 20, search = '', role = 'all' } = query;
    const skip = (page - 1) * limit;

    const whereCondition: any = {};

    if (search) {
      whereCondition.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (role !== 'all') {
      whereCondition.role = role;
    }

    const [users, total] = await Promise.all([
      this.prisma.user.findMany({
        where: whereCondition,
        skip,
        take: parseInt(limit),
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          image: true,
          createdAt: true,
          _count: {
            select: {
              orders: true,
              reviews: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.user.count({ where: whereCondition }),
    ]);

    return {
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 根据ID获取用户详情
   */
  async getUserById(userId: string) {
    return await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        orders: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            items: true,
            payment: true,
          },
        },
        addresses: true,
        _count: {
          select: {
            orders: true,
            reviews: true,
            wishlist: true,
          },
        },
      },
    });
  }

  /**
   * 创建用户
   */
  async createUser(createUserDto: any) {
    const { name, email, password, role = 'CUSTOMER' } = createUserDto;

    // 检查邮箱是否已存在
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new Error('Email already exists');
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 创建用户
    const user = await this.prisma.user.create({
      data: {
        name,
        email,
        hashedPassword,
        role,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });

    return user;
  }

  /**
   * 更新用户
   */
  async updateUser(userId: string, updateUserDto: any) {
    const { password, ...updateData } = updateUserDto;

    // 如果包含密码，需要加密
    if (password) {
      updateData.hashedPassword = await bcrypt.hash(password, 10);
    }

    return await this.prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        image: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  /**
   * 删除用户
   */
  async deleteUser(userId: string) {
    return await this.prisma.user.delete({
      where: { id: userId },
    });
  }

  /**
   * 获取订单列表
   */
  async getOrders(query: any) {
    const { page = 1, limit = 20, status = 'all', search = '' } = query;
    const skip = (page - 1) * limit;

    const whereCondition: any = {};

    if (status !== 'all') {
      whereCondition.status = status;
    }

    if (search) {
      whereCondition.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [orders, total] = await Promise.all([
      this.prisma.order.findMany({
        where: whereCondition,
        skip,
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: true,
          payment: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.order.count({ where: whereCondition }),
    ]);

    return {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * 获取产品列表
   */
  async getProducts(query: any) {
    const { page = 1, limit = 20, category = 'all', status = 'all' } = query;
    const skip = (page - 1) * limit;

    const whereCondition: any = {};

    if (category !== 'all') {
      whereCondition.categoryId = category;
    }

    if (status !== 'all') {
      whereCondition.status = status;
    }

    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where: whereCondition,
        skip,
        take: parseInt(limit),
        include: {
          category: true,
          variants: true,
          _count: {
            select: {
              reviews: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.product.count({ where: whereCondition }),
    ]);

    return {
      products,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }
}
