(()=>{var e={};e.id=8614,e.ids=[8614],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},27370:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>h,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d}),a(1598),a(89090),a(26083),a(35866);var r=a(23191),s=a(88716),i=a(37922),o=a.n(i),n=a(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let d=["",{children:["how-it-works",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1598)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\how-it-works\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\how-it-works\\page.tsx"],x="/how-it-works/page",h={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/how-it-works/page",pathname:"/how-it-works",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},35303:()=>{},1598:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(19510),s=a(63954),i=a(77666),o=a(27162);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,o.Z)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);var l=a(26299);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,o.Z)("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),c=(0,o.Z)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var x=a(31958);let h=({icon:e,title:t,description:a})=>(0,r.jsxs)("div",{className:"flex gap-4 p-6 rounded-2xl bg-white/50 border border-gray-200 hover:border-pink-200    transition-all duration-300 hover:shadow-lg hover:shadow-pink-500/5",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("div",{className:"w-12 h-12 flex items-center justify-center rounded-xl bg-gradient-to-br    from-[#F799A6]/20 to-[#B82E4E]/20",children:r.jsx(e,{className:"w-6 h-6 text-[#B82E4E]"})})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900",children:t}),r.jsx("p",{className:"text-gray-600 leading-relaxed",children:a})]})]});function p(){return(0,r.jsxs)("main",{className:"min-h-screen bg-gradient-to-b from-white to-pink-50/50",children:[(0,r.jsxs)("div",{className:"pt-24 pb-16 text-center",children:[r.jsx("h1",{className:"text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6]    to-[#B82E4E] text-transparent bg-clip-text",children:"How Yolloo eSIM Works"}),r.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto px-4 leading-relaxed",children:"Experience seamless global connectivity with our digital eSIM technology. Stay connected anywhere in the world with just a few simple steps."})]}),r.jsx(s.Z,{}),r.jsx("section",{className:"py-20 px-4",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-6xl",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h2",{className:"text-4xl font-bold mb-6 text-gray-900",children:"Why Choose Our eSIM Service?"}),r.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Discover the advantages of using our eSIM technology for your global connectivity needs"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsx(h,{icon:i.Z,title:"Global Coverage",description:"Connect to reliable networks in over 190+ countries and regions worldwide"}),r.jsx(h,{icon:n,title:"Instant Activation",description:"Get connected immediately after purchase with our instant digital delivery"}),r.jsx(h,{icon:l.Z,title:"Flexible Plans",description:"Choose from a variety of data plans that suit your travel needs and budget"}),r.jsx(h,{icon:d,title:"24/7 Support",description:"Our dedicated support team is always ready to assist you with any questions"}),r.jsx(h,{icon:c,title:"Secure Connection",description:"Enterprise-grade security ensures your data and privacy are protected"}),r.jsx(h,{icon:x.Z,title:"High-Speed Data",description:"Enjoy fast and reliable 4G/5G connections wherever you go"})]})]})}),r.jsx("section",{className:"py-20 px-4 bg-white",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[r.jsx("h2",{className:"text-4xl font-bold mb-6 text-gray-900",children:"Frequently Asked Questions"}),r.jsx("p",{className:"text-xl text-gray-600",children:"Everything you need to know about our eSIM service"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100",children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900",children:"What is an eSIM?"}),r.jsx("p",{className:"text-gray-600",children:"An eSIM (embedded SIM) is a digital SIM card that allows you to activate a cellular plan without using a physical SIM card. It's built into your device and can be programmed remotely."})]}),(0,r.jsxs)("div",{className:"p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100",children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900",children:"Is my device eSIM compatible?"}),r.jsx("p",{className:"text-gray-600",children:"Most recent smartphones support eSIM technology, including iPhone XS and newer models, Google Pixel 3 and newer, and many recent Samsung devices. Check your device settings or contact us to confirm compatibility."})]}),(0,r.jsxs)("div",{className:"p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100",children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900",children:"How do I activate my eSIM?"}),r.jsx("p",{className:"text-gray-600",children:"After purchase, you'll receive a QR code. Simply scan this code with your phone's camera, follow the on-screen instructions to download the eSIM profile, and activate it in your device settings."})]}),(0,r.jsxs)("div",{className:"p-6 rounded-2xl bg-gradient-to-br from-pink-50/50 to-white border border-pink-100",children:[r.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900",children:"Can I use multiple eSIMs?"}),r.jsx("p",{className:"text-gray-600",children:"Yes, most eSIM-compatible devices can store multiple eSIM profiles, though usually only one can be active at a time. This makes it easy to switch between different plans or providers."})]})]})]})})]})}},63954:(e,t,a)=>{"use strict";a.d(t,{Z:()=>c});var r=a(19510),s=a(49622),i=a(50657),o=a(74533);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(27162).Z)("Signal",[["path",{d:"M2 20h.01",key:"4haj6o"}],["path",{d:"M7 20v-4",key:"j294jx"}],["path",{d:"M12 20v-8",key:"i3yub9"}],["path",{d:"M17 20V8",key:"1tkaf5"}],["path",{d:"M22 4v16",key:"sih9yq"}]]);var l=a(73289);let d=({icon:e,step:t,title:a,description:s})=>(0,r.jsxs)("div",{className:"flex flex-col items-center text-center p-8 rounded-3xl bg-gradient-to-br from-[#F799A6]/10 to-[#B82E4E]/10    backdrop-blur-sm transition-all duration-500 hover:from-[#F799A6]/15 hover:to-[#B82E4E]/15    hover:shadow-[0_8px_32px_rgba(247,153,166,0.2)] hover:-translate-y-1 border border-[#F799A6]/30    hover:border-[#F799A6]/50 group",children:[r.jsx("div",{className:"relative",children:r.jsx("div",{className:"w-20 h-20 flex items-center justify-center rounded-2xl bg-gradient-to-br from-[#B82E4E]/20    to-[#F799A6]/20 mb-6 shadow-[0_4px_16px_rgba(247,153,166,0.2)]",children:r.jsx(e,{className:"w-10 h-10 text-[#B82E4E] transition-transform duration-500 group-hover:scale-110"})})}),r.jsx("div",{className:"px-4 py-1 bg-gradient-to-r from-[#F799A6]/20 to-[#B82E4E]/20 rounded-full text-[#B82E4E]    font-semibold mb-3 transition-all duration-500 shadow-sm shadow-pink-500/10 border border-[#F799A6]/30",children:t}),r.jsx("h3",{className:"font-semibold text-xl mb-3 text-gray-900 group-hover:text-[#B82E4E] transition-colors duration-300",children:a}),r.jsx("p",{className:"text-gray-600 leading-relaxed",children:s})]});function c(){return r.jsx(l.X,{isWhite:!1,children:(0,r.jsxs)("div",{className:"container",children:[(0,r.jsxs)("div",{className:"text-center mb-20",children:[r.jsx("div",{className:"inline-block px-6 py-2 bg-gradient-to-br from-[#F799A6]/20 to-[#B82E4E]/20 backdrop-blur-sm    rounded-full border border-[#F799A6]/30 text-[#B82E4E] font-semibold tracking-wide mb-4 shadow-lg shadow-pink-500/10",children:"Simple Setup Process"}),r.jsx("h2",{className:"text-4xl sm:text-5xl font-bold mb-6 bg-gradient-to-r from-[#B82E4E] via-[#F799A6] to-[#B82E4E]    text-transparent bg-clip-text",children:"Get Started in Minutes"}),r.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed font-medium",children:"Our streamlined activation process ensures you can start using your eSIM right away, with no technical expertise required."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto",children:[r.jsx(d,{icon:s.Z,step:"Step 1",title:"Choose Plan",description:"Select your ideal data plan from our range of global coverage options"}),r.jsx(d,{icon:i.Z,step:"Step 2",title:"Instant Delivery",description:"Receive your eSIM QR code immediately in your email after purchase"}),r.jsx(d,{icon:o.Z,step:"Step 3",title:"Quick Install",description:"Scan the QR code with your phone camera to install the eSIM profile"}),r.jsx(d,{icon:n,step:"Step 4",title:"Ready to Go",description:"Activate your eSIM with one tap and enjoy global connectivity"})]})]})})}},73289:(e,t,a)=>{"use strict";a.d(t,{X:()=>s});var r=a(19510);function s({children:e,className:t="",withGradient:a=!0,withGrid:s=!0,withTopGradient:i=!1,isDark:o=!1,isWhite:n=!0}){return(0,r.jsxs)("section",{className:`relative py-32 overflow-hidden ${o?"bg-[#1a1818]":n?"bg-white":"bg-[#f7f9fc]"} ${t}`,children:[(0,r.jsxs)("div",{className:"absolute inset-0 -z-10",children:[a&&r.jsx("div",{className:`absolute inset-0 bg-gradient-to-b ${o?"from-[#F799A6]/30 via-transparent to-transparent":"from-[#F799A6]/15 via-transparent to-[#B82E4E]/15"}`}),i&&r.jsx("div",{className:`absolute top-0 inset-x-0 h-32 bg-gradient-to-b ${o?"from-[#151313] to-[#1a1818]":n?"from-white to-white":"from-white to-[#f7f9fc]"}`}),r.jsx("div",{className:`absolute -top-[30%] -left-[10%] h-[60%] w-[60%] 
        bg-[radial-gradient(circle,${o?"rgba(247,153,166,0.4)":"rgba(247,153,166,0.3)"},transparent_70%)] blur-3xl`}),r.jsx("div",{className:`absolute -bottom-[20%] -right-[10%] h-[60%] w-[60%] 
        bg-[radial-gradient(circle,${o?"rgba(184,46,78,0.35)":"rgba(184,46,78,0.25)"},transparent_70%)] blur-3xl`}),r.jsx("div",{className:`absolute top-[40%] left-[50%] -translate-x-1/2 -translate-y-1/2 h-[40%] w-[80%] 
        bg-[radial-gradient(ellipse,rgba(247,153,166,0.25),transparent_70%)] blur-3xl`}),r.jsx("div",{className:`absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,${o?"rgba(247,153,166,0.25)":"rgba(247,153,166,0.2)"},transparent_50%)]`}),r.jsx("div",{className:`absolute inset-0 bg-[radial-gradient(circle_at_top_right,${o?"rgba(184,46,78,0.25)":"rgba(184,46,78,0.2)"},transparent_50%)]`}),s&&r.jsx("div",{className:`absolute inset-0 ${o?"bg-grid-white/[0.03]":"bg-grid-[#B82E4E]/[0.05]"}`}),(0,r.jsxs)("div",{className:"absolute inset-0",children:[r.jsx("div",{className:`absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent ${o?"via-[#F799A6]/20":"via-[#F799A6]/30"} to-transparent`}),r.jsx("div",{className:`absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent ${o?"via-[#B82E4E]/20":"via-[#B82E4E]/30"} to-transparent`})]}),!o&&r.jsx("div",{className:"absolute inset-0 bg-white/40 backdrop-blur-[1px]"})]}),e]})}},27162:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var r=a(71159);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim();/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:a=2,absoluteStrokeWidth:s,className:n="",children:l,iconNode:d,...c},x)=>(0,r.createElement)("svg",{ref:x,...o,width:t,height:t,stroke:e,strokeWidth:s?24*Number(a)/Number(t):a,className:i("lucide",n),...c},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let a=(0,r.forwardRef)(({className:a,...o},l)=>(0,r.createElement)(n,{ref:l,iconNode:t,className:i(`lucide-${s(e)}`,a),...o}));return a.displayName=`${e}`,a}},26299:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(27162).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},50657:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(27162).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},77666:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(27162).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},49622:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(27162).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},74533:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(27162).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},31958:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(27162).Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},57481:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>a(27370));module.exports=r})();