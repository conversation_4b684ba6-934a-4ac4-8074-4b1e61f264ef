import { Controller, Post, Body, Res, HttpStatus, Req } from '@nestjs/common';
import { Request, Response } from 'express';
import { WebPaymentsService } from './web-payments.service';
import { Public } from '../../common/decorators/public.decorator';

/**
 * Web支付控制器
 * 处理原主应用的支付相关API
 * 路由: /api/web/payments/*
 */
@Controller('api/web/payments')
export class WebPaymentsController {
  constructor(private readonly webPaymentsService: WebPaymentsService) {}

  /**
   * 创建支付意图
   * POST /api/web/payments
   */
  @Post()
  async createPayment(
    @Body() createPaymentDto: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const user = req['user'] as any;
      if (!user) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          error: 'Unauthorized',
        });
      }

      const payment = await this.webPaymentsService.createPayment(user.id, createPaymentDto);
      return res.status(HttpStatus.CREATED).json(payment);
    } catch (error) {
      console.error('[WEB_PAYMENT_CREATE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to create payment',
      });
    }
  }

  /**
   * Stripe Webhook处理
   * POST /api/web/payments/webhook
   */
  @Public()
  @Post('webhook')
  async handleWebhook(
    @Body() body: any,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    try {
      const signature = req.headers['stripe-signature'] as string;
      const result = await this.webPaymentsService.handleStripeWebhook(body, signature);
      return res.json(result);
    } catch (error) {
      console.error('[WEB_PAYMENT_WEBHOOK]', error);
      return res.status(HttpStatus.BAD_REQUEST).json({
        error: 'Webhook processing failed',
      });
    }
  }
}
