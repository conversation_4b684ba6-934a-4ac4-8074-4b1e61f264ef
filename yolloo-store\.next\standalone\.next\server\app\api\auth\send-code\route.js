"use strict";(()=>{var e={};e.id=3579,e.ids=[3579],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},50852:e=>{e.exports=require("async_hooks")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},41808:e=>{e.exports=require("net")},84492:e=>{e.exports=require("node:stream")},22037:e=>{e.exports=require("os")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},47300:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>_,patchFetch:()=>w,requestAsyncStorage:()=>y,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{POST:()=>m,dynamic:()=>p,fetchCache:()=>d,revalidate:()=>f});var n=r(49303),s=r(88716),i=r(60670),a=r(87070),u=r(7410),l=r(93475),c=r(60682);let p="force-dynamic",d="force-no-store",f=0,h=u.z.object({email:u.z.string().email("Invalid email address")});async function m(e){try{let t=await e.json(),{email:r}=h.parse(t),o=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e),n=`send_code:${o}:${r}`,s=await (0,l.yz)(n+":minute",1,60),i=await (0,l.yz)(n+":hour",5,3600);if(!s)return a.NextResponse.json({success:!1,error:"Please wait at least 1 minute before requesting another code"},{status:429});if(!i)return a.NextResponse.json({success:!1,error:"Too many verification codes requested. Please try again later"},{status:429});let u=Math.floor(1e5+9e5*Math.random()).toString();if(!await (0,l.AL)(r,u,300))return a.NextResponse.json({success:!1,error:"Failed to generate verification code. Please try again"},{status:500});try{let e=await (0,c.L5)(r,u,5);if(!e.success)return console.error("Failed to send verification email:",e.error),a.NextResponse.json({success:!1,error:"Failed to send verification code. Please try again"},{status:500})}catch(e){return console.error("Error sending verification email:",e),a.NextResponse.json({success:!1,error:"Failed to send verification code. Please try again"},{status:500})}return a.NextResponse.json({success:!0,message:"Verification code sent successfully. Please check your email."})}catch(e){if(console.error("Error in send-code API:",e),e instanceof u.z.ZodError)return a.NextResponse.json({success:!1,error:"Invalid email address"},{status:400});return a.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/auth/send-code/route",pathname:"/api/auth/send-code",filename:"route",bundlePath:"app/api/auth/send-code/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\auth\\send-code\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:y,staticGenerationAsyncStorage:x,serverHooks:v}=g,_="/api/auth/send-code/route";function w(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}},93475:(e,t,r)=>{r.d(t,{AL:()=>a,Ak:()=>u,qc:()=>l,yz:()=>c});var o=r(62197),n=r.n(o);let s=null;function i(){if(!s){let e=process.env.REDIS_URL||"redis://localhost:6379";(s=new(n())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),s.on("connect",()=>{console.log("Successfully connected to Redis")})}return s}async function a(e,t,r=300){try{let o=i(),n=`verification_code:${e}`;return await o.setex(n,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function u(e){try{let t=i(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function l(e){try{let t=i(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let o=i(),n=`rate_limit:${e}`,s=await o.get(n),a=s?parseInt(s):0;if(a>=t)return!1;return 0===a?await o.setex(n,r,"1"):await o.incr(n),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,s={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),o=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?o:`${o}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[o,n],...s]=a(e),{domain:i,expires:u,httponly:p,maxage:d,path:f,samesite:h,secure:m,partitioned:g,priority:y}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(n),domain:i,...u&&{expires:new Date(u)},...p&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:f,...h&&{sameSite:l.includes(t=(t=h).toLowerCase())?t:void 0},...m&&{secure:!0},...y&&{priority:c.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(s,{RequestCookies:()=>p,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>u,stringifyCookie:()=>i}),e.exports=((e,s,i,a)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let i of o(s))n.call(e,i)||void 0===i||t(e,i,{get:()=>s[i],enumerable:!(a=r(s,i))||a.enumerable});return e})(t({},"__esModule",{value:!0}),s);var l=["strict","lax","none"],c=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let n=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,o,n,s,i=[],a=0;function u(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;u();)if(","===(r=e.charAt(a))){for(o=a,a+=1,u(),n=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=n,i.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!s||a>=e.length)&&i.push(e.substring(t,e.length))}return i}(n)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,9092,5972,2197,5772,7410,5637,682],()=>r(47300));module.exports=o})();