"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WalletService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
let WalletService = WalletService_1 = class WalletService {
    prisma;
    logger = new common_1.Logger(WalletService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getOrCreateWallet(userId) {
        let wallet = await this.prisma.wallet.findUnique({
            where: { userId },
            include: {
                transactions: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                },
                paymentCards: {
                    orderBy: { createdAt: 'desc' },
                },
            },
        });
        if (!wallet) {
            wallet = await this.prisma.wallet.create({
                data: {
                    userId,
                    balance: 0,
                    currency: 'USD',
                },
                include: {
                    transactions: true,
                    paymentCards: true,
                },
            });
        }
        return wallet;
    }
    async addTransaction(walletId, type, amount, currency, description, reference, metadata) {
        return await this.prisma.transaction.create({
            data: {
                walletId,
                type,
                amount,
                currency,
                status: 'PENDING',
                description,
                reference,
                metadata,
            },
        });
    }
    async updateTransactionStatus(transactionId, status) {
        const transaction = await this.prisma.transaction.update({
            where: { id: transactionId },
            data: { status },
            include: { wallet: true },
        });
        if (status === 'COMPLETED') {
            const balanceChange = transaction.type === 'DEPOSIT' || transaction.type === 'REFUND'
                ? transaction.amount
                : -transaction.amount;
            await this.prisma.wallet.update({
                where: { id: transaction.walletId },
                data: {
                    balance: {
                        increment: balanceChange,
                    },
                },
            });
        }
        return transaction;
    }
    async processPayment(userId, amount, description, orderId) {
        const wallet = await this.getOrCreateWallet(userId);
        if (wallet.balance < amount) {
            throw new common_1.BadRequestException('Insufficient wallet balance');
        }
        const transaction = await this.addTransaction(wallet.id, 'PAYMENT', amount, wallet.currency, description, orderId, { orderId });
        await this.updateTransactionStatus(transaction.id, 'COMPLETED');
        return transaction;
    }
    async addPaymentCard(userId, cardData) {
        const wallet = await this.getOrCreateWallet(userId);
        if (cardData.isDefault) {
            await this.prisma.paymentCard.updateMany({
                where: { walletId: wallet.id },
                data: { isDefault: false },
            });
        }
        return await this.prisma.paymentCard.create({
            data: {
                walletId: wallet.id,
                ...cardData,
            },
        });
    }
    async removePaymentCard(userId, cardId) {
        const wallet = await this.getOrCreateWallet(userId);
        const result = await this.prisma.paymentCard.deleteMany({
            where: {
                id: cardId,
                walletId: wallet.id,
            },
        });
        if (result.count === 0) {
            throw new common_1.NotFoundException('Payment card not found');
        }
        return { success: true, message: 'Payment card removed' };
    }
    async setDefaultPaymentCard(userId, cardId) {
        const wallet = await this.getOrCreateWallet(userId);
        const card = await this.prisma.paymentCard.findFirst({
            where: {
                id: cardId,
                walletId: wallet.id,
            },
        });
        if (!card) {
            throw new common_1.NotFoundException('Payment card not found');
        }
        await this.prisma.paymentCard.updateMany({
            where: { walletId: wallet.id },
            data: { isDefault: false },
        });
        await this.prisma.paymentCard.update({
            where: { id: cardId },
            data: { isDefault: true },
        });
        return { success: true, message: 'Default payment card updated' };
    }
};
WalletService = WalletService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WalletService);
exports.WalletService = WalletService;
//# sourceMappingURL=wallet.service.js.map