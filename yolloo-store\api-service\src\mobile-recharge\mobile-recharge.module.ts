import { Module } from '@nestjs/common';
import { MobileRechargeService } from './mobile-recharge.service';
import { MobileRechargeController } from './mobile-recharge.controller';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [MobileRechargeController],
  providers: [MobileRechargeService, PrismaService],
})
export class MobileRechargeModule {}
