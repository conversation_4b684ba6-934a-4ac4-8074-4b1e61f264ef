(()=>{var e={};e.id=5571,e.ids=[5571],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},33758:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(64039),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),o=s(37922),i=s.n(o),n=s(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["terms",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64039)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\terms\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\terms\\page.tsx"],u="/terms/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/terms/page",pathname:"/terms",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35303:()=>{},64039:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(19510);s(71159);var a=s(50650);function o(){return r.jsx("div",{className:"py-16 bg-white",children:r.jsx("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto",children:[r.jsx("h1",{className:"text-4xl font-bold heading-gradient mb-8",children:"Terms of Usage"}),(0,r.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,r.jsxs)("p",{className:"text-gray-600",children:["Last updated: ",a.CN.forUserSafe(new Date)]}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"1. Acceptance of Terms"}),r.jsx("p",{children:"By accessing and using Yolloo's services, you agree to be bound by these Terms of Usage. If you do not agree to these terms, please do not use our services."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"2. Service Description"}),r.jsx("p",{children:"Yolloo provides eSIM services that allow users to access mobile data connectivity. Our services are subject to availability and may vary by region and device compatibility."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"3. User Responsibilities"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[r.jsx("li",{children:"Provide accurate and complete information when creating an account"}),r.jsx("li",{children:"Maintain the security of your account credentials"}),r.jsx("li",{children:"Use the services in compliance with applicable laws and regulations"}),r.jsx("li",{children:"Not engage in any unauthorized or fraudulent use of the service"})]}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"4. Payment and Billing"}),r.jsx("p",{children:"All purchases are final and non-refundable unless otherwise required by law. Prices are subject to change without notice. You agree to pay all charges at the prices then in effect for your purchases."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"5. Service Availability"}),r.jsx("p",{children:"While we strive to provide uninterrupted service, we do not guarantee continuous, uninterrupted access to our services. Network availability may vary and is subject to various factors beyond our control."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"6. Privacy Policy"}),r.jsx("p",{children:"Your use of our services is also governed by our Privacy Policy. Please review our Privacy Policy to understand how we collect, use, and protect your information."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"7. Limitation of Liability"}),r.jsx("p",{children:"To the maximum extent permitted by law, Yolloo shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"8. Changes to Terms"}),r.jsx("p",{children:"We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through our website. Continued use of our services after such modifications constitutes acceptance of the updated terms."}),r.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mt-8 mb-4",children:"9. Contact Information"}),(0,r.jsxs)("p",{children:["If you have any questions about these Terms, please contact us at"," ",r.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]})]})]})})})}},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>s(33758));module.exports=r})();