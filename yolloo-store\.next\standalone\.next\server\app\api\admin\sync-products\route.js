"use strict";(()=>{var e={};e.id=6081,e.ids=[6081],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},50490:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>C,patchFetch:()=>q,requestAsyncStorage:()=>x,routeModule:()=>D,serverHooks:()=>I,staticGenerationAsyncStorage:()=>S});var a={};r.r(a),r.d(a,{POST:()=>w,dynamic:()=>v,fetchCache:()=>h,revalidate:()=>T});var o=r(49303),n=r(88716),i=r(60670),s=r(87070),c=r(75571),d=r(90455),u=r(6570),p=r(43624),l=r(72331),y=r(89880),m=r(50650);function f(e){return e?e.replace(/[,，]/g,";"):e}class g{constructor(e){this.odooService=e}async syncProducts(){try{let e=[];console.log("[Sync] Starting product sync for all types");let t={totalProcessed:0,totalSkipped:0,totalUpdated:0,totalCreated:0,totalVariants:0,byType:{}};for(let r of y.Pm){t.byType[r]={processed:0,skipped:0,updated:0,created:0,variants:0};try{let a=await this.odooService.pullProducts(r);if(!a?.result?.data){console.error(`[Sync] Invalid response structure for ${r}`);continue}for(let o of a.result.data)try{t.totalProcessed++,t.byType[r].processed++;let a=(0,p.X)(o);if(t.totalVariants+=a.variants.length,t.byType[r].variants+=a.variants.length,0===a.variants.length&&0===a.price){console.log(`[Sync] Skipping product ${a.sku} with 0 variants and 0 price`),t.totalSkipped++,t.byType[r].skipped++;continue}let n=await l._.category.findFirst({where:{name:a.category}});n||(n=await l._.category.create({data:{name:a.category,description:y.rf.defaultDescription(a.category)}}));let i={name:(0,m.mo)(a.name),description:a.description,websiteDescription:a.websiteDescription,price:a.price,images:[],stock:a.stock,status:a.isActive?y.eM.ACTIVE:y.eM.INACTIVE,off_shelve:a.off_shelve,requiredUID:a.requiredUID,mcc:f(a.mcc),dataSize:a.dataSize,planType:a.planType,country:f(a.country),countryCode:f(a.countryCode),odooLastSyncAt:new Date,specifications:{odooId:a.metadata.odooId,odooProductCode:a.metadata.odooProductCode},category:{connect:{id:n.id}}},s=await l._.product.findUnique({where:{sku:a.sku}});await l._.product.upsert({where:{sku:a.sku},update:{...i,variants:{deleteMany:{},create:a.variants.map(e=>({price:Number(e.price),currency:e.currency||y.N_.currency,attributes:e.attributes,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType}))},parameters:{deleteMany:{},create:a.parameters.filter(e=>"mcc"!==e.code).map(e=>({code:e.code,name:e.name,value:e.value}))}},create:{...i,sku:a.sku,variants:{create:a.variants.map(e=>({price:Number(e.price),currency:e.currency||y.N_.currency,attributes:e.attributes,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType}))},parameters:{create:a.parameters.filter(e=>"mcc"!==e.code).map(e=>({code:e.code,name:e.name,value:e.value}))}},include:{variants:!0,parameters:!0}}),s?(t.totalUpdated++,t.byType[r].updated++):(t.totalCreated++,t.byType[r].created++),e.push(a)}catch(e){t.totalSkipped++,t.byType[r].skipped++}let o=t.byType[r];console.log(`[Sync] Type=${r}: processed=${o.processed}, created=${o.created}, updated=${o.updated}, skipped=${o.skipped}, variants=${o.variants}`)}catch(e){console.error(`[Sync] Error processing product type ${r}`)}}let r=await l._.product.findMany({include:{variants:!0,parameters:!0}});return console.log(`[Sync] Summary: processed=${t.totalProcessed}, created=${t.totalCreated}, updated=${t.totalUpdated}, skipped=${t.totalSkipped}, variants=${t.totalVariants}`),console.log(`[Sync] Total products in database: ${r.length}`),e}catch(e){throw console.error("[Sync] Error in syncProducts"),e}}async getProduct(e){try{let t=await l._.product.findFirst({where:{OR:[{sku:e},{specifications:{path:["odooProductCode"],equals:e}}]},include:{category:!0,variants:!0,parameters:!0}});if(t)return{id:t.id,name:t.name,description:t.description,websiteDescription:t.websiteDescription,price:t.price,currency:t.variants[0]?.currency||y.N_.currency,images:t.images,stock:t.stock,sku:t.sku,category:t.category.name,isActive:t.status===y.eM.ACTIVE,off_shelve:t.off_shelve,requiredUID:t.requiredUID,mcc:t.mcc??void 0,dataSize:t.dataSize??void 0,planType:t.planType??void 0,country:t.country??void 0,countryCode:t.countryCode??void 0,variants:t.variants.map(e=>({id:e.id,name:e.name,price:Number(e.price),currency:e.currency,attributes:e.attributes,duration:e.duration??void 0,durationType:e.durationType??void 0,variantCode:e.variantCode??e.code??void 0})),parameters:t.parameters.map(e=>({code:e.code,name:e.name,value:e.value})),metadata:{odooId:t.specifications?.odooId,odooProductCode:t.specifications?.odooProductCode}};return null}catch(t){return console.error(`Error getting product ${e}:`,t),null}}async updateProductStock(e){try{let t=await l._.product.findFirst({where:{specifications:{path:["odooProductCode"],equals:e}},include:{category:!0}});if(!t)throw Error("Product not found");let r=t.category?.name||"esim",a=await this.odooService.pullProducts(r.toLowerCase());a.result?.data?.find(t=>t.product_code===e)&&await l._.product.updateMany({where:{specifications:{path:["odooProductCode"],equals:e}},data:{stock:y.N_.stock}})}catch(t){throw console.error(`Error updating product stock for ${e}:`,t),t}}}let v="force-dynamic",h="force-no-store",T=0;async function w(){try{let e=await (0,c.getServerSession)(d.L);if(!e||"ADMIN"!==e.user.role)return new s.NextResponse("Unauthorized",{status:401});let t=(0,u.T)(y.tK),r=new g(t);return await r.syncProducts(),s.NextResponse.json({message:"Products synced successfully"})}catch(e){return console.error("[SYNC_PRODUCTS]",e),new s.NextResponse("Internal error",{status:500})}}let D=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/sync-products/route",pathname:"/api/admin/sync-products",filename:"route",bundlePath:"app/api/admin/sync-products/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\sync-products\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:S,serverHooks:I}=D,C="/api/admin/sync-products/route";function q(){return(0,i.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:S})}},50650:(e,t,r)=>{r.d(t,{CN:()=>m,ED:()=>f,QG:()=>v,T4:()=>u,cn:()=>d,eP:()=>T,mo:()=>h,vI:()=>g});var a=r(55761),o=r(62386),n=r(6180),i=r(4284),s=r(35772),c=r(21740);function d(...e){return(0,o.m6)((0,a.W)(e))}function u(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let p={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function l(){return p.TIMEZONE}function y(e){if(!e)return null;try{let t;if(e instanceof Date)t=e;else if("string"==typeof e)t=e.includes("T")||e.includes("Z")?(0,n.D)(e):new Date(e);else{if("number"!=typeof e)return null;t=new Date(e)}return(0,i.J)(t)?t:null}catch(t){return console.warn("Date parsing error:",t,"Input:",e),null}}let m={short:(e,t="Invalid Date")=>{let r=y(e);return r?(0,s.WU)(r,p.DATE_FORMAT):t},full:(e,t="Invalid Date")=>{let r=y(e);return r?(0,s.WU)(r,p.DATETIME_FORMAT):t},long:(e,t="Invalid Date")=>{let r=y(e);return r?r.toLocaleDateString(p.LOCALE,{year:"numeric",month:"long",day:"numeric"}):t},relative:(e,t="Invalid Date")=>{let r=y(e);return r?(0,c.Q)(r,{addSuffix:!0}):t},time:(e,t="Invalid Time")=>{let r=y(e);return r?(0,s.WU)(r,"HH:mm:ss"):t},timeShort:(e,t="Invalid Time")=>{let r=y(e);return r?(0,s.WU)(r,"HH:mm"):t},withTimezone:(e,t,r="Invalid Date")=>{let a=y(e);return a?new Intl.DateTimeFormat(p.LOCALE,{timeZone:t||p.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(a):r},forUser:(e,t="Invalid Date")=>{let r=y(e);if(!r)return t;let a=l();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:a,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserShort:(e,t="Invalid Date")=>{let r=y(e);if(!r)return t;let a=l();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:a,year:"numeric",month:"2-digit",day:"2-digit"}).format(r)},forUserSafe:(e,t="Invalid Date")=>{let r=y(e);if(!r)return t;let a=p.TIMEZONE;return new Intl.DateTimeFormat(p.LOCALE,{timeZone:a,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserRelative:(e,t="Invalid Date")=>{let r=y(e);return r?(0,c.Q)(r,{addSuffix:!0}):t},iso:(e,t="")=>{let r=y(e);return r?r.toISOString():t},custom:(e,t,r="Invalid Date")=>{let a=y(e);return a?(0,s.WU)(a,t):r}},f={addDays:(e,t)=>new Date((y(e)||new Date).getTime()+864e5*t),addHours:(e,t)=>new Date((y(e)||new Date).getTime()+36e5*t),addMinutes:(e,t)=>new Date((y(e)||new Date).getTime()+6e4*t),daysBetween:(e,t)=>{let r=y(e),a=y(t);return r&&a?Math.floor((a.getTime()-r.getTime())/864e5):0},isExpired:e=>{let t=y(e);return!!t&&t.getTime()<Date.now()}};function g(e){return e?e.replace(/\D/g,""):""}function v(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function h(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function T(e){let t=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return t?{badge:t[1].toUpperCase(),name:t[2]}:{name:e}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7624,9712,6257],()=>r(50490));module.exports=a})();