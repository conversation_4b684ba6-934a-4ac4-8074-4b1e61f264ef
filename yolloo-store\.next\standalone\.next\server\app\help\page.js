(()=>{var e={};e.id=3569,e.ids=[3569],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},60374:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),s(85443),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),o=s(37922),n=s.n(o),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c=["",{children:["help",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85443)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\help\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\help\\page.tsx"],u="/help/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/help/page",pathname:"/help",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35303:()=>{},85443:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(19510);function a(){return r.jsx("div",{className:"py-16 bg-background",children:r.jsx("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[r.jsx("h1",{className:"text-4xl font-bold heading-gradient mb-8",children:"Help Center"}),r.jsx("div",{className:"space-y-12",children:[{title:"Getting Started",items:[{question:"What is an eSIM?",answer:"An eSIM (embedded SIM) is a digital SIM that allows you to activate a cellular plan without using a physical SIM card. It's built directly into your device."},{question:"How do I install my eSIM?",answer:"Installation is simple: After purchase, you'll receive a QR code. Go to your device settings, select 'Add eSIM' or 'Add Cellular Plan', and scan the QR code. Follow the on-screen instructions to complete setup."}]},{title:"Account & Billing",items:[{question:"How do I manage my subscription?",answer:"You can manage your subscription through your account dashboard. Log in to view, modify, or cancel your plans."},{question:"What payment methods do you accept?",answer:"We accept all major credit cards, PayPal, and other popular payment methods. All transactions are secure and encrypted."}]},{title:"Technical Support",items:[{question:"My eSIM isn't working",answer:"First, ensure your device is eSIM compatible and that you've followed the installation steps correctly. If issues persist, contact our support team."},{question:"How do I check my data usage?",answer:"You can check your data usage in your device settings under the cellular or mobile data section, or log into your account dashboard."}]}].map((e,t)=>(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("h2",{className:"text-2xl font-semibold text-foreground",children:e.title}),r.jsx("div",{className:"space-y-4",children:e.items.map((e,t)=>(0,r.jsxs)("div",{className:"bg-card rounded-lg p-6",children:[r.jsx("h3",{className:"text-lg font-medium text-foreground mb-2",children:e.question}),r.jsx("p",{className:"text-muted-foreground",children:e.answer})]},t))})]},t))}),(0,r.jsxs)("div",{className:"mt-12 p-6 bg-card/50 rounded-lg",children:[r.jsx("h2",{className:"text-xl font-semibold text-foreground mb-4",children:"Still Need Help?"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"Can't find what you're looking for? Our support team is here to help."}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Email: ",r.jsx("a",{href:"mailto:<EMAIL>",className:"text-primary hover:underline",children:"<EMAIL>"})]}),r.jsx("p",{className:"text-muted-foreground",children:"Response time: Within 24 hours"})]})]})]})})})}s(71159)},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>s(60374));module.exports=r})();