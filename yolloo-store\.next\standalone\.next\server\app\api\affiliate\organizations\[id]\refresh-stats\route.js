"use strict";(()=>{var e={};e.id=424,e.ids=[424],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},46020:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>g,patchFetch:()=>y,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{POST:()=>f,dynamic:()=>c});var i=t(49303),o=t(88716),n=t(60670),s=t(87070),l=t(75571),u=t(90455),d=t(72331);let c="force-dynamic";async function f(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let t=r.id,a=await d._.affiliateOrganization.findUnique({where:{id:t}});if(!a)return s.NextResponse.json({error:"Organization not found"},{status:404});let i=await d._.affiliateProfile.findFirst({where:{userId:e.user.id,organizationId:t}});if(!i||!i.isAdmin)return s.NextResponse.json({error:"You don't have permission to refresh this organization's statistics"},{status:403});let o=(await d._.affiliateProfile.findMany({where:{organizationId:t},select:{id:!0}})).map(e=>e.id),n=await d._.affiliateReferral.findMany({where:{affiliateId:{in:o}},include:{order:!0}});for(let e of n)if(await d._.affiliateVisit.findFirst({where:{affiliateId:e.affiliateId,orderId:e.orderId}})||await d._.affiliateVisit.create({data:{affiliateId:e.affiliateId,organizationId:t,source:"direct",path:"/",userAgent:"Manual refresh",referrer:"Manual refresh",convertedToOrder:!0,orderId:e.orderId}}),!e.organizationCommissionId){let r=await d._.organizationCommission.findFirst({where:{organizationId:t,referrals:{some:{orderId:e.orderId}}}});if(r)await d._.affiliateReferral.update({where:{id:e.id},data:{organizationCommissionId:r.id}});else{let r=e.order;if(r){let i=r.total*a.commissionRate,o=await d._.organizationCommission.create({data:{organizationId:t,commissionAmount:i,status:"DELIVERED"===r.status?"APPROVED":"PENDING"}});await d._.affiliateReferral.update({where:{id:e.id},data:{organizationCommissionId:o.id}})}}}let c=await d._.organizationCommission.updateMany({where:{organizationId:t,status:"PENDING",referrals:{some:{order:{status:"DELIVERED"}}}},data:{status:"APPROVED"}}),f=await d._.affiliateReferral.updateMany({where:{affiliate:{organizationId:t},status:"PENDING",order:{status:"DELIVERED"}},data:{status:"APPROVED"}}),m=await d._.organizationCommission.aggregate({where:{organizationId:t,status:"APPROVED"},_sum:{commissionAmount:!0}}),p=await d._.affiliateReferral.aggregate({where:{affiliate:{organizationId:t},status:"APPROVED"},_sum:{commissionAmount:!0}}),h=m._sum.commissionAmount||0,w=p._sum.commissionAmount||0,g=h-w;await d._.affiliateOrganization.update({where:{id:t},data:{totalEarnings:g>0?g:0}});let y=await d._.affiliateVisit.count({where:{organizationId:t}}),_=await d._.affiliateVisit.count({where:{organizationId:t,convertedToOrder:!0}});return s.NextResponse.json({success:!0,message:"Organization statistics refreshed successfully",updatedVisits:n.length,updatedCommissions:c.count,updatedReferrals:f.count,stats:{totalVisits:y,totalConversions:_,conversionRate:y>0?_/y*100:0,totalCommissions:h,memberCommissions:w,organizationActualEarnings:g}})}catch(e){return console.error("Error refreshing organization statistics:",e),s.NextResponse.json({error:"Failed to refresh organization statistics"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/refresh-stats/route",pathname:"/api/affiliate/organizations/[id]/refresh-stats",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/refresh-stats/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\refresh-stats\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:w}=m,g="/api/affiliate/organizations/[id]/refresh-stats/route";function y(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:h})}},90455:(e,r,t)=>{t.d(r,{L:()=>d});var a=t(7585),i=t(72331),o=t(77234),n=t(53797),s=t(42023),l=t.n(s),u=t(93475);let d={adapter:{...(0,a.N)(i._),getUser:async e=>{let r=await i._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await i._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await i._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,n.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await i._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await i._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await i._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await i._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:o}){try{if(t&&t.id){let r=o?.headers||new Headers,n=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";a?u=a.code&&!a.password?"email_code":"password":e&&(u=e.provider),await i._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:n||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,i=new URL(a).searchParams.get("callbackUrl");if(i){let e=decodeURIComponent(i);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let o=await i._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return o?{id:o.id,name:o.name,email:o.email,picture:o.image,role:o.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>i});var a=t(53524);let i=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>d});var a=t(62197),i=t.n(a);let o=null;function n(){if(!o){let e=process.env.REDIS_URL||"redis://localhost:6379";(o=new(i())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),o.on("connect",()=>{console.log("Successfully connected to Redis")})}return o}async function s(e,r,t=300){try{let a=n(),i=`verification_code:${e}`;return await a.setex(i,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=n(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=n(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function d(e,r,t){try{let a=n(),i=`rate_limit:${e}`,o=await a.get(i),s=o?parseInt(o):0;if(s>=r)return!1;return 0===s?await a.setex(i,t,"1"):await a.incr(i),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var o=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&({}).hasOwnProperty.call(e,o)){var s=i?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(a,o,s):a[o]=e[o]}return a.default=e,t&&t.set(e,a),a}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===o[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return o[e]}}))})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>t(46020));module.exports=a})();