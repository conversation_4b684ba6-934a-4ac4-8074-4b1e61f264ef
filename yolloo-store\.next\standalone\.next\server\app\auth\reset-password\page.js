(()=>{var e={};e.id=2048,e.ids=[2048],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},81915:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(61397),r(89090),r(26083),r(35866);var t=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d=["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,61397)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\reset-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\reset-password\\page.tsx"],m="/auth/reset-password/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89620:(e,s,r)=>{Promise.resolve().then(r.bind(r,23745))},23745:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t=r(10326),a=r(17577),n=r(35047),i=r(90434),o=r(27256),l=r(74064),d=r(74723),c=r(90772),m=r(54432),p=r(55632),x=r(33071),u=r(77506),f=r(85999);let h=o.z.object({password:o.z.string().min(8,"Password must be at least 8 characters"),confirmPassword:o.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function j(){let e=(0,n.useRouter)(),s=(0,n.useSearchParams)(),r=s?.get("token"),[o,j]=(0,a.useState)(!0),[w,y]=(0,a.useState)(!1),[g,N]=(0,a.useState)(!1),[v,b]=(0,a.useState)(!1),[P,S]=(0,a.useState)(""),R=(0,d.cI)({resolver:(0,l.F)(h),defaultValues:{password:"",confirmPassword:""}}),_=async s=>{if(r)try{N(!0);let t=await fetch("/api/auth/reset-password",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({token:r,password:s.password})}),a=await t.json();if(!t.ok)throw Error(a.error||"Failed to reset password");b(!0),f.A.success("Password reset successful"),setTimeout(()=>{e.push("/auth/signin")},3e3)}catch(e){e instanceof Error?f.A.error(e.message):f.A.error("Failed to reset password")}finally{N(!1)}};return o?t.jsx("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:(0,t.jsxs)(x.Zb,{className:"w-[400px]",children:[(0,t.jsxs)(x.Ol,{className:"text-center",children:[t.jsx(x.ll,{children:"Reset Password"}),t.jsx(x.SZ,{children:"Verifying your reset link..."})]}),t.jsx(x.aY,{className:"flex justify-center py-6",children:t.jsx(u.Z,{className:"h-8 w-8 animate-spin text-primary"})})]})}):w||v?v?t.jsx("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:(0,t.jsxs)(x.Zb,{className:"w-[400px]",children:[(0,t.jsxs)(x.Ol,{className:"text-center",children:[t.jsx(x.ll,{children:"Password Updated"}),t.jsx(x.SZ,{children:"Your password has been reset successfully."})]}),(0,t.jsxs)(x.aY,{className:"text-center py-6",children:[t.jsx("p",{className:"mb-4 text-muted-foreground",children:"You will be redirected to the login page in a few seconds."}),t.jsx(c.Button,{asChild:!0,children:t.jsx(i.default,{href:"/auth/signin",children:"Sign In Now"})})]})]})}):t.jsx("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:(0,t.jsxs)(x.Zb,{className:"w-[400px]",children:[(0,t.jsxs)(x.Ol,{children:[t.jsx(x.ll,{children:"Reset Password"}),(0,t.jsxs)(x.SZ,{children:["Set a new password for ",P]})]}),t.jsx(x.aY,{children:t.jsx(p.l0,{...R,children:(0,t.jsxs)("form",{onSubmit:R.handleSubmit(_),className:"space-y-4",children:[t.jsx(p.Wi,{control:R.control,name:"password",render:({field:e})=>(0,t.jsxs)(p.xJ,{children:[t.jsx(p.lX,{children:"New Password"}),t.jsx(p.NI,{children:t.jsx(m.I,{type:"password",placeholder:"••••••••",...e,disabled:g})}),t.jsx(p.zG,{})]})}),t.jsx(p.Wi,{control:R.control,name:"confirmPassword",render:({field:e})=>(0,t.jsxs)(p.xJ,{children:[t.jsx(p.lX,{children:"Confirm Password"}),t.jsx(p.NI,{children:t.jsx(m.I,{type:"password",placeholder:"••••••••",...e,disabled:g})}),t.jsx(p.zG,{})]})}),(0,t.jsxs)(c.Button,{type:"submit",className:"w-full",disabled:g,children:[g&&t.jsx(u.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Reset Password"]})]})})}),t.jsx(x.eW,{className:"justify-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Remember your password?"," ",t.jsx(i.default,{href:"/auth/signin",className:"text-primary underline-offset-4 hover:underline",children:"Sign in"})]})})]})}):t.jsx("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:(0,t.jsxs)(x.Zb,{className:"w-[400px]",children:[(0,t.jsxs)(x.Ol,{className:"text-center",children:[t.jsx(x.ll,{children:"Invalid Reset Link"}),t.jsx(x.SZ,{children:"This password reset link is invalid or has expired."})]}),(0,t.jsxs)(x.aY,{className:"text-center py-6",children:[t.jsx("p",{className:"mb-4 text-muted-foreground",children:"Please request a new password reset link."}),t.jsx(c.Button,{asChild:!0,children:t.jsx(i.default,{href:"/auth/signin",children:"Return to Sign In"})})]})]})})}},33071:(e,s,r)=>{"use strict";r.d(s,{Ol:()=>o,SZ:()=>d,Zb:()=>i,aY:()=>c,eW:()=>m,ll:()=>l});var t=r(10326),a=r(17577),n=r(77863);let i=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));i.displayName="Card";let o=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...s},r)=>t.jsx("h3",{ref:r,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...s}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},r)=>t.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...s},r)=>t.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},55632:(e,s,r)=>{"use strict";r.d(s,{NI:()=>h,Wi:()=>m,l0:()=>d,lX:()=>f,pf:()=>j,xJ:()=>u,zG:()=>w});var t=r(10326),a=r(17577),n=r(34214),i=r(74723),o=r(77863),l=r(31048);let d=i.RV,c=a.createContext({}),m=({...e})=>t.jsx(c.Provider,{value:{name:e.name},children:t.jsx(i.Qr,{...e})}),p=()=>{let e=a.useContext(c),s=a.useContext(x),{getFieldState:r,formState:t}=(0,i.Gc)(),n=r(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=s;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...n}},x=a.createContext({}),u=a.forwardRef(({className:e,...s},r)=>{let n=a.useId();return t.jsx(x.Provider,{value:{id:n},children:t.jsx("div",{ref:r,className:(0,o.cn)("space-y-2",e),...s})})});u.displayName="FormItem";let f=a.forwardRef(({className:e,...s},r)=>{let{error:a,formItemId:n}=p();return t.jsx(l._,{ref:r,className:(0,o.cn)(a&&"text-destructive",e),htmlFor:n,...s})});f.displayName="FormLabel";let h=a.forwardRef(({...e},s)=>{let{error:r,formItemId:a,formDescriptionId:i,formMessageId:o}=p();return t.jsx(n.g7,{ref:s,id:a,"aria-describedby":r?`${i} ${o}`:`${i}`,"aria-invalid":!!r,...e})});h.displayName="FormControl";let j=a.forwardRef(({className:e,...s},r)=>{let{formDescriptionId:a}=p();return t.jsx("p",{ref:r,id:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...s})});j.displayName="FormDescription";let w=a.forwardRef(({className:e,children:s,...r},a)=>{let{error:n,formMessageId:i}=p(),l=n?String(n?.message):s;return l?t.jsx("p",{ref:a,id:i,className:(0,o.cn)("text-sm font-medium text-destructive",e),...r,children:l}):null});w.displayName="FormMessage"},31048:(e,s,r)=>{"use strict";r.d(s,{_:()=>d});var t=r(10326),a=r(17577),n=r(34478),i=r(79360),o=r(77863);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...s},r)=>t.jsx(n.f,{ref:r,className:(0,o.cn)(l(),e),...s}));d.displayName=n.f.displayName},61397:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var t=r(68570);let a=(0,t.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\reset-password\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let o=(0,t.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\reset-password\page.tsx#default`)},57481:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,1615,5772,7624,5634,6621,6908,4824],()=>r(81915));module.exports=t})();