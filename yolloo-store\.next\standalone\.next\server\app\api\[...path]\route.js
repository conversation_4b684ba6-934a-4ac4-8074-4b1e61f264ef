"use strict";(()=>{var e={};e.id=5602,e.ids=[5602],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9996:(e,t,n)=>{n.r(t),n.d(t,{originalPathname:()=>f,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>d,serverHooks:()=>x,staticGenerationAsyncStorage:()=>m});var r={};n.r(r),n.d(r,{DELETE:()=>c,GET:()=>p,POST:()=>i,PUT:()=>l,dynamic:()=>u});var a=n(49303),s=n(88716),o=n(60670);let u="force-dynamic";async function p(){return new Response(null,{status:404})}async function i(){return new Response(null,{status:404})}async function l(){return new Response(null,{status:404})}async function c(){return new Response(null,{status:404})}let d=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/[...path]/route",pathname:"/api/[...path]",filename:"route",bundlePath:"app/api/[...path]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\[...path]\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:m,serverHooks:x}=d,f="/api/[...path]/route";function v(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:m})}},49303:(e,t,n)=>{e.exports=n(30517)}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[8948],()=>n(9996));module.exports=r})();