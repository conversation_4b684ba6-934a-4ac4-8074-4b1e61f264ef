(()=>{var e={};e.id=6387,e.ids=[6387],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},54164:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>p}),r(84648),r(14417),r(89090),r(26083),r(35866);var a=r(23191),o=r(88716),i=r(37922),s=r.n(i),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let p=["",{children:["affiliate",{children:["organization",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84648)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,14417)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\page.tsx"],c="/affiliate/organization/page",u={require:r,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/affiliate/organization/page",pathname:"/affiliate/organization",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},98385:(e,t,r)=>{Promise.resolve().then(r.bind(r,14441))},35303:()=>{},14441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(10326);r(17577);var o=r(35047);function i(){return(0,o.useRouter)(),a.jsx("div",{className:"flex justify-center items-center h-64",children:a.jsx("p",{className:"text-muted-foreground",children:"Redirecting to Affiliate Center..."})})}},14417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>a});let a={title:"Affiliate Program | Yolloo Store",description:"Manage your affiliate program"};function o({children:e}){return e}},84648:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>s,__esModule:()=>i,default:()=>n});var a=r(68570);let o=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\page.tsx`),{__esModule:i,$$typeof:s}=o;o.default;let n=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(66621);let o=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(54164));module.exports=a})();