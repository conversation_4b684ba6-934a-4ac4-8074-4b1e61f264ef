import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';

@Injectable()
export class RatingService {
  private readonly logger = new Logger(RatingService.name);

  constructor(private prisma: PrismaService) {}

  async calculateProductRating(productId: string) {
    const reviews = await this.prisma.review.findMany({
      where: { productId },
      select: { rating: true },
    });

    if (reviews.length === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      };
    }

    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;

    // 计算评分分布
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach(review => {
      const rating = Math.round(review.rating) as 1 | 2 | 3 | 4 | 5;
      ratingDistribution[rating]++;
    });

    return {
      averageRating: Math.round(averageRating * 10) / 10,
      totalReviews: reviews.length,
      ratingDistribution,
    };
  }

  async getProductStatistics(productId: string) {
    const [reviews, orders, views] = await Promise.all([
      this.prisma.review.findMany({
        where: { productId },
        include: { user: { select: { name: true, image: true } } },
        orderBy: { createdAt: 'desc' },
      }),
      this.prisma.orderItem.count({
        where: { productId },
      }),
      this.prisma.productView.count({
        where: { productId },
      }),
    ]);

    const rating = await this.calculateProductRating(productId);

    return {
      ...rating,
      totalOrders: orders,
      totalViews: views,
      reviews: reviews.map(review => ({
        id: review.id,
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt,
        user: {
          name: review.user?.name || 'Anonymous',
          image: review.user?.image || '/images/defaults/user-avatar.jpg',
        },
      })),
    };
  }

  async updateProductPopularity(productId: string) {
    const stats = await this.getProductStatistics(productId);
    
    // 计算受欢迎程度分数
    const popularityScore = this.calculatePopularityScore(
      stats.averageRating,
      stats.totalReviews,
      stats.totalOrders,
      stats.totalViews
    );

    // 更新产品的受欢迎程度
    await this.prisma.product.update({
      where: { id: productId },
      data: {
        popularityScore,
        isPopular: popularityScore > 70, // 分数大于70认为是热门产品
      },
    });

    return { popularityScore, isPopular: popularityScore > 70 };
  }

  private calculatePopularityScore(
    averageRating: number,
    totalReviews: number,
    totalOrders: number,
    totalViews: number
  ): number {
    // 评分权重 (40%)
    const ratingScore = (averageRating / 5) * 40;
    
    // 评论数权重 (20%)
    const reviewScore = Math.min(totalReviews / 100, 1) * 20;
    
    // 订单数权重 (30%)
    const orderScore = Math.min(totalOrders / 50, 1) * 30;
    
    // 浏览数权重 (10%)
    const viewScore = Math.min(totalViews / 1000, 1) * 10;

    return Math.round(ratingScore + reviewScore + orderScore + viewScore);
  }

  async getTopRatedProducts(limit: number = 10, categoryId?: string) {
    const where: any = {
      status: 'ACTIVE',
      off_shelve: false,
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    const products = await this.prisma.product.findMany({
      where,
      include: {
        reviews: { select: { rating: true } },
        _count: {
          select: {
            orderItems: true,
            reviews: true,
          },
        },
      },
      take: limit * 2, // 获取更多产品以便筛选
    });

    // 计算每个产品的评分并排序
    const productsWithRating = products
      .map(product => {
        const reviews = product.reviews;
        const averageRating = reviews.length > 0
          ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
          : 0;

        return {
          ...product,
          averageRating: Math.round(averageRating * 10) / 10,
          totalReviews: reviews.length,
          totalOrders: product._count.orderItems,
        };
      })
      .filter(product => product.totalReviews >= 5) // 至少5个评价
      .sort((a, b) => {
        // 先按评分排序，再按评价数量排序
        if (b.averageRating !== a.averageRating) {
          return b.averageRating - a.averageRating;
        }
        return b.totalReviews - a.totalReviews;
      })
      .slice(0, limit);

    return productsWithRating;
  }

  async getMostPopularProducts(limit: number = 10, categoryId?: string) {
    const where: any = {
      status: 'ACTIVE',
      off_shelve: false,
      isPopular: true,
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    return await this.prisma.product.findMany({
      where,
      include: {
        reviews: { select: { rating: true } },
        _count: {
          select: {
            orderItems: true,
            reviews: true,
          },
        },
      },
      orderBy: [
        { popularityScore: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });
  }

  async recordProductView(productId: string, userId?: string, ipAddress?: string, userAgent?: string) {
    try {
      await this.prisma.productView.create({
        data: {
          productId,
          userId,
          ipAddress,
          userAgent,
        },
      });

      // 异步更新产品受欢迎程度
      this.updateProductPopularity(productId).catch(error => {
        this.logger.warn(`Failed to update popularity for product ${productId}:`, error);
      });

    } catch (error) {
      this.logger.warn(`Failed to record view for product ${productId}:`, error);
    }
  }

  async getUserRecommendations(userId: string, limit: number = 10) {
    // 获取用户的订单历史
    const userOrders = await this.prisma.order.findMany({
      where: { userId },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                categoryId: true,
                country: true,
              },
            },
          },
        },
      },
    });

    // 分析用户偏好
    const categoryPreferences = new Map<string, number>();
    const countryPreferences = new Map<string, number>();

    userOrders.forEach(order => {
      order.items.forEach(item => {
        if (item.product?.categoryId) {
          categoryPreferences.set(
            item.product.categoryId,
            (categoryPreferences.get(item.product.categoryId) || 0) + 1
          );
        }
        if (item.product?.country) {
          countryPreferences.set(
            item.product.country,
            (countryPreferences.get(item.product.country) || 0) + 1
          );
        }
      });
    });

    // 获取推荐产品
    const preferredCategories = Array.from(categoryPreferences.keys()).slice(0, 3);
    const preferredCountries = Array.from(countryPreferences.keys()).slice(0, 3);

    const recommendations = await this.prisma.product.findMany({
      where: {
        status: 'ACTIVE',
        off_shelve: false,
        OR: [
          { categoryId: { in: preferredCategories } },
          { country: { in: preferredCountries } },
          { isPopular: true },
        ],
      },
      include: {
        reviews: { select: { rating: true } },
        _count: {
          select: { orderItems: true, reviews: true },
        },
      },
      orderBy: [
        { popularityScore: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });

    return recommendations.map(product => {
      const reviews = product.reviews;
      const averageRating = reviews.length > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0;

      return {
        ...product,
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews: reviews.length,
        totalOrders: product._count.orderItems,
      };
    });
  }

  async batchUpdateProductPopularity() {
    const products = await this.prisma.product.findMany({
      where: {
        status: 'ACTIVE',
        off_shelve: false,
      },
      select: { id: true },
    });

    let updatedCount = 0;
    for (const product of products) {
      try {
        await this.updateProductPopularity(product.id);
        updatedCount++;
      } catch (error) {
        this.logger.warn(`Failed to update popularity for product ${product.id}:`, error);
      }
    }

    this.logger.log(`Updated popularity for ${updatedCount} products`);
    return { updatedCount, totalProducts: products.length };
  }

  async getProductRecommendations(productId: string, limit: number = 5) {
    // 获取当前产品信息
    const currentProduct = await this.prisma.product.findUnique({
      where: { id: productId },
      select: {
        categoryId: true,
        country: true,
        price: true,
      },
    });

    if (!currentProduct) {
      return [];
    }

    // 查找相似产品
    const similarProducts = await this.prisma.product.findMany({
      where: {
        id: { not: productId },
        status: 'ACTIVE',
        off_shelve: false,
        OR: [
          { categoryId: currentProduct.categoryId },
          { country: currentProduct.country },
          {
            price: {
              gte: currentProduct.price * 0.8,
              lte: currentProduct.price * 1.2,
            },
          },
        ],
      },
      include: {
        reviews: { select: { rating: true } },
        _count: {
          select: { orderItems: true, reviews: true },
        },
      },
      orderBy: [
        { popularityScore: 'desc' },
        { createdAt: 'desc' },
      ],
      take: limit,
    });

    return similarProducts.map(product => {
      const reviews = product.reviews;
      const averageRating = reviews.length > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0;

      return {
        ...product,
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews: reviews.length,
        totalOrders: product._count.orderItems,
      };
    });
  }
}
