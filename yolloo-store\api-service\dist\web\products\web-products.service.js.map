{"version": 3, "file": "web-products.service.js", "sourceRoot": "", "sources": ["../../../src/web/products/web-products.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,yDAAqD;AAErD,IACa,kBAAkB,GAD/B,MACa,kBAAkB;IACT;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAK7C,KAAK,CAAC,WAAW,CAAC,KAAU;QAC1B,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,KAAK,CAAC;QAE/D,MAAM,cAAc,GAAQ;YAC1B,MAAM;YACN,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,IAAI,QAAQ,EAAE;YACZ,cAAc,CAAC,UAAU,GAAG,QAAQ,CAAC;SACtC;QAED,IAAI,OAAO,EAAE;YACX,cAAc,CAAC,EAAE,GAAG;gBAClB,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;gBACvD,EAAE,WAAW,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;aAC5D,CAAC;SACH;QAED,IAAI,MAAM,EAAE;YACV,cAAc,CAAC,IAAI,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;SACjE;QAED,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACxC,KAAK,EAAE,cAAc;YACrB,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,KAAK,EAAE,IAAI;gBACX,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,IAAI;gBACjB,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;YACD,OAAO,EAAE;gBACP,SAAS,EAAE,KAAK;aACjB;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,SAAiB;QACpC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YACxB,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,OAAO,EAAE;oBACP,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE;gCACN,EAAE,EAAE,IAAI;gCACR,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,IAAI;6BACZ;yBACF;qBACF;oBACD,OAAO,EAAE;wBACP,SAAS,EAAE,MAAM;qBAClB;oBACD,IAAI,EAAE,EAAE;iBACT;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB;QACvB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClD,KAAK,EAAE;gBACL,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;aACvB;YACD,MAAM,EAAE;gBACN,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE,IAAI;aAClB;YACD,QAAQ,EAAE,CAAC,SAAS,CAAC;SACtB,CAAC,CAAC;QAGH,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,IAAI,OAAO,CAAC,OAAO,EAAE;gBACnB,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBACrE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC5B,IAAI,OAAO,EAAE;wBACX,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;qBACxB;gBACH,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,UAAoB;QACzC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACxC,KAAK,EAAE;gBACL,EAAE,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;gBACtB,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,KAAK;aAClB;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;aACf;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,IAAY;QACjC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACzC,KAAK,EAAE;gBACL,GAAG,EAAE,IAAI;gBACT,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,KAAK;aAClB;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;aACjB;SACF,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAAC,SAAiB;QAG5C,OAAO;YACL,SAAS;YACT,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,2CAA2C;SACrD,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,SAAiB,EAAE,KAAU;QAErD,OAAO;YACL,SAAS;YACT,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,wCAAwC;SAClD,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,oBAAoB,CAAC,KAAU;QACnC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,OAAO,EAAE,GAAG,KAAK,CAAC;QACnD,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAEhC,MAAM,cAAc,GAAQ;YAC1B,MAAM,EAAE,QAAQ;YAChB,UAAU,EAAE,KAAK;YACjB,GAAG,OAAO;SACX,CAAC;QAEF,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC3B,KAAK,EAAE,cAAc;gBACrB,IAAI;gBACJ,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;iBACf;gBACD,OAAO,EAAE;oBACP,SAAS,EAAE,MAAM;iBAClB;aACF,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACxB,KAAK,EAAE,cAAc;aACtB,CAAC;SACH,CAAC,CAAC;QAEH,OAAO;YACL,QAAQ;YACR,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC;gBACtB,KAAK;gBACL,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;aAChC;SACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,mBAAmB,CAAC,KAAU;QAElC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC/C,OAAO;YACL,QAAQ;YACR,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,iDAAiD;SAC3D,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,qBAAqB,CAAC,KAAU;QAGpC,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;CACF,CAAA;AA5OY,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,kBAAkB,CA4O9B;AA5OY,gDAAkB"}