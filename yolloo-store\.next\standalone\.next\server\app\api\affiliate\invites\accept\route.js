"use strict";(()=>{var e={};e.id=2226,e.ids=[2226],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},59321:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>j,patchFetch:()=>q,requestAsyncStorage:()=>x,routeModule:()=>v,serverHooks:()=>R,staticGenerationAsyncStorage:()=>_});var a={};t.r(a),t.d(a,{POST:()=>y,dynamic:()=>m,fetchCache:()=>f,revalidate:()=>g});var i=t(49303),n=t(88716),o=t(60670),s=t(87070),l=t(75571),u=t(90455),c=t(72331),d=t(7410),p=t(42023);let m="force-dynamic",f="force-no-store",g=0;d.z.object({inviteCode:d.z.string()});let w=d.z.object({inviteCode:d.z.string(),name:d.z.string().min(2),password:d.z.string().min(8)}),h=d.z.object({inviteCode:d.z.string(),email:d.z.string().email(),name:d.z.string().min(2),password:d.z.string().min(8)});async function y(e){try{let r=await e.json(),t=r.inviteCode;if(!t)return s.NextResponse.json({error:"Invite code is required"},{status:400});let a=await c._.organizationInvite.findUnique({where:{inviteCode:t},include:{organization:!0}});if(!a)return s.NextResponse.json({error:"Invite not found"},{status:404});if("PENDING"!==a.status)return s.NextResponse.json({error:`Invite is ${a.status.toLowerCase()}`},{status:400});if(a.expiresAt&&a.expiresAt<new Date)return await c._.organizationInvite.update({where:{id:a.id},data:{status:"EXPIRED"}}),s.NextResponse.json({error:"Invite has expired"},{status:400});let i=!a.email,n=a.email;if(i){let e=h.safeParse(r);if(!e.success)return s.NextResponse.json({error:"Email, name and password are required for registration",details:e.error.errors},{status:400});n=e.data.email,await c._.user.findUnique({where:{email:n}})}else if(!n)return s.NextResponse.json({error:"Invite has no associated email"},{status:400});let o=await c._.user.findUnique({where:{email:n}});if(o){let e=await (0,l.getServerSession)(u.L);if(!e?.user||e.user.email!==n)return s.NextResponse.json({error:"You must be logged in as the invited user to accept this invitation"},{status:401})}else if(i){let{name:e,password:t}=r,a=await (0,p.hash)(t,10);o=await c._.user.create({data:{name:e,email:n,hashedPassword:a,role:"CUSTOMER"}})}else{let e=w.safeParse(r);if(!e.success)return s.NextResponse.json({error:"Name and password are required for registration",details:e.error.errors},{status:400});let{name:t,password:a}=e.data,i=await (0,p.hash)(a,10);o=await c._.user.create({data:{name:t,email:n,hashedPassword:i,role:"CUSTOMER"}})}let d=await c._.affiliateProfile.findUnique({where:{userId:o.id}});if(!d){let e=`${o.name?.substring(0,3)||"AFF"}-${Math.random().toString(36).substring(2,8).toUpperCase()}`;d=await c._.affiliateProfile.create({data:{userId:o.id,code:e,commissionRate:a.organization.commissionRate,discountRate:a.organization.discountRate}})}if(d.organizationId){if(d.organizationId===a.organizationId)return s.NextResponse.json({success:!0,message:`You are already a member of ${a.organization.name}`,affiliate:d});return s.NextResponse.json({error:"You are already a member of an organization"},{status:400})}let m=await c._.affiliateProfile.update({where:{id:d.id},data:{organizationId:a.organizationId,commissionRate:a.organization.commissionRate,isAdmin:!1}});return s.NextResponse.json({success:!0,message:`You have joined ${a.organization.name}`,affiliate:m})}catch(e){return console.error("Error accepting invite:",e),s.NextResponse.json({error:"Failed to accept invite"},{status:500})}}let v=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/affiliate/invites/accept/route",pathname:"/api/affiliate/invites/accept",filename:"route",bundlePath:"app/api/affiliate/invites/accept/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\invites\\accept\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:_,serverHooks:R}=v,j="/api/affiliate/invites/accept/route";function q(){return(0,o.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:_})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var a=t(7585),i=t(72331),n=t(77234),o=t(53797),s=t(42023),l=t.n(s),u=t(93475);let c={adapter:{...(0,a.N)(i._),getUser:async e=>{let r=await i._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await i._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await i._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await i._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await i._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await i._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await i._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:n}){try{if(t&&t.id){let r=n?.headers||new Headers,o=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";a?u=a.code&&!a.password?"email_code":"password":e&&(u=e.provider),await i._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,i=new URL(a).searchParams.get("callbackUrl");if(i){let e=decodeURIComponent(i);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let n=await i._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>i});var a=t(53524);let i=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var a=t(62197),i=t.n(a);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(i())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,r,t=300){try{let a=o(),i=`verification_code:${e}`;return await a.setex(i,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let a=o(),i=`rate_limit:${e}`,n=await a.get(i),s=n?parseInt(n):0;if(s>=r)return!1;return 0===s?await a.setex(i,t,"1"):await a.incr(i),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=i?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(a,n,s):a[n]=e[n]}return a.default=e,t&&t.set(e,a),a}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005,7410],()=>t(59321));module.exports=a})();