(()=>{var e={};e.id=6862,e.ids=[6862],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},43181:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(83012),t(14417),t(89090),t(26083),t(35866);var r=t(23191),a=t(88716),i=t(37922),n=t.n(i),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["affiliate",{children:["organization",{children:["[id]",{children:["members",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,83012)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\[id]\\members\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,14417)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\[id]\\members\\page.tsx"],m="/affiliate/organization/[id]/members/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/affiliate/organization/[id]/members/page",pathname:"/affiliate/organization/[id]/members",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},70013:(e,s,t)=>{Promise.resolve().then(t.bind(t,40202))},35303:()=>{},40202:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>X});var r=t(10326),a=t(17577),i=t(35047),n=t(44099),l=t(77506),o=t(11890),d=t(79635),c=t(47035),m=t(90772),u=t(33071),x=t(54432),f=t(85999),p=t(97401),h=t(28758),g=t(90434),j=t(15940),b=t(567),v=t(27256),y=t(74064),N=t(74723),w=t(62288),k=t(55632),C=t(87673),A=t(96655),z=t(79210),R=t(43273),S=t(62881);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let Z=(0,S.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var E=t(30361);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let M=(0,S.Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var q=t(63685),T=t(41291),P=t(5932);let I=v.z.object({emails:v.z.string().min(1,"Please enter at least one email"),commissionRate:v.z.coerce.number().min(0,"Min is 0%").max(1,"Max is 100%").default(.5),isAdmin:v.z.boolean().default(!1)}),F=v.z.object({emails:v.z.string().min(1,"Please enter at least one email"),commissionRate:v.z.coerce.number().min(0,"Min is 0%").max(1,"Max is 100%").default(.5),isAdmin:v.z.boolean().default(!1)}),$=e=>e.split(/[,;\r\n]+/).map(e=>e.trim()).filter(e=>e.length>0);function _({organizationId:e}){let s=(0,i.useRouter)(),[t,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)("add-members"),[u,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)([]),[v,S]=(0,a.useState)(!1),_=(0,N.cI)({resolver:(0,y.F)(I),defaultValues:{emails:"",commissionRate:.5,isAdmin:!1}}),D=(0,N.cI)({resolver:(0,y.F)(F),defaultValues:{emails:"",commissionRate:.5,isAdmin:!1}}),V=async t=>{try{p(!0),g([]),S(!1);let r=$(t.emails);if(0===r.length){f.A.error("No valid emails found");return}let a=await n.Z.post(`/api/affiliate/organizations/${e}/members/batch`,{emails:r,commissionRate:t.commissionRate,isAdmin:t.isAdmin}),i=[...(a.data.addedMembers||[]).map(e=>({email:e.user.email,name:e.user.name,success:!0,message:"Member added successfully"})),...(a.data.errors||[]).map(e=>({email:e.email,success:!1,error:e.message||"Failed to add member"}))];g(i),S(!0);let l=i.filter(e=>e.success).length;l>0&&(f.A.success(`Successfully added ${l} members`),s.refresh()),l===r.length&&_.reset()}catch(e){console.error("Error bulk adding members:",e),f.A.error("Failed to bulk add members")}finally{p(!1)}},B=async t=>{try{p(!0),g([]),S(!1);let r=$(t.emails);if(0===r.length){f.A.error("No valid emails found");return}let a=(await n.Z.post(`/api/affiliate/organizations/${e}/invites/batch`,{emails:r,commissionRate:t.commissionRate,isAdmin:t.isAdmin})).data.results||[];g(a),S(!0);let i=a.filter(e=>e.success).length;i>0&&(f.A.success(`Successfully sent ${i} invitations`),s.refresh()),i===r.length&&D.reset()}catch(e){console.error("Error sending invitations:",e),f.A.error("Failed to send invitations")}finally{p(!1)}},G=()=>{g([]),S(!1),_.reset(),D.reset()};return(0,r.jsxs)(w.Vq,{open:t,onOpenChange:e=>{e||G(),o(e)},children:[r.jsx(w.hg,{asChild:!0,children:(0,r.jsxs)(m.Button,{variant:"default",children:[r.jsx(Z,{className:"mr-2 h-4 w-4"}),"Bulk Operations"]})}),(0,r.jsxs)(w.cZ,{className:"sm:max-w-[600px]",children:[(0,r.jsxs)(w.fK,{children:[r.jsx(w.$N,{children:"Bulk Member Operations"}),r.jsx(w.Be,{children:"Add existing users or invite new users to join your organization."})]}),(0,r.jsxs)(z.Tabs,{value:d,onValueChange:e=>{c(e),G()},className:"w-full",children:[(0,r.jsxs)(z.TabsList,{className:"grid w-full grid-cols-2",children:[r.jsx(z.TabsTrigger,{value:"add-members",children:"Add Existing Users"}),r.jsx(z.TabsTrigger,{value:"invite-users",children:"Invite New Users"})]}),v?(0,r.jsxs)("div",{className:"space-y-4 py-4",children:[r.jsx(R.bZ,{className:h.every(e=>e.success)?"bg-green-50":"bg-amber-50",children:(0,r.jsxs)(R.X,{children:[h.filter(e=>e.success).length," of ",h.length," operations completed successfully."]})}),r.jsx("div",{className:"max-h-[300px] overflow-y-auto border rounded-md",children:(0,r.jsxs)(j.iA,{children:[r.jsx(j.xD,{children:(0,r.jsxs)(j.SC,{children:[r.jsx(j.ss,{children:"Status"}),r.jsx(j.ss,{children:"Email"}),r.jsx(j.ss,{children:"Details"})]})}),r.jsx(j.RM,{children:h.map((e,s)=>(0,r.jsxs)(j.SC,{children:[r.jsx(j.pj,{children:e.success?r.jsx(E.Z,{className:"h-5 w-5 text-green-500"}):r.jsx(M,{className:"h-5 w-5 text-red-500"})}),r.jsx(j.pj,{children:e.email}),r.jsx(j.pj,{children:e.success?r.jsx(b.C,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:e.message||"Success"}):r.jsx(b.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200",children:!1===e.success&&e.error})})]},s))})]})}),(0,r.jsxs)(w.cN,{children:[r.jsx(m.Button,{variant:"secondary",onClick:G,children:"Start Over"}),r.jsx(m.Button,{onClick:()=>o(!1),children:"Done"})]})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(z.TabsContent,{value:"add-members",className:"space-y-4 py-4",children:r.jsx(k.l0,{..._,children:(0,r.jsxs)("form",{onSubmit:_.handleSubmit(V),className:"space-y-4",children:[r.jsx(k.Wi,{control:_.control,name:"emails",render:({field:e})=>(0,r.jsxs)(k.xJ,{children:[r.jsx(k.lX,{children:"Email Addresses"}),r.jsx(k.NI,{children:r.jsx(C.g,{placeholder:"Enter emails (one per line or comma-separated)",rows:6,...e,disabled:u})}),r.jsx(k.pf,{children:"Enter email addresses of existing users. Separate multiple emails with commas, semicolons, or new lines."}),r.jsx(k.zG,{})]})}),r.jsx(k.Wi,{control:_.control,name:"commissionRate",render:({field:e})=>(0,r.jsxs)(k.xJ,{children:[r.jsx(k.lX,{children:"Commission Rate (%)"}),r.jsx(k.NI,{children:r.jsx(x.I,{type:"number",step:"0.01",min:"0",max:"1",placeholder:"0.5",...e,disabled:u})}),r.jsx(k.pf,{children:"This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%)."}),r.jsx(k.zG,{})]})}),r.jsx(k.Wi,{control:_.control,name:"isAdmin",render:({field:e})=>(0,r.jsxs)(k.xJ,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(k.lX,{children:"Admin Access"}),r.jsx(k.pf,{children:"Grant administrator access to these members"})]}),r.jsx(k.NI,{children:r.jsx(A.r,{checked:e.value,onCheckedChange:e.onChange,disabled:u})})]})}),r.jsx(w.cN,{children:r.jsx(m.Button,{type:"submit",disabled:u,children:u?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(q.Z,{className:"mr-2 h-4 w-4"}),"Add Members"]})})})]})})}),r.jsx(z.TabsContent,{value:"invite-users",className:"space-y-4 py-4",children:r.jsx(k.l0,{...D,children:(0,r.jsxs)("form",{onSubmit:D.handleSubmit(B),className:"space-y-4",children:[r.jsx(k.Wi,{control:D.control,name:"emails",render:({field:e})=>(0,r.jsxs)(k.xJ,{children:[r.jsx(k.lX,{children:"Email Addresses to Invite"}),r.jsx(k.NI,{children:r.jsx(C.g,{placeholder:"Enter emails to invite (one per line or comma-separated)",rows:6,...e,disabled:u})}),r.jsx(k.pf,{children:"Enter email addresses to invite. Separate multiple emails with commas, semicolons, or new lines. Each recipient will receive an invitation email with a link to join your organization."}),r.jsx(k.zG,{})]})}),r.jsx(k.Wi,{control:D.control,name:"commissionRate",render:({field:e})=>(0,r.jsxs)(k.xJ,{children:[r.jsx(k.lX,{children:"Commission Rate (%)"}),r.jsx(k.NI,{children:r.jsx(x.I,{type:"number",step:"0.01",min:"0",max:"1",placeholder:"0.5",...e,disabled:u})}),r.jsx(k.pf,{children:"This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%)."}),r.jsx(k.zG,{})]})}),r.jsx(k.Wi,{control:D.control,name:"isAdmin",render:({field:e})=>(0,r.jsxs)(k.xJ,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(k.lX,{children:"Admin Access"}),r.jsx(k.pf,{children:"Grant administrator access to invited members"})]}),r.jsx(k.NI,{children:r.jsx(A.r,{checked:e.value,onCheckedChange:e.onChange,disabled:u})})]})}),(0,r.jsxs)(R.bZ,{className:"bg-blue-50 border-blue-200",children:[r.jsx(T.Z,{className:"h-4 w-4 text-blue-600"}),r.jsx(R.X,{className:"text-blue-800 ml-2",children:"Each invited user will receive an email with instructions to join your organization. Invitations are valid for 7 days."})]}),r.jsx(w.cN,{children:r.jsx(m.Button,{type:"submit",disabled:u,children:u?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(P.Z,{className:"mr-2 h-4 w-4"}),"Send Invitations"]})})})]})})})]})]})]})]})}var D=t(7027),V=t(32933),B=t(36283),G=t(21405),L=t(77863);function O({organizationId:e}){let[s,t]=(0,a.useState)(!1),[i,o]=(0,a.useState)(!1),[d,c]=(0,a.useState)(""),[u,p]=(0,a.useState)(""),[h,g]=(0,a.useState)(!1),[j,b]=(0,a.useState)(!1),v=async()=>{try{t(!0);let s=(await n.Z.get(`/api/affiliate/organizations/${e}/invites`)).data.find(e=>!e.email&&"PENDING"===e.status&&new Date(e.expiresAt)>new Date);if(s){let e=s.inviteCode,t=window.location.origin,r=`${t}/invite/${e}`,a=L.CN.forUserSafe(s.expiresAt);c(r),p(a)}else{let s=await n.Z.post(`/api/affiliate/organizations/${e}/invites/general`),t=s.data.inviteCode,r=window.location.origin,a=`${r}/invite/${t}`,i=L.CN.forUserSafe(s.data.expiresAt);c(a),p(i)}b(!1)}catch(e){console.error("Error with invite link:",e),f.A.error("Failed to generate invite link")}finally{t(!1)}},y=async()=>{try{o(!0);let s=await n.Z.post(`/api/affiliate/organizations/${e}/invites/general`),t=s.data.inviteCode,r=window.location.origin,a=`${r}/invite/${t}`,i=L.CN.forUserSafe(s.data.expiresAt);c(a),p(i),b(!1),f.A.success("New invite link generated")}catch(e){console.error("Error generating invite link:",e),f.A.error("Failed to generate new invite link")}finally{o(!1)}};return(0,r.jsxs)(w.Vq,{open:h,onOpenChange:e=>{g(e),e&&v()},children:[r.jsx(w.hg,{asChild:!0,children:(0,r.jsxs)(m.Button,{size:"sm",variant:"outline",className:"gap-1",children:[r.jsx(D.Z,{className:"h-4 w-4"}),"Invite Link"]})}),r.jsx(w.cZ,{className:"sm:max-w-md",children:s?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[r.jsx(l.Z,{className:"h-8 w-8 animate-spin text-primary"}),r.jsx("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading invite link..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(w.fK,{children:[r.jsx(w.$N,{children:"Organization Invite Link"}),r.jsx(w.Be,{children:"Share this link with anyone to join your organization. The link will expire in 7 days."})]}),(0,r.jsxs)("div",{className:"space-y-4 py-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(x.I,{readOnly:!0,value:d,className:"font-mono text-sm"}),r.jsx(m.Button,{size:"icon",variant:"outline",onClick:()=>{navigator.clipboard.writeText(d).then(()=>{b(!0),f.A.success("Invite link copied to clipboard"),setTimeout(()=>{b(!1)},2e3)})},disabled:!d,className:"shrink-0",title:"Copy to clipboard",children:j?r.jsx(V.Z,{className:"h-4 w-4"}):r.jsx(B.Z,{className:"h-4 w-4"})})]}),u&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["This link will expire on ",u]})]}),r.jsx("div",{className:"flex justify-between items-center",children:r.jsx(m.Button,{variant:"outline",size:"sm",onClick:y,disabled:i,className:"gap-1",children:i?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"h-4 w-4 animate-spin"}),"Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(G.Z,{className:"h-4 w-4"}),"Generate New Link"]})})})]})]})})]})}function X({params:e}){(0,i.useRouter)();let[s,t]=(0,a.useState)(!0),[v,y]=(0,a.useState)([]),[N,w]=(0,a.useState)(""),[k,C]=(0,a.useState)(!1),[A,z]=(0,a.useState)(!1),R=""===N.trim()?v:v.filter(e=>e.user.name.toLowerCase().includes(N.toLowerCase())||e.user.email.toLowerCase().includes(N.toLowerCase())),S=async(s,t)=>{try{await n.Z.patch(`/api/affiliate/organizations/${e.id}/members/${s}`,{isAdmin:!t}),y(v.map(e=>e.id===s?{...e,isAdmin:!t}:e)),f.A.success(`Member ${t?"removed from":"set as"} admin`)}catch(e){console.error("Error updating member admin status:",e),n.Z.isAxiosError(e)&&e.response?.status===403?f.A.error(e.response.data.error||"Permission denied for this operation"):f.A.error("Failed to update member status")}},Z=async s=>{if(confirm("Are you sure you want to remove this member?"))try{await n.Z.delete(`/api/affiliate/organizations/${e.id}/members/${s}`),y(v.filter(e=>e.id!==s)),f.A.success("Member removed successfully")}catch(e){console.error("Error removing member:",e),n.Z.isAxiosError(e)&&e.response?.status===403?f.A.error(e.response.data.error||"Permission denied for this operation"):f.A.error("Failed to remove member")}};return s?r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx(l.Z,{className:"h-8 w-8 animate-spin text-primary"})}):A?null:(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[r.jsx("div",{className:"mb-6 flex items-center justify-between",children:r.jsx(m.Button,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(g.default,{href:`/affiliate/organization/${e.id}`,children:[r.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Back to Organization"]})})}),(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(u.ll,{children:"Organization Members"}),r.jsx(u.SZ,{children:"Manage the members of your organization"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[k&&r.jsx(O,{organizationId:e.id}),k&&r.jsx(_,{organizationId:e.id})]})]}),r.jsx(u.aY,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(d.Z,{className:"h-4 w-4 text-muted-foreground"}),r.jsx(x.I,{placeholder:"Search members...",className:"max-w-sm",value:N,onChange:e=>w(e.target.value)})]}),0===v.length?r.jsx("div",{className:"text-center py-8 text-muted-foreground",children:"No members found. Invite members to join your organization."}):(0,r.jsxs)(j.iA,{children:[r.jsx(j.xD,{children:(0,r.jsxs)(j.SC,{children:[r.jsx(j.ss,{children:"Member"}),r.jsx(j.ss,{children:"Email"}),r.jsx(j.ss,{children:"Role"}),r.jsx(j.ss,{children:"Joined"}),r.jsx(j.ss,{className:"text-right",children:"Actions"})]})}),r.jsx(j.RM,{children:R.map(e=>(0,r.jsxs)(j.SC,{children:[r.jsx(j.pj,{className:"font-medium",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(h.qE,{className:"h-8 w-8",children:[r.jsx(h.F$,{src:e.user.image,alt:e.user.name}),r.jsx(h.Q5,{children:e.user.name.substring(0,2).toUpperCase()})]}),r.jsx("span",{children:e.user.name})]})}),r.jsx(j.pj,{children:e.user.email}),r.jsx(j.pj,{children:e.isAdmin?r.jsx(b.C,{variant:"outline",className:"bg-primary/10 text-primary",children:"Admin"}):r.jsx(b.C,{variant:"outline",className:"bg-muted text-muted-foreground",children:"Member"})}),r.jsx(j.pj,{children:(0,p.Q)(new Date(e.createdAt),{addSuffix:!0})}),r.jsx(j.pj,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex items-center justify-end gap-2",children:[!e.isAdmin&&r.jsx(m.Button,{variant:"outline",size:"sm",onClick:()=>S(e.id,e.isAdmin),children:"Make Admin"}),!e.isAdmin&&(0,r.jsxs)(m.Button,{variant:"destructive",size:"sm",onClick:()=>Z(e.id),children:[r.jsx(c.Z,{className:"h-4 w-4 mr-1"}),"Remove"]})]})})]},e.id))})]})]})})]})]})}},43273:(e,s,t)=>{"use strict";t.d(s,{Cd:()=>d,X:()=>c,bZ:()=>o});var r=t(10326),a=t(17577),i=t(79360),n=t(77863);let l=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:s,...t},a)=>r.jsx("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:s}),e),...t}));o.displayName="Alert";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s}));d.displayName="AlertTitle";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));c.displayName="AlertDescription"},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>l});var r=t(10326);t(17577);var a=t(79360),i=t(77863);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return r.jsx("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>m,ll:()=>o});var r=t(10326),a=t(17577),i=t(77863);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},62288:(e,s,t)=>{"use strict";t.d(s,{$N:()=>p,Be:()=>h,Vq:()=>o,cN:()=>f,cZ:()=>u,fK:()=>x,hg:()=>d});var r=t(10326),a=t(17577),i=t(11123),n=t(94019),l=t(77863);let o=i.fC,d=i.xz,c=i.h_;i.x8;let m=a.forwardRef(({className:e,...s},t)=>r.jsx(i.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));m.displayName=i.aV.displayName;let u=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(c,{children:[r.jsx(m,{}),(0,r.jsxs)(i.VY,{ref:a,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,r.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=i.VY.displayName;let x=({className:e,...s})=>r.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});x.displayName="DialogHeader";let f=({className:e,...s})=>r.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});f.displayName="DialogFooter";let p=a.forwardRef(({className:e,...s},t)=>r.jsx(i.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=i.Dx.displayName;let h=a.forwardRef(({className:e,...s},t)=>r.jsx(i.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));h.displayName=i.dk.displayName},55632:(e,s,t)=>{"use strict";t.d(s,{NI:()=>h,Wi:()=>m,l0:()=>d,lX:()=>p,pf:()=>g,xJ:()=>f,zG:()=>j});var r=t(10326),a=t(17577),i=t(34214),n=t(74723),l=t(77863),o=t(31048);let d=n.RV,c=a.createContext({}),m=({...e})=>r.jsx(c.Provider,{value:{name:e.name},children:r.jsx(n.Qr,{...e})}),u=()=>{let e=a.useContext(c),s=a.useContext(x),{getFieldState:t,formState:r}=(0,n.Gc)(),i=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},x=a.createContext({}),f=a.forwardRef(({className:e,...s},t)=>{let i=a.useId();return r.jsx(x.Provider,{value:{id:i},children:r.jsx("div",{ref:t,className:(0,l.cn)("space-y-2",e),...s})})});f.displayName="FormItem";let p=a.forwardRef(({className:e,...s},t)=>{let{error:a,formItemId:i}=u();return r.jsx(o._,{ref:t,className:(0,l.cn)(a&&"text-destructive",e),htmlFor:i,...s})});p.displayName="FormLabel";let h=a.forwardRef(({...e},s)=>{let{error:t,formItemId:a,formDescriptionId:n,formMessageId:l}=u();return r.jsx(i.g7,{ref:s,id:a,"aria-describedby":t?`${n} ${l}`:`${n}`,"aria-invalid":!!t,...e})});h.displayName="FormControl";let g=a.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:a}=u();return r.jsx("p",{ref:t,id:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...s})});g.displayName="FormDescription";let j=a.forwardRef(({className:e,children:s,...t},a)=>{let{error:i,formMessageId:n}=u(),o=i?String(i?.message):s;return o?r.jsx("p",{ref:a,id:n,className:(0,l.cn)("text-sm font-medium text-destructive",e),...t,children:o}):null});j.displayName="FormMessage"},31048:(e,s,t)=>{"use strict";t.d(s,{_:()=>d});var r=t(10326),a=t(17577),i=t(34478),n=t(79360),l=t(77863);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...s},t)=>r.jsx(i.f,{ref:t,className:(0,l.cn)(o(),e),...s}));d.displayName=i.f.displayName},96655:(e,s,t)=>{"use strict";t.d(s,{r:()=>w});var r=t(10326),a=t(17577),i=t(82561),n=t(48051),l=t(93095),o=t(52067),d=t(53405),c=t(2566),m=t(45226),u="Switch",[x,f]=(0,l.b)(u),[p,h]=x(u),g=a.forwardRef((e,s)=>{let{__scopeSwitch:t,name:l,checked:d,defaultChecked:c,required:u,disabled:x,value:f="on",onCheckedChange:h,form:g,...j}=e,[b,N]=a.useState(null),w=(0,n.e)(s,e=>N(e)),k=a.useRef(!1),C=!b||g||!!b.closest("form"),[A=!1,z]=(0,o.T)({prop:d,defaultProp:c,onChange:h});return(0,r.jsxs)(p,{scope:t,checked:A,disabled:x,children:[(0,r.jsx)(m.WV.button,{type:"button",role:"switch","aria-checked":A,"aria-required":u,"data-state":y(A),"data-disabled":x?"":void 0,disabled:x,value:f,...j,ref:w,onClick:(0,i.M)(e.onClick,e=>{z(e=>!e),C&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),C&&(0,r.jsx)(v,{control:b,bubbles:!k.current,name:l,value:f,checked:A,required:u,disabled:x,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=u;var j="SwitchThumb",b=a.forwardRef((e,s)=>{let{__scopeSwitch:t,...a}=e,i=h(j,t);return(0,r.jsx)(m.WV.span,{"data-state":y(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:s})});b.displayName=j;var v=e=>{let{control:s,checked:t,bubbles:i=!0,...n}=e,l=a.useRef(null),o=(0,d.D)(t),m=(0,c.t)(s);return a.useEffect(()=>{let e=l.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==t&&s){let r=new Event("click",{bubbles:i});s.call(e,t),e.dispatchEvent(r)}},[o,t,i]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:l,style:{...e.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function y(e){return e?"checked":"unchecked"}var N=t(77863);let w=a.forwardRef(({className:e,...s},t)=>r.jsx(g,{className:(0,N.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:t,children:r.jsx(b,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=g.displayName},15940:(e,s,t)=>{"use strict";t.d(s,{RM:()=>o,SC:()=>d,iA:()=>n,pj:()=>m,ss:()=>c,xD:()=>l});var r=t(10326),a=t(17577),i=t(77863);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));o.displayName="TableBody",a.forwardRef(({className:e,...s},t)=>r.jsx("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("th",{ref:t,className:(0,i.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s}));c.displayName="TableHead";let m=a.forwardRef(({className:e,...s},t)=>r.jsx("td",{ref:t,className:(0,i.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s}));m.displayName="TableCell",a.forwardRef(({className:e,...s},t)=>r.jsx("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},79210:(e,s,t)=>{"use strict";t.d(s,{Tabs:()=>l,TabsContent:()=>c,TabsList:()=>o,TabsTrigger:()=>d});var r=t(10326),a=t(17577),i=t(13239),n=t(77863);let l=i.fC,o=a.forwardRef(({className:e,...s},t)=>r.jsx(i.aV,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));o.displayName=i.aV.displayName;let d=a.forwardRef(({className:e,...s},t)=>r.jsx(i.xz,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.xz.displayName;let c=a.forwardRef(({className:e,...s},t)=>r.jsx(i.VY,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=i.VY.displayName},87673:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var r=t(10326),a=t(17577),i=t(77863);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},41291:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},30361:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7027:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},63685:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},14417:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a,metadata:()=>r});let r={title:"Affiliate Program | Yolloo Store",description:"Manage your affiliate program"};function a({children:e}){return e}},83012:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=t(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\[id]\members\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\[id]\members\page.tsx#default`)},57481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},53405:(e,s,t)=>{"use strict";t.d(s,{D:()=>a});var r=t(17577);function a(e){let s=r.useRef({value:e,previous:e});return r.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1615,5772,7624,5634,6621,1123,6908,3239,4099,4824],()=>t(43181));module.exports=r})();