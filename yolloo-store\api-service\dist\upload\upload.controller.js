"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const upload_service_1 = require("./upload.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const path = require("path");
const fs = require("fs");
let UploadController = class UploadController {
    uploadService;
    constructor(uploadService) {
        this.uploadService = uploadService;
    }
    async uploadImage(file, category = 'temp', req) {
        if (!file) {
            throw new common_1.BadRequestException('No file uploaded');
        }
        const validCategories = ['products', 'users', 'categories', 'rewards', 'temp'];
        if (!validCategories.includes(category)) {
            throw new common_1.BadRequestException('Invalid category');
        }
        return await this.uploadService.uploadImage(file, category, req.user?.id);
    }
    async uploadMultipleImages(files, category = 'temp', req) {
        if (!files || files.length === 0) {
            throw new common_1.BadRequestException('No files uploaded');
        }
        const validCategories = ['products', 'users', 'categories', 'rewards', 'temp'];
        if (!validCategories.includes(category)) {
            throw new common_1.BadRequestException('Invalid category');
        }
        return await this.uploadService.uploadMultipleImages(files, category, req.user?.id);
    }
    async getUpload(id) {
        return await this.uploadService.getUploadById(id);
    }
    async serveFile(id, res) {
        try {
            const upload = await this.uploadService.getUploadById(id);
            const filePath = path.join(process.cwd(), 'uploads', upload.path);
            if (!fs.existsSync(filePath)) {
                return res.status(404).json({ message: 'File not found' });
            }
            res.setHeader('Content-Type', upload.mimeType);
            res.setHeader('Cache-Control', 'public, max-age=31536000');
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        }
        catch (error) {
            return res.status(404).json({ message: 'Upload not found' });
        }
    }
    async deleteUpload(id, req) {
        return await this.uploadService.deleteUpload(id, req.user?.id);
    }
    async getUserUploads(req, category) {
        return await this.uploadService.getUserUploads(req.user.id, category);
    }
    async moveUpload(id, category, req) {
        const validCategories = ['products', 'users', 'categories', 'rewards'];
        if (!validCategories.includes(category)) {
            throw new common_1.BadRequestException('Invalid category');
        }
        const upload = await this.uploadService.getUploadById(id);
        if (upload.uploadedBy !== req.user.id) {
            throw new common_1.BadRequestException('Unauthorized');
        }
        return await this.uploadService.moveUploadToCategory(id, category);
    }
    async cleanupTempFiles(hours) {
        const hoursNum = hours ? parseInt(hours) : 24;
        return await this.uploadService.cleanupTempFiles(hoursNum);
    }
};
__decorate([
    (0, common_1.Post)('image'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "uploadImage", null);
__decorate([
    (0, common_1.Post)('images'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 10)),
    __param(0, (0, common_1.UploadedFiles)()),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String, Object]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "uploadMultipleImages", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "getUpload", null);
__decorate([
    (0, common_1.Get)(':id/file'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "serveFile", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "deleteUpload", null);
__decorate([
    (0, common_1.Get)('user/my-uploads'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "getUserUploads", null);
__decorate([
    (0, common_1.Post)(':id/move'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('category')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "moveUpload", null);
__decorate([
    (0, common_1.Post)('cleanup-temp'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Query)('hours')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UploadController.prototype, "cleanupTempFiles", null);
UploadController = __decorate([
    (0, common_1.Controller)('uploads'),
    __metadata("design:paramtypes", [upload_service_1.UploadService])
], UploadController);
exports.UploadController = UploadController;
//# sourceMappingURL=upload.controller.js.map