import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ProductsService } from './products.service';
import { ProductSearchDto } from './dto/product-search.dto';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('products')
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Public()
  @Get('categories')
  getCategories(@RequestCtx() ctx: RequestContext) {
    return this.productsService.getCategories(ctx);
  }

  @Public()
  @Get('category/:categoryId')
  getProductsByCategory(
    @Param('categoryId') categoryId: string,
    @RequestCtx() ctx: RequestContext,
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number,
  ) {
    return this.productsService.getProductsByCategory(
      categoryId,
      page,
      pageSize,
      ctx,
    );
  }

  @Public()
  @Get('search')
  searchProducts(
    @Query() searchDto: ProductSearchDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.productsService.searchProducts(searchDto, ctx);
  }

  @Public()
  @Get(':productId')
  getProductById(
    @Param('productId') productId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.productsService.getProductById(productId, ctx);
  }
}
