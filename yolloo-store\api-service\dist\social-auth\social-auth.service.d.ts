import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { PrismaService } from '../prisma.service';
export interface SocialUserInfo {
    id: string;
    email: string;
    name: string;
    picture?: string;
    provider: 'google' | 'facebook' | 'apple' | 'wechat';
}
export declare class SocialAuthService {
    private prisma;
    private configService;
    private httpService;
    private readonly logger;
    constructor(prisma: PrismaService, configService: ConfigService, httpService: HttpService);
    verifyGoogleToken(token: string): Promise<SocialUserInfo>;
    verifyFacebookToken(token: string): Promise<SocialUserInfo>;
    verifyAppleToken(token: string): Promise<SocialUserInfo>;
    verifyWechatCode(code: string): Promise<SocialUserInfo>;
    findOrCreateSocialUser(socialInfo: SocialUserInfo): Promise<any>;
    unlinkSocialAccount(userId: string, provider: string): Promise<void>;
    getUserSocialAccounts(userId: string): Promise<any[]>;
}
