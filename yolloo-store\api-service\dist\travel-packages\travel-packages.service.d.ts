import { PrismaService } from '../prisma.service';
import { RatingService } from '../rating/rating.service';
import { TravelPackagesQueryDto, TravelPackageOrderDto } from './dto/travel-packages-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
export declare class TravelPackagesService {
    private prisma;
    private ratingService;
    private readonly logger;
    constructor(prisma: PrismaService, ratingService: RatingService);
    getTravelPackages(query: TravelPackagesQueryDto, ctx: RequestContext): Promise<{
        packages: never[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        filters: {
            regions: never[];
            durations: never[];
            dataSizes: never[];
            destination?: undefined;
            region?: undefined;
            duration?: undefined;
            dataSize?: undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    } | {
        packages: Promise<{
            id: any;
            name: any;
            description: any;
            destination: string;
            region: string;
            countries: string[];
            duration: number;
            dataSize: string;
            price: number;
            originalPrice: number;
            currency: any;
            features: string[];
            networkType: string;
            coverage: string;
            imageUrl: any;
            isPopular: boolean;
            rating: number;
            reviewCount: number;
        }>[];
        pagination: {
            total: number;
            page: number;
            pageSize: number;
            hasMore: boolean;
        };
        filters: {
            destination: string | undefined;
            region: string | undefined;
            duration: "7" | "15" | "30" | "90" | undefined;
            dataSize: string | undefined;
            regions?: undefined;
            durations?: undefined;
            dataSizes?: undefined;
        };
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    getPackageById(packageId: string, ctx: RequestContext): Promise<{
        package: Promise<{
            detailedInfo: {
                activation: string;
                validity: string;
                speed: string;
                compatibility: string;
            };
            includedServices: string[];
            variants: any;
            reviews: any;
            id: any;
            name: any;
            description: any;
            destination: string;
            region: string;
            countries: string[];
            duration: number;
            dataSize: string;
            price: number;
            originalPrice: number;
            currency: any;
            features: string[];
            networkType: string;
            coverage: string;
            imageUrl: any;
            isPopular: boolean;
            rating: number;
            reviewCount: number;
        }>;
        context: {
            language: string;
            theme: string;
            currency: string;
        };
    }>;
    createTravelOrder(orderData: TravelPackageOrderDto, ctx: RequestContext): Promise<{
        order: {
            id: string;
            packageId: string;
            startDate: string | undefined;
            countries: string[] | undefined;
            status: string;
            statusText: string;
            createdAt: string;
            estimatedDeliveryTime: string;
        };
        message: string;
    }>;
    private formatProductAsPackage;
    private formatProductAsDetailedPackage;
    private buildOrderBy;
    private getRegionFromCountries;
    private formatDataSize;
}
