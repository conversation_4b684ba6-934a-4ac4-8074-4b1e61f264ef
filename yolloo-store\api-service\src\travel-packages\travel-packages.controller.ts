import { Controller, Get, Post, Body, Query, Param, UseGuards } from '@nestjs/common';
import { TravelPackagesService } from './travel-packages.service';
import { TravelPackagesQueryDto, TravelPackageOrderDto } from './dto/travel-packages-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('travel-packages')
export class TravelPackagesController {
  constructor(private readonly travelPackagesService: TravelPackagesService) {}

  @Public()
  @Get()
  getTravelPackages(
    @Query() query: TravelPackagesQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.travelPackagesService.getTravelPackages(query, ctx);
  }

  @Public()
  @Get(':packageId')
  getPackageById(
    @Param('packageId') packageId: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.travelPackagesService.getPackageById(packageId, ctx);
  }

  @Post('order')
  createTravelOrder(
    @Body() orderData: TravelPackageOrderDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.travelPackagesService.createTravelOrder(orderData, ctx);
  }
}
