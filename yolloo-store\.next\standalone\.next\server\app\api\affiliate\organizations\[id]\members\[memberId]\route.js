"use strict";(()=>{var e={};e.id=1001,e.ids=[1001],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},53025:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>j,patchFetch:()=>E,requestAsyncStorage:()=>v,routeModule:()=>b,serverHooks:()=>R,staticGenerationAsyncStorage:()=>_});var i={};t.r(i),t.d(i,{DELETE:()=>x,GET:()=>y,PATCH:()=>g,dynamic:()=>m,fetchCache:()=>p,revalidate:()=>f});var a=t(49303),n=t(88716),o=t(60670),s=t(87070),l=t(75571),u=t(90455),c=t(72331),d=t(7410);let m="force-dynamic",p="force-no-store",f=0,h=d.z.object({commissionRate:d.z.number().min(0).max(1).optional(),isAdmin:d.z.boolean().optional()});async function w(e,r){let t=await c._.user.findUnique({where:{id:e},select:{role:!0,affiliate:{select:{id:!0,organizationId:!0,isAdmin:!0}}}});return!!t&&("ADMIN"===t.role||t.affiliate?.organizationId===r&&!!t.affiliate.isAdmin)}async function y(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t,memberId:i}=r;if(!await w(e.user.id,t))return s.NextResponse.json({error:"Access denied"},{status:403});let a=await c._.affiliateProfile.findFirst({where:{id:i,organizationId:t},include:{user:{select:{id:!0,name:!0,email:!0,image:!0}},referrals:{take:10,orderBy:{createdAt:"desc"},include:{order:{select:{id:!0,total:!0,createdAt:!0}}}},_count:{select:{referrals:!0,visits:!0}}}});if(!a)return s.NextResponse.json({error:"Member not found"},{status:404});return s.NextResponse.json(a)}catch(e){return console.error("Error fetching member:",e),s.NextResponse.json({error:"Failed to fetch member"},{status:500})}}async function g(e,{params:r}){try{let t=await (0,l.getServerSession)(u.L);if(!t?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let{id:i,memberId:a}=r;if(!await w(t.user.id,i))return s.NextResponse.json({error:"Access denied"},{status:403});let n=await e.json(),o=h.safeParse(n);if(!o.success)return s.NextResponse.json({error:o.error.errors},{status:400});let d=await c._.affiliateProfile.findFirst({where:{id:a,organizationId:i}});if(!d)return s.NextResponse.json({error:"Member not found"},{status:404});if(d.isAdmin&&!1===o.data.isAdmin)return s.NextResponse.json({error:"Cannot remove admin status from an admin member"},{status:403});let m=await c._.affiliateProfile.update({where:{id:a},data:o.data});return s.NextResponse.json(m)}catch(e){return console.error("Error updating member:",e),s.NextResponse.json({error:"Failed to update member"},{status:500})}}async function x(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t,memberId:i}=r;if(!await w(e.user.id,t))return s.NextResponse.json({error:"Access denied"},{status:403});let a=await c._.affiliateProfile.findFirst({where:{id:i,organizationId:t}});if(!a)return s.NextResponse.json({error:"Member not found"},{status:404});if(a.isAdmin)return s.NextResponse.json({error:"Cannot remove an admin member"},{status:403});let n=await c._.affiliateProfile.update({where:{id:i},data:{organizationId:null,isAdmin:!1}});return s.NextResponse.json(n)}catch(e){return console.error("Error removing member:",e),s.NextResponse.json({error:"Failed to remove member"},{status:500})}}let b=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/members/[memberId]/route",pathname:"/api/affiliate/organizations/[id]/members/[memberId]",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/members/[memberId]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\members\\[memberId]\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:v,staticGenerationAsyncStorage:_,serverHooks:R}=b,j="/api/affiliate/organizations/[id]/members/[memberId]/route";function E(){return(0,o.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:_})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var i=t(7585),a=t(72331),n=t(77234),o=t(53797),s=t(42023),l=t.n(s),u=t(93475);let c={adapter:{...(0,i.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:i,request:n}){try{if(t&&t.id){let r=n?.headers||new Headers,o=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";i?u=i.code&&!i.password?"email_code":"password":e&&(u=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],i=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(i).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let i=new URL(e);if(t.some(e=>i.hostname===e||i.hostname.includes(e)||i.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(i);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return i}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:i}){if("update"===t&&i)return{...e,...i.user};let n=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>a});var i=t(53524);let a=global.prisma||new i.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var i=t(62197),a=t.n(i);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,r,t=300){try{let i=o(),a=`verification_code:${e}`;return await i.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let i=o(),a=`rate_limit:${e}`,n=await i.get(a),s=n?parseInt(n):0;if(s>=r)return!1;return 0===s?await i.setex(a,t,"1"):await i.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var i={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=a?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(i,n,s):i[n]=e[n]}return i.default=e,t&&t.set(e,i),i}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[8948,1615,9092,5972,2197,2023,7005,7410],()=>t(53025));module.exports=i})();