(()=>{var e={};e.id=4122,e.ids=[4122],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},84704:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(65917),s(75718),s(85460),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["admin",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65917)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,75718)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\page.tsx"],u="/admin/products/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/products/page",pathname:"/admin/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4224:(e,t,s)=>{Promise.resolve().then(s.bind(s,80375))},35303:()=>{},80375:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var r=s(10326),a=s(17577),i=s(35047),n=s(567),l=s(90772),o=s(33071),d=s(57372),c=s(88307),u=s(62881);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,u.Z)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]);var p=s(11890),x=s(15919),h=s(39183);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,u.Z)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);var g=s(50949),j=s(99440),y=s(54432),b=s(85999),v=s(34474),N=s(68762),w=s(31048),k=s(77863),C=s(62288);function P({open:e,onOpenChange:t,categories:s,onSuccess:i}){let[o,c]=(0,a.useState)([]),[u,m]=(0,a.useState)("increase"),[p,x]=(0,a.useState)(""),[h,f]=(0,a.useState)(!1),g=async()=>{if(0===o.length){b.A.error("Please select at least one category");return}let e=parseFloat(p);if(isNaN(e)||e<=0||e>100){b.A.error("Please enter a valid percentage between 1 and 100");return}f(!0);try{let s=await fetch("/api/admin/products/bulk-price-update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({categoryIds:o,adjustmentType:u,adjustmentPercentage:e})});if(!s.ok){let e=await s.text();throw Error(e||"Failed to update prices")}let r=await s.json();b.A.success(`Successfully updated ${r.updatedCount} products. Prices ${"increase"===u?"increased":"decreased"} by ${e}%.`),c([]),m("increase"),x(""),t(!1),i()}catch(e){console.error("Error updating prices:",e),b.A.error(e instanceof Error?e.message:"Failed to update prices")}finally{f(!1)}},j=e=>{c(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return r.jsx(C.Vq,{open:e,onOpenChange:t,children:(0,r.jsxs)(C.cZ,{className:"sm:max-w-[500px]",children:[(0,r.jsxs)(C.fK,{children:[(0,r.jsxs)(C.$N,{className:"flex items-center",children:[r.jsx(d.P.dollarSign,{className:"mr-2 h-5 w-5"}),"Bulk Price Update"]}),r.jsx(C.Be,{children:"Adjust prices for products in selected categories by a percentage."})]}),(0,r.jsxs)("div",{className:"space-y-6 py-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx(w._,{className:"text-sm font-medium",children:"Select Categories"}),r.jsx(l.Button,{variant:"ghost",size:"sm",onClick:()=>{o.length===s.length?c([]):c(s.map(e=>e.id))},className:"text-xs",children:o.length===s.length?"Deselect All":"Select All"})]}),r.jsx("div",{className:"max-h-40 overflow-y-auto border rounded-md p-3 space-y-2",children:s.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(N.X,{id:e.id,checked:o.includes(e.id),onCheckedChange:()=>j(e.id)}),r.jsx(w._,{htmlFor:e.id,className:"text-sm font-normal cursor-pointer flex-1",children:e.name})]},e.id))}),o.length>0&&r.jsx("div",{className:"flex flex-wrap gap-1",children:o.map(e=>{let t=s.find(t=>t.id===e);return t?r.jsx(n.C,{variant:"secondary",className:"text-xs",children:t.name},e):null})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(w._,{htmlFor:"adjustmentType",children:"Adjustment Type"}),(0,r.jsxs)(v.Ph,{value:u,onValueChange:e=>m(e),children:[r.jsx(v.i4,{children:r.jsx(v.ki,{})}),(0,r.jsxs)(v.Bw,{children:[r.jsx(v.Ql,{value:"increase",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(d.P.trendingUp,{className:"mr-2 h-4 w-4 text-green-600"}),"Increase Price"]})}),r.jsx(v.Ql,{value:"decrease",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(d.P.trendingDown,{className:"mr-2 h-4 w-4 text-red-600"}),"Decrease Price"]})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(w._,{htmlFor:"percentage",children:"Adjustment Percentage"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(y.I,{id:"percentage",type:"number",min:"1",max:"100",step:"0.1",placeholder:"Enter percentage (1-100)",value:p,onChange:e=>x(e.target.value),className:"pr-8"}),r.jsx("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:"%"})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Example: Enter 30 to ","increase"===u?"increase":"decrease"," prices by 30%"]})]})]}),(0,r.jsxs)(C.cN,{children:[r.jsx(l.Button,{variant:"outline",onClick:()=>t(!1),disabled:h,children:"Cancel"}),r.jsx(l.Button,{onClick:g,disabled:h||0===o.length||!p,className:"increase"===u?"bg-green-600 hover:bg-green-700":"bg-red-600 hover:bg-red-700",children:h?(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.P.dollarSign,{className:"mr-2 h-4 w-4"}),"Update Prices"]})})]})]})})}function S(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)(null),[u,C]=(0,a.useState)(!1),[S,E]=(0,a.useState)([]),[_,R]=(0,a.useState)([]),[A,T]=(0,a.useState)([]),[F,O]=(0,a.useState)(!0),[D,z]=(0,a.useState)(null),[q,B]=(0,a.useState)(null),[V,M]=(0,a.useState)(1),[$,I]=(0,a.useState)(1),[L,Z]=(0,a.useState)(0),[Q,U]=(0,a.useState)(""),[Y,W]=(0,a.useState)(!1),[H,X]=(0,a.useState)(!1),[G,K]=(0,a.useState)([]),[J,ee]=(0,a.useState)(!1),[et,es]=(0,a.useState)({search:"",category:"all",status:"all",stock:"all",offShelve:"all",country:"all",page:1,limit:12,sort:"newest"}),er=u||H,ea=async()=>{try{let e=new URLSearchParams;Q&&e.append("search",Q),"all"!==et.category&&e.append("category",et.category),"all"!==et.status&&e.append("status",et.status),"all"!==et.offShelve&&e.append("offShelve",et.offShelve),"all"!==et.country&&e.append("country",et.country),et.sort&&e.append("sort",et.sort),e.append("page",V.toString()),e.append("limit",et.limit.toString());let t=await fetch(`/api/admin/products?${e.toString()}`);if(!t.ok)throw Error("Failed to fetch products");let s=await t.json();E(s.products),I(s.pages),Z(s.total),R(s.categories||[])}catch(e){console.error("Error loading products:",e),b.A.error("Failed to load products")}finally{O(!1)}},ei=async()=>{C(!0);let e=!1,t=!1;try{(await fetch("/api/admin/sync-products",{method:"POST"})).ok?(b.A.success("Odoo products synced successfully"),e=!0):b.A.error("Failed to sync Odoo products"),(await fetch("/api/admin/products/sync-qr-products",{method:"POST"})).ok?(b.A.success("QR Odoo products synced successfully"),t=!0):b.A.error("Failed to sync QR Odoo products"),(e||t)&&await ea()}catch(e){console.error(e),b.A.error("Failed to sync products")}finally{C(!1)}};async function en(e,t){s(e);try{if(!(await fetch(`/api/admin/products/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})})).ok)throw Error("Failed to update product status");b.A.success("Product status updated successfully"),ea()}catch(e){console.error(e),b.A.error("Something went wrong")}finally{s(null)}}async function el(e){try{if(e.startsWith("force_")){let t=e.replace("force_","");if(!(await fetch(`/api/admin/products/${t}/force-delete`,{method:"DELETE"})).ok)throw Error("Failed to force delete product");b.A.success("Product has been force deleted successfully")}else{let t=await fetch(`/api/admin/products/${e}/check-orders`),{hasOrders:s}=await t.json();if(s){if(!(await fetch(`/api/admin/products/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"DELETED"})})).ok)throw Error("Failed to delete product");b.A.success("Product has been archived due to existing orders")}else{if(!(await fetch(`/api/admin/products/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete product");b.A.success("Product deleted successfully")}}ea()}catch(e){console.error(e),b.A.error("Failed to delete product")}finally{z(null)}}async function eo(e){try{let t=e.map(e=>fetch(`/api/admin/products/${e}/check-orders`).then(e=>e.json())),s=await Promise.all(t),r=e.filter((e,t)=>s[t].hasOrders),a=e.filter((e,t)=>!s[t].hasOrders);if(r.length>0){let e=r.map(e=>fetch(`/api/admin/products/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"DELETED"})}));await Promise.all(e)}if(a.length>0){let e=a.map(e=>fetch(`/api/admin/products/${e}`,{method:"DELETE"}));await Promise.all(e)}b.A.success("Selected products have been processed"),K([]),ea()}catch(e){console.error(e),b.A.error("Failed to process selected products")}}async function ed(){try{X(!0);let e=await fetch("/api/admin/products/delete-all",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to delete all products");let t=await e.json();b.A.success(`Products processed: ${t.deletedCount} deleted, ${t.skippedCount} skipped`),ea()}catch(e){console.error(e),b.A.error("Failed to delete all products")}finally{X(!1),W(!1)}}return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-3xl font-bold tracking-tight",children:"Products"}),r.jsx("p",{className:"text-muted-foreground mt-1",children:"Manage your product catalog and inventory"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[G.length>0&&(0,r.jsxs)(l.Button,{variant:"destructive",onClick:()=>z("batch"),className:"flex items-center",children:[r.jsx(d.P.trash,{className:"mr-2 h-4 w-4"}),"Delete Selected (",G.length,")"]}),(0,r.jsxs)(l.Button,{onClick:()=>ee(!0),disabled:er,variant:"outline",className:"flex items-center bg-green-50 hover:bg-green-100 text-green-700 border-green-200 hover:border-green-300",children:[r.jsx(d.P.dollarSign,{className:"mr-2 h-4 w-4"}),"Bulk Price Update"]}),r.jsx(l.Button,{onClick:ei,disabled:er,variant:"outline",className:"flex items-center",children:u?(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Syncing..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.P.refresh,{className:"mr-2 h-4 w-4"}),"Sync with Odoo"]})}),(0,r.jsxs)(l.Button,{onClick:()=>e.push("/admin/products/new"),className:"bg-primary hover:bg-primary/90 text-white flex items-center",children:[r.jsx(d.P.add,{className:"mr-2 h-4 w-4"}),"Add Product"]}),(0,r.jsxs)(l.Button,{onClick:()=>W(!0),disabled:er,variant:"destructive",className:"flex items-center",children:[r.jsx(d.P.trash,{className:"mr-2 h-4 w-4"}),"Delete All Products"]})]})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("h3",{className:"text-lg font-medium",children:"Search & Filters"}),(0,r.jsxs)(l.Button,{variant:"ghost",size:"sm",onClick:()=>{es({search:"",category:"all",status:"all",stock:"all",offShelve:"all",country:"all",page:1,limit:12,sort:"newest"}),U(""),M(1)},className:"text-sm flex items-center",children:[r.jsx(d.P.refresh,{className:"mr-2 h-3.5 w-3.5"}),"Reset Filters"]})]}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(c.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),r.jsx(y.I,{placeholder:"Search by name, SKU, product code, or description...",value:Q,onChange:e=>U(e.target.value),className:"pl-10 bg-background"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx(w._,{htmlFor:"country",children:"Country"}),(0,r.jsxs)(v.Ph,{value:et.country,onValueChange:e=>es(t=>({...t,country:e,page:1})),children:[r.jsx(v.i4,{id:"country",children:r.jsx(v.ki,{placeholder:"Select country"})}),(0,r.jsxs)(v.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[r.jsx(v.Ql,{value:"all",children:"All Destinations"}),A.map(e=>r.jsx(v.Ql,{value:e,children:e},e))]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(w._,{htmlFor:"category",children:"Category"}),(0,r.jsxs)(v.Ph,{value:et.category,onValueChange:e=>es(t=>({...t,category:e,page:1})),children:[r.jsx(v.i4,{id:"category",children:r.jsx(v.ki,{placeholder:"Select category"})}),(0,r.jsxs)(v.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[r.jsx(v.Ql,{value:"all",children:"All Categories"}),_.map(e=>r.jsx(v.Ql,{value:e.id,children:e.name},e.id))]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(w._,{htmlFor:"status",children:"Status"}),(0,r.jsxs)(v.Ph,{value:et.status,onValueChange:e=>es(t=>({...t,status:e,page:1})),children:[r.jsx(v.i4,{id:"status",children:r.jsx(v.ki,{placeholder:"Select status"})}),(0,r.jsxs)(v.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[r.jsx(v.Ql,{value:"all",children:"All Statuses"}),r.jsx(v.Ql,{value:"ACTIVE",children:"Active"}),r.jsx(v.Ql,{value:"INACTIVE",children:"Inactive"}),r.jsx(v.Ql,{value:"OUT_OF_STOCK",children:"Out of Stock"}),r.jsx(v.Ql,{value:"DELETED",children:"Deleted"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(w._,{htmlFor:"offShelve",children:"Shelf Status"}),(0,r.jsxs)(v.Ph,{value:et.offShelve,onValueChange:e=>es(t=>({...t,offShelve:e,page:1})),children:[r.jsx(v.i4,{id:"shelf",children:r.jsx(v.ki,{placeholder:"Select shelf status"})}),(0,r.jsxs)(v.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[r.jsx(v.Ql,{value:"all",children:"All"}),r.jsx(v.Ql,{value:"false",children:"On Shelf"}),r.jsx(v.Ql,{value:"true",children:"Off Shelf"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(w._,{htmlFor:"sort",children:"Sort By"}),(0,r.jsxs)(v.Ph,{value:et.sort,onValueChange:e=>es(t=>({...t,sort:e,page:1})),children:[r.jsx(v.i4,{id:"sort",children:r.jsx(v.ki,{placeholder:"Sort by"})}),(0,r.jsxs)(v.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[r.jsx(v.Ql,{value:"newest",children:"Newest First"}),r.jsx(v.Ql,{value:"price_asc",children:"Price: Low to High"}),r.jsx(v.Ql,{value:"price_desc",children:"Price: High to Low"}),r.jsx(v.Ql,{value:"variants_asc",children:"Variants: Fewest First"}),r.jsx(v.Ql,{value:"variants_desc",children:"Variants: Most First"})]})]})]})]})]}),r.jsx("div",{className:"bg-white dark:bg-gray-950 p-6 rounded-lg border shadow-sm space-y-4",children:F?r.jsx("div",{className:"flex items-center justify-center p-12",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx(d.P.spinner,{className:"h-10 w-10 animate-spin text-primary"}),r.jsx("p",{className:"mt-4 text-muted-foreground",children:"Loading products..."})]})}):0===S.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center p-12 text-center",children:[r.jsx("div",{className:"bg-muted/30 p-4 rounded-full",children:r.jsx(d.P.package,{className:"h-12 w-12 text-muted-foreground"})}),r.jsx("h3",{className:"mt-6 text-xl font-semibold",children:"No products found"}),r.jsx("p",{className:"mt-2 text-muted-foreground max-w-md",children:"No products match your current filters. Try adjusting your search criteria or add a new product."}),(0,r.jsxs)(l.Button,{className:"mt-6",onClick:()=>e.push("/admin/products/new"),children:[r.jsx(d.P.add,{className:"mr-2 h-4 w-4"}),"Add Product"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(N.X,{checked:S.length>0&&S.every(e=>G.includes(e.id)),onCheckedChange:e=>{if(e){let e=[...G];S.forEach(t=>{e.includes(t.id)||e.push(t.id)}),K(e)}else K(e=>e.filter(e=>!S.find(t=>t.id===e)))},className:"rounded-md"}),r.jsx("span",{className:"text-sm font-medium",children:"Select All"})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",S.length," of ",L," products"]})]}),r.jsx("div",{className:"space-y-4",children:S.map(s=>r.jsx(o.Zb,{className:"overflow-hidden hover:border-primary/5 transition-colors duration-300",children:(0,r.jsxs)("div",{className:"p-6 grid gap-6 md:grid-cols-[auto_1fr_auto] relative",children:[(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[r.jsx(N.X,{checked:G.includes(s.id),onCheckedChange:e=>{K(t=>e?[...t,s.id]:t.filter(e=>e!==s.id))},className:"mt-1 rounded-md"}),s.images&&s.images.length>0?(0,r.jsxs)("div",{className:"relative w-20 h-20 rounded-lg overflow-hidden border bg-muted/20 group",children:[r.jsx("img",{src:s.images[0],alt:s.name,className:"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"}),s.images.length>1&&(0,r.jsxs)("div",{className:"absolute bottom-1 right-1 bg-background/80 text-xs rounded-full h-5 w-5 flex items-center justify-center",children:["+",s.images.length-1]})]}):r.jsx("div",{className:"flex items-center justify-center w-20 h-20 rounded-lg border bg-muted/20",children:r.jsx(d.P.image,{className:"h-8 w-8 text-muted-foreground/50"})}),(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("h3",{className:"font-medium text-lg leading-tight truncate max-w-[300px]",title:s.name,children:s.name}),r.jsx("p",{className:"text-sm text-muted-foreground",children:(0,r.jsxs)("span",{className:"font-mono",children:["SKU:",s.sku]})}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-2 mt-2",children:[s.category&&r.jsx(n.C,{variant:"outline",className:"bg-primary/5 text-primary border-primary/20",title:s.category.name,children:r.jsx("span",{className:"truncate max-w-[100px] inline-block align-bottom",children:s.category.name})}),"ACTIVE"===s.status?r.jsx(n.C,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800",children:(0,r.jsxs)("span",{className:"flex items-center",children:[r.jsx("span",{className:"w-1.5 h-1.5 rounded-full bg-green-500 mr-1 animate-pulse"}),"Active"]})}):"INACTIVE"===s.status?r.jsx(n.C,{variant:"outline",className:"bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-400 dark:border-amber-800",children:(0,r.jsxs)("span",{className:"flex items-center",children:[r.jsx("span",{className:"w-1.5 h-1.5 rounded-full bg-amber-500 mr-1"}),"Inactive"]})}):r.jsx(n.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800",children:(0,r.jsxs)("span",{className:"flex items-center",children:[r.jsx("span",{className:"w-1.5 h-1.5 rounded-full bg-red-500 mr-1"}),"Archived"]})}),s.off_shelve&&r.jsx(n.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800",children:(0,r.jsxs)("span",{className:"flex items-center",children:[r.jsx(d.P.eyeOff,{className:"h-3 w-3 mr-1"}),"Off Shelf"]})})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-8 gap-y-3 mt-2 md:mt-0",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Price"}),r.jsx("p",{className:"text-base font-medium mt-1 flex items-center",children:(0,r.jsxs)("span",{className:"text-lg font-semibold text-primary",children:["$",s.price.toFixed(2)]})})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Variants"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[r.jsx("p",{className:"text-base font-medium",children:s.variants?s.variants.length:0}),s.variants&&s.variants.length>0&&r.jsx(n.C,{variant:"outline",className:"text-xs bg-violet-50 text-violet-700 border-violet-200 dark:bg-violet-950 dark:text-violet-400 dark:border-violet-800",children:s.variants.length>1?"variants":"variant"})]})]}),s.planType&&(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Plan Type"}),r.jsx("p",{className:"text-base font-medium mt-1 truncate max-w-[150px]",title:s.planType,children:s.planType})]}),s.dataSize&&(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Data Size"}),(0,r.jsxs)("p",{className:"text-base font-medium mt-1 flex items-center",children:[r.jsx("span",{children:s.dataSize>=1024?`${(s.dataSize/1024).toFixed(2)}`:`${s.dataSize}`}),s.dataSize>=1024&&r.jsx(n.C,{variant:"outline",className:"ml-2 text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800",children:"GB"}),s.dataSize<1024&&r.jsx(n.C,{variant:"outline",className:"ml-2 text-xs bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-400 dark:border-indigo-800",children:"MB"})]})]}),s.country&&(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Country"}),(0,r.jsxs)("p",{className:"text-base font-medium mt-1 truncate max-w-[150px]",title:`${s.country}${s.countryCode?` (${s.countryCode})`:""}`,children:[s.country,s.countryCode&&` (${s.countryCode})`]})]}),s.mcc&&(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"MCC"}),r.jsx("p",{className:"text-base font-medium mt-1 truncate max-w-[150px]",title:s.mcc,children:s.mcc})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Stock"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[r.jsx("p",{className:"text-base font-medium",children:s.stock}),s.stock<=0?r.jsx(n.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800 text-xs",children:"Out of stock"}):s.stock<=10&&r.jsx(n.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800 text-xs",children:"Low"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-xs uppercase font-medium text-muted-foreground",children:"Created"}),r.jsx("p",{className:"text-sm mt-1",children:k.CN.withTimezone(s.createdAt)})]})]}),(0,r.jsxs)("div",{className:"flex flex-row md:flex-col justify-end gap-2 mt-4 md:mt-0",children:[(0,r.jsxs)(l.Button,{variant:"outline",size:"sm",onClick:()=>e.push(`/admin/products/${s.id}`),className:"flex items-center hover:bg-primary/5 hover:text-primary hover:border-primary/30 transition-colors",children:[r.jsx(d.P.edit,{className:"mr-2 h-4 w-4"}),"Edit"]}),"DELETED"===s.status?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.Button,{variant:"outline",size:"sm",onClick:()=>en(s.id,"ACTIVE"),className:"flex items-center hover:bg-green-50 hover:text-green-700 hover:border-green-300 transition-colors",children:[r.jsx(d.P.undo,{className:"mr-2 h-4 w-4"}),"Restore"]}),(0,r.jsxs)(l.Button,{variant:"destructive",size:"sm",onClick:()=>z(`force_${s.id}`),className:"flex items-center hover:bg-red-600 transition-colors",children:[r.jsx(d.P.trash,{className:"mr-2 h-4 w-4"}),"Force Delete"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.Button,{variant:"outline",size:"sm",disabled:t===s.id,onClick:()=>en(s.id,"ACTIVE"===s.status?"INACTIVE":"ACTIVE"),className:`flex items-center transition-colors ${"ACTIVE"===s.status?"hover:bg-amber-50 hover:text-amber-700 hover:border-amber-300":"hover:bg-green-50 hover:text-green-700 hover:border-green-300"}`,children:[t===s.id?r.jsx(d.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}):"ACTIVE"===s.status?r.jsx(d.P.eyeOff,{className:"mr-2 h-4 w-4"}):r.jsx(d.P.eye,{className:"mr-2 h-4 w-4"}),"ACTIVE"===s.status?"Deactivate":"Activate"]}),(0,r.jsxs)(l.Button,{variant:"destructive",size:"sm",onClick:()=>z(s.id),className:"flex items-center hover:bg-red-600 transition-colors",children:[r.jsx(d.P.trash,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]}),r.jsx("div",{className:"md:hidden w-full h-px bg-border my-2 col-span-full"})]})},s.id))}),r.jsx("div",{className:"flex justify-center mt-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 bg-muted/20 p-1 rounded-lg",children:[r.jsx(l.Button,{variant:"ghost",size:"sm",onClick:()=>M(1),disabled:1===V,className:"h-8 w-8 p-0 flex items-center justify-center",children:r.jsx(m,{className:"h-4 w-4"})}),r.jsx(l.Button,{variant:"ghost",size:"sm",onClick:()=>M(e=>Math.max(e-1,1)),disabled:1===V,className:"h-8 w-8 p-0 flex items-center justify-center",children:r.jsx(p.Z,{className:"h-4 w-4"})}),r.jsx("div",{className:"flex items-center gap-1",children:Array.from({length:$},(e,t)=>t+1).filter(e=>$<=7||(V<=4?e<=5||e===$:V>=$-3?1===e||e>$-5:1===e||e===$||1>=Math.abs(e-V))).map((e,t,s)=>{let i=t>0&&e-s[t-1]>1,n=t<s.length-1&&s[t+1]-e>1;return(0,r.jsxs)(a.Fragment,{children:[i&&r.jsx("div",{className:"h-8 w-8 flex items-center justify-center",children:r.jsx(x.Z,{className:"h-4 w-4"})}),r.jsx(l.Button,{variant:V===e?"default":"ghost",size:"sm",onClick:()=>M(e),className:"h-8 w-8 p-0 flex items-center justify-center",children:e}),n&&r.jsx("div",{className:"h-8 w-8 flex items-center justify-center",children:r.jsx(x.Z,{className:"h-4 w-4"})})]},e)})}),r.jsx(l.Button,{variant:"ghost",size:"sm",onClick:()=>M(e=>Math.min(e+1,$)),disabled:V===$,className:"h-8 w-8 p-0 flex items-center justify-center",children:r.jsx(h.Z,{className:"h-4 w-4"})}),r.jsx(l.Button,{variant:"ghost",size:"sm",onClick:()=>M($),disabled:V===$,className:"h-8 w-8 p-0 flex items-center justify-center",children:r.jsx(f,{className:"h-4 w-4"})})]})})]})}),r.jsx(j.aR,{open:!!D,onOpenChange:()=>z(null),children:(0,r.jsxs)(j._T,{className:"max-w-md",children:[(0,r.jsxs)(j.fY,{children:[r.jsx(j.f$,{className:"text-xl",children:D&&D.startsWith("force_")?(0,r.jsxs)("span",{className:"flex items-center gap-2 text-destructive",children:[r.jsx(g.Z,{className:"h-5 w-5"}),"Force Delete Product"]}):"Are you sure?"}),r.jsx(j.yT,{className:"text-muted-foreground mt-2",children:"batch"===D?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{children:["You are about to process ",r.jsx("span",{className:"font-medium text-foreground",children:G.length})," products."]}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[r.jsx("li",{children:"Products with orders will be archived"}),r.jsx("li",{children:"Products without orders will be permanently deleted"})]})]}):D&&D.startsWith("force_")?(0,r.jsxs)(r.Fragment,{children:[r.jsx("p",{className:"font-medium text-destructive mb-2",children:"This is a destructive action that cannot be easily undone!"}),(0,r.jsxs)("p",{children:["You are about to ",r.jsx("span",{className:"font-medium text-foreground",children:"force delete"})," this product, even though it may have associated orders or eSIMs."]}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[r.jsx("li",{children:"For associated orders, a placeholder record will be created to maintain order history"}),r.jsx("li",{children:"For associated eSIMs, a placeholder product will be created to maintain eSIM functionality"}),r.jsx("li",{children:"The original product will be permanently deleted"})]}),r.jsx("p",{className:"mt-3 font-medium",children:"Are you absolutely sure you want to continue?"})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("p",{children:"This action cannot be easily undone."}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[r.jsx("li",{children:"If this product has orders, it will be archived"}),r.jsx("li",{children:"If this product has no orders, it will be permanently deleted"})]})]})})]}),(0,r.jsxs)(j.xo,{className:"mt-4 gap-2",children:[r.jsx(j.le,{className:"mt-0",children:"Cancel"}),r.jsx(j.OL,{onClick:()=>{"batch"===D?eo(G):D&&el(D)},className:"bg-destructive hover:bg-destructive/90 text-destructive-foreground",children:"batch"===D?"Delete Selected":D&&D.startsWith("force_")?"Force Delete Product":"Delete Product"})]})]})}),r.jsx(j.aR,{open:Y,onOpenChange:W,children:(0,r.jsxs)(j._T,{className:"max-w-md",children:[(0,r.jsxs)(j.fY,{children:[(0,r.jsxs)(j.f$,{className:"flex items-center gap-2 text-xl text-destructive",children:[r.jsx(g.Z,{className:"h-5 w-5"}),"Warning: Delete All Products"]}),(0,r.jsxs)(j.yT,{className:"text-muted-foreground mt-2",children:[r.jsx("p",{className:"font-medium text-destructive mb-2",children:"This is a destructive action that cannot be easily undone!"}),(0,r.jsxs)("p",{children:["You are about to ",r.jsx("span",{className:"font-medium text-foreground",children:"permanently delete all products"})," in the system."]}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[r.jsx("li",{children:"All products will be permanently deleted from the database"}),r.jsx("li",{children:"For products with orders, their information will be preserved for order history"}),r.jsx("li",{children:"Products linked to eSIMs will be skipped to maintain data integrity"})]}),r.jsx("p",{className:"mt-3 font-medium",children:"Are you absolutely sure you want to continue?"})]})]}),(0,r.jsxs)(j.xo,{className:"mt-4 gap-2",children:[r.jsx(j.le,{className:"mt-0",children:"Cancel"}),r.jsx(j.OL,{onClick:ed,disabled:H,className:"bg-destructive hover:bg-destructive/90 text-destructive-foreground",children:H?(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Delete All Products"})]})]})}),r.jsx(P,{open:J,onOpenChange:ee,categories:_,onSuccess:ea})]})}},99440:(e,t,s)=>{"use strict";s.d(t,{OL:()=>f,_T:()=>u,aR:()=>l,f$:()=>x,fY:()=>m,le:()=>g,vW:()=>o,xo:()=>p,yT:()=>h});var r=s(10326),a=s(17577),i=s(12194),n=s(77863);let l=i.fC,o=i.xz,d=i.h_,c=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aV,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:s}));c.displayName=i.aV.displayName;let u=a.forwardRef(({className:e,...t},s)=>(0,r.jsxs)(d,{children:[r.jsx(c,{}),r.jsx(i.VY,{ref:s,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));u.displayName=i.VY.displayName;let m=({className:e,...t})=>r.jsx("div",{className:(0,n.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...t});m.displayName="AlertDialogHeader";let p=({className:e,...t})=>r.jsx("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});p.displayName="AlertDialogFooter";let x=a.forwardRef(({className:e,...t},s)=>r.jsx(i.Dx,{ref:s,className:(0,n.cn)("text-lg font-semibold",e),...t}));x.displayName=i.Dx.displayName;let h=a.forwardRef(({className:e,...t},s)=>r.jsx(i.dk,{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=i.dk.displayName;let f=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aU,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",e),...t}));f.displayName=i.aU.displayName;let g=a.forwardRef(({className:e,...t},s)=>r.jsx(i.$j,{ref:s,className:(0,n.cn)("mt-2 inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-semibold ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 sm:mt-0",e),...t}));g.displayName=i.$j.displayName},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var r=s(10326);s(17577);var a=s(79360),i=s(77863);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return r.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},33071:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>o});var r=s(10326),a=s(17577),i=s(77863);let n=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let l=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},s)=>r.jsx("h3",{ref:s,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},s)=>r.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},68762:(e,t,s)=>{"use strict";s.d(t,{X:()=>P});var r=s(10326),a=s(17577),i=s(48051),n=s(93095),l=s(82561),o=s(52067),d=s(53405),c=s(2566),u=s(9815),m=s(45226),p="Checkbox",[x,h]=(0,n.b)(p),[f,g]=x(p),j=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:n,checked:d,defaultChecked:c,required:u,disabled:p,value:x="on",onCheckedChange:h,form:g,...j}=e,[y,b]=a.useState(null),k=(0,i.e)(t,e=>b(e)),C=a.useRef(!1),P=!y||g||!!y.closest("form"),[S=!1,E]=(0,o.T)({prop:d,defaultProp:c,onChange:h}),_=a.useRef(S);return a.useEffect(()=>{let e=y?.form;if(e){let t=()=>E(_.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[y,E]),(0,r.jsxs)(f,{scope:s,state:S,disabled:p,children:[(0,r.jsx)(m.WV.button,{type:"button",role:"checkbox","aria-checked":N(S)?"mixed":S,"aria-required":u,"data-state":w(S),"data-disabled":p?"":void 0,disabled:p,value:x,...j,ref:k,onKeyDown:(0,l.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(e.onClick,e=>{E(e=>!!N(e)||!e),P&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),P&&(0,r.jsx)(v,{control:y,bubbles:!C.current,name:n,value:x,checked:S,required:u,disabled:p,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!N(c)&&c})]})});j.displayName=p;var y="CheckboxIndicator",b=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:a,...i}=e,n=g(y,s);return(0,r.jsx)(u.z,{present:a||N(n.state)||!0===n.state,children:(0,r.jsx)(m.WV.span,{"data-state":w(n.state),"data-disabled":n.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});b.displayName=y;var v=e=>{let{control:t,checked:s,bubbles:i=!0,defaultChecked:n,...l}=e,o=a.useRef(null),u=(0,d.D)(s),m=(0,c.t)(t);a.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==s&&t){let r=new Event("click",{bubbles:i});e.indeterminate=N(s),t.call(e,!N(s)&&s),e.dispatchEvent(r)}},[u,s,i]);let p=a.useRef(!N(s)&&s);return(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n??p.current,...l,tabIndex:-1,ref:o,style:{...e.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return"indeterminate"===e}function w(e){return N(e)?"indeterminate":e?"checked":"unchecked"}var k=s(32933),C=s(77863);let P=a.forwardRef(({className:e,...t},s)=>r.jsx(j,{ref:s,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:r.jsx(b,{className:(0,C.cn)("flex items-center justify-center text-current"),children:r.jsx(k.Z,{className:"h-4 w-4"})})}));P.displayName=j.displayName},62288:(e,t,s)=>{"use strict";s.d(t,{$N:()=>h,Be:()=>f,Vq:()=>o,cN:()=>x,cZ:()=>m,fK:()=>p,hg:()=>d});var r=s(10326),a=s(17577),i=s(11123),n=s(94019),l=s(77863);let o=i.fC,d=i.xz,c=i.h_;i.x8;let u=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=i.aV.displayName;let m=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(c,{children:[r.jsx(u,{}),(0,r.jsxs)(i.VY,{ref:a,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,r.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.VY.displayName;let p=({className:e,...t})=>r.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let x=({className:e,...t})=>r.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});x.displayName="DialogFooter";let h=a.forwardRef(({className:e,...t},s)=>r.jsx(i.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=i.Dx.displayName;let f=a.forwardRef(({className:e,...t},s)=>r.jsx(i.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=i.dk.displayName},31048:(e,t,s)=>{"use strict";s.d(t,{_:()=>d});var r=s(10326),a=s(17577),i=s(34478),n=s(79360),l=s(77863);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>r.jsx(i.f,{ref:s,className:(0,l.cn)(o(),e),...t}));d.displayName=i.f.displayName},34474:(e,t,s)=>{"use strict";s.d(t,{Bw:()=>m,Ph:()=>d,Ql:()=>p,i4:()=>u,ki:()=>c});var r=s(10326),a=s(17577),i=s(18792),n=s(941),l=s(32933),o=s(77863);let d=i.fC;i.ZA;let c=i.B4,u=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.xz,{ref:a,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:[t,r.jsx(i.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=i.xz.displayName;let m=a.forwardRef(({className:e,children:t,position:s="popper",...a},n)=>r.jsx(i.h_,{children:r.jsx(i.VY,{ref:n,className:(0,o.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...a,children:r.jsx(i.l_,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));m.displayName=i.VY.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(i.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.__.displayName;let p=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.ck,{ref:a,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(l.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:t})]}));p.displayName=i.ck.displayName,a.forwardRef(({className:e,...t},s)=>r.jsx(i.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.Z0.displayName},15919:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(62881).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},75718:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(72331);async function a(){return await r._.product.findMany({orderBy:{createdAt:"desc"},include:{category:!0}})}async function i({children:e}){let t=await a();return e?"ProductsPage"===e.type.name?e.type({products:t}):e:null}},65917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\products\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\products\page.tsx#default`)},58585:(e,t,s)=>{"use strict";var r=s(61085);s.o(r,"notFound")&&s.d(t,{notFound:function(){return r.notFound}}),s.o(r,"redirect")&&s.d(t,{redirect:function(){return r.redirect}})},61085:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{ReadonlyURLSearchParams:function(){return n},RedirectType:function(){return r.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return r.permanentRedirect},redirect:function(){return r.redirect}});let r=s(83953),a=s(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class n extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{isNotFoundError:function(){return a},notFound:function(){return r}});let s="NEXT_NOT_FOUND";function r(){let e=Error(s);throw e.digest=s,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===s}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return s}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,s)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{RedirectType:function(){return r},getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return x},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return m},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return d}});let a=s(54580),i=s(72934),n=s(8586),l="NEXT_REDIRECT";function o(e,t,s){void 0===s&&(s=n.RedirectStatusCode.TemporaryRedirect);let r=Error(l);r.digest=l+";"+t+";"+e+";"+s+";";let i=a.requestAsyncStorage.getStore();return i&&(r.mutableCookies=i.mutableCookies),r}function d(e,t){void 0===t&&(t="replace");let s=i.actionAsyncStorage.getStore();throw o(e,t,(null==s?void 0:s.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let s=i.actionAsyncStorage.getStore();throw o(e,t,(null==s?void 0:s.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,s,r,a]=e.digest.split(";",4),i=Number(a);return t===l&&("replace"===s||"push"===s)&&"string"==typeof r&&!isNaN(i)&&i in n.RedirectStatusCode}function m(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function x(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var r=s(17577),a=s(45226),i=s(10326),n=r.forwardRef((e,t)=>(0,i.jsx)(a.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,2194,4824,7123],()=>s(84704));module.exports=r})();