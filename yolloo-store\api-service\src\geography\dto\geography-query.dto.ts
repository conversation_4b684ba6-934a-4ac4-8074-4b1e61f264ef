import { IsOptional, IsS<PERSON>, <PERSON>In, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class GeographyQueryDto {
  @IsOptional()
  @IsString()
  continent?: string;

  @IsOptional()
  @IsString()
  countryCode?: string;

  @IsOptional()
  @IsIn(['Total', 'Daily'])
  planType?: 'Total' | 'Daily';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'price';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

export class ProductsByCountryDto {
  @IsOptional()
  @IsString()
  countryCode?: string;

  @IsOptional()
  @IsIn(['Total', 'Daily'])
  planType?: 'Total' | 'Daily';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;
}
