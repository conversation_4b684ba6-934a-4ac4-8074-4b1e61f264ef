import { IsOptional, IsS<PERSON>, IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class RechargeQueryDto {
  @IsOptional()
  @IsString()
  operator?: string; // 运营商

  @IsOptional()
  @IsIn(['prepaid', 'postpaid'])
  accountType?: 'prepaid' | 'postpaid';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'amount';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

export class RechargeOrderDto {
  @IsString()
  phoneNumber: string;

  @IsString()
  operator: string;

  @Type(() => Number)
  @IsNumber()
  @Min(1)
  amount: number;

  @IsOptional()
  @IsString()
  accountType?: string;
}
