import { PrismaService } from '../prisma.service';
import { WalletService } from '../wallet/wallet.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { WalletDepositDto } from './dto/wallet-deposit.dto';
import { NotificationQueryDto } from './dto/notification-query.dto';
import { CheckinHistoryQueryDto } from './dto/checkin-history-query.dto';
import { CouponQueryDto } from './dto/coupon-query.dto';
export declare class UsersService {
    private prisma;
    private walletService;
    private readonly logger;
    constructor(prisma: PrismaService, walletService: WalletService);
    getProfile(userId: string): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        image: string | null;
        walletBalance: number;
        currency: string;
        points: number;
        totalSpent: number;
        orderCount: number;
        memberSince: Date;
    }>;
    updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<{
        id: string;
        email: string | null;
        name: string | null;
        image: string | null;
    }>;
    getPoints(userId: string): Promise<{
        points: number;
        level: string;
        nextLevel: string;
        pointsToNextLevel: number;
        pointsExpiringSoon: number;
        expiryDate: null;
        history: {
            id: string;
            type: "earned";
            amount: number;
            description: string;
            transactionDate: string;
            orderRef: string;
        }[];
        rewards: {
            id: string;
            name: string;
            description: string;
            pointsCost: number;
            imageUrl: string;
        }[];
    }>;
    private calculateMemberLevel;
    private getNextMemberLevel;
    private getPointsToNextLevel;
    checkin(userId: string): Promise<{
        success: boolean;
        message: string;
        pointsEarned: number;
        alreadyCheckedIn: boolean;
        currentStreak?: undefined;
        totalCheckins?: undefined;
        nextReward?: undefined;
    } | {
        success: boolean;
        message: string;
        pointsEarned: number;
        currentStreak: number;
        totalCheckins: number;
        nextReward: {
            days: number;
            reward: string;
            remaining: number;
        };
        alreadyCheckedIn?: undefined;
    }>;
    getCheckinHistory(userId: string, query: CheckinHistoryQueryDto): Promise<{
        currentStreak: number;
        longestStreak: number;
        totalCheckins: number;
        currentMonth: string;
        checkinDays: number[];
        rewards: {
            streakDays: number;
            reward: string;
            claimed: boolean;
            claimedDate: string | null;
        }[];
    }>;
    getNotifications(userId: string, query: NotificationQueryDto): Promise<{
        unreadCount: number;
        notifications: {
            id: string;
            type: string;
            title: string;
            content: string;
            isRead: boolean;
            createdAt: string;
            data: string | number | true | import(".prisma/client").Prisma.JsonObject | import(".prisma/client").Prisma.JsonArray;
        }[];
        pagination: {
            total: number;
            page: number | undefined;
            pageSize: number | undefined;
            hasMore: boolean;
        };
    }>;
    markNotificationAsRead(userId: string, notificationId: string): Promise<{
        id: string;
        isRead: boolean;
        message: string;
    }>;
    markAllNotificationsAsRead(userId: string): Promise<{
        success: boolean;
        count: number;
        message: string;
    }>;
    deleteNotification(userId: string, notificationId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getWallet(userId: string): Promise<{
        balance: number;
        currency: string;
        pendingTransactions: number;
        transactions: {
            id: string;
            type: string;
            amount: number;
            currency: string;
            status: string;
            description: string;
            createdAt: string;
            reference: string;
        }[];
        cards: {
            id: string;
            type: string;
            brand: string;
            last4: string;
            expiryMonth: number;
            expiryYear: number;
            isDefault: boolean;
        }[];
    }>;
    depositToWallet(userId: string, depositDto: WalletDepositDto): Promise<{
        transactionId: string;
        amount: number;
        currency: string;
        status: string;
        paymentIntent: {
            id: string;
            clientSecret: string;
        };
        message: string;
    }>;
    getCoupons(userId: string, query: CouponQueryDto): Promise<{
        coupons: {
            id: string;
            code: string;
            type: string;
            value: number;
            minPurchase: number | null;
            maxDiscount: number | null;
            currency: string;
            description: string;
            validFrom: string;
            validUntil: string;
            status: string;
            restrictions: string | number | true | import(".prisma/client").Prisma.JsonObject | import(".prisma/client").Prisma.JsonArray;
        }[];
    }>;
    private calculateCheckinStats;
    private getNextReward;
    private generateRewards;
}
