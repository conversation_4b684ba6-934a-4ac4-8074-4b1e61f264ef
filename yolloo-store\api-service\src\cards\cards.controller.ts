import { Body, Controller, Get, Param, Post, UseGuards, BadRequestException } from '@nestjs/common';
import { CardsService } from './cards.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { RegisterCardDto } from './dto/register-card.dto';
import { AddEsimDto } from './dto/add-esim.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('cards')
export class CardsController {
  constructor(private readonly cardsService: CardsService) {}

  @Get()
  getUserCards(@CurrentUser() user: any) {
    if (!user) {
      throw new BadRequestException('User not authenticated');
    }

    return this.cardsService.getUserCards(user.id);
  }

  @Get(':cardId')
  getCardById(
    @CurrentUser() user: any,
    @Param('cardId') cardId: string,
  ) {
    if (!user) {
      throw new BadRequestException('User not authenticated');
    }

    if (!cardId || cardId.trim() === '') {
      throw new BadRequestException('Card ID is required');
    }

    return this.cardsService.getCardById(user.id, cardId);
  }

  @Post()
  registerCard(
    @CurrentUser() user: any,
    @Body() registerCardDto: RegisterCardDto,
  ) {
    if (!user) {
      throw new BadRequestException('User not authenticated');
    }

    return this.cardsService.registerCard(user.id, registerCardDto);
  }

  @Post(':cardId/activate')
  activateCard(
    @CurrentUser() user: any,
    @Param('cardId') cardId: string,
  ) {
    if (!user) {
      throw new BadRequestException('User not authenticated');
    }

    if (!cardId || cardId.trim() === '') {
      throw new BadRequestException('Card ID is required');
    }

    return this.cardsService.activateCard(user.id, cardId);
  }

  @Post(':cardId/esims')
  addEsimToCard(
    @CurrentUser() user: any,
    @Param('cardId') cardId: string,
    @Body() addEsimDto: AddEsimDto,
  ) {
    if (!user) {
      throw new BadRequestException('User not authenticated');
    }

    if (!cardId || cardId.trim() === '') {
      throw new BadRequestException('Card ID is required');
    }

    return this.cardsService.addEsimToCard(user.id, cardId, addEsimDto);
  }

  // eSIM activation endpoint moved to EsimsController
}
