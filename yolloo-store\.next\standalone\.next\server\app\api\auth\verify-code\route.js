"use strict";(()=>{var e={};e.id=6153,e.ids=[6153],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},33120:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>_,requestAsyncStorage:()=>y,routeModule:()=>m,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g});var n={};r.r(n),r.d(n,{POST:()=>h,dynamic:()=>c,fetchCache:()=>p,revalidate:()=>d});var o=r(49303),i=r(88716),s=r(60670),a=r(87070),u=r(7410),l=r(93475);let c="force-dynamic",p="force-no-store",d=0,f=u.z.object({email:u.z.string().email("Invalid email address"),code:u.z.string().length(6,"Verification code must be 6 digits")});async function h(e){try{let t=await e.json(),{email:r,code:n}=f.parse(t),o=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e),i=`verify_code:${o}:${r}`;if(!await (0,l.yz)(i,5,60))return a.NextResponse.json({success:!1,error:"Too many verification attempts. Please wait before trying again"},{status:429});let s=await (0,l.Ak)(r);if(!s)return a.NextResponse.json({success:!1,error:"Verification code has expired or does not exist. Please request a new code"},{status:400});if(s!==n)return a.NextResponse.json({success:!1,error:"Invalid verification code. Please check and try again"},{status:400});return await (0,l.qc)(r),a.NextResponse.json({success:!0,message:"Verification successful"})}catch(e){if(console.error("Error in verify-code API:",e),e instanceof u.z.ZodError)return a.NextResponse.json({success:!1,error:"Invalid input data"},{status:400});return a.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/verify-code/route",pathname:"/api/auth/verify-code",filename:"route",bundlePath:"app/api/auth/verify-code/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\auth\\verify-code\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:v}=m,x="/api/auth/verify-code/route";function _(){return(0,s.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}},93475:(e,t,r)=>{r.d(t,{AL:()=>a,Ak:()=>u,qc:()=>l,yz:()=>c});var n=r(62197),o=r.n(n);let i=null;function s(){if(!i){let e=process.env.REDIS_URL||"redis://localhost:6379";(i=new(o())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),i.on("connect",()=>{console.log("Successfully connected to Redis")})}return i}async function a(e,t,r=300){try{let n=s(),o=`verification_code:${e}`;return await n.setex(o,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function u(e){try{let t=s(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function l(e){try{let t=s(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let n=s(),o=`rate_limit:${e}`,i=await n.get(o),a=i?parseInt(i):0;if(a>=t)return!1;return 0===a?await n.setex(o,r,"1"):await n.incr(o),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,i={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[n,o],...i]=a(e),{domain:s,expires:u,httponly:p,maxage:d,path:f,samesite:h,secure:m,partitioned:y,priority:g}=Object.fromEntries(i.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(o),domain:s,...u&&{expires:new Date(u)},...p&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:f,...h&&{sameSite:l.includes(t=(t=h).toLowerCase())?t:void 0},...m&&{secure:!0},...g&&{priority:c.includes(r=(r=g).toLowerCase())?r:void 0},...y&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(i,{RequestCookies:()=>p,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>u,stringifyCookie:()=>s}),e.exports=((e,i,s,a)=>{if(i&&"object"==typeof i||"function"==typeof i)for(let s of n(i))o.call(e,s)||void 0===s||t(e,s,{get:()=>i[s],enumerable:!(a=r(i,s))||a.enumerable});return e})(t({},"__esModule",{value:!0}),i);var l=["strict","lax","none"],c=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(o)?o:function(e){if(!e)return[];var t,r,n,o,i,s=[],a=0;function u(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,i=!1;u();)if(","===(r=e.charAt(a))){for(n=a,a+=1,u(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(i=!0,a=o,s.push(e.substring(t,n)),t=a):a=n+1}else a+=1;(!i||a>=e.length)&&s.push(e.substring(t,e.length))}return s}(o)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,9092,5972,2197,7410],()=>r(33120));module.exports=n})();