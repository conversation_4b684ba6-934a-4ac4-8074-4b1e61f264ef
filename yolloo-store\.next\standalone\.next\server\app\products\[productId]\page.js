(()=>{var e={};e.id=8360,e.ids=[8360],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},18530:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l}),s(76215),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),i=s(37922),n=s.n(i),o=s(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let l=["",{children:["products",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76215)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\products\\[productId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\products\\[productId]\\page.tsx"],u="/products/[productId]/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/products/[productId]/page",pathname:"/products/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16627:(e,t,s)=>{Promise.resolve().then(s.bind(s,2951)),Promise.resolve().then(s.bind(s,62331)),Promise.resolve().then(s.bind(s,79210))},62331:(e,t,s)=>{"use strict";s.d(t,{ProductActions:()=>v});var r=s(10326),a=s(17577),i=s(88846),n=s(31048),o=s(54432),d=s(90772),l=s(34789),c=s(79492);function u({product:e,selectedVariant:t,disabled:s,uid:i}){let{toast:n}=(0,l.pm)(),{addItem:o}=(0,c.j)(),[u,m]=(0,a.useState)(!1),p=u||s||!!(e.variants?.length&&!t);return r.jsx(d.Button,{onClick:()=>{if(e.variants&&e.variants.length>0&&!t){n({title:"Please select options",description:"You need to select product options before adding to cart",variant:"destructive"});return}if(e.requiredUID&&!i){n({title:"UID Required",description:"Please select or enter a UID before adding to cart",variant:"destructive"});return}m(!0);try{let s={id:`${e.id}${t?`-${t.id}`:""}${i?`-${i}`:""}`,productId:e.id,name:e.name,price:t?.price||e.price,quantity:1,image:e.images?.[0],stock:e.stock,variant:t?{id:t.id,price:t.price,duration:t.duration,durationType:t.durationType}:void 0,uid:i};console.log("Adding to cart:",{product:e,selectedVariant:t,uid:i,cartItem:s}),o(s),n({title:"Added to cart",description:`Added ${e.name}${t?` ${t.duration?t.duration:""}(${t.durationType?t.durationType:""})`:""} to your cart`,duration:2e3})}catch(e){n({title:"Error",description:"Failed to add the product to cart",variant:"destructive"})}finally{m(!1)}},disabled:p,className:"w-full",children:u?"Adding to cart...":"Add to Cart"})}var m=s(77109),p=s(33071);s(85999);var x=s(57372),h=s(77863);function f({onSelect:e,selectedUid:t}){let{data:s,status:o}=(0,m.useSession)(),[l,c]=(0,a.useState)([]),[u,f]=(0,a.useState)(!0);if("loading"===o||u)return r.jsx("div",{className:"flex items-center justify-center h-24",children:r.jsx(x.P.spinner,{className:"h-6 w-6 animate-spin"})});if("unauthenticated"===o)return r.jsx(p.Zb,{children:r.jsx(p.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Please sign in to select a card"}),r.jsx(d.Button,{className:"mt-4",asChild:!0,children:r.jsx("a",{href:`/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`,children:"Sign In"})})]})})});if(0===l.length)return r.jsx(p.Zb,{children:r.jsx(p.aY,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"You don't have any cards yet"}),r.jsx(d.Button,{className:"mt-4",asChild:!0,children:r.jsx("a",{href:"/cards",children:"Add a Card"})})]})})});let g=e=>e.replace(/(\d{5})(?=\d)/g,"$1 ");return(0,r.jsxs)(p.Zb,{className:"border-2 shadow-sm",children:[(0,r.jsxs)(p.Ol,{className:"pb-3",children:[r.jsx(p.ll,{className:"text-xl",children:"Select a Card"}),r.jsx(p.SZ,{children:"Choose a card to bind this eSIM to"})]}),r.jsx(p.aY,{children:r.jsx(i.E,{value:t,onValueChange:e,className:"grid gap-4",children:l.map(s=>{let a=t===s.number;return(0,r.jsxs)("div",{className:`
                  relative rounded-xl border-2 p-4 transition-all duration-200 cursor-pointer
                  ${a?"border-primary bg-primary/5 shadow-md":"border-muted hover:border-primary/30 hover:bg-accent/30"}
                `,onClick:()=>e(s.number),children:[(0,r.jsxs)("div",{className:"absolute right-4 top-4",children:[r.jsx(i.m,{value:s.number,id:s.id,className:"sr-only"}),r.jsx("div",{className:`
                    w-5 h-5 rounded-full border-2 flex items-center justify-center
                    ${a?"border-primary bg-primary text-primary-foreground":"border-muted-foreground"}
                  `,children:a&&r.jsx(x.P.check,{className:"h-3 w-3"})})]}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2 pr-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:s.id,className:"text-base font-medium cursor-pointer",children:s.customName||`${"physical"===s.type?"Physical":"Virtual"} Card`}),(0,r.jsxs)("div",{className:"mt-1 font-mono text-sm sm:text-base text-primary font-medium",children:["UID: ",g(s.number)]})]}),r.jsx("span",{className:`
                      text-sm px-3 py-1.5 rounded-full self-start
                      ${"Active"===s.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"Inactive"===s.status?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"}
                    `,children:s.status})]}),s.activationDate&&(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Activated: ",h.CN.forUserSafe(s.activationDate),s.expiryDate&&` • Expires: ${h.CN.forUserSafe(s.expiryDate)}`]})]})]},s.id)})})})]})}var g=s(96655),b=s(19990);function v({product:e}){let[t,s]=(0,a.useState)(e.variants?.[0]??null),[d,l]=(0,a.useState)(),[c,m]=(0,a.useState)(""),[p,h]=(0,a.useState)(""),[v,j]=(0,a.useState)("select"),[y,N]=(0,a.useState)(e.requiredUID||!1),[w,k]=(0,a.useState)(""),[P,S]=(0,a.useState)(!1),I=e=>e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"),C=e=>{k("");let t=e.replace(/-/g,"");return t?/^\d+$/.test(t)?20!==t.length?(k("UID must be exactly 20 digits"),S(!1),!1):(S(!0),!0):(k("UID must contain only numbers"),S(!1),!1):(k("UID cannot be empty"),S(!1),!1)},$=y?"select"===v?d:P?c:void 0:void 0,D=e.category?.name?.toLowerCase().includes("card")||!1;return(0,r.jsxs)("div",{className:"space-y-6",children:[(!e.variants||0===e.variants.length)&&(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",e.price.toFixed(2)]}),e.variants&&e.variants.length>0&&r.jsx(b.A,{variants:e.variants,onVariantSelect:e=>s(e),selectedVariant:t}),!D&&(0,r.jsxs)("div",{className:"space-y-4",children:[!e.requiredUID&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(g.r,{checked:y,onCheckedChange:N,id:"enable-uid"}),r.jsx(n._,{htmlFor:"enable-uid",children:"Bind to a card"})]}),y&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"mb-4",children:(0,r.jsxs)(i.E,{value:v,onValueChange:e=>{j(e),l(void 0),m(""),h("")},className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,r.jsxs)("div",{className:`
                      flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all
                      ${"select"===v?"border-primary bg-primary/5":"border-muted hover:border-primary/30"}
                    `,onClick:()=>{j("select"),l(void 0),m(""),h("")},children:[r.jsx(i.m,{value:"select",id:"select",className:"mr-3"}),r.jsx(n._,{htmlFor:"select",className:"cursor-pointer font-medium",children:"Select from my cards"})]}),(0,r.jsxs)("div",{className:`
                      flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all
                      ${"manual"===v?"border-primary bg-primary/5":"border-muted hover:border-primary/30"}
                    `,onClick:()=>{j("manual"),l(void 0),m(""),h("")},children:[r.jsx(i.m,{value:"manual",id:"manual",className:"mr-3"}),r.jsx(n._,{htmlFor:"manual",className:"cursor-pointer font-medium",children:"Enter card UID manually"})]})]})}),"select"===v?r.jsx(f,{onSelect:l,selectedUid:d}):(0,r.jsxs)("div",{className:"space-y-3 border-2 rounded-xl p-4 shadow-sm",children:[(0,r.jsxs)("div",{children:[r.jsx(n._,{htmlFor:"uid",className:"text-base font-medium",children:"Card UID"}),r.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Enter your 20-digit card number without spaces or special characters"})]}),r.jsx("div",{className:"relative",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground font-mono",children:"UID:"}),r.jsx(o.I,{id:"uid",value:p,onChange:e=>{let t=e.target.value.replace(/\D/g,"").slice(0,20);m(t),h(I(t)),t?C(t):(k(""),S(!1))},placeholder:"Example: 29901000000000000025",className:`
                          text-base py-6 pl-12 pr-4 font-mono
                          ${w?"border-destructive":"border-input focus:border-primary"}
                        `}),p&&!w&&r.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-green-600",children:r.jsx(x.P.check,{className:"h-5 w-5"})})]})}),w&&(0,r.jsxs)("p",{className:"text-sm text-destructive flex items-center gap-1.5",children:[r.jsx(x.P.warning,{className:"h-4 w-4"}),w]}),!w&&p&&(0,r.jsxs)("p",{className:"text-sm text-green-600 flex items-center gap-1.5",children:[r.jsx(x.P.check,{className:"h-4 w-4"}),"Valid card number format"]})]}),e.requiredUID&&!$&&r.jsx("div",{className:"mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-destructive flex items-center gap-2",children:[r.jsx(x.P.warning,{className:"h-4 w-4 flex-shrink-0"}),r.jsx("span",{children:"Please select a card or enter a valid UID to continue"})]})})]})]}),r.jsx(u,{product:e,selectedVariant:t,disabled:e.requiredUID&&!$,uid:$})]})}},96655:(e,t,s)=>{"use strict";s.d(t,{r:()=>w});var r=s(10326),a=s(17577),i=s(82561),n=s(48051),o=s(93095),d=s(52067),l=s(53405),c=s(2566),u=s(45226),m="Switch",[p,x]=(0,o.b)(m),[h,f]=p(m),g=a.forwardRef((e,t)=>{let{__scopeSwitch:s,name:o,checked:l,defaultChecked:c,required:m,disabled:p,value:x="on",onCheckedChange:f,form:g,...b}=e,[v,N]=a.useState(null),w=(0,n.e)(t,e=>N(e)),k=a.useRef(!1),P=!v||g||!!v.closest("form"),[S=!1,I]=(0,d.T)({prop:l,defaultProp:c,onChange:f});return(0,r.jsxs)(h,{scope:s,checked:S,disabled:p,children:[(0,r.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":S,"aria-required":m,"data-state":y(S),"data-disabled":p?"":void 0,disabled:p,value:x,...b,ref:w,onClick:(0,i.M)(e.onClick,e=>{I(e=>!e),P&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),P&&(0,r.jsx)(j,{control:v,bubbles:!k.current,name:o,value:x,checked:S,required:m,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var b="SwitchThumb",v=a.forwardRef((e,t)=>{let{__scopeSwitch:s,...a}=e,i=f(b,s);return(0,r.jsx)(u.WV.span,{"data-state":y(i.checked),"data-disabled":i.disabled?"":void 0,...a,ref:t})});v.displayName=b;var j=e=>{let{control:t,checked:s,bubbles:i=!0,...n}=e,o=a.useRef(null),d=(0,l.D)(s),u=(0,c.t)(t);return a.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==s&&t){let r=new Event("click",{bubbles:i});t.call(e,s),e.dispatchEvent(r)}},[d,s,i]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:o,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function y(e){return e?"checked":"unchecked"}var N=s(77863);let w=a.forwardRef(({className:e,...t},s)=>r.jsx(g,{className:(0,N.cn)("peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:s,children:r.jsx(v,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=g.displayName},79210:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>o,TabsContent:()=>c,TabsList:()=>d,TabsTrigger:()=>l});var r=s(10326),a=s(17577),i=s(13239),n=s(77863);let o=i.fC,d=a.forwardRef(({className:e,...t},s)=>r.jsx(i.aV,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=i.aV.displayName;let l=a.forwardRef(({className:e,...t},s)=>r.jsx(i.xz,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));l.displayName=i.xz.displayName;let c=a.forwardRef(({className:e,...t},s)=>r.jsx(i.VY,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.VY.displayName},76215:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(19510),a=s(58585),i=s(72331),n=s(68570);let o=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\products\product-actions.tsx`),{__esModule:d,$$typeof:l}=o;o.default;let c=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\products\product-actions.tsx#ProductActions`);var u=s(50650),m=s(38137);let p=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\tabs.tsx`),{__esModule:x,$$typeof:h}=p;p.default;let f=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\tabs.tsx#Tabs`),g=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\tabs.tsx#TabsList`),b=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\tabs.tsx#TabsTrigger`),v=(0,n.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\ui\tabs.tsx#TabsContent`);async function j({params:e}){let t=await i._.product.findFirst({where:{id:e.productId,status:"ACTIVE",off_shelve:!1},include:{category:!0,variants:!0,parameters:!0}});return t||(0,a.notFound)(),(0,r.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,r.jsxs)("div",{className:"grid gap-8 lg:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:(0,u.cn)("relative aspect-square max-h-[600px] overflow-hidden rounded-xl shadow-md bg-gradient-to-tr from-gray-50 to-white dark:from-gray-900 dark:to-gray-950",t.off_shelve&&"opacity-60"),children:[t.images?.[0]?r.jsx("img",{src:t.images[0],alt:(0,u.mo)(t.name),className:(0,u.cn)("h-full w-full rounded-xl object-contain p-4 transition-all duration-300 hover:scale-105",t.off_shelve&&"grayscale")}):r.jsx("div",{className:"flex aspect-square w-full items-center justify-center rounded-xl bg-secondary",children:r.jsx("span",{className:"text-muted-foreground",children:"No image available"})}),t.off_shelve&&r.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black/30 rounded-xl",children:r.jsx("span",{className:"bg-black/70 text-white px-4 py-2 rounded-full font-medium",children:"Off Shelf"})})]}),r.jsx("div",{className:"grid grid-cols-4 gap-2",children:t.images?.slice(1).map((e,s)=>r.jsx("img",{src:e,alt:`${u.mo(t.name)} ${s+2}`,className:u.cn("aspect-square h-24 w-24 rounded-lg object-cover shadow-sm border border-gray-100 dark:border-gray-800 transition-all duration-200 hover:shadow-md hover:scale-105",t.off_shelve&&"opacity-60 grayscale")},s))})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("div",{children:(()=>{let{badge:e,name:s}=(0,u.eP)(t.name);return(0,r.jsxs)("h1",{className:(0,u.cn)("text-2xl font-bold flex items-center gap-3 bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent",t.off_shelve&&"from-muted-foreground to-muted-foreground/70"),children:[e&&r.jsx("span",{className:"inline-block rounded-full bg-gradient-to-r from-primary to-primary/70 text-white font-bold px-3 py-1 text-xs shadow-sm",children:e}),(0,u.mo)(s)]})})()}),r.jsx("div",{className:"prose max-w-none text-sm text-muted-foreground",children:r.jsx(m.T,{text:t.websiteDescription||"",maxLines:10,plain:!1})}),r.jsx(c,{product:{id:t.id,name:t.name,price:t.price,images:t.images,stock:t.stock,category:t.category,requiredUID:t.requiredUID,variants:t.variants.map(e=>{let s=`Variant ${e.id.substring(0,5)}`;return e.duration&&e.durationType&&(s=`${t.name} ${e.duration} ${e.durationType}${e.duration>1?"s":""}`),{id:e.id,name:s,price:Number(e.price),duration:e.duration,durationType:e.durationType}}),reviews:t.reviews||[]}})]})]}),r.jsx("div",{className:"mt-12",children:(0,r.jsxs)(f,{defaultValue:"description",className:"w-full",children:[(0,r.jsxs)(g,{className:"mb-6 grid w-full grid-cols-2",children:[r.jsx(b,{value:"description",children:"Description"}),r.jsx(b,{value:"specifications",children:"Specifications"})]}),r.jsx(v,{value:"description",className:"space-y-4",children:r.jsx("div",{className:"prose max-w-none",children:r.jsx(m.T,{text:t.websiteDescription||""})})}),r.jsx(v,{value:"specifications",className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-muted/50 p-4 rounded-lg",children:[r.jsx("h3",{className:"font-medium mb-2",children:"Product Details"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{className:"flex justify-between",children:[r.jsx("span",{className:"text-muted-foreground",children:"Category"}),r.jsx("span",{className:"font-medium",children:t.category?.name||"N/A"})]}),(0,r.jsxs)("li",{className:"flex justify-between",children:[r.jsx("span",{className:"text-muted-foreground",children:"Stock"}),(0,r.jsxs)("span",{className:"font-medium",children:[t.stock," units"]})]})]})]}),Array.isArray(t.parameters)&&t.parameters.length>0&&(0,r.jsxs)("div",{className:"bg-muted/50 p-4 rounded-lg",children:[r.jsx("h3",{className:"font-medium mb-2",children:"Technical Specifications"}),r.jsx("ul",{className:"space-y-2",children:t.parameters.map((e,t)=>(0,r.jsxs)("li",{className:"flex justify-between",children:[r.jsx("span",{className:"text-muted-foreground",children:e.name||e.code||"Parameter"}),r.jsx("span",{className:"font-medium",children:e.value||"N/A"})]},t))})]})]})})]})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,3239,4812,4824,3442,1245],()=>s(18530));module.exports=r})();