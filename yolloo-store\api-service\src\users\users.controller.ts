import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { WalletDepositDto } from './dto/wallet-deposit.dto';
import { NotificationQueryDto } from './dto/notification-query.dto';
import { CheckinHistoryQueryDto } from './dto/checkin-history-query.dto';
import { CouponQueryDto } from './dto/coupon-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('profile')
  getProfile(@CurrentUser() user: any) {
    return this.usersService.getProfile(user.id);
  }

  @Put('profile')
  updateProfile(
    @CurrentUser() user: any,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    return this.usersService.updateProfile(user.id, updateProfileDto);
  }

  @Get('points')
  getPoints(@CurrentUser() user: any) {
    return this.usersService.getPoints(user.id);
  }

  @Post('checkin')
  checkin(@CurrentUser() user: any) {
    return this.usersService.checkin(user.id);
  }

  @Get('checkin-history')
  getCheckinHistory(
    @CurrentUser() user: any,
    @Query() query: CheckinHistoryQueryDto,
  ) {
    return this.usersService.getCheckinHistory(user.id, query);
  }

  @Get('notifications')
  getNotifications(
    @CurrentUser() user: any,
    @Query() query: NotificationQueryDto,
  ) {
    return this.usersService.getNotifications(user.id, query);
  }

  @Put('notifications/:notificationId/read')
  markNotificationAsRead(
    @CurrentUser() user: any,
    @Param('notificationId') notificationId: string,
  ) {
    return this.usersService.markNotificationAsRead(user.id, notificationId);
  }

  @Put('notifications/read-all')
  markAllNotificationsAsRead(@CurrentUser() user: any) {
    return this.usersService.markAllNotificationsAsRead(user.id);
  }

  @Delete('notifications/:notificationId')
  deleteNotification(
    @CurrentUser() user: any,
    @Param('notificationId') notificationId: string,
  ) {
    return this.usersService.deleteNotification(user.id, notificationId);
  }

  @Get('wallet')
  getWallet(@CurrentUser() user: any) {
    return this.usersService.getWallet(user.id);
  }

  @Post('wallet/deposit')
  depositToWallet(
    @CurrentUser() user: any,
    @Body() depositDto: WalletDepositDto,
  ) {
    return this.usersService.depositToWallet(user.id, depositDto);
  }

  @Get('coupons')
  getCoupons(
    @CurrentUser() user: any,
    @Query() query: CouponQueryDto,
  ) {
    return this.usersService.getCoupons(user.id, query);
  }
}
