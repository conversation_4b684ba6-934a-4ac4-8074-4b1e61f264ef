{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}, {"key": "X-Next-Dynamic-Rendering", "value": "true"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/admin/cards/[cardId]", "regex": "^/admin/cards/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcardId": "nxtPcardId"}, "namedRegex": "^/admin/cards/(?<nxtPcardId>[^/]+?)(?:/)?$"}, {"page": "/admin/orders/[orderId]", "regex": "^/admin/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/admin/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/admin/organizations/[id]", "regex": "^/admin/organizations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/organizations/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/organizations/[id]/members/[memberId]", "regex": "^/admin/organizations/([^/]+?)/members/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPmemberId": "nxtPmemberId"}, "namedRegex": "^/admin/organizations/(?<nxtPid>[^/]+?)/members/(?<nxtPmemberId>[^/]+?)(?:/)?$"}, {"page": "/admin/products/[productId]", "regex": "^/admin/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/admin/products/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/admin/users/[userId]", "regex": "^/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/admin/users/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/affiliate/organization/[id]", "regex": "^/affiliate/organization/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/organization/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/affiliate/organization/[id]/analytics", "regex": "^/affiliate/organization/([^/]+?)/analytics(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/organization/(?<nxtPid>[^/]+?)/analytics(?:/)?$"}, {"page": "/affiliate/organization/[id]/edit", "regex": "^/affiliate/organization/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/organization/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/affiliate/organization/[id]/members", "regex": "^/affiliate/organization/([^/]+?)/members(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/organization/(?<nxtPid>[^/]+?)/members(?:/)?$"}, {"page": "/affiliate/organization/[id]/members/invite", "regex": "^/affiliate/organization/([^/]+?)/members/invite(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/organization/(?<nxtPid>[^/]+?)/members/invite(?:/)?$"}, {"page": "/affiliate/organization/[id]/withdrawals", "regex": "^/affiliate/organization/([^/]+?)/withdrawals(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/affiliate/organization/(?<nxtPid>[^/]+?)/withdrawals(?:/)?$"}, {"page": "/api/addresses/[addressId]", "regex": "^/api/addresses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPaddressId": "nxtPaddressId"}, "namedRegex": "^/api/addresses/(?<nxtPaddressId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/affiliates/[affiliateId]", "regex": "^/api/admin/affiliates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPaffiliateId": "nxtPaffiliateId"}, "namedRegex": "^/api/admin/affiliates/(?<nxtPaffiliateId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/cards/[cardId]", "regex": "^/api/admin/cards/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcardId": "nxtPcardId"}, "namedRegex": "^/api/admin/cards/(?<nxtPcardId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/categories/[categoryId]", "regex": "^/api/admin/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcategoryId": "nxtPcategoryId"}, "namedRegex": "^/api/admin/categories/(?<nxtPcategoryId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/commissions/[id]", "regex": "^/api/admin/commissions/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/commissions/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/orders/[orderId]", "regex": "^/api/admin/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/api/admin/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/orders/[orderId]/odoo", "regex": "^/api/admin/orders/([^/]+?)/odoo(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/api/admin/orders/(?<nxtPorderId>[^/]+?)/odoo(?:/)?$"}, {"page": "/api/admin/orders/[orderId]/update-status", "regex": "^/api/admin/orders/([^/]+?)/update\\-status(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/api/admin/orders/(?<nxtPorderId>[^/]+?)/update\\-status(?:/)?$"}, {"page": "/api/admin/organizations/[id]", "regex": "^/api/admin/organizations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/organizations/[id]/analytics", "regex": "^/api/admin/organizations/([^/]+?)/analytics(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)/analytics(?:/)?$"}, {"page": "/api/admin/organizations/[id]/invites", "regex": "^/api/admin/organizations/([^/]+?)/invites(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)/invites(?:/)?$"}, {"page": "/api/admin/organizations/[id]/members", "regex": "^/api/admin/organizations/([^/]+?)/members(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)/members(?:/)?$"}, {"page": "/api/admin/organizations/[id]/members/batch", "regex": "^/api/admin/organizations/([^/]+?)/members/batch(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)/members/batch(?:/)?$"}, {"page": "/api/admin/organizations/[id]/members/[memberId]", "regex": "^/api/admin/organizations/([^/]+?)/members/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPmemberId": "nxtPmemberId"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)/members/(?<nxtPmemberId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/organizations/[id]/refresh-stats", "regex": "^/api/admin/organizations/([^/]+?)/refresh\\-stats(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/organizations/(?<nxtPid>[^/]+?)/refresh\\-stats(?:/)?$"}, {"page": "/api/admin/products/[productId]", "regex": "^/api/admin/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/api/admin/products/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/api/admin/products/[productId]/check-orders", "regex": "^/api/admin/products/([^/]+?)/check\\-orders(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/api/admin/products/(?<nxtPproductId>[^/]+?)/check\\-orders(?:/)?$"}, {"page": "/api/admin/products/[productId]/force-delete", "regex": "^/api/admin/products/([^/]+?)/force\\-delete(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/api/admin/products/(?<nxtPproductId>[^/]+?)/force\\-delete(?:/)?$"}, {"page": "/api/admin/users/[userId]", "regex": "^/api/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPuserId": "nxtPuserId"}, "namedRegex": "^/api/admin/users/(?<nxtPuserId>[^/]+?)(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]", "regex": "^/api/affiliate/organizations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/analytics", "regex": "^/api/affiliate/organizations/([^/]+?)/analytics(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/analytics(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/invites", "regex": "^/api/affiliate/organizations/([^/]+?)/invites(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/invites(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/invites/batch", "regex": "^/api/affiliate/organizations/([^/]+?)/invites/batch(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/invites/batch(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/invites/general", "regex": "^/api/affiliate/organizations/([^/]+?)/invites/general(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/invites/general(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/invites/[inviteId]", "regex": "^/api/affiliate/organizations/([^/]+?)/invites/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPinviteId": "nxtPinviteId"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/invites/(?<nxtPinviteId>[^/]+?)(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/members", "regex": "^/api/affiliate/organizations/([^/]+?)/members(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/members(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/members/batch", "regex": "^/api/affiliate/organizations/([^/]+?)/members/batch(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/members/batch(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/members/create-accounts", "regex": "^/api/affiliate/organizations/([^/]+?)/members/create\\-accounts(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/members/create\\-accounts(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/members/[memberId]", "regex": "^/api/affiliate/organizations/([^/]+?)/members/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPmemberId": "nxtPmemberId"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/members/(?<nxtPmemberId>[^/]+?)(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/refresh-stats", "regex": "^/api/affiliate/organizations/([^/]+?)/refresh\\-stats(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/refresh\\-stats(?:/)?$"}, {"page": "/api/affiliate/organizations/[id]/withdrawals", "regex": "^/api/affiliate/organizations/([^/]+?)/withdrawals(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/affiliate/organizations/(?<nxtPid>[^/]+?)/withdrawals(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/cards/[cardId]", "regex": "^/api/cards/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcardId": "nxtPcardId"}, "namedRegex": "^/api/cards/(?<nxtPcardId>[^/]+?)(?:/)?$"}, {"page": "/api/cards/[cardId]/activate", "regex": "^/api/cards/([^/]+?)/activate(?:/)?$", "routeKeys": {"nxtPcardId": "nxtPcardId"}, "namedRegex": "^/api/cards/(?<nxtPcardId>[^/]+?)/activate(?:/)?$"}, {"page": "/api/cart/[cartItemId]", "regex": "^/api/cart/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcartItemId": "nxtPcartItemId"}, "namedRegex": "^/api/cart/(?<nxtPcartItemId>[^/]+?)(?:/)?$"}, {"page": "/api/orders/[orderId]", "regex": "^/api/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/api/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/api/products/[productId]", "regex": "^/api/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/api/products/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/api/products/[productId]/status", "regex": "^/api/products/([^/]+?)/status(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/api/products/(?<nxtPproductId>[^/]+?)/status(?:/)?$"}, {"page": "/api/test-organization-data/[id]", "regex": "^/api/test\\-organization\\-data/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/test\\-organization\\-data/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/[...path]", "regex": "^/api/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/cards/[cardId]", "regex": "^/cards/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcardId": "nxtPcardId"}, "namedRegex": "^/cards/(?<nxtPcardId>[^/]+?)(?:/)?$"}, {"page": "/esims/[productId]", "regex": "^/esims/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/esims/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/invite/[code]", "regex": "^/invite/([^/]+?)(?:/)?$", "routeKeys": {"nxtPcode": "nxtPcode"}, "namedRegex": "^/invite/(?<nxtPcode>[^/]+?)(?:/)?$"}, {"page": "/orders/[orderId]", "regex": "^/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPorderId": "nxtPorderId"}, "namedRegex": "^/orders/(?<nxtPorderId>[^/]+?)(?:/)?$"}, {"page": "/products/[productId]", "regex": "^/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPproductId": "nxtPproductId"}, "namedRegex": "^/products/(?<nxtPproductId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/account", "regex": "^/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/account(?:/)?$"}, {"page": "/activate", "regex": "^/activate(?:/)?$", "routeKeys": {}, "namedRegex": "^/activate(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/affiliates", "regex": "^/admin/affiliates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/affiliates(?:/)?$"}, {"page": "/admin/cards", "regex": "^/admin/cards(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/cards(?:/)?$"}, {"page": "/admin/cards/import", "regex": "^/admin/cards/import(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/cards/import(?:/)?$"}, {"page": "/admin/cards/new", "regex": "^/admin/cards/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/cards/new(?:/)?$"}, {"page": "/admin/commissions", "regex": "^/admin/commissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/commissions(?:/)?$"}, {"page": "/admin/orders", "regex": "^/admin/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/orders(?:/)?$"}, {"page": "/admin/organizations", "regex": "^/admin/organizations(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/organizations(?:/)?$"}, {"page": "/admin/products", "regex": "^/admin/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products(?:/)?$"}, {"page": "/admin/products/new", "regex": "^/admin/products/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products/new(?:/)?$"}, {"page": "/admin/products/sync", "regex": "^/admin/products/sync(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products/sync(?:/)?$"}, {"page": "/admin/settings", "regex": "^/admin/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings(?:/)?$"}, {"page": "/admin/subscribers", "regex": "^/admin/subscribers(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/subscribers(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/affiliate", "regex": "^/affiliate(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate(?:/)?$"}, {"page": "/affiliate/organization", "regex": "^/affiliate/organization(?:/)?$", "routeKeys": {}, "namedRegex": "^/affiliate/organization(?:/)?$"}, {"page": "/auth/error", "regex": "^/auth/error(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/error(?:/)?$"}, {"page": "/auth/forgot-password", "regex": "^/auth/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/forgot\\-password(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/reset-password", "regex": "^/auth/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/reset\\-password(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/bind-card", "regex": "^/bind\\-card(?:/)?$", "routeKeys": {}, "namedRegex": "^/bind\\-card(?:/)?$"}, {"page": "/cards", "regex": "^/cards(?:/)?$", "routeKeys": {}, "namedRegex": "^/cards(?:/)?$"}, {"page": "/cart", "regex": "^/cart(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/esims", "regex": "^/esims(?:/)?$", "routeKeys": {}, "namedRegex": "^/esims(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/how-it-works", "regex": "^/how\\-it\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/how\\-it\\-works(?:/)?$"}, {"page": "/invite/error", "regex": "^/invite/error(?:/)?$", "routeKeys": {}, "namedRegex": "^/invite/error(?:/)?$"}, {"page": "/landing", "regex": "^/landing(?:/)?$", "routeKeys": {}, "namedRegex": "^/landing(?:/)?$"}, {"page": "/odoo-integration", "regex": "^/odoo\\-integration(?:/)?$", "routeKeys": {}, "namedRegex": "^/odoo\\-integration(?:/)?$"}, {"page": "/orders", "regex": "^/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders(?:/)?$"}, {"page": "/presale", "regex": "^/presale(?:/)?$", "routeKeys": {}, "namedRegex": "^/presale(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/products", "regex": "^/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/products(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/test/uid-extraction", "regex": "^/test/uid\\-extraction(?:/)?$", "routeKeys": {}, "namedRegex": "^/test/uid\\-extraction(?:/)?$"}, {"page": "/yolloo-smart", "regex": "^/yolloo\\-smart(?:/)?$", "routeKeys": {}, "namedRegex": "^/yolloo\\-smart(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}