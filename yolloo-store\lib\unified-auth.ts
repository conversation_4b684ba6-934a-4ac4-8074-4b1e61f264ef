/**
 * 统一认证客户端
 * 支持Web端和移动端的JWT认证
 */

interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role: string;
}

interface LoginResponse {
  user: User;
  message: string;
}

interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

class UnifiedAuthClient {
  private baseUrl: string;
  private isServer: boolean;

  constructor() {
    this.isServer = typeof window === 'undefined';
    this.baseUrl = this.isServer 
      ? process.env.API_SERVICE_URL || 'http://localhost:4000'
      : process.env.NEXT_PUBLIC_API_SERVICE_URL || 'http://localhost:4000';
  }

  /**
   * 登录
   */
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/web/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // 包含Cookie
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || 'Login failed' };
      }

      return { data };
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'Network error' };
    }
  }

  /**
   * 注册
   */
  async signup(name: string, email: string, password: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/web/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ name, email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || 'Signup failed' };
      }

      return { data };
    } catch (error) {
      console.error('Signup error:', error);
      return { error: 'Network error' };
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/web/auth/logout`, {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error('Logout error:', error);
      return { error: 'Network error' };
    }
  }

  /**
   * 获取当前用户
   */
  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    try {
      const response = await fetch(`${this.baseUrl}/api/web/auth/me`, {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || 'Failed to get user' };
      }

      return { data };
    } catch (error) {
      console.error('Get user error:', error);
      return { error: 'Network error' };
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<ApiResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/web/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || 'Token refresh failed' };
      }

      return { data };
    } catch (error) {
      console.error('Token refresh error:', error);
      return { error: 'Network error' };
    }
  }

  /**
   * 通用API调用方法
   */
  async apiCall<T = any>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      // 如果是401错误，尝试刷新token
      if (response.status === 401) {
        const refreshResult = await this.refreshToken();
        if (refreshResult.error) {
          return { error: 'Authentication failed' };
        }

        // 重试原请求
        const retryResponse = await fetch(`${this.baseUrl}${endpoint}`, {
          ...options,
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
        });

        const retryData = await retryResponse.json();
        if (!retryResponse.ok) {
          return { error: retryData.error || 'Request failed' };
        }

        return { data: retryData };
      }

      const data = await response.json();

      if (!response.ok) {
        return { error: data.error || 'Request failed' };
      }

      return { data };
    } catch (error) {
      console.error('API call error:', error);
      return { error: 'Network error' };
    }
  }
}

// 导出单例实例
export const authClient = new UnifiedAuthClient();

// 导出类型
export type { User, LoginResponse, ApiResponse };
