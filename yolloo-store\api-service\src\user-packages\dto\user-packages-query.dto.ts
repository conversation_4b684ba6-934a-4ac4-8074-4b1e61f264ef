import { IsOptional, IsString, IsIn, <PERSON><PERSON><PERSON>ber, <PERSON>, <PERSON>, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';

export class UserPackagesQueryDto {
  @IsOptional()
  @IsIn(['activating', 'to_be_activated', 'expired', 'all'])
  status?: 'activating' | 'to_be_activated' | 'expired' | 'all' = 'all';

  @IsOptional()
  @IsIn(['domestic', 'roaming', 'local'])
  packageType?: 'domestic' | 'roaming' | 'local';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class ActivatePackageDto {
  @IsString()
  packageId: string;
}

export class UsageStatsQueryDto {
  @IsOptional()
  @IsIn(['daily', 'weekly', 'monthly'])
  period?: 'daily' | 'weekly' | 'monthly' = 'daily';

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;
}

export class UsageHistoryQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsIn(['daily', 'weekly', 'monthly'])
  period?: 'daily' | 'weekly' | 'monthly' = 'daily';

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;
}
