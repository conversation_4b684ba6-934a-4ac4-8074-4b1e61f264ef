(()=>{var e={};e.id=8888,e.ids=[8888],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},48649:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>l}),t(91082),t(85460),t(89090),t(26083),t(35866);var s=t(23191),n=t(88716),o=t(37922),i=t.n(o),a=t(95231),d={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let l=["",{children:["admin",{children:["subscribers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91082)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\subscribers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\subscribers\\page.tsx"],c="/admin/subscribers/page",p={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/admin/subscribers/page",pathname:"/admin/subscribers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},35303:()=>{},91082:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(19510),n=t(50650),o=t(72331),i=t(71159);let a=i.forwardRef(({className:e,...r},t)=>s.jsx("div",{className:"relative w-full overflow-auto",children:s.jsx("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",e),...r})}));a.displayName="Table";let d=i.forwardRef(({className:e,...r},t)=>s.jsx("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",e),...r}));d.displayName="TableHeader";let l=i.forwardRef(({className:e,...r},t)=>s.jsx("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...r}));l.displayName="TableBody",i.forwardRef(({className:e,...r},t)=>s.jsx("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...r})).displayName="TableFooter";let u=i.forwardRef(({className:e,...r},t)=>s.jsx("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...r}));u.displayName="TableRow";let c=i.forwardRef(({className:e,...r},t)=>s.jsx("th",{ref:t,className:(0,n.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r}));c.displayName="TableHead";let p=i.forwardRef(({className:e,...r},t)=>s.jsx("td",{ref:t,className:(0,n.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r}));p.displayName="TableCell",i.forwardRef(({className:e,...r},t)=>s.jsx("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...r})).displayName="TableCaption";var f=t(56881);async function m(){let e=await o._.preSaleSubscription.findMany({orderBy:{subscribedAt:"desc"},include:{user:!0}}),r=e.map(e=>e.referralCode).filter(e=>!!e),t=new Map((r.length>0?await o._.affiliateProfile.findMany({where:{code:{in:r}},include:{user:!0}}):[]).map(e=>[e.code,e.user.name||e.user.email||"Unknown"]));return e.map(e=>({...e,referrerName:e.referralCode?t.get(e.referralCode)||"Unknown":null}))}async function b(){let e=await m();return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Presale Subscribers"}),s.jsx("p",{className:"text-muted-foreground",children:"Manage and view all presale subscribers."})]}),s.jsx("div",{className:"rounded-md border",children:(0,s.jsxs)(a,{children:[s.jsx(d,{children:(0,s.jsxs)(u,{children:[s.jsx(c,{children:"Email"}),s.jsx(c,{children:"Status"}),s.jsx(c,{children:"Referral Code"}),s.jsx(c,{children:"Referrer"}),s.jsx(c,{children:"Subscribed At"}),s.jsx(c,{children:"Converted to User"}),s.jsx(c,{children:"IP Address"})]})}),s.jsx(l,{children:e.map(e=>(0,s.jsxs)(u,{children:[s.jsx(p,{children:e.email}),s.jsx(p,{children:s.jsx(f.C,{variant:"CONFIRMED"===e.status?"success":"UNSUBSCRIBED"===e.status?"destructive":"default",children:e.status})}),s.jsx(p,{children:e.referralCode||"-"}),s.jsx(p,{children:e.referrerName||"-"}),s.jsx(p,{children:n.CN.withTimezone(e.subscribedAt)}),s.jsx(p,{children:s.jsx(f.C,{variant:e.convertedToUser?"success":"secondary",children:e.convertedToUser?"Yes":"No"})}),s.jsx(p,{children:e.ipAddress||"-"})]},e.id))})]})})]})}},56881:(e,r,t)=>{"use strict";let s,n;t.d(r,{C:()=>c});var o=t(19510);t(71159);var i=t(55761);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,d=i.W;var l=t(50650);let u=(s="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",n={variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}},e=>{var r;if((null==n?void 0:n.variants)==null)return d(s,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:t,defaultVariants:o}=n,i=Object.keys(t).map(r=>{let s=null==e?void 0:e[r],n=null==o?void 0:o[r];if(null===s)return null;let i=a(s)||a(n);return t[r][i]}),l=e&&Object.entries(e).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return d(s,i,null==n?void 0:null===(r=n.compoundVariants)||void 0===r?void 0:r.reduce((e,r)=>{let{class:t,className:s,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...l}[r]):({...o,...l})[r]===t})?[...e,t,s]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function c({className:e,variant:r,...t}){return o.jsx("div",{className:(0,l.cn)(u({variant:r}),e),...t})}},58585:(e,r,t)=>{"use strict";var s=t(61085);t.o(s,"notFound")&&t.d(r,{notFound:function(){return s.notFound}}),t.o(s,"redirect")&&t.d(r,{redirect:function(){return s.redirect}})},61085:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return s.RedirectType},notFound:function(){return n.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=t(83953),n=t(16399);class o extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new o}delete(){throw new o}set(){throw new o}sort(){throw new o}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},16399:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{isNotFoundError:function(){return n},notFound:function(){return s}});let t="NEXT_NOT_FOUND";function s(){let e=Error(t);throw e.digest=t,e}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===t}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8586:(e,r)=>{"use strict";var t;Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"RedirectStatusCode",{enumerable:!0,get:function(){return t}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(t||(t={})),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},83953:(e,r,t)=>{"use strict";var s;Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{RedirectType:function(){return s},getRedirectError:function(){return d},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return c},permanentRedirect:function(){return u},redirect:function(){return l}});let n=t(54580),o=t(72934),i=t(8586),a="NEXT_REDIRECT";function d(e,r,t){void 0===t&&(t=i.RedirectStatusCode.TemporaryRedirect);let s=Error(a);s.digest=a+";"+r+";"+e+";"+t+";";let o=n.requestAsyncStorage.getStore();return o&&(s.mutableCookies=o.mutableCookies),s}function l(e,r){void 0===r&&(r="replace");let t=o.actionAsyncStorage.getStore();throw d(e,r,(null==t?void 0:t.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function u(e,r){void 0===r&&(r="replace");let t=o.actionAsyncStorage.getStore();throw d(e,r,(null==t?void 0:t.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function c(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[r,t,s,n]=e.digest.split(";",4),o=Number(n);return r===a&&("replace"===t||"push"===t)&&"string"==typeof s&&!isNaN(o)&&o in i.RedirectStatusCode}function p(e){return c(e)?e.digest.split(";",3)[2]:null}function f(e){if(!c(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!c(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,4824,7123],()=>t(48649));module.exports=s})();