(()=>{var e={};e.id=799,e.ids=[799],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},42451:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),a(4192),a(89090),a(26083),a(35866);var o=a(23191),s=a(88716),r=a(37922),n=a.n(r),i=a(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c=["",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4192)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\faq\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\faq\\page.tsx"],u="/faq/page",p={require:a,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/faq/page",pathname:"/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35303:()=>{},4192:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var o=a(19510);function s(){return o.jsx("div",{className:"py-16 bg-white",children:o.jsx("div",{className:"container mx-auto px-4",children:(0,o.jsxs)("div",{className:"max-w-3xl mx-auto",children:[o.jsx("h1",{className:"text-4xl font-bold heading-gradient mb-8",children:"Frequently Asked Questions"}),o.jsx("div",{className:"space-y-8",children:[{question:"What is Yolloo eSIM?",answer:"Yolloo eSIM is a digital SIM card service that allows you to connect to mobile networks without a physical SIM card. It's perfect for travelers and anyone who needs flexible mobile data connectivity."},{question:"Which devices are compatible with eSIM?",answer:"Most modern smartphones support eSIM, including recent iPhone models (iPhone XS and newer), Google Pixel devices (Pixel 3 and newer), and many Samsung Galaxy devices. Check your device settings or manufacturer's website for eSIM compatibility."},{question:"How do I activate my eSIM?",answer:"After purchase, you'll receive a QR code. On your device, go to Settings > Cellular/Mobile Data > Add eSIM/Cellular Plan, then scan the QR code. Follow the on-screen instructions to complete activation."},{question:"Can I use multiple eSIMs?",answer:"Yes, most eSIM-compatible devices can store multiple eSIM profiles, though usually only one can be active at a time alongside a physical SIM card."},{question:"What happens if I change phones?",answer:"You'll need to deactivate your eSIM on your old device and reactivate it on your new device using the original QR code. Contact support if you need help with this process."},{question:"Do you offer refunds?",answer:"Due to the digital nature of our service, we generally don't offer refunds once the eSIM has been activated. Please ensure your device is compatible before purchasing."},{question:"How long are your eSIM plans valid?",answer:"Plan validity varies by package. We offer plans ranging from 7 days to 30 days. The validity period starts from the time of activation, not purchase."},{question:"What countries do you cover?",answer:"We offer coverage in multiple countries worldwide. You can check specific country coverage and pricing on our plans page before making a purchase."},{question:"Can I top up my data plan?",answer:"Yes, you can purchase additional data through your account dashboard if you need more data during your plan period."},{question:"What speeds can I expect?",answer:"Data speeds vary by location and network conditions. We provide 4G/LTE connectivity in most locations, with 5G available in select areas where supported."}].map((e,t)=>(0,o.jsxs)("div",{className:"border-b border-gray-200 pb-8 last:border-b-0",children:[o.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:e.question}),o.jsx("p",{className:"text-gray-600",children:e.answer})]},t))}),(0,o.jsxs)("div",{className:"mt-12 p-6 bg-blue-50 rounded-lg",children:[o.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Still Have Questions?"}),o.jsx("p",{className:"text-gray-600 mb-4",children:"If you couldn't find the answer you were looking for, please don't hesitate to contact our support team."}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("p",{className:"text-gray-600",children:["Email: ",o.jsx("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"<EMAIL>"})]}),(0,o.jsxs)("p",{className:"text-gray-600",children:["Visit our ",o.jsx("a",{href:"/help",className:"text-blue-600 hover:underline",children:"Help Center"})," for more detailed information."]})]})]})]})})})}a(71159)},57481:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var o=a(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>a(42451));module.exports=o})();