/// <reference types="cookie-parser" />
import { Request, Response } from 'express';
import { WebCartsService } from './web-carts.service';
export declare class WebCartsController {
    private readonly webCartsService;
    constructor(webCartsService: WebCartsService);
    getCart(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    addToCart(addToCartDto: any, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    updateCartItem(cartItemId: string, updateCartItemDto: any, res: Response): Promise<Response<any, Record<string, any>>>;
    removeFromCart(cartItemId: string, res: Response): Promise<Response<any, Record<string, any>>>;
}
