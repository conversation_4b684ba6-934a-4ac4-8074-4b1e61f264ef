import { Response } from 'express';
import { WebCartsService } from './web-carts.service';
export declare class WebCartsController {
    private readonly webCartsService;
    constructor(webCartsService: WebCartsService);
    getCart(res: Response): Promise<Response<any, Record<string, any>>>;
    addToCart(addToCartDto: any, res: Response): Promise<Response<any, Record<string, any>>>;
    updateCartItem(cartItemId: string, updateCartItemDto: any, res: Response): Promise<Response<any, Record<string, any>>>;
    removeFromCart(cartItemId: string, res: Response): Promise<Response<any, Record<string, any>>>;
}
