"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebProductsModule = void 0;
const common_1 = require("@nestjs/common");
const web_products_controller_1 = require("./web-products.controller");
const web_products_service_1 = require("./web-products.service");
const prisma_service_1 = require("../../prisma.service");
let WebProductsModule = class WebProductsModule {
};
WebProductsModule = __decorate([
    (0, common_1.Module)({
        controllers: [web_products_controller_1.WebProductsController],
        providers: [web_products_service_1.WebProductsService, prisma_service_1.PrismaService],
        exports: [web_products_service_1.WebProductsService],
    })
], WebProductsModule);
exports.WebProductsModule = WebProductsModule;
//# sourceMappingURL=web-products.module.js.map