(()=>{var e={};e.id=9455,e.ids=[9455],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},44472:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>l}),r(99309),r(85460),r(89090),r(26083),r(35866);var s=r(23191),o=r(88716),a=r(37922),n=r.n(a),i=r(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l=["",{children:["admin",{children:["commissions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99309)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\commissions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\commissions\\page.tsx"],u="/admin/commissions/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/admin/commissions/page",pathname:"/admin/commissions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},65542:(e,t,r)=>{Promise.resolve().then(r.bind(r,92505))},92505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(10326),o=r(17577),a=r(35047),n=r(567),i=r(90772),d=r(54432),l=r(31048),c=r(15940),u=r(34474),p=r(57372),f=r(85999),m=r(77863);let x={PENDING:{label:"Pending",variant:"warning"},APPROVED:{label:"Approved",variant:"success"},REJECTED:{label:"Rejected",variant:"destructive"},PAID:{label:"Paid",variant:"default"}};function h(){(0,a.useRouter)();let[e,t]=(0,o.useState)(!1),[r,h]=(0,o.useState)([]),[b,g]=(0,o.useState)("");async function y(){t(!0);try{let e=await fetch("/api/admin/commissions");if(!e.ok)throw Error("Failed to fetch commissions");let t=await e.json();h(t)}catch(e){console.error(e),f.A.error("Failed to load commissions")}finally{t(!1)}}async function j(e,t){try{if(!(await fetch(`/api/admin/commissions/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t})})).ok)throw Error("Failed to update commission status");f.A.success("Commission status updated successfully"),y()}catch(e){console.error(e),f.A.error("Failed to update commission status")}}let v=r.filter(e=>e.userName.toLowerCase().includes(b.toLowerCase())||e.id.toLowerCase().includes(b.toLowerCase()));return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Commission Management"}),(0,s.jsxs)(i.Button,{onClick:y,disabled:e,children:[e&&s.jsx(p.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Refresh"]})]}),s.jsx("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx(l._,{htmlFor:"search",children:"Search"}),s.jsx(d.I,{id:"search",placeholder:"Search by user name or commission ID...",value:b,onChange:e=>g(e.target.value)})]})}),s.jsx("div",{className:"rounded-md border",children:(0,s.jsxs)(c.iA,{children:[s.jsx(c.xD,{children:(0,s.jsxs)(c.SC,{children:[s.jsx(c.ss,{children:"ID"}),s.jsx(c.ss,{children:"User"}),s.jsx(c.ss,{children:"Amount"}),s.jsx(c.ss,{children:"Status"}),s.jsx(c.ss,{children:"Created At"}),s.jsx(c.ss,{children:"Description"}),s.jsx(c.ss,{children:"Actions"})]})}),(0,s.jsxs)(c.RM,{children:[v.map(e=>(0,s.jsxs)(c.SC,{children:[s.jsx(c.pj,{className:"font-mono",children:e.id}),s.jsx(c.pj,{children:e.userName}),(0,s.jsxs)(c.pj,{children:["$",e.amount.toFixed(2)]}),s.jsx(c.pj,{children:s.jsx(n.C,{variant:x[e.status]?.variant||"secondary",children:x[e.status]?.label||e.status})}),s.jsx(c.pj,{children:m.CN.withTimezone(e.createdAt)}),s.jsx(c.pj,{children:e.description}),s.jsx(c.pj,{children:(0,s.jsxs)(u.Ph,{defaultValue:e.status,onValueChange:t=>j(e.id,t),children:[s.jsx(u.i4,{className:"w-[130px]",children:s.jsx(u.ki,{})}),s.jsx(u.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:Object.entries(x).map(([e,{label:t}])=>s.jsx(u.Ql,{value:e,children:t},e))})]})})]},e.id)),0===v.length&&s.jsx(c.SC,{children:s.jsx(c.pj,{colSpan:7,className:"text-center",children:"No commissions found"})})]})]})})]})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var s=r(10326);r(17577);var o=r(79360),a=r(77863);let n=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return s.jsx("div",{className:(0,a.cn)(n({variant:t}),e),...r})}},31048:(e,t,r)=>{"use strict";r.d(t,{_:()=>l});var s=r(10326),o=r(17577),a=r(34478),n=r(79360),i=r(77863);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=o.forwardRef(({className:e,...t},r)=>s.jsx(a.f,{ref:r,className:(0,i.cn)(d(),e),...t}));l.displayName=a.f.displayName},34474:(e,t,r)=>{"use strict";r.d(t,{Bw:()=>p,Ph:()=>l,Ql:()=>f,i4:()=>u,ki:()=>c});var s=r(10326),o=r(17577),a=r(18792),n=r(941),i=r(32933),d=r(77863);let l=a.fC;a.ZA;let c=a.B4,u=o.forwardRef(({className:e,children:t,...r},o)=>(0,s.jsxs)(a.xz,{ref:o,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:[t,s.jsx(a.JO,{asChild:!0,children:s.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=a.xz.displayName;let p=o.forwardRef(({className:e,children:t,position:r="popper",...o},n)=>s.jsx(a.h_,{children:s.jsx(a.VY,{ref:n,className:(0,d.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...o,children:s.jsx(a.l_,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));p.displayName=a.VY.displayName,o.forwardRef(({className:e,...t},r)=>s.jsx(a.__,{ref:r,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=a.__.displayName;let f=o.forwardRef(({className:e,children:t,...r},o)=>(0,s.jsxs)(a.ck,{ref:o,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(a.wU,{children:s.jsx(i.Z,{className:"h-4 w-4"})})}),s.jsx(a.eT,{children:t})]}));f.displayName=a.ck.displayName,o.forwardRef(({className:e,...t},r)=>s.jsx(a.Z0,{ref:r,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=a.Z0.displayName},15940:(e,t,r)=>{"use strict";r.d(t,{RM:()=>d,SC:()=>l,iA:()=>n,pj:()=>u,ss:()=>c,xD:()=>i});var s=r(10326),o=r(17577),a=r(77863);let n=o.forwardRef(({className:e,...t},r)=>s.jsx("div",{className:"relative w-full overflow-auto",children:s.jsx("table",{ref:r,className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})}));n.displayName="Table";let i=o.forwardRef(({className:e,...t},r)=>s.jsx("thead",{ref:r,className:(0,a.cn)("[&_tr]:border-b",e),...t}));i.displayName="TableHeader";let d=o.forwardRef(({className:e,...t},r)=>s.jsx("tbody",{ref:r,className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t}));d.displayName="TableBody",o.forwardRef(({className:e,...t},r)=>s.jsx("tfoot",{ref:r,className:(0,a.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let l=o.forwardRef(({className:e,...t},r)=>s.jsx("tr",{ref:r,className:(0,a.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));l.displayName="TableRow";let c=o.forwardRef(({className:e,...t},r)=>s.jsx("th",{ref:r,className:(0,a.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));c.displayName="TableHead";let u=o.forwardRef(({className:e,...t},r)=>s.jsx("td",{ref:r,className:(0,a.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));u.displayName="TableCell",o.forwardRef(({className:e,...t},r)=>s.jsx("caption",{ref:r,className:(0,a.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},99309:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});var s=r(68570);let o=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\commissions\page.tsx`),{__esModule:a,$$typeof:n}=o;o.default;let i=(0,s.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\commissions\page.tsx#default`)},58585:(e,t,r)=>{"use strict";var s=r(61085);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"redirect")&&r.d(t,{redirect:function(){return s.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return n},RedirectType:function(){return s.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=r(83953),o=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class n extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return s}});let r="NEXT_NOT_FOUND";function s(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return s},getRedirectError:function(){return d},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return l}});let o=r(54580),a=r(72934),n=r(8586),i="NEXT_REDIRECT";function d(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let s=Error(i);s.digest=i+";"+t+";"+e+";"+r+";";let a=o.requestAsyncStorage.getStore();return a&&(s.mutableCookies=a.mutableCookies),s}function l(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw d(e,t,(null==r?void 0:r.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw d(e,t,(null==r?void 0:r.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,s,o]=e.digest.split(";",4),a=Number(o);return t===i&&("replace"===r||"push"===r)&&"string"==typeof s&&!isNaN(a)&&a in n.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var s=r(17577),o=r(45226),a=r(10326),n=s.forwardRef((e,t)=>(0,a.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,4824,7123],()=>r(44472));module.exports=s})();