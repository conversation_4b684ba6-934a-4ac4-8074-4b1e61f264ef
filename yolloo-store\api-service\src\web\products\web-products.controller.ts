import { Controller, Get, Post, Patch, Delete, Body, Param, Query, Res, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { WebProductsService } from './web-products.service';
import { Public } from '../../common/decorators/public.decorator';

/**
 * Web产品控制器
 * 处理原主应用的产品相关API
 * 路由: /api/web/products/*
 */
@Controller('api/web/products')
export class WebProductsController {
  constructor(private readonly webProductsService: WebProductsService) {}

  /**
   * 获取产品列表
   * GET /api/web/products
   */
  @Public()
  @Get()
  async getProducts(
    @Query() query: any,
    @Res() res: Response,
  ) {
    try {
      const products = await this.webProductsService.getProducts(query);
      return res.json({ products });
    } catch (error) {
      console.error('[WEB_PRODUCTS_GET]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch products',
      });
    }
  }

  /**
   * 获取单个产品详情
   * GET /api/web/products/:productId
   */
  @Public()
  @Get(':productId')
  async getProduct(
    @Param('productId') productId: string,
    @Res() res: Response,
  ) {
    try {
      const product = await this.webProductsService.getProductById(productId);

      if (!product) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: 'Product not found',
        });
      }

      return res.json(product);
    } catch (error) {
      console.error('[WEB_PRODUCT_GET]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal error',
      });
    }
  }

  /**
   * 获取产品国家列表
   * GET /api/web/products/countries
   */
  @Public()
  @Get('countries')
  async getProductCountries(@Res() res: Response) {
    try {
      const countries = await this.webProductsService.getProductCountries();
      return res.json(countries);
    } catch (error) {
      console.error('[WEB_PRODUCTS_COUNTRIES]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch countries',
      });
    }
  }

  /**
   * 批量获取产品
   * POST /api/web/products/batch
   */
  @Post('batch')
  async getProductsBatch(
    @Body() body: { productIds: string[] },
    @Res() res: Response,
  ) {
    try {
      const products = await this.webProductsService.getProductsBatch(body.productIds);
      return res.json({ products });
    } catch (error) {
      console.error('[WEB_PRODUCTS_BATCH]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch products',
      });
    }
  }

  /**
   * 根据产品代码获取产品
   * GET /api/web/products/by-code/:code
   */
  @Get('by-code/:code')
  async getProductByCode(
    @Param('code') code: string,
    @Res() res: Response,
  ) {
    try {
      const product = await this.webProductsService.getProductByCode(code);
      
      if (!product) {
        return res.status(HttpStatus.NOT_FOUND).json({
          error: 'Product not found',
        });
      }

      return res.json(product);
    } catch (error) {
      console.error('[WEB_PRODUCT_BY_CODE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Internal error',
      });
    }
  }

  /**
   * 获取产品外部数据
   * GET /api/web/products/:productId/external-data
   */
  @Get(':productId/external-data')
  async getProductExternalData(
    @Param('productId') productId: string,
    @Res() res: Response,
  ) {
    try {
      const externalData = await this.webProductsService.getProductExternalData(productId);
      return res.json(externalData);
    } catch (error) {
      console.error('[WEB_PRODUCT_EXTERNAL_DATA]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch external data',
      });
    }
  }

  /**
   * 获取产品卡片链接
   * GET /api/web/products/:productId/get-card-links
   */
  @Get(':productId/get-card-links')
  async getProductCardLinks(
    @Param('productId') productId: string,
    @Query() query: any,
    @Res() res: Response,
  ) {
    try {
      const cardLinks = await this.webProductsService.getProductCardLinks(productId, query);
      return res.json(cardLinks);
    } catch (error) {
      console.error('[WEB_PRODUCT_CARD_LINKS]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch card links',
      });
    }
  }

  /**
   * 获取分页产品列表
   * GET /api/web/products/paginated
   */
  @Get('paginated')
  async getPaginatedProducts(
    @Query() query: any,
    @Res() res: Response,
  ) {
    try {
      const result = await this.webProductsService.getPaginatedProducts(query);
      return res.json(result);
    } catch (error) {
      console.error('[WEB_PRODUCTS_PAGINATED]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch paginated products',
      });
    }
  }

  /**
   * 获取增强产品数据
   * GET /api/web/products/enhanced
   */
  @Get('enhanced')
  async getEnhancedProducts(
    @Query() query: any,
    @Res() res: Response,
  ) {
    try {
      const products = await this.webProductsService.getEnhancedProducts(query);
      return res.json(products);
    } catch (error) {
      console.error('[WEB_PRODUCTS_ENHANCED]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch enhanced products',
      });
    }
  }

  /**
   * 缓存优先获取产品
   * GET /api/web/products/cache-first
   */
  @Get('cache-first')
  async getCacheFirstProducts(
    @Query() query: any,
    @Res() res: Response,
  ) {
    try {
      const products = await this.webProductsService.getCacheFirstProducts(query);
      return res.json(products);
    } catch (error) {
      console.error('[WEB_PRODUCTS_CACHE_FIRST]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch products',
      });
    }
  }
}
