"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const path_1 = require("path");
const cookieParser = require("cookie-parser");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.setGlobalPrefix('api/mobile');
    app.use(cookieParser());
    app.useStaticAssets((0, path_1.join)(__dirname, '..', 'public'), {
        prefix: '/api/mobile/static/',
    });
    app.enableCors({
        origin: [
            'http://localhost:3000',
            'http://localhost:8000',
            'https://esim.yolloo.com',
            'https://yolloo.com',
        ],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    const port = process.env.MOBILE_API_PORT || process.env.PORT || 4000;
    await app.listen(port, '0.0.0.0');
    console.log(`Mobile API is running on port ${port}, URL: ${await app.getUrl()}`);
}
bootstrap();
//# sourceMappingURL=main.js.map