"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const path_1 = require("path");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.setGlobalPrefix('api/mobile');
    app.useStaticAssets((0, path_1.join)(__dirname, '..', 'public'), {
        prefix: '/api/mobile/static/',
    });
    app.enableCors();
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    const port = process.env.MOBILE_API_PORT || process.env.PORT || 4000;
    await app.listen(port, '0.0.0.0');
    console.log(`Mobile API is running on port ${port}, URL: ${await app.getUrl()}`);
}
bootstrap();
//# sourceMappingURL=main.js.map