(()=>{var e={};e.id=590,e.ids=[590],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},62126:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c}),t(1365),t(89090),t(26083),t(35866);var a=t(23191),o=t(88716),s=t(37922),n=t.n(s),i=t(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c=["",{children:["auth",{children:["error",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1365)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\error\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\error\\page.tsx"],u="/auth/error/page",p={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/auth/error/page",pathname:"/auth/error",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53863:(e,r,t)=>{Promise.resolve().then(t.bind(t,45320))},45320:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var a=t(10326),o=t(35047),s=t(90772),n=t(90434);function i(){let e=(0,o.useSearchParams)().get("error"),r="An error occurred during authentication";return"AccessDenied"===e?r="Access denied. You do not have permission to access this resource.":"Configuration"===e?r="There is a problem with the server configuration.":"Verification"===e?r="The verification token has expired or has already been used.":"OAuthSignin"===e?r="Error occurred during sign in. Please try again.":"OAuthCallback"===e?r="Error occurred during OAuth callback. Please try again.":"OAuthCreateAccount"===e?r="Could not create OAuth provider account.":"EmailCreateAccount"===e?r="Could not create email provider account.":"Callback"===e?r="Error occurred during callback processing.":"EmailSignin"===e?r="The e-mail could not be sent.":"CredentialsSignin"===e&&(r="Invalid credentials. Please check your email and password."),a.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen py-2",children:(0,a.jsxs)("div",{className:"p-8 bg-white rounded-lg shadow-md max-w-md w-full space-y-4",children:[a.jsx("h1",{className:"text-2xl font-bold text-center text-red-600",children:"Authentication Error"}),a.jsx("p",{className:"text-center text-gray-600",children:r}),a.jsx("div",{className:"flex justify-center pt-4",children:a.jsx(s.Button,{asChild:!0,children:a.jsx(n.default,{href:"/auth/signin",children:"Try Again"})})})]})})}},1365:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>s,default:()=>i});var a=t(68570);let o=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\error\page.tsx`),{__esModule:s,$$typeof:n}=o;o.default;let i=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\error\page.tsx#default`)},57481:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var a=t(66621);let o=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>t(62126));module.exports=a})();