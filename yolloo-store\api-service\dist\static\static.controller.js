"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StaticController = void 0;
const common_1 = require("@nestjs/common");
const path = require("path");
const fs = require("fs");
let StaticController = class StaticController {
    staticDir = path.join(process.cwd(), 'static');
    constructor() {
        this.ensureStaticDirectories();
    }
    ensureStaticDirectories() {
        const dirs = [
            'static',
            'static/images',
            'static/images/defaults',
            'static/images/products',
            'static/images/categories',
            'static/images/rewards',
        ];
        dirs.forEach(dir => {
            const dirPath = path.join(process.cwd(), dir);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }
        });
        this.createDefaultPlaceholders();
    }
    createDefaultPlaceholders() {
        const placeholders = [
            'defaults/product-placeholder.jpg',
            'defaults/travel-package-placeholder.jpg',
            'defaults/author-avatar.jpg',
            'defaults/user-avatar.jpg',
        ];
        placeholders.forEach(placeholder => {
            const filePath = path.join(this.staticDir, 'images', placeholder);
            if (!fs.existsSync(filePath)) {
                const svgContent = this.generatePlaceholderSVG(placeholder);
                fs.writeFileSync(filePath.replace('.jpg', '.svg'), svgContent);
            }
        });
    }
    generatePlaceholderSVG(type) {
        const isAvatar = type.includes('avatar');
        const width = isAvatar ? 100 : 300;
        const height = isAvatar ? 100 : 200;
        return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="#999" text-anchor="middle" dy=".3em">
    ${isAvatar ? 'Avatar' : 'Image'}
  </text>
</svg>`;
    }
    async serveDefaultImage(filename, res) {
        return this.serveStaticFile('defaults', filename, res);
    }
    async serveProductImage(filename, res) {
        return this.serveStaticFile('products', filename, res);
    }
    async serveCategoryImage(filename, res) {
        return this.serveStaticFile('categories', filename, res);
    }
    async serveRewardImage(filename, res) {
        return this.serveStaticFile('rewards', filename, res);
    }
    async serveStaticFile(category, filename, res) {
        try {
            if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
                throw new common_1.NotFoundException('Invalid filename');
            }
            const filePath = path.join(this.staticDir, 'images', category, filename);
            if (!fs.existsSync(filePath) && filename.endsWith('.jpg')) {
                const svgPath = filePath.replace('.jpg', '.svg');
                if (fs.existsSync(svgPath)) {
                    res.setHeader('Content-Type', 'image/svg+xml');
                    res.setHeader('Cache-Control', 'public, max-age=86400');
                    const fileStream = fs.createReadStream(svgPath);
                    return fileStream.pipe(res);
                }
            }
            if (!fs.existsSync(filePath)) {
                throw new common_1.NotFoundException('Image not found');
            }
            const ext = path.extname(filename).toLowerCase();
            const mimeTypes = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp',
                '.svg': 'image/svg+xml',
            };
            const mimeType = mimeTypes[ext] || 'application/octet-stream';
            res.setHeader('Content-Type', mimeType);
            res.setHeader('Cache-Control', 'public, max-age=86400');
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        }
        catch (error) {
            throw new common_1.NotFoundException('Image not found');
        }
    }
};
__decorate([
    (0, common_1.Get)('defaults/:filename'),
    __param(0, (0, common_1.Param)('filename')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StaticController.prototype, "serveDefaultImage", null);
__decorate([
    (0, common_1.Get)('products/:filename'),
    __param(0, (0, common_1.Param)('filename')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StaticController.prototype, "serveProductImage", null);
__decorate([
    (0, common_1.Get)('categories/:filename'),
    __param(0, (0, common_1.Param)('filename')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StaticController.prototype, "serveCategoryImage", null);
__decorate([
    (0, common_1.Get)('rewards/:filename'),
    __param(0, (0, common_1.Param)('filename')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StaticController.prototype, "serveRewardImage", null);
StaticController = __decorate([
    (0, common_1.Controller)('images'),
    __metadata("design:paramtypes", [])
], StaticController);
exports.StaticController = StaticController;
//# sourceMappingURL=static.controller.js.map