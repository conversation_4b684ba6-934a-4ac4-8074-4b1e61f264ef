"use strict";(()=>{var e={};e.id=8491,e.ids=[8491],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},20105:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>C,patchFetch:()=>S,requestAsyncStorage:()=>_,routeModule:()=>g,serverHooks:()=>T,staticGenerationAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{POST:()=>h,dynamic:()=>q,fetchCache:()=>x,revalidate:()=>v});var o=r(49303),s=r(88716),n=r(60670),i=r(87070),c=r(75571),p=r(72331),d=r(90455),u=r(43624),l=r(6570),y=r(89880);let m={address:process.env.ODOO_QR_ADDRESS||"",channelId:process.env.ODOO_QR_CHANNEL_ID||"",channelLanguage:process.env.ODOO_QR_CHANNEL_LANGUAGE||"en_US",authSecret:process.env.ODOO_QR_AUTH_SECRET||"",signMethod:process.env.ODOO_QR_SIGN_METHOD||"md5"},q="force-dynamic",x="force-no-store",v=0;async function h(e){try{let t=await (0,c.getServerSession)(d.L);if(!t||"ADMIN"!==t.user.role)return new i.NextResponse("Unauthorized",{status:401});let r={};try{r=await e.json()}catch(e){}let{productTypes:a,skipEmptyVariantsAndZeroPrice:o=!0,allProductTypes:s}=r;s&&Array.isArray(s)&&s.length>0&&console.log(`[QR_SYNC] Using custom product types: ${s.join(", ")}`);let n=await p._.category.findFirst({where:{name:"qr_code"}});n||(n=await p._.category.create({data:{name:"qr_code",description:"Category for QR code products"}}));let q=new l.z(m),x=[],v={totalProcessed:0,totalSkipped:0,totalUpdated:0,totalCreated:0,totalVariants:0,byType:{}};for(let e of a&&a.length>0?a:y.Pm){v.byType[e]={processed:0,skipped:0,updated:0,created:0,variants:0};try{let t=await q.pullProducts(e);if(!t?.result?.data)continue;for(let r of t.result.data)try{v.totalProcessed++,v.byType[e].processed++;let t={...(0,u.X)(r),category:"qr_code"};if(v.totalVariants+=t.variants.length,v.byType[e].variants+=t.variants.length,o&&0===t.variants.length&&0===t.price){console.log(`[QR_SYNC] Skipping product ${t.sku} with 0 variants and 0 price`),v.totalSkipped++,v.byType[e].skipped++;continue}let a={name:t.name,description:t.description,websiteDescription:t.websiteDescription,price:t.price,images:[],stock:t.stock,status:t.isActive?y.eM.ACTIVE:y.eM.INACTIVE,off_shelve:t.off_shelve,requiredUID:t.requiredUID,mcc:t.mcc,dataSize:t.dataSize,planType:t.planType,country:t.country,countryCode:t.countryCode,odooLastSyncAt:new Date,specifications:{odooId:t.metadata.odooId,odooProductCode:t.metadata.odooProductCode,sourceType:"qr"},category:{connect:{id:n.id}}},s=await p._.product.findFirst({where:{sku:t.sku,specifications:{path:["sourceType"],equals:"qr"}}});await p._.product.upsert({where:{sku:t.sku},update:{...a,variants:{deleteMany:{},create:t.variants.map(e=>({price:Number(e.price),currency:e.currency||y.N_.currency,attributes:e.attributes,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType}))},parameters:{deleteMany:{},create:t.parameters.filter(e=>"mcc"!==e.code).map(e=>({code:e.code,name:e.name,value:e.value}))}},create:{...a,sku:t.sku,variants:{create:t.variants.map(e=>({price:Number(e.price),currency:e.currency||y.N_.currency,attributes:e.attributes,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType}))},parameters:{create:t.parameters.filter(e=>"mcc"!==e.code).map(e=>({code:e.code,name:e.name,value:e.value}))}},include:{variants:!0,parameters:!0}}),s?(v.totalUpdated++,v.byType[e].updated++):(v.totalCreated++,v.byType[e].created++),x.push(t)}catch(t){v.totalSkipped++,v.byType[e].skipped++}}catch(e){}}return i.NextResponse.json({message:"QR Odoo products sync completed",stats:v,count:x.length})}catch(e){return console.error("[QR_PRODUCTS_SYNC]",e),new i.NextResponse("Internal error",{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/products/sync-qr-products/route",pathname:"/api/admin/products/sync-qr-products",filename:"route",bundlePath:"app/api/admin/products/sync-qr-products/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\products\\sync-qr-products\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:_,staticGenerationAsyncStorage:f,serverHooks:T}=g,C="/api/admin/products/sync-qr-products/route";function S(){return(0,n.patchFetch)({serverHooks:T,staticGenerationAsyncStorage:f})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,9092,5972,2197,2023,7005,9712,6257],()=>r(20105));module.exports=a})();