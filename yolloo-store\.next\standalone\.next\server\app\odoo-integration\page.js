(()=>{var e={};e.id=7273,e.ids=[7273],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},50103:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(53484),t(89090),t(26083),t(35866);var a=t(23191),r=t(88716),o=t(37922),n=t.n(o),l=t(95231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(s,i);let c=["",{children:["odoo-integration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53484)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\odoo-integration\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\odoo-integration\\page.tsx"],m="/odoo-integration/page",x={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/odoo-integration/page",pathname:"/odoo-integration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},35303:()=>{},53484:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(19510);function r(){return a.jsx("main",{className:"min-h-screen bg-white text-gray-900",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,a.jsxs)("div",{className:"max-w-5xl mx-auto",children:[a.jsx("h1",{className:"text-4xl font-bold mb-8 font-sans",children:"Odoo Integration Service"}),(0,a.jsxs)("div",{className:"mb-12",children:[a.jsx("h2",{className:"text-2xl font-semibold mb-4 font-sans",children:"Production APIs:"}),(0,a.jsxs)("ul",{className:"space-y-6",children:[(0,a.jsxs)("li",{children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"Products:"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/odoo/products"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Get product list"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"POST /api/odoo/products/price"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Get product prices"})]})]})]}),(0,a.jsxs)("li",{children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"Orders:"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"POST /api/odoo/orders"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Create new order"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"PUT /api/odoo/orders"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Update order status"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"POST /api/odoo/orders/status"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Query order status"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"POST /api/odoo/orders/qrcode"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Get order QR code"})]})]})]}),(0,a.jsxs)("li",{children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"Invoices:"}),a.jsx("ul",{className:"space-y-2",children:(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"POST /api/odoo/invoices"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Get invoice orders"})]})})]}),(0,a.jsxs)("li",{children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"Webhook:"}),a.jsx("ul",{className:"space-y-2",children:(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"POST /api/odoo/webhook"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Receive Odoo push data"})]})})]})]})]}),(0,a.jsxs)("div",{className:"mb-12",children:[a.jsx("h2",{className:"text-2xl font-semibold mb-4 font-sans",children:"Test APIs (Development Only):"}),a.jsx("div",{className:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-r",children:a.jsx("p",{className:"text-yellow-700",children:"These test APIs are for development and testing purposes only. They contain pre-configured test data and should not be used in production."})}),(0,a.jsxs)("ul",{className:"space-y-6",children:[(0,a.jsxs)("li",{children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"Environment Test:"}),a.jsx("ul",{className:"space-y-2",children:(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/env"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test environment variables"})]})})]}),(0,a.jsxs)("li",{children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"Feature Tests:"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/webhook"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test webhook with sample product data"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/flow"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test complete business flow (create order → status update → get QR)"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/order"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test order creation with sample data"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/price"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test product price query with sample product code"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/qrcode"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test QR code generation with sample order"})]}),(0,a.jsxs)("li",{className:"flex items-center",children:[a.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded font-mono",children:"GET /api/test/invoice"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"- Test invoice query with sample parameters"})]})]})]})]}),(0,a.jsxs)("div",{className:"mt-8 bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r",children:[a.jsx("h3",{className:"font-semibold mb-2 font-sans",children:"How to use test APIs:"}),(0,a.jsxs)("ol",{className:"space-y-2 text-blue-700 ml-4 list-decimal",children:[(0,a.jsxs)("li",{children:["Start with ",a.jsx("code",{className:"bg-blue-100 px-2 py-0.5 rounded font-mono",children:"/api/test/env"})," to verify your environment setup"]}),(0,a.jsxs)("li",{children:["Use ",a.jsx("code",{className:"bg-blue-100 px-2 py-0.5 rounded font-mono",children:"/api/test/flow"})," to test the complete business flow"]}),a.jsx("li",{children:"Individual feature tests can be used to debug specific functionality"}),a.jsx("li",{children:"All test APIs use GET method and return sample responses"}),a.jsx("li",{children:"No request body needed for test APIs - they use pre-configured test data"})]})]})]})]})})})}},57481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>t(50103));module.exports=a})();