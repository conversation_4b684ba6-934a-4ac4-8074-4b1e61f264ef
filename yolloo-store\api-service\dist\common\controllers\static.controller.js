"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StaticController = void 0;
const common_1 = require("@nestjs/common");
const path_1 = require("path");
const fs_1 = require("fs");
const public_decorator_1 = require("../decorators/public.decorator");
const utils_1 = require("../utils");
let StaticController = class StaticController {
    publicPath = (0, path_1.join)(process.cwd(), 'public');
    async getImage(type, subPath, filename, res) {
        const filePath = (0, path_1.join)(this.publicPath, type, subPath, filename);
        if (!(0, fs_1.existsSync)(filePath)) {
            throw new common_1.NotFoundException('Image not found');
        }
        res.set({
            'Cache-Control': 'public, max-age=31536000',
            'ETag': `"${filename}"`,
        });
        return res.sendFile(filePath);
    }
    async getNestedImage(type, subPath1, subPath2, filename, res) {
        const filePath = (0, path_1.join)(this.publicPath, type, subPath1, subPath2, filename);
        if (!(0, fs_1.existsSync)(filePath)) {
            throw new common_1.NotFoundException('Image not found');
        }
        res.set({
            'Cache-Control': 'public, max-age=31536000',
            'ETag': `"${filename}"`,
        });
        return res.sendFile(filePath);
    }
    checkHealth() {
        const imagesPath = (0, path_1.join)(this.publicPath, 'images');
        const exists = (0, fs_1.existsSync)(imagesPath);
        return {
            status: exists ? 'ok' : 'error',
            message: exists ? 'Static assets directory is accessible' : 'Static assets directory not found',
            path: imagesPath,
            timestamp: utils_1.DateFormatter.iso(new Date()),
        };
    }
};
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(':type/:subPath/:filename'),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, common_1.Param)('subPath')),
    __param(2, (0, common_1.Param)('filename')),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object]),
    __metadata("design:returntype", Promise)
], StaticController.prototype, "getImage", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(':type/:subPath1/:subPath2/:filename'),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, common_1.Param)('subPath1')),
    __param(2, (0, common_1.Param)('subPath2')),
    __param(3, (0, common_1.Param)('filename')),
    __param(4, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], StaticController.prototype, "getNestedImage", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StaticController.prototype, "checkHealth", null);
StaticController = __decorate([
    (0, common_1.Controller)('static')
], StaticController);
exports.StaticController = StaticController;
//# sourceMappingURL=static.controller.js.map