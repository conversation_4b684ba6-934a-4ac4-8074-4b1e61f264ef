"use strict";(()=>{var e={};e.id=5556,e.ids=[5556],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},32727:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>I,patchFetch:()=>S,requestAsyncStorage:()=>v,routeModule:()=>w,serverHooks:()=>E,staticGenerationAsyncStorage:()=>O});var a={};r.r(a),r.d(a,{POST:()=>y,dynamic:()=>g,fetchCache:()=>h,revalidate:()=>m});var o=r(49303),i=r(88716),s=r(60670),n=r(87070),d=r(75571),l=r(72331),c=r(90455),u=r(6570),p=r(89880),f=r(71615);let g="force-dynamic",h="force-no-store",m=0,_=(0,u.T)(p.tK);async function y(e){try{let t=await (0,d.getServerSession)(c.L);if(!t?.user?.id)return new n.NextResponse("Unauthorized",{status:401});let r=(0,f.cookies)(),a=r.get("referralCode")?.value;console.log(`[EXTERNAL_ESIM_ACTIVATION] Referral code from cookie: ${a||"none"}`);let{cardNumber:o,lpaString:i}=await e.json();if(!o||!i)return new n.NextResponse("Missing required fields",{status:400});if(!await l._.yollooCard.findFirst({where:{number:o,userId:t.user.id}}))return new n.NextResponse("Card not found or unauthorized",{status:404});let s=await l._.user.findUnique({where:{id:t.user.id}});if(!s||!s.email)return new n.NextResponse("User not found",{status:404});let u=await l._.product.findFirst({where:{category:{name:"external_data"},status:"ACTIVE",off_shelve:!1},include:{variants:{take:1,select:{id:!0,variantCode:!0,price:!0,currency:!0,duration:!0,durationType:!0}}}});if(!u)return new n.NextResponse("External data product not found",{status:404});let p=u.variants[0],g=p?.price??u.price,h=Number(g),m=await l._.address.findFirst({where:{userId:null,name:"System Default Address"}}),y=u.specifications.odooProductCode,w=u.name;p?.duration&&p?.durationType&&(w+=` ${p.duration} ${p.durationType}`);let v=await l._.order.create({data:{userId:t.user.id,addressId:m.id,status:"PAID",total:h,...a&&{referralCode:a},shippingAddressSnapshot:{name:m.name,phone:m.phone,address1:m.address1,address2:m.address2,city:m.city,state:m.state,postalCode:m.postalCode,country:m.country},items:{create:{productCode:u.sku,variantCode:p?.variantCode,variantText:w,quantity:1,price:h,uid:o,lpaString:i}}},include:{items:!0}}),O=o?o.replace(/[^0-9,]/g,""):null,E=p?.variantCode||"default";if(await l._.odooOrderStatus.create({data:{orderId:v.id,variantCode:E,status:"processing",description:"External eSIM activation initiated",isDigital:!0,deliveredQty:0,uid:O,lastCheckedAt:new Date}}),a){console.log(`Processing affiliate commission for order: ${v.id}, referral code: ${a}`);try{let e=await fetch("http://localhost:8000/api/affiliate/track",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:v.id,referralCode:a})});if(e.ok){let t=await e.json();console.log(`Commission processing result: ${JSON.stringify(t)}`)}else console.error(`Failed to process commission: ${e.statusText}`)}catch(e){console.error("Error processing commission:",e)}}let I={customer_order_ref:v.id,shipping_address:{name:m.name,country:m.country,city:m.city,zip:m.postalCode,address:`${m.address1} ${m.address2||""}`.trim(),phone:m.phone},payment_method:"prepaid",email:s.email,order_lines:[{customer_order_line_ref:v.items[0]?.id||`web-order-${v.id}`,product_code:p&&p.variantCode||y||"",product_uom_qty:1,card_uid:o,lpa_string:i}]};try{let e=await _.createOrder(I);console.log("[EXTERNAL_ESIM_ACTIVATION] Odoo create order response:",JSON.stringify(e,null,2));let t=e.result?.status||"unknown",r=e.result?.message||"No response message",a="ok"===t||"success"===t,s=a?"processing":"error",d=a?`Successful: ${r}`:`Failed: ${r}`,c=p?.variantCode||"default",u=o?o.replace(/[^0-9,]/g,""):null,f=await l._.odooOrderStatus.findFirst({where:{orderId:v.id,variantCode:c,uid:u}});return f?await l._.odooOrderStatus.update({where:{id:f.id},data:{status:s,description:d,lastCheckedAt:new Date}}):await l._.odooOrderStatus.create({data:{orderId:v.id,variantCode:c,uid:u,status:s,description:d,lastCheckedAt:new Date}}),a||(console.log(`[EXTERNAL_ESIM_ACTIVATION] Odoo returned error status, updating order ${v.id} status from PAID to PROCESSING`),await l._.order.update({where:{id:v.id},data:{status:"PROCESSING"}})),n.NextResponse.json({success:!0,orderId:v.id,message:"eSIM activation initiated successfully",status:e.result.status,data:{cardNumber:o,lpaString:i}})}catch(e){console.error("[EXTERNAL_ESIM_ACTIVATION] Odoo API error:",e);try{let t=p?.variantCode||"default",r=o?o.replace(/[^0-9,]/g,""):null,a=await l._.odooOrderStatus.findFirst({where:{orderId:v.id,variantCode:t,uid:r}}),i=`Error creating Odoo order: ${e instanceof Error?e.message:"Unknown error"}`;a?await l._.odooOrderStatus.update({where:{id:a.id},data:{status:"error",description:i,lastCheckedAt:new Date}}):await l._.odooOrderStatus.create({data:{orderId:v.id,variantCode:t,uid:r,status:"error",description:i,lastCheckedAt:new Date}}),console.log(`[EXTERNAL_ESIM_ACTIVATION] Odoo API call failed, updating order ${v.id} status from PAID to PROCESSING`),await l._.order.update({where:{id:v.id},data:{status:"PROCESSING"}})}catch(e){console.error("[EXTERNAL_ESIM_ACTIVATION] Failed to record Odoo error in database:",e)}return n.NextResponse.json({success:!0,orderId:v.id,message:"eSIM activation initiated successfully, but Odoo synchronization failed",status:"error",data:{cardNumber:o,lpaString:i}})}}catch(e){return console.error("[EXTERNAL_ESIM_ACTIVATION]",e),new n.NextResponse("Internal error",{status:500})}}let w=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/esims/external-activation/route",pathname:"/api/esims/external-activation",filename:"route",bundlePath:"app/api/esims/external-activation/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\esims\\external-activation\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:v,staticGenerationAsyncStorage:O,serverHooks:E}=w,I="/api/esims/external-activation/route";function S(){return(0,s.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:O})}},89880:(e,t,r)=>{r.d(t,{N_:()=>d,Pm:()=>o,eM:()=>s,gA:()=>i,rf:()=>n,tK:()=>a});let a={address:process.env.ODOO_ADDRESS||"",channelId:process.env.ODOO_CHANNEL_ID||"",channelLanguage:process.env.ODOO_CHANNEL_LANGUAGE||"en_US",authSecret:process.env.ODOO_AUTH_SECRET||"",signMethod:process.env.ODOO_SIGN_METHOD||"md5"},o=["esim","data","effective_date","external_data","other","esim-card"],i={defaultStart:0,defaultLength:500,maxLength:5e3},s={ACTIVE:"ACTIVE",INACTIVE:"INACTIVE"},n={defaultCategory:"default",defaultDescription:e=>`Category for ${e} products`},d={currency:"USD",stock:999}},6570:(e,t,r)=>{r.d(t,{T:()=>d,z:()=>n});var a=r(29712),o=r(6113),i=r(89880),s=r(72331);class n{constructor(e){this.config=e,this.client=a.Z.create({baseURL:"",headers:{"Content-Type":"application/json","X-Channel-Id":e.channelId,"X-Channel-Language":e.channelLanguage},timeout:3e5})}generateSignature(e){let t=`${this.config.authSecret}${e}${this.config.channelId}`.replace(/\s/g,"");return(0,o.createHash)("md5").update(t,"utf8").digest("hex").toLowerCase()}verifySignature(e,t){return!!t&&this.generateSignature(e)===t.toLowerCase()}async request(e,t,r){let o=JSON.stringify(r||{},null,0);console.log("[Odoo] req Body: ",o);let i=this.generateSignature(o),s=`${this.config.address}${t}`,n={Accept:"application/json","Content-Type":"application/json","X-Channel-Id":this.config.channelId,"X-Channel-Language":this.config.channelLanguage,"X-sign-Method":this.config.signMethod.toLowerCase(),"X-Sign-Value":i};try{return(await this.client.request({method:e,url:s,data:o,headers:n})).data}catch(e){if(a.Z.isAxiosError(e))throw console.error("Odoo API Error:",{status:e.response?.status,statusText:e.response?.statusText,data:e.response?.data,config:{url:e.config?.url,method:e.config?.method,headers:e.config?.headers,data:e.config?.data}}),Error(`Odoo API Error: ${e.response?.data?.error||e.message}`);throw console.error("Unexpected error:",e),e}}async pullProducts(e="esim"){let t=[],r=i.gA.defaultStart,a=!0,o=null,s=0;for(console.log(`[Odoo] Fetching products=${e}`);a&&r<i.gA.maxLength;){s++;let n={product_type:e,start:r,length:i.gA.defaultLength},d=await this.request("POST","/openapi/v3/get_proudct_list",n);if(o=d,"success"!==d.status&&"200"!==d.status&&(!d.result||"success"!==d.result.status&&"ok"!==d.result.status&&"操作成功"!==d.result.message))throw Error(`Failed to fetch products: ${d.result?.message||"Unknown error"}`);if(!d.result?.data||!Array.isArray(d.result.data))throw Error("No product data returned from API");let l=d.result.data;t=[...t,...l],l.length<i.gA.defaultLength&&(a=!1),r+=i.gA.defaultLength}if(console.log(`[Odoo] Fetched ${t.length} products of type=${e} in ${s} batches`),!o)throw Error("No response received from Odoo API");return{...o,result:{...o.result,data:t}}}async getProductPrices(e){let t={product_code:e?.product_code,start:e?.start||i.gA.defaultStart,length:e?.length||i.gA.defaultLength};return this.request("POST","/openapi/v3/get_proudct_price",t)}async createOrder(e){return this.request("POST","/openapi/v3/create_purchase_order",e)}async queryOrderStatus(e){let t={customer_order_ref:[e]};return console.log("Sending request to Odoo:",{url:"/openapi/v3/get_sale_order_status",data:t}),this.request("POST","/openapi/v3/get_sale_order_status",t)}async queryOrderStatusMultiple(e){let t={customer_order_ref:e};return console.log("Sending request to Odoo for multiple orders:",{url:"/openapi/v3/get_sale_order_status",data:t}),this.request("POST","/openapi/v3/get_sale_order_status",t)}async getInvoiceOrders(e){return this.request("POST","/openapi/v3/get_invoice_order_list",e)}async handleProductPush(e){console.log("Processing product push:",e);try{for(let t of e){let{product_code:e,name:r,description_sale:a,website_description:o,off_shelve:i,variants:n}=t,d=await s._.product.findFirst({where:{specifications:{path:["odooProductCode"],equals:e}}});if(d&&(await s._.product.update({where:{id:d.id},data:{name:r,description:a,websiteDescription:o,off_shelve:i??!1,...n?.[0]?.supply_price&&{price:n[0].supply_price}}}),n&&n.length>0))for(let e of n){if(e.variant_attributes?.some(e=>"card"===e.code)){console.log(`Skipping variant with card attribute: ${e.variant_code}`);continue}let t=e.variant_attributes?.reduce((e,t)=>(e[t.code]=t.value,e),{})||{},r=await s._.productVariant.findFirst({where:{AND:[{productId:d.id},{variantCode:e.variant_code}]}});r?await s._.productVariant.update({where:{id:r.id},data:{price:e.supply_price,currency:e.currency,attributes:t}}):await s._.productVariant.create({data:{price:e.supply_price,currency:e.currency,variantCode:e.variant_code,attributes:t,productId:d.id}})}}}catch(e){throw console.error("Error processing product push:",e),e}}async handleOrderStatusPush(e){console.log("Processing order status push:",e);try{for(let t of e){let{customer_order_ref:e,order_lines:r}=t;if(!r||0===r.length)continue;let a=r[0],o=[],i=null;a.data&&(Array.isArray(a.data)?(o=a.data.filter(e=>e&&e.uid).map(e=>e.uid)).length>0&&(i=o.join(",")):"object"==typeof a.data&&a.data.uid&&(i=a.data.uid)),console.log(`[ORDER_STATUS_PUSH] Extracted UIDs for order ${e}:`,o);let n=a.variant_code||"default",d=i?i.replace(/[^0-9,]/g,""):"";console.log(`[ORDER_STATUS_PUSH] Using variantCode: ${n}, formatted UID: ${d}`);let l=await s._.odooOrderStatus.findFirst({where:{orderId:e,variantCode:n,uid:d||null}});if(l?await s._.odooOrderStatus.update({where:{id:l.id},data:{status:a.status,description:a.description,productName:a.product_name,isDigital:a.is_digital||!1,deliveredQty:a.delivered_qty||0,trackingNumber:a.tracking_number,planState:a.data?.[0]?.plan_state,lastCheckedAt:new Date}}):await s._.odooOrderStatus.create({data:{orderId:e,variantCode:n,status:a.status,description:a.description,productName:a.product_name,isDigital:a.is_digital||!1,deliveredQty:a.delivered_qty||0,trackingNumber:a.tracking_number,planState:a.data?.[0]?.plan_state,uid:d||null,lastCheckedAt:new Date}}),"delivered"===a.status){await s._.order.update({where:{id:e},data:{status:"DELIVERED"}});let t=await s._.affiliateReferral.findFirst({where:{orderId:e,status:"PENDING"},include:{organizationCommission:!0}});t&&(console.log(`Updating affiliate referral status for order: ${e}`),await s._.affiliateReferral.update({where:{id:t.id},data:{status:"APPROVED"}}),t.organizationCommission&&(console.log(`Updating organization commission status for order: ${e}`),await s._.organizationCommission.update({where:{id:t.organizationCommissionId},data:{status:"APPROVED"}})))}}}catch(e){throw console.error("Error processing order status push:",e),e}}async getOrderQRCode(e){let t={customer_order_ref:Array.isArray(e)?e.join(","):e};return this.request("POST","/openapi/v3/get_sale_order_esim_qrcode",t)}}let d=e=>new n(e)},90455:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(7585),o=r(72331),i=r(77234),s=r(53797),n=r(42023),d=r.n(n),l=r(93475);let c={adapter:{...(0,a.N)(o._),getUser:async e=>{let t=await o._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await o._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await o._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,s.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await o._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await d().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,s.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,l.Ak)(e.email);if(!t||t!==e.code)return null;await (0,l.qc)(e.email);let r=await o._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await o._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await o._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:a,request:i}){try{if(r&&r.id){let t=i?.headers||new Headers,s=t.get("user-agent")||"",n=t.get("x-forwarded-for"),d=n?n.split(/, /)[0]:t.get("REMOTE_ADDR")||"",l="unknown";a?l=a.code&&!a.password?"email_code":"password":e&&(l=e.provider),await o._.userLoginHistory.create({data:{userId:r.id,ipAddress:d||null,userAgent:s||null,loginMethod:l,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,o=new URL(a).searchParams.get("callbackUrl");if(o){let e=decodeURIComponent(o);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(r.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(a);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return a}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:a}){if("update"===r&&a)return{...e,...a.user};let i=await o._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return i?{id:i.id,name:i.name,email:i.email,picture:i.image,role:i.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,t,r)=>{r.d(t,{_:()=>o});var a=r(53524);let o=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,t,r)=>{r.d(t,{AL:()=>n,Ak:()=>d,qc:()=>l,yz:()=>c});var a=r(62197),o=r.n(a);let i=null;function s(){if(!i){let e=process.env.REDIS_URL||"redis://localhost:6379";(i=new(o())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),i.on("connect",()=>{console.log("Successfully connected to Redis")})}return i}async function n(e,t,r=300){try{let a=s(),o=`verification_code:${e}`;return await a.setex(o,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function d(e){try{let t=s(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function l(e){try{let t=s(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let a=s(),o=`rate_limit:${e}`,i=await a.get(o),n=i?parseInt(i):0;if(n>=t)return!1;return 0===n?await a.setex(o,r,"1"):await a.incr(o),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var o=r(69955);Object.keys(o).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===o[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(void 0);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var n=o?Object.getOwnPropertyDescriptor(e,i):null;n&&(n.get||n.set)?Object.defineProperty(a,i,n):a[i]=e[i]}return a.default=e,r&&r.set(e,a),a}(r(45609));function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,9092,5972,2197,2023,7005,9712],()=>r(32727));module.exports=a})();