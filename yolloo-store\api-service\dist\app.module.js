"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const prisma_service_1 = require("./prisma.service");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const home_module_1 = require("./home/<USER>");
const products_module_1 = require("./products/products.module");
const cart_module_1 = require("./cart/cart.module");
const orders_module_1 = require("./orders/orders.module");
const cards_module_1 = require("./cards/cards.module");
const health_module_1 = require("./health/health.module");
const location_module_1 = require("./location/location.module");
const pages_module_1 = require("./pages/pages.module");
const number_retention_module_1 = require("./number-retention/number-retention.module");
const mobile_recharge_module_1 = require("./mobile-recharge/mobile-recharge.module");
const travel_packages_module_1 = require("./travel-packages/travel-packages.module");
const local_packages_module_1 = require("./local-packages/local-packages.module");
const data_boosters_module_1 = require("./data-boosters/data-boosters.module");
const user_packages_module_1 = require("./user-packages/user-packages.module");
const geography_module_1 = require("./geography/geography.module");
const articles_module_1 = require("./articles/articles.module");
const upload_module_1 = require("./upload/upload.module");
const static_module_1 = require("./static/static.module");
const web_module_1 = require("./web/web.module");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
const redis_module_1 = require("./redis/redis.module");
const context_middleware_1 = require("./common/middleware/context.middleware");
const static_controller_1 = require("./common/controllers/static.controller");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(context_middleware_1.ContextMiddleware).forRoutes('*');
    }
};
AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
            }),
            redis_module_1.RedisModule,
            axios_1.HttpModule,
            web_module_1.WebModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            home_module_1.HomeModule,
            products_module_1.ProductsModule,
            cart_module_1.CartModule,
            orders_module_1.OrdersModule,
            cards_module_1.CardsModule,
            health_module_1.HealthModule,
            location_module_1.LocationModule,
            pages_module_1.PagesModule,
            number_retention_module_1.NumberRetentionModule,
            mobile_recharge_module_1.MobileRechargeModule,
            travel_packages_module_1.TravelPackagesModule,
            local_packages_module_1.LocalPackagesModule,
            data_boosters_module_1.DataBoostersModule,
            user_packages_module_1.UserPackagesModule,
            geography_module_1.GeographyModule,
            articles_module_1.ArticlesModule,
            upload_module_1.UploadModule,
            static_module_1.StaticModule,
        ],
        controllers: [static_controller_1.StaticController],
        providers: [
            prisma_service_1.PrismaService,
            {
                provide: core_1.APP_FILTER,
                useClass: http_exception_filter_1.HttpExceptionFilter,
            },
        ],
        exports: [prisma_service_1.PrismaService],
    })
], AppModule);
exports.AppModule = AppModule;
//# sourceMappingURL=app.module.js.map