import { Module } from '@nestjs/common';
import { TravelPackagesService } from './travel-packages.service';
import { TravelPackagesController } from './travel-packages.controller';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';
import { RatingModule } from '../rating/rating.module';

@Module({
  imports: [AuthModule, RatingModule],
  controllers: [TravelPackagesController],
  providers: [TravelPackagesService, PrismaService],
})
export class TravelPackagesModule {}
