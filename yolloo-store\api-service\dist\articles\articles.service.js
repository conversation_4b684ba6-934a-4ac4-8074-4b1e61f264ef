"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ArticlesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ArticlesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const image_util_1 = require("../common/utils/image.util");
let ArticlesService = ArticlesService_1 = class ArticlesService {
    prisma;
    logger = new common_1.Logger(ArticlesService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getCategories(query, ctx) {
        const isZh = ctx.language.startsWith('zh');
        try {
            const articleCategories = await this.prisma.category.findMany({
                where: {
                    OR: [
                        { name: { contains: '文章', mode: 'insensitive' } },
                        { name: { contains: 'article', mode: 'insensitive' } },
                        { name: { contains: '攻略', mode: 'insensitive' } },
                        { name: { contains: 'guide', mode: 'insensitive' } },
                        { name: { contains: '教程', mode: 'insensitive' } },
                        { name: { contains: 'tutorial', mode: 'insensitive' } },
                    ],
                },
                include: {
                    products: {
                        where: {
                            status: 'ACTIVE',
                            off_shelve: false,
                        },
                        select: {
                            id: true,
                        },
                    },
                },
            });
            const formattedCategories = articleCategories.map(category => ({
                id: category.id,
                name: category.name,
                description: category.description || (isZh ? '相关文章和指南' : 'Related articles and guides'),
                icon: image_util_1.ImageUtil.getIconUrl(category.name.toLowerCase().replace(/\s+/g, '-')),
                articleCount: category.products.length,
            }));
            if (formattedCategories.length === 0) {
                return {
                    categories: [],
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            return {
                categories: formattedCategories,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching article categories:', error);
            return {
                categories: [],
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getArticles(query, ctx) {
        const isZh = ctx.language.startsWith('zh');
        const { category, page = 1, pageSize = 10, limit } = query;
        try {
            const whereConditions = {
                isPublished: true,
            };
            if (category) {
                whereConditions.category = category;
            }
            if (query.tag) {
                whereConditions.tags = {
                    has: query.tag,
                };
            }
            const total = await this.prisma.article.count({
                where: whereConditions,
            });
            if (total > 0) {
                const skip = (page - 1) * pageSize;
                const takeCount = limit && limit > 0 ? Math.min(limit, pageSize) : pageSize;
                const articles = await this.prisma.article.findMany({
                    where: whereConditions,
                    skip: limit && limit > 0 ? 0 : skip,
                    take: takeCount,
                    orderBy: {
                        publishDate: 'desc',
                    },
                });
                this.logger.log(`Found ${articles.length} articles from Article table`);
                const formattedArticles = articles.map(article => ({
                    id: article.id,
                    title: article.title,
                    summary: article.summary || article.content.substring(0, 200) + '...',
                    category: article.category,
                    imageUrl: article.imageUrl || image_util_1.ImageUtil.getBannerUrl('articles', article.id, isZh ? 'zh' : 'en'),
                    content: article.content,
                    author: {
                        id: 'author-' + article.id,
                        name: article.author,
                        avatar: '/images/defaults/author-avatar.jpg',
                    },
                    publishDate: article.publishDate.toISOString(),
                    readCount: article.readCount,
                    likeCount: article.likeCount,
                    tags: article.tags,
                }));
                return {
                    articles: formattedArticles,
                    pagination: {
                        page,
                        pageSize,
                        total: formattedArticles.length,
                        totalPages: Math.ceil(formattedArticles.length / pageSize),
                    },
                    filters: {
                        category,
                        country: query.country,
                        tag: query.tag,
                    },
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const productWhereConditions = {
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    { name: { contains: '攻略', mode: 'insensitive' } },
                    { name: { contains: 'guide', mode: 'insensitive' } },
                    { name: { contains: '教程', mode: 'insensitive' } },
                    { name: { contains: 'tutorial', mode: 'insensitive' } },
                    { name: { contains: '文章', mode: 'insensitive' } },
                    { name: { contains: 'article', mode: 'insensitive' } },
                    { description: { contains: '攻略', mode: 'insensitive' } },
                    { description: { contains: 'guide', mode: 'insensitive' } },
                    { description: { contains: '教程', mode: 'insensitive' } },
                    { description: { contains: 'tutorial', mode: 'insensitive' } },
                    { websiteDescription: { not: '' } },
                ],
            };
            if (category) {
                productWhereConditions.AND = productWhereConditions.AND || [];
                productWhereConditions.AND.push({
                    OR: [
                        { categoryId: category },
                        { category: { name: { contains: category, mode: 'insensitive' } } },
                    ],
                });
            }
            if (query.country) {
                productWhereConditions.AND = productWhereConditions.AND || [];
                productWhereConditions.AND.push({
                    OR: [
                        { country: { contains: query.country, mode: 'insensitive' } },
                        { countryCode: { contains: query.country, mode: 'insensitive' } },
                        { name: { contains: query.country, mode: 'insensitive' } },
                    ],
                });
            }
            const productTotal = await this.prisma.product.count({
                where: productWhereConditions,
            });
            if (productTotal === 0) {
                return {
                    articles: [],
                    pagination: {
                        total: 0,
                        page: page,
                        pageSize: pageSize,
                        hasMore: false,
                    },
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const skip = (page - 1) * pageSize;
            const takeCount = limit && limit > 0 ? Math.min(limit, pageSize) : pageSize;
            const products = await this.prisma.product.findMany({
                where: productWhereConditions,
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                            comment: true,
                            user: {
                                select: {
                                    name: true,
                                    image: true,
                                },
                            },
                        },
                        orderBy: {
                            createdAt: 'desc',
                        },
                        take: 3,
                    },
                },
                skip: limit && limit > 0 ? 0 : skip,
                take: takeCount,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const formattedArticles = products.map(product => this.formatProductAsArticle(product, ctx, isZh));
            let filteredArticles = formattedArticles;
            if (query.tag) {
                filteredArticles = formattedArticles.filter(article => article.tags.some(tag => tag.toLowerCase().includes(query.tag?.toLowerCase() || '')));
            }
            try {
                for (const article of filteredArticles.slice(0, 5)) {
                    await this.prisma.article.upsert({
                        where: { id: article.id },
                        update: {
                            readCount: { increment: 1 },
                        },
                        create: {
                            id: article.id,
                            title: article.title,
                            summary: article.summary,
                            content: article.content,
                            category: article.category,
                            imageUrl: article.imageUrl,
                            author: article.author.name,
                            publishDate: new Date(article.publishDate),
                            readCount: article.readCount,
                            likeCount: article.likeCount,
                            tags: article.tags,
                            isPublished: true,
                        },
                    });
                }
                this.logger.log(`Created/updated ${Math.min(filteredArticles.length, 5)} article records`);
            }
            catch (createError) {
                this.logger.warn(`Failed to create article records: ${createError.message}`);
            }
            return {
                articles: filteredArticles,
                pagination: {
                    page,
                    pageSize,
                    total: filteredArticles.length,
                    totalPages: Math.ceil(filteredArticles.length / pageSize),
                },
                filters: {
                    category,
                    country: query.country,
                    tag: query.tag,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching articles:', error);
            return {
                articles: [],
                pagination: {
                    total: 0,
                    page: query.page || 1,
                    pageSize: query.pageSize || 10,
                    hasMore: false,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getArticleById(id, ctx) {
        const isZh = ctx.language.startsWith('zh');
        try {
            const article = await this.prisma.article.findUnique({
                where: { id },
            });
            if (article && article.isPublished) {
                await this.prisma.article.update({
                    where: { id },
                    data: { readCount: { increment: 1 } },
                });
                const relatedArticles = await this.prisma.article.findMany({
                    where: {
                        category: article.category,
                        id: { not: id },
                        isPublished: true,
                    },
                    take: 3,
                    orderBy: {
                        publishDate: 'desc',
                    },
                });
                const formattedRelatedArticles = relatedArticles.map(relatedArticle => ({
                    id: relatedArticle.id,
                    title: relatedArticle.title,
                    summary: relatedArticle.summary || relatedArticle.content.substring(0, 200) + '...',
                    category: relatedArticle.category,
                    imageUrl: relatedArticle.imageUrl || image_util_1.ImageUtil.getBannerUrl('articles', relatedArticle.id, isZh ? 'zh' : 'en'),
                    content: relatedArticle.content,
                    author: {
                        id: 'author-' + relatedArticle.id,
                        name: relatedArticle.author,
                        avatar: '/images/defaults/author-avatar.jpg',
                    },
                    publishDate: relatedArticle.publishDate.toISOString(),
                    readCount: relatedArticle.readCount,
                    likeCount: relatedArticle.likeCount,
                    tags: relatedArticle.tags,
                }));
                this.logger.log(`Found article from Article table: ${article.title}`);
                return {
                    article: {
                        id: article.id,
                        title: article.title,
                        summary: article.summary,
                        category: article.category,
                        imageUrl: article.imageUrl || image_util_1.ImageUtil.getBannerUrl('articles', article.id, isZh ? 'zh' : 'en'),
                        content: article.content,
                        fullContent: article.content,
                        author: {
                            id: 'author-' + article.id,
                            name: article.author,
                            avatar: '/images/defaults/author-avatar.jpg',
                        },
                        publishDate: article.publishDate.toISOString(),
                        readCount: article.readCount,
                        likeCount: article.likeCount,
                        tags: article.tags,
                    },
                    relatedArticles: formattedRelatedArticles,
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const product = await this.prisma.product.findUnique({
                where: { id },
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                            comment: true,
                            createdAt: true,
                            user: {
                                select: {
                                    name: true,
                                    image: true,
                                },
                            },
                        },
                        orderBy: {
                            createdAt: 'desc',
                        },
                        take: 10,
                    },
                },
            });
            if (!product) {
                return {
                    error: 'Article not found',
                    articleId: id,
                };
            }
            const formattedArticle = this.formatProductAsDetailedArticle(product, ctx, isZh);
            const relatedProducts = await this.prisma.product.findMany({
                where: {
                    categoryId: product.categoryId,
                    id: { not: id },
                    status: 'ACTIVE',
                    off_shelve: false,
                },
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                        },
                    },
                },
                take: 3,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const relatedArticles = relatedProducts.map(p => this.formatProductAsArticle(p, ctx, isZh));
            return {
                article: formattedArticle,
                relatedArticles,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching article by id:', error);
            return {
                error: 'Failed to fetch article',
                articleId: id,
            };
        }
    }
    formatProductAsArticle(product, ctx, isZh) {
        const avgRating = product.reviews.length > 0
            ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
            : 0;
        let tags = [];
        let author = { id: '1', name: isZh ? '编辑部' : 'Editorial Team', avatar: '/images/defaults/author-avatar.jpg' };
        try {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            tags = specs?.tags || [];
            if (specs?.author) {
                author = specs.author;
            }
        }
        catch (error) {
            this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
        }
        if (tags.length === 0) {
            tags = [
                isZh ? '使用指南' : 'usage-guide',
                isZh ? '技巧' : 'tips',
                product.category?.name || (isZh ? '通用' : 'general')
            ];
        }
        const summary = product.description.length > 100
            ? product.description.substring(0, 100) + '...'
            : product.description;
        return {
            id: product.id,
            title: product.name,
            summary: summary,
            category: product.category?.id || 'general',
            imageUrl: product.images && product.images.length > 0
                ? product.images[0]
                : image_util_1.ImageUtil.getBannerUrl('articles', '001', isZh ? 'zh' : 'en'),
            content: product.description,
            author: author,
            publishDate: product.createdAt.toISOString(),
            readCount: 0,
            likeCount: Math.floor(avgRating * 10),
            tags: tags,
        };
    }
    formatProductAsDetailedArticle(product, ctx, isZh) {
        const basicArticle = this.formatProductAsArticle(product, ctx, isZh);
        const fullContent = product.websiteDescription && product.websiteDescription.trim() !== ''
            ? product.websiteDescription
            : this.generateFullContent(product, isZh);
        return {
            ...basicArticle,
            fullContent: fullContent,
            comments: product.reviews.map((review) => ({
                id: `comment-${review.user?.name || 'anonymous'}-${Date.now()}`,
                author: {
                    name: review.user?.name || (isZh ? '匿名用户' : 'Anonymous'),
                    avatar: review.user?.image || '/images/defaults/user-avatar.jpg',
                },
                content: review.comment,
                publishDate: review.createdAt.toISOString(),
                rating: review.rating,
            })),
            metadata: {
                wordCount: fullContent.length,
                readingTime: Math.ceil(fullContent.length / 500),
                lastUpdated: product.updatedAt.toISOString(),
            },
        };
    }
    generateFullContent(product, isZh) {
        const title = product.name;
        const description = product.description;
        if (isZh) {
            return `# ${title}

${description}

## 产品特点

本产品具有以下特点：
- 高质量的服务体验
- 简单易用的操作界面
- 全天候的客户支持
- 安全可靠的技术保障

## 使用指南

### 第一步：了解产品
在使用前，请仔细阅读产品说明，了解产品的基本功能和使用方法。

### 第二步：开始使用
按照指南步骤，逐步完成产品的设置和配置。

### 第三步：获得支持
如果在使用过程中遇到问题，请联系我们的客服团队获得帮助。

## 总结

${title}为用户提供了优质的服务体验，希望能够满足您的需求。如有任何问题，欢迎随时联系我们。`;
        }
        else {
            return `# ${title}

${description}

## Product Features

This product offers the following features:
- High-quality service experience
- Simple and easy-to-use interface
- 24/7 customer support
- Secure and reliable technology

## Usage Guide

### Step 1: Understand the Product
Before using, please carefully read the product description to understand the basic functions and usage methods.

### Step 2: Start Using
Follow the guide steps to gradually complete the product setup and configuration.

### Step 3: Get Support
If you encounter any problems during use, please contact our customer service team for help.

## Summary

${title} provides users with a high-quality service experience and we hope it meets your needs. If you have any questions, please feel free to contact us at any time.`;
        }
    }
};
ArticlesService = ArticlesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ArticlesService);
exports.ArticlesService = ArticlesService;
//# sourceMappingURL=articles.service.js.map