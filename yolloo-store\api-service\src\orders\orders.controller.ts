import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { OrdersService } from './orders.service';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { CreateOrderDto } from './dto/create-order.dto';
import { OrderQueryDto } from './dto/order-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('orders')
export class OrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  createOrder(
    @CurrentUser() user: any,
    @Body() createOrderDto: CreateOrderDto,
  ) {
    return this.ordersService.createOrder(user.id, createOrderDto);
  }

  @Get(':orderId')
  getOrderById(
    @CurrentUser() user: any,
    @Param('orderId') orderId: string,
  ) {
    return this.ordersService.getOrderById(user.id, orderId);
  }

  @Get()
  getUserOrders(
    @CurrentUser() user: any,
    @Query() query: OrderQueryDto,
  ) {
    return this.ordersService.getUserOrders(user.id, query);
  }
}
