(()=>{var e={};e.id=1301,e.ids=[1301],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},47807:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(20205),r(89090),r(26083),r(35866);var s=r(23191),i=r(88716),a=r(37922),n=r.n(a),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["invite",{children:["[code]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20205)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\invite\\[code]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\invite\\[code]\\page.tsx"],u="/invite/[code]/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/invite/[code]/page",pathname:"/invite/[code]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1545:(e,t,r)=>{Promise.resolve().then(r.bind(r,98598))},98598:(e,t,r)=>{"use strict";r.d(t,{InviteAcceptForm:()=>v});var s=r(10326),i=r(17577),a=r(35047),n=r(77109),o=r(90772),l=r(54432),c=r(31048),d=r(33071),u=r(79210),m=r(57372),p=r(85999),f=r(567),g=r(30361),x=r(24230),h=r(36283);function v({inviteCode:e,email:t,userExists:r,isLoggedIn:v,isGeneralInvite:y=!1,currentUserEmail:b,commissionRate:w}){let j=(0,a.useRouter)(),[N,_]=i.useState(!1),[R,P]=i.useState(t||""),C=async()=>{try{_(!0);let t=await fetch("/api/affiliate/invites/accept-logged-in",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({inviteCode:e})}),r=await t.json();if(!t.ok)throw Error(r.error||"Failed to accept invitation");p.A.success(r.message||"Successfully joined organization"),j.push("/affiliate")}catch(e){e instanceof Error?p.A.error(e.message):p.A.error("Failed to accept invitation")}finally{_(!1)}},E=async r=>{r.preventDefault(),_(!0);try{let s=new FormData(r.currentTarget),i=s.get("name"),a=s.get("password"),o=s.get("confirmPassword"),l=y?s.get("email"):t;if(!i||!a||!o||y&&!l){p.A.error("All fields are required");return}if(a!==o){p.A.error("Passwords do not match");return}if(a.length<8){p.A.error("Password must be at least 8 characters long");return}if(y&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(l)){p.A.error("Please enter a valid email address");return}let c=await fetch("/api/affiliate/invites/accept",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({inviteCode:e,name:i,password:a,...y&&{email:l}})}),d=await c.json();if(!c.ok)throw Error(d.error||"Failed to register and accept invitation");p.A.success("Successfully registered and joined organization");let u=await (0,n.signIn)("credentials",{email:l,password:a,redirect:!1});u?.error?(p.A.error("Account created but failed to sign in automatically"),j.push("/auth/signin")):j.push("/affiliate")}catch(e){e instanceof Error?p.A.error(e.message):p.A.error("Failed to register and accept invitation")}finally{_(!1)}},O=async e=>{e.preventDefault(),_(!0);try{let r=new FormData(e.currentTarget).get("password");if(!r){p.A.error("Password is required");return}let s=await (0,n.signIn)("credentials",{email:t,password:r,redirect:!1});if(s?.error)throw Error("Invalid email or password");await C()}catch(e){e instanceof Error?p.A.error(e.message):p.A.error("Failed to sign in")}finally{_(!1)}};return s.jsx(d.Zb,{className:"border-t-4 border-t-primary",children:(0,s.jsxs)(u.Tabs,{defaultValue:v?"accept":r?"login":"register",children:[!v&&s.jsx(d.Ol,{children:(0,s.jsxs)(u.TabsList,{className:"grid w-full grid-cols-2",children:[s.jsx(u.TabsTrigger,{value:"register",children:"Register"}),s.jsx(u.TabsTrigger,{value:"login",children:"Login"})]})}),v&&(0,s.jsxs)(d.Ol,{children:[(0,s.jsxs)(d.ll,{className:"flex items-center gap-2",children:[s.jsx(g.Z,{className:"h-5 w-5 text-primary"}),"Accept Invitation"]}),b&&(0,s.jsxs)(d.SZ,{className:"mt-1",children:["Continue as ",s.jsx("strong",{children:b})," to join this organization"]})]}),w&&s.jsx("div",{className:"px-6 pb-2",children:(0,s.jsxs)("div",{className:"flex items-center justify-between rounded-md bg-muted/50 p-3 text-sm",children:[s.jsx("span",{className:"text-muted-foreground",children:"Your commission rate:"}),(0,s.jsxs)(f.C,{className:"bg-primary/10 hover:bg-primary/20 text-primary font-medium",children:[(100*w).toFixed(0),"%"]})]})}),s.jsx(d.aY,{className:"pt-6",children:v?(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("div",{className:"rounded-md bg-muted p-4",children:(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[s.jsx(g.Z,{className:"h-4 w-4 text-green-500"}),(0,s.jsxs)("span",{children:["You are logged in as ",s.jsx("strong",{children:b})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[s.jsx(g.Z,{className:"h-4 w-4 text-green-500"}),s.jsx("span",{children:"You will become an affiliate member"})]}),w&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[s.jsx(g.Z,{className:"h-4 w-4 text-green-500"}),(0,s.jsxs)("span",{children:["You will earn ",(0,s.jsxs)("strong",{children:[(100*w).toFixed(0),"%"]})," commission on sales"]})]})]})}),(0,s.jsxs)(o.Button,{className:"w-full group",onClick:C,disabled:N,children:[N?s.jsx(m.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}):s.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Accept Invitation",s.jsx(x.Z,{className:"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1"})]})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(u.TabsContent,{value:"register",children:(0,s.jsxs)("form",{onSubmit:E,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"name",children:"Full Name"}),s.jsx(l.I,{id:"name",name:"name",placeholder:"Your full name",required:!0,disabled:N,autoComplete:"name"})]}),y?(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"email",children:"Email Address"}),s.jsx(l.I,{id:"email",name:"email",type:"email",placeholder:"Your email address",required:!0,disabled:N,value:R,onChange:e=>P(e.target.value),autoComplete:"email",pattern:"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",title:"Please enter a valid email address in <NAME_EMAIL>"})]}):(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"email",children:"Email Address"}),s.jsx(l.I,{id:"email",type:"email",value:t,disabled:!0,readOnly:!0,className:"bg-muted/50"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"password",children:"Password"}),s.jsx(l.I,{id:"password",name:"password",type:"password",placeholder:"Create a password (min. 8 characters)",required:!0,disabled:N,minLength:8,autoComplete:"new-password"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"confirmPassword",children:"Confirm Password"}),s.jsx(l.I,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm your password",required:!0,disabled:N,minLength:8,autoComplete:"new-password"})]}),w&&s.jsx("div",{className:"mt-4 rounded-md bg-muted/50 p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-muted-foreground",children:"You will earn:"}),(0,s.jsxs)(f.C,{className:"bg-primary/10 hover:bg-primary/20 text-primary font-medium",children:[(100*w).toFixed(0),"% Commission"]})]})}),(0,s.jsxs)(o.Button,{type:"submit",className:"w-full mt-2",disabled:N,children:[N&&s.jsx(m.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}),"Register & Accept Invitation"]})]})}),s.jsx(u.TabsContent,{value:"login",children:(0,s.jsxs)("form",{onSubmit:O,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"login-email",children:"Email Address"}),s.jsx(l.I,{id:"login-email",type:"email",value:t,disabled:!0,readOnly:!0,className:"bg-muted/50"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(c._,{htmlFor:"login-password",children:"Password"}),s.jsx(l.I,{id:"login-password",name:"password",type:"password",placeholder:"Your password",required:!0,disabled:N,autoComplete:"current-password"})]}),w&&s.jsx("div",{className:"mt-4 rounded-md bg-muted/50 p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-muted-foreground",children:"You will earn:"}),(0,s.jsxs)(f.C,{className:"bg-primary/10 hover:bg-primary/20 text-primary font-medium",children:[(100*w).toFixed(0),"% Commission"]})]})}),(0,s.jsxs)(o.Button,{type:"submit",className:"w-full mt-2",disabled:N,children:[N?s.jsx(m.P.spinner,{className:"mr-2 h-4 w-4 animate-spin"}):s.jsx(h.Z,{className:"mr-2 h-4 w-4"}),"Login & Accept Invitation"]})]})})]})})]})})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var s=r(10326);r(17577);var i=r(79360),a=r(77863);let n=(0,i.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,a.cn)(n({variant:t}),e),...r})}},33071:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>c,Zb:()=>n,aY:()=>d,eW:()=>u,ll:()=>l});var s=r(10326),i=r(17577),a=r(77863);let n=i.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let o=i.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=i.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,a.cn)("font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=i.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=i.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=i.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},31048:(e,t,r)=>{"use strict";r.d(t,{_:()=>c});var s=r(10326),i=r(17577),a=r(34478),n=r(79360),o=r(77863);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef(({className:e,...t},r)=>s.jsx(a.f,{ref:r,className:(0,o.cn)(l(),e),...t}));c.displayName=a.f.displayName},79210:(e,t,r)=>{"use strict";r.d(t,{Tabs:()=>o,TabsContent:()=>d,TabsList:()=>l,TabsTrigger:()=>c});var s=r(10326),i=r(17577),a=r(13239),n=r(77863);let o=a.fC,l=i.forwardRef(({className:e,...t},r)=>s.jsx(a.aV,{ref:r,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=a.aV.displayName;let c=i.forwardRef(({className:e,...t},r)=>s.jsx(a.xz,{ref:r,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=a.xz.displayName;let d=i.forwardRef(({className:e,...t},r)=>s.jsx(a.VY,{ref:r,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=a.VY.displayName},30361:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},20205:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,metadata:()=>f});var s=r(19510),i=r(58585),a=r(68570);let n=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\invites\invite-accept-form.tsx`),{__esModule:o,$$typeof:l}=n;n.default;let c=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\components\invites\invite-accept-form.tsx#InviteAcceptForm`);var d=r(75571),u=r(90455),m=r(56881),p=r(72331);let f={title:"Accept Invitation",description:"Accept invitation to organization"};async function g({params:e}){let{code:t}=e,r=await fetch(`http://localhost:8000/api/affiliate/invites/verify?code=${t}`,{method:"GET",headers:{"Content-Type":"application/json"},cache:"no-store"}),a=await r.json();if(!a.valid)return(0,i.redirect)("/invite/error?error="+encodeURIComponent(a.error||"Invalid invite"));let n=await (0,d.getServerSession)(u.L),o=!!n?.user,l=o&&a.invite.email&&n?.user?.email!==a.invite.email,f=a.invite.isGeneralInvite,g=await p._.organizationInvite.findUnique({where:{inviteCode:t},include:{organization:{select:{id:!0,name:!0,code:!0,logo:!0,description:!0,commissionRate:!0,discountRate:!0}},affiliate:{select:{user:{select:{name:!0,email:!0,image:!0}}}}}}),x=g?.commissionRate||g?.organization.commissionRate||.5,h=g?.affiliate?.user?.name;return s.jsx("div",{className:"container max-w-md mx-auto py-10 px-4 sm:px-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4 text-center",children:[a.invite.organization?.logo&&s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("img",{src:a.invite.organization.logo,alt:a.invite.organization.name,className:"w-20 h-20 object-contain"})}),(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:["Join Organization ",a.invite.organization.name]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{className:"text-muted-foreground",children:["You've been invited to join ",s.jsx("strong",{children:a.invite.organization.name})," as an affiliate member."]}),g?.organization.description&&(0,s.jsxs)("p",{className:"text-sm text-muted-foreground border rounded-md p-3 bg-muted/30",children:['"',g.organization.description,'"']}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 items-center mt-4",children:[h&&(0,s.jsxs)("div",{className:"text-sm",children:[s.jsx("span",{className:"text-muted-foreground",children:"Invited by:"})," ",s.jsx("strong",{children:h})]}),(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,s.jsxs)(m.C,{variant:"outline",className:"bg-green-50 text-green-700 hover:bg-green-50",children:[(100*x).toFixed(0),"% Commission Rate"]}),g?.organization.discountRate&&g.organization.discountRate>0&&(0,s.jsxs)(m.C,{variant:"outline",className:"bg-blue-50 text-blue-700 hover:bg-blue-50",children:[(100*g.organization.discountRate).toFixed(0),"% Customer Discount"]})]})]}),a.invite.email&&(0,s.jsxs)("p",{className:"text-sm mt-2",children:["This invitation was sent to ",s.jsx("strong",{children:a.invite.email})]})]})]}),l?s.jsx("div",{className:"bg-yellow-50 p-4 rounded-md border border-yellow-200",children:(0,s.jsxs)("p",{className:"text-sm text-yellow-800",children:["You're logged in as ",s.jsx("strong",{children:n.user.email})," but this invitation was sent to ",s.jsx("strong",{children:a.invite.email}),". Please log out and sign in with the correct account, or continue to create a new account with the invited email."]})}):s.jsx(c,{inviteCode:t,email:a.invite.email||"",userExists:a.userExists,isLoggedIn:o,isGeneralInvite:f,currentUserEmail:n?.user?.email||"",commissionRate:x})]})})}},56881:(e,t,r)=>{"use strict";let s,i;r.d(t,{C:()=>u});var a=r(19510);r(71159);var n=r(55761);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.W;var c=r(50650);let d=(s="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",i={variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}},e=>{var t;if((null==i?void 0:i.variants)==null)return l(s,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:a}=i,n=Object.keys(r).map(t=>{let s=null==e?void 0:e[t],i=null==a?void 0:a[t];if(null===s)return null;let n=o(s)||o(i);return r[t][n]}),c=e&&Object.entries(e).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return l(s,n,null==i?void 0:null===(t=i.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:s,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...c}[t]):({...a,...c})[t]===r})?[...e,r,s]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)});function u({className:e,variant:t,...r}){return a.jsx("div",{className:(0,c.cn)(d({variant:t}),e),...r})}},90455:(e,t,r)=>{"use strict";r.d(t,{L:()=>d});var s=r(7585),i=r(72331),a=r(77234),n=r(53797),o=r(42023),l=r.n(o),c=r(93475);let d={adapter:{...(0,s.N)(i._),getUser:async e=>{let t=await i._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await i._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await i._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,n.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await i._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await l().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,c.Ak)(e.email);if(!t||t!==e.code)return null;await (0,c.qc)(e.email);let r=await i._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await i._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await i._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,a.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:s,request:a}){try{if(r&&r.id){let t=a?.headers||new Headers,n=t.get("user-agent")||"",o=t.get("x-forwarded-for"),l=o?o.split(/, /)[0]:t.get("REMOTE_ADDR")||"",c="unknown";s?c=s.code&&!s.password?"email_code":"password":e&&(c=e.provider),await i._.userLoginHistory.create({data:{userId:r.id,ipAddress:l||null,userAgent:n||null,loginMethod:c,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],s=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,i=new URL(s).searchParams.get("callbackUrl");if(i){let e=decodeURIComponent(i);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let s=new URL(e);if(r.some(e=>s.hostname===e||s.hostname.includes(e)||s.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(s);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return s}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:s}){if("update"===r&&s)return{...e,...s.user};let a=await i._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return a?{id:a.id,name:a.name,email:a.email,picture:a.image,role:a.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,t,r)=>{"use strict";r.d(t,{_:()=>i});var s=r(53524);let i=global.prisma||new s.PrismaClient({log:["error"]})},93475:(e,t,r)=>{"use strict";r.d(t,{AL:()=>o,Ak:()=>l,qc:()=>c,yz:()=>d});var s=r(62197),i=r.n(s);let a=null;function n(){if(!a){let e=process.env.REDIS_URL||"redis://localhost:6379";(a=new(i())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),a.on("connect",()=>{console.log("Successfully connected to Redis")})}return a}async function o(e,t,r=300){try{let s=n(),i=`verification_code:${e}`;return await s.setex(i,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let t=n(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function c(e){try{let t=n(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function d(e,t,r){try{let s=n(),i=`rate_limit:${e}`,a=await s.get(i),o=a?parseInt(a):0;if(o>=t)return!1;return 0===o?await s.setex(i,r,"1"):await s.incr(i),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var a=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(void 0);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&({}).hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,r&&r.set(e,s),s}(r(45609));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))})},58585:(e,t,r)=>{"use strict";var s=r(61085);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"redirect")&&r.d(t,{redirect:function(){return s.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return n},RedirectType:function(){return s.RedirectType},notFound:function(){return i.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=r(83953),i=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class n extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return i},notFound:function(){return s}});let r="NEXT_NOT_FOUND";function s(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return s},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return f},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return m},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return c}});let i=r(54580),a=r(72934),n=r(8586),o="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let s=Error(o);s.digest=o+";"+t+";"+e+";"+r+";";let a=i.requestAsyncStorage.getStore();return a&&(s.mutableCookies=a.mutableCookies),s}function c(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,s,i]=e.digest.split(";",4),a=Number(i);return t===o&&("replace"===r||"push"===r)&&"string"==typeof s&&!isNaN(a)&&a in n.RedirectStatusCode}function m(e){return u(e)?e.digest.split(";",3)[2]:null}function p(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function f(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(66621);let i=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>o});var s=r(17577),i=r(45226),a=r(10326),n=s.forwardRef((e,t)=>(0,a.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,3239,4824],()=>r(47807));module.exports=s})();