"use strict";(()=>{var e={};e.id=4689,e.ids=[4689],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},34074:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>p,serverHooks:()=>w,staticGenerationAsyncStorage:()=>h});var a={};r.r(a),r.d(a,{GET:()=>m,dynamic:()=>d});var i=r(49303),n=r(88716),o=r(60670),s=r(87070),l=r(75571),u=r(90455),c=r(72331);let d="force-dynamic";async function m(e,{params:t}){try{let e=await (0,l.getServerSession)(u.L);if(!e?.user)return s.NextResponse.json({error:"Unauthorized"},{status:401});let r=t.id,a=await c._.affiliateOrganization.findUnique({where:{id:r},include:{_count:{select:{members:!0,visits:!0,commissions:!0}}}});if(!a)return s.NextResponse.json({error:"Organization not found"},{status:404});let i=await c._.affiliateProfile.findFirst({where:{userId:e.user.id,organizationId:r}});if(!i||!i.isAdmin)return s.NextResponse.json({error:"You don't have permission to view this organization's analytics"},{status:403});let n=await c._.affiliateVisit.count({where:{organizationId:r}}),o=await c._.affiliateReferral.count({where:{status:"APPROVED",order:{status:{in:["PAID","PROCESSING","SHIPPED","DELIVERED"]}},affiliate:{organizationId:r}}}),d=(await c._.organizationCommission.aggregate({where:{organizationId:r,status:{in:["APPROVED","PAID"]}},_sum:{commissionAmount:!0}}))._sum.commissionAmount||0,m=(await c._.affiliateReferral.aggregate({where:{affiliate:{organizationId:r},status:{in:["APPROVED","PAID"]}},_sum:{commissionAmount:!0}}))._sum.commissionAmount||0,p=d-m,g=n>0?o/n*100:0,h=await c._.affiliateProfile.findMany({where:{organizationId:r},include:{user:{select:{id:!0,name:!0,email:!0,image:!0}}}}),w=h.map(e=>e.id),y=await Promise.all(h.slice(0,5).map(async e=>{let t=await c._.affiliateReferral.count({where:{affiliateId:e.id,status:{in:["APPROVED","PAID"]}}});return{id:e.id,user:{id:e.user.id,name:e.user.name,image:e.user.image},referrals:t,earnings:e.totalEarnings}}));y.sort((e,t)=>t.earnings-e.earnings);let _=await c._.affiliateReferral.findMany({where:{affiliateId:{in:w}},include:{affiliate:{include:{user:!0}},order:{select:{id:!0,total:!0,status:!0,createdAt:!0,updatedAt:!0}},organizationCommission:{select:{id:!0,commissionAmount:!0,status:!0}}},orderBy:{createdAt:"desc"},take:200}),v=await f(r),x={totalMembers:h.length,totalReferrals:o,totalEarnings:p,monthlyEarnings:v,topPerformers:y};return s.NextResponse.json({analytics:x,stats:{totalVisits:n,totalConversions:o,conversionRate:g,totalCommissions:d,memberCommissions:m,organizationActualEarnings:p},memberOrders:_,organization:a,needsRefresh:0===_.length&&h.length>0})}catch(e){return console.error("Error fetching organization analytics:",e),s.NextResponse.json({error:"Failed to fetch organization analytics"},{status:500})}}async function f(e){let t=new Date,r=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],a=[];for(let i=0;i<6;i++){let n=new Date;n.setMonth(t.getMonth()-i);let o=new Date(n.getFullYear(),n.getMonth(),1),s=new Date(n.getFullYear(),n.getMonth()+1,0,23,59,59),l=await c._.organizationCommission.aggregate({where:{organizationId:e,status:{in:["APPROVED","PAID"]},createdAt:{gte:o,lte:s}},_sum:{commissionAmount:!0}}),u=await c._.affiliateReferral.aggregate({where:{affiliate:{organizationId:e},status:{in:["APPROVED","PAID"]},createdAt:{gte:o,lte:s}},_sum:{commissionAmount:!0}}),d=l._sum.commissionAmount||0,m=u._sum.commissionAmount||0,f=d-m;a.push({month:r[n.getMonth()],year:n.getFullYear(),amount:f,totalCommissions:d,memberCommissions:m})}return a.reverse()}let p=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/affiliate/organizations/[id]/analytics/route",pathname:"/api/affiliate/organizations/[id]/analytics",filename:"route",bundlePath:"app/api/affiliate/organizations/[id]/analytics/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\affiliate\\organizations\\[id]\\analytics\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:w}=p,y="/api/affiliate/organizations/[id]/analytics/route";function _(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:h})}},90455:(e,t,r)=>{r.d(t,{L:()=>c});var a=r(7585),i=r(72331),n=r(77234),o=r(53797),s=r(42023),l=r.n(s),u=r(93475);let c={adapter:{...(0,a.N)(i._),getUser:async e=>{let t=await i._.user.findUnique({where:{id:e}});return t?{...t,role:t.role}:null},getUserByEmail:async e=>{let t=await i._.user.findUnique({where:{email:e}});return t?{...t,role:t.role}:null},createUser:async e=>{let t=await i._.user.create({data:{...e,role:"CUSTOMER"}});return{...t,role:t.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let t=await i._.user.findUnique({where:{email:e.email}});if(!t||!t.hashedPassword||!await l().compare(e.password,t.hashedPassword))throw Error("Invalid credentials");return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let t=await (0,u.Ak)(e.email);if(!t||t!==e.code)return null;await (0,u.qc)(e.email);let r=await i._.user.findUnique({where:{email:e.email}});if(r)r.emailVerified||(r=await i._.user.update({where:{id:r.id},data:{emailVerified:new Date}}));else{let t=e.email.split("@")[0];r=await i._.user.create({data:{email:e.email,name:t,role:"CUSTOMER",emailVerified:new Date}})}return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:t,user:r,credentials:a,request:n}){try{if(r&&r.id){let t=n?.headers||new Headers,o=t.get("user-agent")||"",s=t.get("x-forwarded-for"),l=s?s.split(/, /)[0]:t.get("REMOTE_ADDR")||"",u="unknown";a?u=a.code&&!a.password?"email_code":"password":e&&(u=e.provider),await i._.userLoginHistory.create({data:{userId:r.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!t?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:t}){try{let r=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${t}${e.startsWith("/")?e:`/${e}`}`,i=new URL(a).searchParams.get("callbackUrl");if(i){let e=decodeURIComponent(i);if(e.startsWith("/"))return`${t}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(r.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(t).hostname))return e}catch(r){if(console.error("Invalid callback URL:",r),!e.includes("://"))return`${t}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${t}${e}`;try{let e=new URL(a);if(r.some(r=>e.hostname===r||e.hostname.includes(r)||e.hostname===new URL(t).hostname))return a}catch(e){console.error("URL parse error:",e)}return t}catch(e){return console.error("Redirect URL parse error:",e),t}},session:async({token:e,session:t})=>(e&&(t.user.id=e.id,t.user.name=e.name,t.user.email=e.email,t.user.image=e.picture,t.user.role=e.role),t),async jwt({token:e,user:t,trigger:r,session:a}){if("update"===r&&a)return{...e,...a.user};let n=await i._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(t&&(e.id=t?.id,e.role=t?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,t,r)=>{r.d(t,{_:()=>i});var a=r(53524);let i=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,t,r)=>{r.d(t,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var a=r(62197),i=r.n(a);let n=null;function o(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(i())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function s(e,t,r=300){try{let a=o(),i=`verification_code:${e}`;return await a.setex(i,r,t),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let t=o(),r=`verification_code:${e}`;return await t.get(r)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let t=o(),r=`verification_code:${e}`;return await t.del(r),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,t,r){try{let a=o(),i=`rate_limit:${e}`,n=await a.get(i),s=n?parseInt(n):0;if(s>=t)return!1;return 0===s?await a.setex(i,r,"1"):await a.incr(i),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},75571:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var a={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n.default}});var i=r(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(void 0);if(r&&r.has(e))return r.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var s=i?Object.getOwnPropertyDescriptor(e,n):null;s&&(s.get||s.set)?Object.defineProperty(a,n,s):a[n]=e[n]}return a.default=e,r&&r.set(e,a),a}(r(45609));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in t&&t[e]===n[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}}))})}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>r(34074));module.exports=a})();