"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var HomeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HomeService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma.service");
const image_util_1 = require("../common/utils/image.util");
let HomeService = HomeService_1 = class HomeService {
    prisma;
    logger = new common_1.Logger(HomeService_1.name);
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getHomeData(userId, query, ctx) {
        console.log('Context in getHomeData:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const lang = isZh ? 'zh' : 'en';
        try {
            const banners = await this.prisma.banner.findMany({
                where: {
                    isActive: true,
                    language: isZh ? 'zh' : 'en',
                    startDate: { lte: new Date() },
                    OR: [
                        { endDate: null },
                        { endDate: { gte: new Date() } }
                    ]
                },
                orderBy: {
                    priority: 'desc',
                },
                take: 5,
            });
            const categories = await this.prisma.category.findMany({
                where: {
                    parentId: null,
                },
                take: 6,
                orderBy: {
                    createdAt: 'asc',
                },
            });
            const recommendedProducts = await this.prisma.product.findMany({
                where: {
                    status: 'ACTIVE',
                    off_shelve: false,
                },
                include: {
                    category: true,
                    variants: {
                        take: 1,
                        orderBy: {
                            price: 'asc',
                        },
                    },
                },
                take: 5,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const homeFeatures = await this.prisma.homeFeature.findMany({
                where: {
                    isActive: true,
                    language: isZh ? 'zh' : 'en',
                },
                orderBy: {
                    position: 'asc',
                },
                take: 9,
            });
            const travelTips = await this.prisma.travelTip.findMany({
                where: {
                    isActive: true,
                    language: isZh ? 'zh' : 'en',
                },
                orderBy: {
                    priority: 'desc',
                },
                take: 5,
            });
            const formattedCategories = categories.map(category => ({
                id: category.id,
                name: category.name,
                icon: category.image || image_util_1.ImageUtil.getIconUrl(category.name.toLowerCase()),
                link: `/categories/${category.id}`,
            }));
            const formattedRecommendedPackages = recommendedProducts.map(product => {
                const variant = product.variants[0];
                const price = variant ? parseFloat(variant.price.toString()) : product.price;
                return {
                    id: product.id,
                    name: product.name,
                    description: product.description,
                    price: price,
                    currency: variant?.currency || 'USD',
                    imageUrl: product.images[0] || image_util_1.ImageUtil.getBannerUrl('home', product.id, lang),
                    dataSize: product.dataSize || 0,
                    planType: product.planType || 'Total',
                    countries: product.country ? product.country.split(',').map(c => c.trim()) : [],
                };
            });
            const formattedBanners = banners.length > 0 ? banners.map(banner => ({
                id: banner.id,
                imageUrl: banner.imageUrl,
                title: banner.title,
                subtitle: banner.subtitle,
                link: banner.link,
                priority: banner.priority,
            })) : recommendedProducts.slice(0, 3).map((product, index) => ({
                id: product.id,
                imageUrl: product.images[0] || image_util_1.ImageUtil.getBannerUrl('home', `00${index + 1}`, lang),
                title: isZh ? `${product.name} 促销` : `${product.name} Promotion`,
                link: `/products/${product.id}`,
                priority: index + 1,
            }));
            const formattedGridButtons = homeFeatures.length > 0 ? homeFeatures.map(feature => ({
                id: feature.id,
                title: feature.title,
                icon: feature.icon,
                type: feature.type,
                action: feature.action,
                position: feature.position,
                color: feature.color,
            })) : [
                {
                    id: 'number-retention',
                    title: isZh ? '保号套餐' : 'Number Retention',
                    icon: 'shield',
                    type: 'native',
                    action: '/number-retention',
                    position: 1,
                    color: '#007AFF',
                },
                {
                    id: 'mobile-recharge',
                    title: isZh ? '手机充值' : 'Mobile Recharge',
                    icon: 'smartphone',
                    type: 'native',
                    action: '/mobile-recharge',
                    position: 2,
                    color: '#34C759',
                },
                {
                    id: 'travel-packages',
                    title: isZh ? '旅游套餐' : 'Travel Packages',
                    icon: 'map',
                    type: 'native',
                    action: '/travel-packages',
                    position: 3,
                    color: '#FF9500',
                },
                {
                    id: 'local-packages',
                    title: isZh ? '本地套餐' : 'Local Packages',
                    icon: 'map-pin',
                    type: 'native',
                    action: '/local-packages',
                    position: 4,
                    color: '#5856D6',
                },
                {
                    id: 'data-boosters',
                    title: isZh ? '加油流量包' : 'Data Boosters',
                    icon: 'zap',
                    type: 'native',
                    action: '/data-boosters',
                    position: 5,
                    color: '#FF2D92',
                },
                {
                    id: 'data-cards',
                    title: isZh ? '流量卡' : 'Data Cards',
                    icon: 'credit-card',
                    type: 'html',
                    action: '/pages/content?pageId=data-cards',
                    position: 6,
                    color: '#FF3B30',
                },
                {
                    id: 'travel-data',
                    title: isZh ? '出行流量' : 'Travel Data',
                    icon: 'navigation',
                    type: 'html',
                    action: '/pages/content?pageId=travel-data',
                    position: 7,
                    color: '#30D158',
                },
                {
                    id: '5g-packages',
                    title: isZh ? '5G套餐' : '5G Packages',
                    icon: 'wifi',
                    type: 'html',
                    action: '/pages/content?pageId=5g-packages',
                    position: 8,
                    color: '#64D2FF',
                },
                {
                    id: 'international-roaming',
                    title: isZh ? '国际漫游' : 'International Roaming',
                    icon: 'globe',
                    type: 'html',
                    action: '/pages/content?pageId=international-roaming',
                    position: 9,
                    color: '#8E8E93',
                }
            ];
            const formattedTravelTips = travelTips.length > 0 ? travelTips.map(tip => ({
                id: tip.id,
                title: tip.title,
                content: tip.content,
                imageUrl: tip.imageUrl,
                link: tip.link,
                category: tip.category,
            })) : [
                {
                    id: '1',
                    title: isZh ? '亚洲最佳数据套餐' : 'Best Data Plans for Asia',
                    imageUrl: image_util_1.ImageUtil.getBannerUrl('tips', 'asia', lang),
                    link: '/tips/asia-data-plans',
                },
            ];
            return {
                banners: formattedBanners,
                categories: formattedCategories,
                gridButtons: formattedGridButtons,
                recommendedPackages: formattedRecommendedPackages,
                travelTips: formattedTravelTips,
            };
        }
        catch (error) {
            console.error('Error fetching home data:', error);
            return {
                banners: [],
                categories: [],
                travelTips: [],
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getBanners(userId, query, ctx) {
        console.log('Context in getBanners:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const lang = isZh ? 'zh' : 'en';
        try {
            const realBanners = await this.prisma.banner.findMany({
                where: {
                    isActive: true,
                    language: isZh ? 'zh' : 'en',
                    startDate: { lte: new Date() },
                    OR: [
                        { endDate: null },
                        { endDate: { gte: new Date() } }
                    ]
                },
                orderBy: {
                    priority: 'desc',
                },
                take: 5,
            });
            if (realBanners.length > 0) {
                const banners = realBanners.map(banner => ({
                    id: banner.id,
                    type: 'carousel',
                    title: banner.title,
                    subtitle: banner.subtitle,
                    imageUrl: banner.imageUrl,
                    link: banner.link,
                    position: query.position || 'home',
                    priority: banner.priority,
                    startDate: banner.startDate.toISOString(),
                    endDate: banner.endDate?.toISOString() || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                }));
                return { banners };
            }
            const products = await this.prisma.product.findMany({
                where: {
                    status: 'ACTIVE',
                    off_shelve: false,
                },
                include: {
                    variants: {
                        take: 1,
                        orderBy: {
                            price: 'asc',
                        },
                    },
                },
                take: 5,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            const banners = products.map((product, index) => {
                const variant = product.variants[0];
                const price = variant ? parseFloat(variant.price.toString()) : product.price;
                return {
                    id: product.id,
                    type: 'carousel',
                    title: isZh ? `${product.name} 促销` : `${product.name} Promotion`,
                    subtitle: isZh ? `特价 ${price} ${variant?.currency || 'USD'}` : `Special Price ${price} ${variant?.currency || 'USD'}`,
                    imageUrl: product.images[0] || image_util_1.ImageUtil.getBannerUrl('home', `00${index + 1}`, lang),
                    link: `/products/${product.id}`,
                    position: query.position || 'home',
                    priority: index + 1,
                    startDate: new Date().toISOString(),
                    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                };
            });
            return { banners };
        }
        catch (error) {
            console.error('Error fetching banners:', error);
            return {
                banners: [],
            };
        }
    }
    async getTravelTips(userId, query, ctx) {
        console.log('Context in getTravelTips:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const { category, page = 1, pageSize = 10, limit } = query;
        try {
            const whereConditions = {
                isActive: true,
                language: isZh ? 'zh' : 'en',
            };
            if (category) {
                whereConditions.category = category;
            }
            const realTravelTips = await this.prisma.travelTip.findMany({
                where: whereConditions,
                orderBy: {
                    priority: 'desc',
                },
                skip: (page - 1) * pageSize,
                take: limit && limit > 0 ? Math.min(limit, pageSize) : pageSize,
            });
            if (realTravelTips.length > 0) {
                const tips = realTravelTips.map(tip => ({
                    id: tip.id,
                    title: tip.title,
                    summary: tip.content || '',
                    category: tip.category,
                    imageUrl: tip.imageUrl,
                    content: tip.content || '',
                    link: tip.link || `/tips/${tip.id}`,
                    readCount: 0,
                    publishDate: tip.createdAt.toISOString(),
                    tags: [tip.category],
                }));
                const total = await this.prisma.travelTip.count({ where: whereConditions });
                return {
                    travelTips: tips,
                    pagination: {
                        page,
                        pageSize,
                        total,
                        totalPages: Math.ceil(total / pageSize),
                    },
                    filters: {
                        category,
                        country: query.country,
                    },
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const productWhereConditions = {
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    { name: { contains: '攻略', mode: 'insensitive' } },
                    { name: { contains: 'guide', mode: 'insensitive' } },
                    { name: { contains: '贴士', mode: 'insensitive' } },
                    { name: { contains: 'tips', mode: 'insensitive' } },
                    { name: { contains: '旅行', mode: 'insensitive' } },
                    { name: { contains: 'travel', mode: 'insensitive' } },
                    { description: { contains: '攻略', mode: 'insensitive' } },
                    { description: { contains: 'guide', mode: 'insensitive' } },
                    { description: { contains: '贴士', mode: 'insensitive' } },
                    { description: { contains: 'tips', mode: 'insensitive' } },
                    { websiteDescription: { not: '' } },
                ],
            };
            if (category) {
                productWhereConditions.AND = productWhereConditions.AND || [];
                productWhereConditions.AND.push({
                    OR: [
                        { categoryId: category },
                        { category: { name: { contains: category, mode: 'insensitive' } } },
                    ],
                });
            }
            if (query.country) {
                productWhereConditions.AND = productWhereConditions.AND || [];
                productWhereConditions.AND.push({
                    OR: [
                        { country: { contains: query.country, mode: 'insensitive' } },
                        { countryCode: { contains: query.country, mode: 'insensitive' } },
                        { name: { contains: query.country, mode: 'insensitive' } },
                    ],
                });
            }
            const skip = (page - 1) * pageSize;
            const takeCount = limit && limit > 0 ? Math.min(limit, pageSize) : pageSize;
            const products = await this.prisma.product.findMany({
                where: productWhereConditions,
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                        },
                    },
                },
                skip: limit && limit > 0 ? 0 : skip,
                take: takeCount,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            if (products.length === 0) {
                return {
                    travelTips: [],
                    pagination: {
                        page,
                        pageSize,
                        total: 0,
                        totalPages: 0,
                    },
                    filters: {
                        category,
                        country: query.country,
                    },
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const tips = products.map(product => this.formatProductAsTip(product, ctx, isZh));
            const total = await this.prisma.product.count({ where: productWhereConditions });
            return {
                travelTips: tips,
                pagination: {
                    page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize),
                },
                filters: {
                    category,
                    country: query.country,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching travel tips:', error);
            return {
                travelTips: [],
                pagination: {
                    page: query.page || 1,
                    pageSize: query.pageSize || 10,
                    total: 0,
                    totalPages: 0,
                },
                filters: {
                    category: query.category,
                    country: query.country,
                },
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    async getRecommendations(userId, query, ctx) {
        console.log('Context in getRecommendations:', ctx);
        const isZh = ctx.language.startsWith('zh');
        const lang = isZh ? 'zh' : 'en';
        const recommendations = [
            {
                id: '1',
                type: 'promotion',
                bannerImageUrl: image_util_1.ImageUtil.getBannerUrl('promotion', '001', lang),
                textLine1: isZh ? '周四流量特惠日' : 'Thursday Data Special',
                textLine2: isZh ? '抢100GB流量包仅需50元！' : 'Get 100GB for only $50!',
                textLine3: isZh ? '查看流量套餐使用攻略>' : 'View Data Package Guide>',
                htmlLink: '/pages/content?pageId=data-special-thursday',
                priority: 1,
            },
            {
                id: '2',
                type: 'promotion',
                bannerImageUrl: image_util_1.ImageUtil.getBannerUrl('promotion', '002', lang),
                textLine1: isZh ? '欧洲套餐夏季特惠' : 'Europe Summer Special',
                textLine2: isZh ? '30天欧洲多国流量套餐' : '30-day Europe Multi-country',
                textLine3: isZh ? '立享75折优惠>' : 'Get 25% Off Now>',
                htmlLink: '/pages/content?pageId=europe-summer-promo',
                priority: 2,
            },
            {
                id: '3',
                type: 'package',
                bannerImageUrl: image_util_1.ImageUtil.getBannerUrl('promotion', '003', lang),
                textLine1: isZh ? '环球旅行者套餐' : 'Global Traveler Package',
                textLine2: isZh ? '覆盖190+国家的全球流量卡' : 'Coverage in 190+ countries',
                textLine3: isZh ? '立即购买享优惠>' : 'Buy Now with Discount>',
                htmlLink: '/pages/content?pageId=global-traveler-package',
                price: 49.99,
                originalPrice: 59.99,
                currency: ctx.currency,
                priority: 3,
            },
            {
                id: '4',
                type: 'tip',
                bannerImageUrl: image_util_1.ImageUtil.getBannerUrl('promotion', '004', lang),
                textLine1: isZh ? '境外流量使用技巧' : 'Overseas Data Tips',
                textLine2: isZh ? '5个实用技巧帮你控制数据使用' : '5 practical tips to control usage',
                textLine3: isZh ? '查看详细攻略>' : 'View Detailed Guide>',
                htmlLink: '/pages/content?pageId=data-usage-tips',
                priority: 4,
            },
        ];
        let filteredRecommendations = recommendations;
        if (query.type) {
            filteredRecommendations = recommendations.filter(rec => rec.type === query.type);
        }
        if (query.limit && query.limit > 0) {
            filteredRecommendations = filteredRecommendations.slice(0, query.limit);
        }
        return {
            recommendations: filteredRecommendations,
        };
    }
    async getNearbyGuides(query, ctx) {
        console.log('Context in getNearbyGuides:', ctx);
        const isZh = ctx.language.startsWith('zh');
        try {
            const whereConditions = {
                status: 'ACTIVE',
                off_shelve: false,
                OR: [
                    { name: { contains: '指南', mode: 'insensitive' } },
                    { name: { contains: 'guide', mode: 'insensitive' } },
                    { name: { contains: '本地', mode: 'insensitive' } },
                    { name: { contains: 'local', mode: 'insensitive' } },
                    { name: { contains: '城市', mode: 'insensitive' } },
                    { name: { contains: 'city', mode: 'insensitive' } },
                    { description: { contains: '指南', mode: 'insensitive' } },
                    { description: { contains: 'guide', mode: 'insensitive' } },
                    { description: { contains: '本地', mode: 'insensitive' } },
                    { description: { contains: 'local', mode: 'insensitive' } },
                ],
            };
            if (query.lat && query.lng) {
                whereConditions.AND = whereConditions.AND || [];
                whereConditions.AND.push({
                    OR: [
                        { country: { not: null } },
                        { countryCode: { not: null } },
                    ],
                });
            }
            const products = await this.prisma.product.findMany({
                where: whereConditions,
                include: {
                    category: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    reviews: {
                        select: {
                            rating: true,
                            comment: true,
                            user: {
                                select: {
                                    name: true,
                                    image: true,
                                },
                            },
                        },
                        orderBy: {
                            createdAt: 'desc',
                        },
                        take: 3,
                    },
                },
                take: query.limit || 10,
                orderBy: {
                    createdAt: 'desc',
                },
            });
            if (products.length === 0) {
                return {
                    guides: [],
                    context: {
                        language: ctx.language,
                        theme: ctx.theme,
                        currency: ctx.currency,
                    },
                };
            }
            const guides = products.map(product => this.formatProductAsGuide(product, ctx, isZh, query));
            const sortedGuides = guides.sort((a, b) => a.distance - b.distance);
            let filteredGuides = sortedGuides;
            if (query.radius) {
                filteredGuides = sortedGuides.filter(guide => guide.distance <= query.radius);
            }
            if (query.limit && query.limit > 0) {
                filteredGuides = filteredGuides.slice(0, query.limit);
            }
            return {
                guides: filteredGuides,
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
        catch (error) {
            this.logger.error('Error fetching nearby guides:', error);
            return {
                guides: [],
                context: {
                    language: ctx.language,
                    theme: ctx.theme,
                    currency: ctx.currency,
                },
            };
        }
    }
    formatProductAsTip(product, ctx, isZh) {
        const avgRating = product.reviews.length > 0
            ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
            : 0;
        let tags = [];
        let category = 'data-usage-guide';
        try {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            tags = specs?.tags || [];
            category = specs?.category || this.determineTipCategory(product.name, product.description);
        }
        catch (error) {
            this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
        }
        if (tags.length === 0) {
            tags = [
                isZh ? '使用指南' : 'usage-guide',
                isZh ? '技巧' : 'tips',
                product.category?.name || (isZh ? '通用' : 'general')
            ];
        }
        const summary = product.description.length > 100
            ? product.description.substring(0, 100) + '...'
            : product.description;
        return {
            id: product.id,
            title: product.name,
            summary: summary,
            category: category,
            imageUrl: product.images && product.images.length > 0
                ? product.images[0]
                : image_util_1.ImageUtil.getBannerUrl('tips', '001', isZh ? 'zh' : 'en'),
            content: product.websiteDescription || product.description,
            link: `/articles/${product.id}`,
            readCount: 0,
            publishDate: product.createdAt.toISOString(),
            tags: tags,
        };
    }
    formatProductAsGuide(product, ctx, isZh, query) {
        const avgRating = product.reviews.length > 0
            ? product.reviews.reduce((sum, review) => sum + review.rating, 0) / product.reviews.length
            : 0;
        const distance = this.calculateMockDistance(product.id, query.lat, query.lng);
        let tags = [];
        try {
            const specs = typeof product.specifications === 'string'
                ? JSON.parse(product.specifications)
                : product.specifications;
            tags = specs?.tags || [];
        }
        catch (error) {
            this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
        }
        if (tags.length === 0) {
            tags = [
                product.country || (isZh ? '本地' : 'local'),
                isZh ? '指南' : 'guide',
                product.category?.name || (isZh ? '通用' : 'general')
            ];
        }
        const summary = product.description.length > 80
            ? product.description.substring(0, 80) + '...'
            : product.description;
        return {
            id: product.id,
            title: product.name,
            summary: summary,
            imageUrl: product.images && product.images.length > 0
                ? product.images[0]
                : 'https://example.com/default-guide.jpg',
            content: product.websiteDescription || product.description,
            distance: distance,
            author: {
                id: '1',
                name: isZh ? '本地专家' : 'Local Expert',
                avatar: 'https://example.com/default-author.jpg',
            },
            publishDate: product.createdAt.toISOString(),
            readCount: Math.floor(avgRating * 100),
            likeCount: Math.floor(avgRating * 10),
            tags: tags,
        };
    }
    determineTipCategory(name, description) {
        const text = (name + ' ' + description).toLowerCase();
        if (text.includes('流量') || text.includes('data'))
            return 'data-usage-guide';
        if (text.includes('套餐') || text.includes('package'))
            return 'hot-packages';
        if (text.includes('5g'))
            return '5g-promotion';
        if (text.includes('特惠') || text.includes('special'))
            return 'weekly-special';
        return 'data-usage-guide';
    }
    calculateMockDistance(productId, lat, lng) {
        const hash = productId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        const baseDistance = (hash % 50) / 10;
        if (lat && lng) {
            const coordFactor = (Math.abs(lat) + Math.abs(lng)) % 10;
            return Math.round((baseDistance + coordFactor / 10) * 10) / 10;
        }
        return Math.round(baseDistance * 10) / 10;
    }
};
HomeService = HomeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], HomeService);
exports.HomeService = HomeService;
//# sourceMappingURL=home.service.js.map