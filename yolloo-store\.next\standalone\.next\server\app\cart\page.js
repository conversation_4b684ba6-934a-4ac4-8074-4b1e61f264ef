(()=>{var e={};e.id=1565,e.ids=[1565],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},59301:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(56826),s(89090),s(26083),s(35866);var r=s(23191),a=s(88716),n=s(37922),i=s.n(n),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["cart",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56826)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\cart\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\cart\\page.tsx"],u="/cart/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/cart/page",pathname:"/cart",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44899:(e,t,s)=>{Promise.resolve().then(s.bind(s,27770))},27770:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(10326),a=s(90434),n=s(90772),i=s(33071),l=s(46226),o=s(57372),c=s(79492),d=s(34789),u=s(17577),m=s(567),x=s(77863),p=s(54432);function h({item:e}){let{updateQuantity:t,removeItem:s}=(0,c.j)(),{toast:a}=(0,d.pm)(),[i,h]=(0,u.useState)(!1),[f,g]=(0,u.useState)(e.quantity.toString()),[v,j]=(0,u.useState)(!1);console.log("CartItem:",{item:e,hasVariant:!!e.variant,variantAttributes:e.variant?.attributes,uid:e.uid});let b=s=>{if(s>e.stock){a({title:"Cannot add more",description:`Only ${e.stock} items available in stock`,variant:"destructive"}),g(e.quantity.toString());return}if(s<1){g("1");return}console.log("Updating quantity:",{productId:e.productId,quantity:s,variantId:e.variant?.id,uid:e.uid}),t(e.productId,s,e.variant?.id,e.uid),g(s.toString())};return(0,r.jsxs)("div",{className:"flex gap-4 py-4",children:[r.jsx("div",{className:"relative aspect-square h-24 w-24 min-w-fit overflow-hidden rounded-lg border",children:e.image&&!i?r.jsx(l.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100px, 200px",priority:!0,onError:()=>h(!0)}):r.jsx("div",{className:"flex h-full w-full items-center justify-center bg-secondary",children:r.jsx(o.P.image,{className:"h-8 w-8 text-muted-foreground"})})}),(0,r.jsxs)("div",{className:"flex flex-1 flex-col gap-2",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium leading-none",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["$",e.price.toFixed(2)]}),(0,r.jsxs)("div",{className:"mt-2 flex flex-wrap gap-1.5",children:[e.variant?.duration&&e.variant?.durationType&&(0,r.jsxs)(m.C,{variant:"secondary",className:"capitalize",children:["Duration: ",e.variant.duration," ",e.variant.durationType]}),e.variant?.attributes&&Object.entries(e.variant.attributes).map(([e,t])=>(0,r.jsxs)(m.C,{variant:"secondary",className:"capitalize",children:[e.replace("_"," "),": ",t]},e)),e.uid&&(0,r.jsxs)(m.C,{variant:"outline",className:"border-primary/20 bg-primary/10 flex flex-col sm:flex-row sm:items-center px-2 py-1.5 h-auto",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(o.P.billing,{className:"mr-1 h-3 w-3 flex-shrink-0"}),r.jsx("span",{className:"font-medium",children:"UID:"})]}),r.jsx("div",{className:"mt-0.5 sm:mt-0 sm:ml-1 font-mono text-xs break-all",children:(0,x.QG)(e.uid)})]})]}),e.stock<=5&&(0,r.jsxs)("p",{className:"mt-1 text-xs text-red-500",children:["Only ",e.stock," left in stock"]})]}),(0,r.jsxs)(n.Button,{variant:"ghost",size:"icon",onClick:()=>{s(e.productId,e.variant?.id,e.uid),a({title:"Item removed",description:"The item has been removed from your cart",duration:2e3})},className:"hover:text-red-500",title:"Remove item",children:[r.jsx(o.P.close,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Remove"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(n.Button,{variant:"outline",size:"icon",className:"h-8 w-8",disabled:e.quantity<=1,onClick:()=>{b(e.quantity-1)},title:"Decrease quantity",children:[r.jsx(o.P.minus,{className:"h-3 w-3"}),r.jsx("span",{className:"sr-only",children:"Decrease quantity"})]}),r.jsx("div",{className:"flex h-8 w-16 items-center justify-center rounded-md border bg-background overflow-hidden",children:r.jsx(p.I,{type:"text",value:f,onChange:e=>{g(e.target.value.replace(/[^0-9]/g,""))},onBlur:()=>{let t=parseInt(f,10);isNaN(t)||""===f?(t=e.quantity,g(e.quantity.toString())):b(t),j(!1)},onKeyDown:e=>{"Enter"===e.key&&e.currentTarget.blur()},onClick:()=>j(!0),className:"h-full w-full border-0 text-center p-0 focus-visible:ring-0 focus-visible:ring-offset-0","aria-label":"Product quantity"})}),(0,r.jsxs)(n.Button,{variant:"outline",size:"icon",className:"h-8 w-8",disabled:e.quantity>=e.stock,onClick:()=>{b(e.quantity+1)},title:"Increase quantity",children:[r.jsx(o.P.add,{className:"h-3 w-3"}),r.jsx("span",{className:"sr-only",children:"Increase quantity"})]})]}),(0,r.jsxs)("p",{className:"ml-auto font-medium",children:["$",(e.price*e.quantity).toFixed(2)]})]})]})]})}function f(){let{items:e}=(0,c.j)(),t=e.reduce((e,t)=>e+t.price*t.quantity,0);return r.jsx("div",{className:"container py-8",children:(0,r.jsxs)("div",{className:"grid gap-8 lg:grid-cols-12",children:[r.jsx("div",{className:"lg:col-span-8",children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Shopping Cart"}),0===e.length?r.jsx(i.Zb,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"mb-4 text-muted-foreground",children:"Your cart is empty"}),r.jsx(n.Button,{asChild:!0,children:r.jsx(a.default,{href:"/products",children:"Continue Shopping"})})]})}):r.jsx("div",{className:"space-y-4",children:e.map(e=>r.jsx(i.Zb,{className:"p-4",children:r.jsx(h,{item:e})},e.id))})]})}),r.jsx("div",{className:"lg:col-span-4",children:(0,r.jsxs)(i.Zb,{className:"p-6",children:[r.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Order Summary"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between border-b pb-4",children:[r.jsx("span",{className:"text-muted-foreground",children:"Subtotal"}),r.jsx("span",{className:"font-medium",children:(0,x.T4)(t)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between border-b pb-4",children:[r.jsx("span",{className:"text-muted-foreground",children:"Shipping"}),r.jsx("span",{className:"font-medium",children:"Free"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"font-medium",children:"Total"}),r.jsx("span",{className:"font-bold",children:(0,x.T4)(t)})]}),0===e.length?r.jsx(n.Button,{className:"w-full",size:"lg",disabled:!0,children:"Proceed to Checkout"}):r.jsx(n.Button,{className:"w-full",size:"lg",asChild:!0,children:r.jsx(a.default,{href:"/checkout",children:"Proceed to Checkout"})})]})]})})]})})}},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var r=s(10326);s(17577);var a=s(79360),n=s(77863);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return r.jsx("div",{className:(0,n.cn)(i({variant:t}),e),...s})}},56826:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>l});var r=s(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\cart\page.tsx`),{__esModule:n,$$typeof:i}=a;a.default;let l=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\cart\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,1615,5772,7624,5634,6621,4824,3442],()=>s(59301));module.exports=r})();