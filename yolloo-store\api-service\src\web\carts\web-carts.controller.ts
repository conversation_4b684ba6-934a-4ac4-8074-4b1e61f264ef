import { <PERSON>, Get, Post, Patch, Delete, Body, Param, Res, HttpStatus, Req } from '@nestjs/common';
import { Request, Response } from 'express';
import { WebCartsService } from './web-carts.service';

@Controller('api/web/cart')
export class WebCartsController {
  constructor(private readonly webCartsService: WebCartsService) {}

  @Get()
  async getCart(@Req() req: Request, @Res() res: Response) {
    try {
      const user = req['user'] as any;
      if (!user) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          error: 'Unauthorized',
        });
      }

      const cart = await this.webCartsService.getCart(user.id);
      return res.json(cart);
    } catch (error) {
      console.error('[WEB_CART_GET]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to fetch cart',
      });
    }
  }

  @Post()
  async addToCart(
    @Body() addToCartDto: any,
    @Req() req: Request,
    @Res() res: Response
  ) {
    try {
      const user = req['user'] as any;
      if (!user) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          error: 'Unauthorized',
        });
      }

      const cartItem = await this.webCartsService.addToCart(user.id, addToCartDto);
      return res.status(HttpStatus.CREATED).json(cartItem);
    } catch (error) {
      console.error('[WEB_CART_ADD]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to add to cart',
      });
    }
  }

  @Patch(':cartItemId')
  async updateCartItem(
    @Param('cartItemId') cartItemId: string,
    @Body() updateCartItemDto: any,
    @Res() res: Response,
  ) {
    try {
      const cartItem = await this.webCartsService.updateCartItem(cartItemId, updateCartItemDto);
      return res.json(cartItem);
    } catch (error) {
      console.error('[WEB_CART_UPDATE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to update cart item',
      });
    }
  }

  @Delete(':cartItemId')
  async removeFromCart(
    @Param('cartItemId') cartItemId: string,
    @Res() res: Response,
  ) {
    try {
      await this.webCartsService.removeFromCart(cartItemId);
      return res.status(HttpStatus.NO_CONTENT).send();
    } catch (error) {
      console.error('[WEB_CART_REMOVE]', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        error: 'Failed to remove from cart',
      });
    }
  }
}
