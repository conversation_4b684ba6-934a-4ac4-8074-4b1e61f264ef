import { PrismaService } from '../../prisma.service';
export declare class WebCartsService {
    private prisma;
    constructor(prisma: PrismaService);
    getCart(userId: string): Promise<{
        items: ({
            product: import("@prisma/client/runtime").GetResult<{
                id: string;
                name: string;
                description: string;
                websiteDescription: string;
                price: number;
                images: string[];
                categoryId: string;
                stock: number;
                specifications: import(".prisma/client").Prisma.JsonValue;
                status: import(".prisma/client").ProductStatus;
                sku: string;
                requiredUID: boolean;
                createdAt: Date;
                updatedAt: Date;
                mcc: string | null;
                off_shelve: boolean;
                dataSize: number | null;
                planType: string | null;
                country: string | null;
                countryCode: string | null;
                odooLastSyncAt: Date | null;
                popularityScore: number | null;
                isPopular: boolean;
            }, unknown> & {};
            variant: (import("@prisma/client/runtime").GetResult<{
                id: string;
                price: import("@prisma/client/runtime").Decimal;
                currency: string;
                productId: string;
                variantCode: string | null;
                duration: number | null;
                durationType: string | null;
                attributes: import(".prisma/client").Prisma.JsonValue;
                createdAt: Date;
                updatedAt: Date;
            }, unknown> & {}) | null;
        } & import("@prisma/client/runtime").GetResult<{
            id: string;
            userId: string;
            productId: string;
            variantId: string | null;
            quantity: number;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {})[];
        total: number;
        count: number;
    }>;
    addToCart(userId: string, addToCartDto: any): Promise<{
        product: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string;
            websiteDescription: string;
            price: number;
            images: string[];
            categoryId: string;
            stock: number;
            specifications: import(".prisma/client").Prisma.JsonValue;
            status: import(".prisma/client").ProductStatus;
            sku: string;
            requiredUID: boolean;
            createdAt: Date;
            updatedAt: Date;
            mcc: string | null;
            off_shelve: boolean;
            dataSize: number | null;
            planType: string | null;
            country: string | null;
            countryCode: string | null;
            odooLastSyncAt: Date | null;
            popularityScore: number | null;
            isPopular: boolean;
        }, unknown> & {};
        variant: (import("@prisma/client/runtime").GetResult<{
            id: string;
            price: import("@prisma/client/runtime").Decimal;
            currency: string;
            productId: string;
            variantCode: string | null;
            duration: number | null;
            durationType: string | null;
            attributes: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {}) | null;
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        productId: string;
        variantId: string | null;
        quantity: number;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    updateCartItem(cartItemId: string, updateCartItemDto: any): Promise<{
        product: import("@prisma/client/runtime").GetResult<{
            id: string;
            name: string;
            description: string;
            websiteDescription: string;
            price: number;
            images: string[];
            categoryId: string;
            stock: number;
            specifications: import(".prisma/client").Prisma.JsonValue;
            status: import(".prisma/client").ProductStatus;
            sku: string;
            requiredUID: boolean;
            createdAt: Date;
            updatedAt: Date;
            mcc: string | null;
            off_shelve: boolean;
            dataSize: number | null;
            planType: string | null;
            country: string | null;
            countryCode: string | null;
            odooLastSyncAt: Date | null;
            popularityScore: number | null;
            isPopular: boolean;
        }, unknown> & {};
        variant: (import("@prisma/client/runtime").GetResult<{
            id: string;
            price: import("@prisma/client/runtime").Decimal;
            currency: string;
            productId: string;
            variantCode: string | null;
            duration: number | null;
            durationType: string | null;
            attributes: import(".prisma/client").Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
        }, unknown> & {}) | null;
    } & import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        productId: string;
        variantId: string | null;
        quantity: number;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
    removeFromCart(cartItemId: string): Promise<import("@prisma/client/runtime").GetResult<{
        id: string;
        userId: string;
        productId: string;
        variantId: string | null;
        quantity: number;
        createdAt: Date;
        updatedAt: Date;
    }, unknown> & {}>;
}
