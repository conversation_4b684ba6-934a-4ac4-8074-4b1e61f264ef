/**
 * RequestContext interface defines the structure of the context object
 * that is attached to each request by the ContextMiddleware.
 * It contains information about the client's preferences such as
 * language, theme, and currency.
 */
export interface RequestContext {
  /**
   * The preferred language of the client.
   * Format: ISO 639-1 language code with optional ISO 3166-1 country code
   * Example: 'en-US', 'zh-CN', 'fr'
   * Default: 'en-US'
   */
  language: string;

  /**
   * The preferred theme of the client.
   * Possible values: 'light', 'dark'
   * Default: 'light'
   */
  theme: string;

  /**
   * The preferred currency for displaying prices.
   * Format: ISO 4217 currency code
   * Example: 'USD', 'EUR', 'CNY'
   * Default: 'USD'
   */
  currency: string;
}
