"use strict";(()=>{var e={};e.id=1225,e.ids=[1225],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},63413:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>O,patchFetch:()=>v,requestAsyncStorage:()=>w,routeModule:()=>y,serverHooks:()=>E,staticGenerationAsyncStorage:()=>S});var a={};t.r(a),t.d(a,{GET:()=>f,POST:()=>h,dynamic:()=>p,fetchCache:()=>m,revalidate:()=>g});var n=t(49303),s=t(88716),o=t(60670),i=t(87070),l=t(73826),u=t(75571),c=t(90455),d=t(72331);let p="force-dynamic",m="force-no-store",g=0;async function f(e){try{let r=await (0,u.getServerSession)(c.L);if(!r?.user?.id)return i.NextResponse.json({error:"Unauthorized"},{status:401});let t=e.nextUrl.searchParams,a=t.get("cardId"),n=t.get("orderSn"),s=t.get("action");if(!a&&!n)return i.NextResponse.json({resultCode:"400",resultMsg:"Card ID or Order SN is required",data:null},{status:400});if(n){let e;let r=null;try{switch(s){case"queryUsage":e=await l.L.queryUsageOrderPlan(n);break;case"queryDetails":e=await l.L.queryOrderPlan(n);break;default:return i.NextResponse.json({resultCode:"400",resultMsg:"Invalid action",data:null},{status:400})}}catch(t){console.error(`Error executing Boss service action ${s}:`,t),r={code:"API_ERROR",message:t instanceof Error?t.message:"Error communicating with Boss service"},e={resultCode:"10000",resultMsg:"Error occurred but returning partial data",data:{hasError:!0,errorDetails:r}}}return i.NextResponse.json(e)}let o=await d._.yollooCard.findFirst({where:{AND:[{id:a},{userId:r.user.id}]}});if(!o)return i.NextResponse.json({resultCode:"404",resultMsg:"Card not found",data:null},{status:404});let p=o.number,m=[],g=0,f=!0,h=null;try{for(;f;){let e={pageNum:g,pageSize:100,uid:p,queryParam:{uid:p}},r=await l.L.getDeviceOrderPage(e);"10000"===r.resultCode&&r.data&&r.data.list?(m=[...m,...r.data.list],r.data.list.length<100?f=!1:g++):(h={code:r.resultCode||"UNKNOWN",message:r.resultMsg||"Unknown error from Boss service"},f=!1)}}catch(e){console.error("Error during Boss service pagination:",e),h={code:"API_ERROR",message:e instanceof Error?e.message:"Error communicating with Boss service"}}let y={resultCode:"10000",resultMsg:h?"Partial data available":"Success",data:{list:m,total:m.length,hasError:!!h,errorDetails:h}};return i.NextResponse.json(y)}catch(e){return console.error("Error fetching packages from Boss service:",e),i.NextResponse.json({resultCode:"500",resultMsg:"Failed to fetch packages",data:null},{status:500})}}async function h(e){try{let r;let t=await (0,u.getServerSession)(c.L);if(!t?.user?.id)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{action:a,orderSn:n,cardId:s,uid:o}=await e.json();if(!a||!n)return i.NextResponse.json({resultCode:"400",resultMsg:"Action and Order SN are required",data:null},{status:400});if(s&&!await d._.yollooCard.findFirst({where:{AND:[{id:s},{userId:t.user.id}]}}))return i.NextResponse.json({resultCode:"404",resultMsg:"Card not found",data:null},{status:404});try{switch(a){case"closeOrderPlan":r=await l.L.closeOrderPlan(n);break;case"toppingOrderPlan":if(!o)return i.NextResponse.json({resultCode:"400",resultMsg:"UID is required for toppingOrderPlan",data:null},{status:400});r=await l.L.toppingOrderPlan(n,o);break;default:return i.NextResponse.json({resultCode:"400",resultMsg:"Invalid action",data:null},{status:400})}return"10000"!==r.resultCode&&console.warn(`Boss service action ${a} returned non-success code:`,r.resultCode,r.resultMsg),i.NextResponse.json(r)}catch(e){return console.error(`Error executing Boss service action ${a}:`,e),i.NextResponse.json({resultCode:"500",resultMsg:e instanceof Error?e.message:"Error communicating with Boss service",data:{action:a,orderSn:n,error:!0,errorDetails:{message:e instanceof Error?e.message:"Unknown error occurred"}}})}}catch(e){return console.error("Error performing action on package:",e),i.NextResponse.json({resultCode:"500",resultMsg:"Failed to perform action",data:null},{status:500})}}let y=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/boss/packages/route",pathname:"/api/boss/packages",filename:"route",bundlePath:"app/api/boss/packages/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\boss\\packages\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:S,serverHooks:E}=y,O="/api/boss/packages/route";function v(){return(0,o.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:S})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var a=t(7585),n=t(72331),s=t(77234),o=t(53797),i=t(42023),l=t.n(i),u=t(93475);let c={adapter:{...(0,a.N)(n._),getUser:async e=>{let r=await n._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await n._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await n._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await n._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await n._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await n._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await n._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,s.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:s}){try{if(t&&t.id){let r=s?.headers||new Headers,o=r.get("user-agent")||"",i=r.get("x-forwarded-for"),l=i?i.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";a?u=a.code&&!a.password?"email_code":"password":e&&(u=e.provider),await n._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,n=new URL(a).searchParams.get("callbackUrl");if(n){let e=decodeURIComponent(n);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let s=await n._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return s?{id:s.id,name:s.name,email:s.email,picture:s.image,role:s.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},73826:(e,r,t)=>{t.d(r,{L:()=>l});var a=t(29712),n=t(6113),s=t(50650);class o{constructor(e){this.config=e,this.client=a.Z.create({baseURL:e.baseUrl,headers:{"Content-Type":"application/json"}})}generateNonce(e=16){let r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t="";for(let a=0;a<e;a++)t+=r.charAt(Math.floor(Math.random()*r.length));return t}generateSignature(e,r,t,a){let o=e+r+t;console.log("[BOSS_SERVICE] Generating signature",{timestamp:s.CN.iso(new Date),components:{deviceApp:e,nonce:r,timestamp:t},concatenatedMessage:o,secretKey:a});let i=(0,n.createHmac)("sha256",a).update(o).digest("hex");return console.log("[BOSS_SERVICE] Generated signature:",{signature:i,algorithm:"HmacSHA256",encoding:"hex",debug:{inputMessage:o,hexSignature:i}}),i}getAuthHeaders(){let e=Date.now().toString(),r=this.generateNonce(),t=this.generateSignature(this.config.deviceApp,r,e,this.config.appSecret);return{APP:this.config.deviceApp,TIMESTAMP:e,NONCE:r,SIGN:t}}async request(e,r,t){let n=t||{},o=this.getAuthHeaders(),i=Date.now();console.log(`[BOSS_SERVICE] Starting ${e} request`,{url:this.config.baseUrl+r,method:e,timestamp:s.CN.iso(new Date),requestData:n,headers:o});try{let t=await this.client.request({method:e,url:r,data:n,headers:{...o,"Content-Type":"application/json"}}),a=Date.now();return console.log("[BOSS_SERVICE] Request completed successfully",{url:this.config.baseUrl+r,method:e,statusCode:t.status,duration:`${a-i}ms`,timestamp:s.CN.iso(new Date),responseData:t.data}),t.data}catch(n){let t=Date.now();throw console.error("[BOSS_SERVICE] Request failed",{url:this.config.baseUrl+r,method:e,duration:`${t-i}ms`,timestamp:s.CN.iso(new Date),error:a.Z.isAxiosError(n)?{status:n.response?.status,statusText:n.response?.statusText,data:n.response?.data,headers:n.response?.headers}:n}),n}}async getDeviceOrderPage(e){return console.log("[BOSS_SERVICE] Getting device order page",{timestamp:s.CN.iso(new Date),params:e}),this.request("POST","/open/device/order/page",e)}async createProductOrder(e){return console.log("[BOSS_SERVICE] Creating product order",{timestamp:s.CN.iso(new Date),params:e}),this.request("POST","/open/order/plan/createProductOrder",e)}async createRpmOrder(e){return console.log("[BOSS_SERVICE] Creating RPM order",{timestamp:s.CN.iso(new Date),params:e}),this.request("POST","/open/order/plan/createRpmOrder",e)}async cancelOrderPlan(e){return console.log("[BOSS_SERVICE] Cancelling order plan",{timestamp:s.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/cancelOrderPlan",{orderSn:e})}async closeOrderPlan(e){return console.log("[BOSS_SERVICE] Closing order plan",{timestamp:s.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/closeOrderPlan",{orderSn:e})}async queryUsageOrderPlan(e){return console.log("[BOSS_SERVICE] Querying order plan usage",{timestamp:s.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/qryusageOrderPlan",{orderSn:e})}async queryOrderPlan(e){return console.log("[BOSS_SERVICE] Querying order plan details",{timestamp:s.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/queryOrderPlan",{orderSn:e})}async toppingOrderPlan(e,r){return console.log("[BOSS_SERVICE] Setting order plan as top",{timestamp:s.CN.iso(new Date),orderSn:e,uid:r}),this.request("POST","/open/order/plan/toppingOrderPlan",{orderSn:e,uid:r})}}let i={baseUrl:process.env.BOSS_API_URL||"",deviceApp:process.env.BOSS_APP||"ea83c1581b2e9e975423d55098e70cbe",appSecret:process.env.BOSS_APP_SECRET||"3hxb08a3pn1kld3zla8vkiwuip98z5uz"};if(!process.env.BOSS_API_URL)throw Error("BOSS_API_URL is not defined");if(!process.env.BOSS_APP)throw Error("BOSS_APP is not defined");if(!process.env.BOSS_APP_SECRET)throw Error("BOSS_APP_SECRET is not defined");let l=new o(i)},72331:(e,r,t)=>{t.d(r,{_:()=>n});var a=t(53524);let n=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>i,Ak:()=>l,qc:()=>u,yz:()=>c});var a=t(62197),n=t.n(a);let s=null;function o(){if(!s){let e=process.env.REDIS_URL||"redis://localhost:6379";(s=new(n())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),s.on("connect",()=>{console.log("Successfully connected to Redis")})}return s}async function i(e,r,t=300){try{let a=o(),n=`verification_code:${e}`;return await a.setex(n,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let a=o(),n=`rate_limit:${e}`,s=await a.get(n),i=s?parseInt(s):0;if(i>=r)return!1;return 0===i?await a.setex(n,t,"1"):await a.incr(n),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},50650:(e,r,t)=>{t.d(r,{CN:()=>g,ED:()=>f,QG:()=>y,T4:()=>c,cn:()=>u,eP:()=>S,mo:()=>w,vI:()=>h});var a=t(55761),n=t(62386),s=t(6180),o=t(4284),i=t(35772),l=t(21740);function u(...e){return(0,n.m6)((0,a.W)(e))}function c(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let d={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function p(){return d.TIMEZONE}function m(e){if(!e)return null;try{let r;if(e instanceof Date)r=e;else if("string"==typeof e)r=e.includes("T")||e.includes("Z")?(0,s.D)(e):new Date(e);else{if("number"!=typeof e)return null;r=new Date(e)}return(0,o.J)(r)?r:null}catch(r){return console.warn("Date parsing error:",r,"Input:",e),null}}let g={short:(e,r="Invalid Date")=>{let t=m(e);return t?(0,i.WU)(t,d.DATE_FORMAT):r},full:(e,r="Invalid Date")=>{let t=m(e);return t?(0,i.WU)(t,d.DATETIME_FORMAT):r},long:(e,r="Invalid Date")=>{let t=m(e);return t?t.toLocaleDateString(d.LOCALE,{year:"numeric",month:"long",day:"numeric"}):r},relative:(e,r="Invalid Date")=>{let t=m(e);return t?(0,l.Q)(t,{addSuffix:!0}):r},time:(e,r="Invalid Time")=>{let t=m(e);return t?(0,i.WU)(t,"HH:mm:ss"):r},timeShort:(e,r="Invalid Time")=>{let t=m(e);return t?(0,i.WU)(t,"HH:mm"):r},withTimezone:(e,r,t="Invalid Date")=>{let a=m(e);return a?new Intl.DateTimeFormat(d.LOCALE,{timeZone:r||d.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(a):t},forUser:(e,r="Invalid Date")=>{let t=m(e);if(!t)return r;let a=p();return new Intl.DateTimeFormat(d.LOCALE,{timeZone:a,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(t)},forUserShort:(e,r="Invalid Date")=>{let t=m(e);if(!t)return r;let a=p();return new Intl.DateTimeFormat(d.LOCALE,{timeZone:a,year:"numeric",month:"2-digit",day:"2-digit"}).format(t)},forUserSafe:(e,r="Invalid Date")=>{let t=m(e);if(!t)return r;let a=d.TIMEZONE;return new Intl.DateTimeFormat(d.LOCALE,{timeZone:a,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(t)},forUserRelative:(e,r="Invalid Date")=>{let t=m(e);return t?(0,l.Q)(t,{addSuffix:!0}):r},iso:(e,r="")=>{let t=m(e);return t?t.toISOString():r},custom:(e,r,t="Invalid Date")=>{let a=m(e);return a?(0,i.WU)(a,r):t}},f={addDays:(e,r)=>new Date((m(e)||new Date).getTime()+864e5*r),addHours:(e,r)=>new Date((m(e)||new Date).getTime()+36e5*r),addMinutes:(e,r)=>new Date((m(e)||new Date).getTime()+6e4*r),daysBetween:(e,r)=>{let t=m(e),a=m(r);return t&&a?Math.floor((a.getTime()-t.getTime())/864e5):0},isExpired:e=>{let r=m(e);return!!r&&r.getTime()<Date.now()}};function h(e){return e?e.replace(/\D/g,""):""}function y(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function w(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function S(e){let r=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return r?{badge:r[1].toUpperCase(),name:r[2]}:{name:e}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s.default}});var n=t(69955);Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))});var s=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var i=n?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(a,s,i):a[s]=e[s]}return a.default=e,t&&t.set(e,a),a}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===s[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return s[e]}}))})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7624,9712],()=>t(63413));module.exports=a})();