"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebAdminService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma.service");
const bcrypt = require("bcrypt");
let WebAdminService = class WebAdminService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getDashboardData() {
        const [totalUsers, totalOrders, totalProducts, recentOrders, userStats, orderStats,] = await Promise.all([
            this.prisma.user.count(),
            this.prisma.order.count(),
            this.prisma.product.count(),
            this.prisma.order.findMany({
                take: 10,
                orderBy: { createdAt: 'desc' },
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                    items: true,
                },
            }),
            this.prisma.user.groupBy({
                by: ['role'],
                _count: true,
            }),
            this.prisma.order.groupBy({
                by: ['status'],
                _count: true,
            }),
        ]);
        return {
            stats: {
                totalUsers,
                totalOrders,
                totalProducts,
            },
            recentOrders,
            userStats,
            orderStats,
        };
    }
    async getUsers(query) {
        const { page = 1, limit = 20, search = '', role = 'all' } = query;
        const skip = (page - 1) * limit;
        const whereCondition = {};
        if (search) {
            whereCondition.OR = [
                { name: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } },
            ];
        }
        if (role !== 'all') {
            whereCondition.role = role;
        }
        const [users, total] = await Promise.all([
            this.prisma.user.findMany({
                where: whereCondition,
                skip,
                take: parseInt(limit),
                select: {
                    id: true,
                    name: true,
                    email: true,
                    role: true,
                    image: true,
                    createdAt: true,
                    _count: {
                        select: {
                            orders: true,
                            reviews: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.user.count({ where: whereCondition }),
        ]);
        return {
            users,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getUserById(userId) {
        return await this.prisma.user.findUnique({
            where: { id: userId },
            include: {
                orders: {
                    orderBy: { createdAt: 'desc' },
                    take: 10,
                    include: {
                        items: true,
                        payment: true,
                    },
                },
                addresses: true,
                _count: {
                    select: {
                        orders: true,
                        reviews: true,
                        wishlist: true,
                    },
                },
            },
        });
    }
    async createUser(createUserDto) {
        const { name, email, password, role = 'CUSTOMER' } = createUserDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            throw new Error('Email already exists');
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const user = await this.prisma.user.create({
            data: {
                name,
                email,
                hashedPassword,
                role,
            },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                createdAt: true,
            },
        });
        return user;
    }
    async updateUser(userId, updateUserDto) {
        const { password, ...updateData } = updateUserDto;
        if (password) {
            updateData.hashedPassword = await bcrypt.hash(password, 10);
        }
        return await this.prisma.user.update({
            where: { id: userId },
            data: updateData,
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                image: true,
                createdAt: true,
                updatedAt: true,
            },
        });
    }
    async deleteUser(userId) {
        return await this.prisma.user.delete({
            where: { id: userId },
        });
    }
    async getOrders(query) {
        const { page = 1, limit = 20, status = 'all', search = '' } = query;
        const skip = (page - 1) * limit;
        const whereCondition = {};
        if (status !== 'all') {
            whereCondition.status = status;
        }
        if (search) {
            whereCondition.OR = [
                { id: { contains: search, mode: 'insensitive' } },
                { user: { email: { contains: search, mode: 'insensitive' } } },
            ];
        }
        const [orders, total] = await Promise.all([
            this.prisma.order.findMany({
                where: whereCondition,
                skip,
                take: parseInt(limit),
                include: {
                    user: {
                        select: {
                            id: true,
                            name: true,
                            email: true,
                        },
                    },
                    items: true,
                    payment: true,
                },
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.order.count({ where: whereCondition }),
        ]);
        return {
            orders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
    async getProducts(query) {
        const { page = 1, limit = 20, category = 'all', status = 'all' } = query;
        const skip = (page - 1) * limit;
        const whereCondition = {};
        if (category !== 'all') {
            whereCondition.categoryId = category;
        }
        if (status !== 'all') {
            whereCondition.status = status;
        }
        const [products, total] = await Promise.all([
            this.prisma.product.findMany({
                where: whereCondition,
                skip,
                take: parseInt(limit),
                include: {
                    category: true,
                    variants: true,
                    _count: {
                        select: {
                            reviews: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
            }),
            this.prisma.product.count({ where: whereCondition }),
        ]);
        return {
            products,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit),
            },
        };
    }
};
WebAdminService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], WebAdminService);
exports.WebAdminService = WebAdminService;
//# sourceMappingURL=web-admin.service.js.map