"use strict";(()=>{var e={};e.id=4189,e.ids=[4189],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},76901:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>O,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>S,staticGenerationAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{GET:()=>c,POST:()=>m,dynamic:()=>u,fetchCache:()=>d,revalidate:()=>p});var a=r(49303),o=r(88716),s=r(60670),i=r(87070),l=r(73826);let u="force-dynamic",d="force-no-store",p=0;async function c(e){try{let t=e.nextUrl.searchParams,r=parseInt(t.get("pageNum")||"0"),n=parseInt(t.get("pageSize")||"10"),a=t.get("uid"),o=t.get("orderSn"),s=t.get("packageStatus")?parseInt(t.get("packageStatus")):void 0,u=t.get("esimProfileStatus"),d={pageNum:r,pageSize:n,uid:a,queryParam:{...a&&{uid:a},...o&&{orderSn:o},...void 0!==s&&{packageStatus:s},...u&&{esimProfileStatus:u}}},p=await l.L.getDeviceOrderPage(d);return i.NextResponse.json(p)}catch(e){return console.error("Error fetching order data from Boss service:",e),i.NextResponse.json({resultCode:"500",resultMsg:"Failed to fetch order data",data:null},{status:500})}}async function m(e){try{let t;let{action:r,...n}=await e.json();switch(r){case"createProductOrder":t=await l.L.createProductOrder(n);break;case"createRpmOrder":t=await l.L.createRpmOrder(n);break;case"cancelOrderPlan":t=await l.L.cancelOrderPlan(n.orderSn);break;case"closeOrderPlan":t=await l.L.closeOrderPlan(n.orderSn);break;case"queryUsageOrderPlan":t=await l.L.queryUsageOrderPlan(n.orderSn);break;case"queryOrderPlan":t=await l.L.queryOrderPlan(n.orderSn);break;case"toppingOrderPlan":t=await l.L.toppingOrderPlan(n.orderSn,n.uid);break;default:return i.NextResponse.json({resultCode:"400",resultMsg:"Invalid action",data:null},{status:400})}return i.NextResponse.json(t)}catch(e){return console.error("Error processing order operation:",e),i.NextResponse.json({resultCode:"500",resultMsg:"Failed to process order operation",data:null},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/boss/orders/route",pathname:"/api/boss/orders",filename:"route",bundlePath:"app/api/boss/orders/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\boss\\orders\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:h,staticGenerationAsyncStorage:f,serverHooks:S}=g,y="/api/boss/orders/route";function O(){return(0,s.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:f})}},73826:(e,t,r)=>{r.d(t,{L:()=>l});var n=r(29712),a=r(6113),o=r(50650);class s{constructor(e){this.config=e,this.client=n.Z.create({baseURL:e.baseUrl,headers:{"Content-Type":"application/json"}})}generateNonce(e=16){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let n=0;n<e;n++)r+=t.charAt(Math.floor(Math.random()*t.length));return r}generateSignature(e,t,r,n){let s=e+t+r;console.log("[BOSS_SERVICE] Generating signature",{timestamp:o.CN.iso(new Date),components:{deviceApp:e,nonce:t,timestamp:r},concatenatedMessage:s,secretKey:n});let i=(0,a.createHmac)("sha256",n).update(s).digest("hex");return console.log("[BOSS_SERVICE] Generated signature:",{signature:i,algorithm:"HmacSHA256",encoding:"hex",debug:{inputMessage:s,hexSignature:i}}),i}getAuthHeaders(){let e=Date.now().toString(),t=this.generateNonce(),r=this.generateSignature(this.config.deviceApp,t,e,this.config.appSecret);return{APP:this.config.deviceApp,TIMESTAMP:e,NONCE:t,SIGN:r}}async request(e,t,r){let a=r||{},s=this.getAuthHeaders(),i=Date.now();console.log(`[BOSS_SERVICE] Starting ${e} request`,{url:this.config.baseUrl+t,method:e,timestamp:o.CN.iso(new Date),requestData:a,headers:s});try{let r=await this.client.request({method:e,url:t,data:a,headers:{...s,"Content-Type":"application/json"}}),n=Date.now();return console.log("[BOSS_SERVICE] Request completed successfully",{url:this.config.baseUrl+t,method:e,statusCode:r.status,duration:`${n-i}ms`,timestamp:o.CN.iso(new Date),responseData:r.data}),r.data}catch(a){let r=Date.now();throw console.error("[BOSS_SERVICE] Request failed",{url:this.config.baseUrl+t,method:e,duration:`${r-i}ms`,timestamp:o.CN.iso(new Date),error:n.Z.isAxiosError(a)?{status:a.response?.status,statusText:a.response?.statusText,data:a.response?.data,headers:a.response?.headers}:a}),a}}async getDeviceOrderPage(e){return console.log("[BOSS_SERVICE] Getting device order page",{timestamp:o.CN.iso(new Date),params:e}),this.request("POST","/open/device/order/page",e)}async createProductOrder(e){return console.log("[BOSS_SERVICE] Creating product order",{timestamp:o.CN.iso(new Date),params:e}),this.request("POST","/open/order/plan/createProductOrder",e)}async createRpmOrder(e){return console.log("[BOSS_SERVICE] Creating RPM order",{timestamp:o.CN.iso(new Date),params:e}),this.request("POST","/open/order/plan/createRpmOrder",e)}async cancelOrderPlan(e){return console.log("[BOSS_SERVICE] Cancelling order plan",{timestamp:o.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/cancelOrderPlan",{orderSn:e})}async closeOrderPlan(e){return console.log("[BOSS_SERVICE] Closing order plan",{timestamp:o.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/closeOrderPlan",{orderSn:e})}async queryUsageOrderPlan(e){return console.log("[BOSS_SERVICE] Querying order plan usage",{timestamp:o.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/qryusageOrderPlan",{orderSn:e})}async queryOrderPlan(e){return console.log("[BOSS_SERVICE] Querying order plan details",{timestamp:o.CN.iso(new Date),orderSn:e}),this.request("POST","/open/order/plan/queryOrderPlan",{orderSn:e})}async toppingOrderPlan(e,t){return console.log("[BOSS_SERVICE] Setting order plan as top",{timestamp:o.CN.iso(new Date),orderSn:e,uid:t}),this.request("POST","/open/order/plan/toppingOrderPlan",{orderSn:e,uid:t})}}let i={baseUrl:process.env.BOSS_API_URL||"",deviceApp:process.env.BOSS_APP||"ea83c1581b2e9e975423d55098e70cbe",appSecret:process.env.BOSS_APP_SECRET||"3hxb08a3pn1kld3zla8vkiwuip98z5uz"};if(!process.env.BOSS_API_URL)throw Error("BOSS_API_URL is not defined");if(!process.env.BOSS_APP)throw Error("BOSS_APP is not defined");if(!process.env.BOSS_APP_SECRET)throw Error("BOSS_APP_SECRET is not defined");let l=new s(i)},50650:(e,t,r)=>{r.d(t,{CN:()=>g,ED:()=>h,QG:()=>S,T4:()=>d,cn:()=>u,eP:()=>O,mo:()=>y,vI:()=>f});var n=r(55761),a=r(62386),o=r(6180),s=r(4284),i=r(35772),l=r(21740);function u(...e){return(0,a.m6)((0,n.W)(e))}function d(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let p={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function c(){return p.TIMEZONE}function m(e){if(!e)return null;try{let t;if(e instanceof Date)t=e;else if("string"==typeof e)t=e.includes("T")||e.includes("Z")?(0,o.D)(e):new Date(e);else{if("number"!=typeof e)return null;t=new Date(e)}return(0,s.J)(t)?t:null}catch(t){return console.warn("Date parsing error:",t,"Input:",e),null}}let g={short:(e,t="Invalid Date")=>{let r=m(e);return r?(0,i.WU)(r,p.DATE_FORMAT):t},full:(e,t="Invalid Date")=>{let r=m(e);return r?(0,i.WU)(r,p.DATETIME_FORMAT):t},long:(e,t="Invalid Date")=>{let r=m(e);return r?r.toLocaleDateString(p.LOCALE,{year:"numeric",month:"long",day:"numeric"}):t},relative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,l.Q)(r,{addSuffix:!0}):t},time:(e,t="Invalid Time")=>{let r=m(e);return r?(0,i.WU)(r,"HH:mm:ss"):t},timeShort:(e,t="Invalid Time")=>{let r=m(e);return r?(0,i.WU)(r,"HH:mm"):t},withTimezone:(e,t,r="Invalid Date")=>{let n=m(e);return n?new Intl.DateTimeFormat(p.LOCALE,{timeZone:t||p.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(n):r},forUser:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=c();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserShort:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=c();return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit"}).format(r)},forUserSafe:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=p.TIMEZONE;return new Intl.DateTimeFormat(p.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserRelative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,l.Q)(r,{addSuffix:!0}):t},iso:(e,t="")=>{let r=m(e);return r?r.toISOString():t},custom:(e,t,r="Invalid Date")=>{let n=m(e);return n?(0,i.WU)(n,t):r}},h={addDays:(e,t)=>new Date((m(e)||new Date).getTime()+864e5*t),addHours:(e,t)=>new Date((m(e)||new Date).getTime()+36e5*t),addMinutes:(e,t)=>new Date((m(e)||new Date).getTime()+6e4*t),daysBetween:(e,t)=>{let r=m(e),n=m(t);return r&&n?Math.floor((n.getTime()-r.getTime())/864e5):0},isExpired:e=>{let t=m(e);return!!t&&t.getTime()<Date.now()}};function f(e){return e?e.replace(/\D/g,""):""}function S(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function y(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function O(e){let t=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return t?{badge:t[1].toUpperCase(),name:t[2]}:{name:e}}},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,o={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function i(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,a]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=a?a:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,a],...o]=i(e),{domain:s,expires:l,httponly:p,maxage:c,path:m,samesite:g,secure:h,partitioned:f,priority:S}=Object.fromEntries(o.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:n,value:decodeURIComponent(a),domain:s,...l&&{expires:new Date(l)},...p&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:m,...g&&{sameSite:u.includes(t=(t=g).toLowerCase())?t:void 0},...h&&{secure:!0},...S&&{priority:d.includes(r=(r=S).toLowerCase())?r:void 0},...f&&{partitioned:!0}})}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(o,{RequestCookies:()=>p,ResponseCookies:()=>c,parseCookie:()=>i,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,o,s,i)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let s of n(o))a.call(e,s)||void 0===s||t(e,s,{get:()=>o[s],enumerable:!(i=r(o,s))||i.enumerable});return e})(t({},"__esModule",{value:!0}),o);var u=["strict","lax","none"],d=["low","medium","high"],p=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of i(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},c=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let a=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(a)?a:function(e){if(!e)return[];var t,r,n,a,o,s=[],i=0;function l(){for(;i<e.length&&/\s/.test(e.charAt(i));)i+=1;return i<e.length}for(;i<e.length;){for(t=i,o=!1;l();)if(","===(r=e.charAt(i))){for(n=i,i+=1,l(),a=i;i<e.length&&"="!==(r=e.charAt(i))&&";"!==r&&","!==r;)i+=1;i<e.length&&"="===e.charAt(i)?(o=!0,i=a,s.push(e.substring(t,n)),t=i):i=n+1}else i+=1;(!o||i>=e.length)&&s.push(e.substring(t,e.length))}return s}(a)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,a=this._parsed;return a.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(a,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return n.RequestCookies},ResponseCookies:function(){return n.ResponseCookies}});let n=r(79925)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,9092,5972,5772,7624,9712],()=>r(76901));module.exports=n})();