/// <reference types="cookie-parser" />
import { Request, Response } from 'express';
import { WebAuthService } from './web-auth.service';
import { AuthService } from '../../auth/auth.service';
export declare class WebAuthController {
    private readonly webAuthService;
    private readonly authService;
    constructor(webAuthService: WebAuthService, authService: AuthService);
    login(loginDto: {
        email: string;
        password: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    refresh(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    logout(res: Response): Promise<Response<any, Record<string, any>>>;
    getCurrentUser(req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    signup(signupDto: {
        name: string;
        email: string;
        password: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    forgotPassword(body: {
        email: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    resetPassword(body: {
        token: string;
        password: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    sendCode(body: {
        email: string;
        type: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    verifyCode(body: {
        email: string;
        code: string;
        type: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    changePassword(body: {
        currentPassword: string;
        newPassword: string;
    }, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
