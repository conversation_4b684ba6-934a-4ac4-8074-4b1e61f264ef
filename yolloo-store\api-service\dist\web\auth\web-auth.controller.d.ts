import { Request, Response } from 'express';
import { WebAuthService } from './web-auth.service';
export declare class WebAuthController {
    private readonly webAuthService;
    constructor(webAuthService: WebAuthService);
    signup(signupDto: {
        name: string;
        email: string;
        password: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    forgotPassword(body: {
        email: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    resetPassword(body: {
        token: string;
        password: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    sendCode(body: {
        email: string;
        type: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    verifyCode(body: {
        email: string;
        code: string;
        type: string;
    }, res: Response): Promise<Response<any, Record<string, any>>>;
    changePassword(body: {
        currentPassword: string;
        newPassword: string;
    }, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
