"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPackagesController = void 0;
const common_1 = require("@nestjs/common");
const user_packages_service_1 = require("./user-packages.service");
const user_packages_query_dto_1 = require("./dto/user-packages-query.dto");
const request_context_decorator_1 = require("../common/decorators/request-context.decorator");
const current_user_decorator_1 = require("../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let UserPackagesController = class UserPackagesController {
    userPackagesService;
    constructor(userPackagesService) {
        this.userPackagesService = userPackagesService;
    }
    getUserPackages(user, query, ctx) {
        return this.userPackagesService.getUserPackages(user.id, query, ctx);
    }
    getPackageById(user, packageId, ctx) {
        return this.userPackagesService.getPackageById(user.id, packageId, ctx);
    }
    activatePackage(user, packageId, ctx) {
        const activateData = { packageId };
        return this.userPackagesService.activatePackage(user.id, activateData, ctx);
    }
    getPackageUsageStats(user, packageId, query, ctx) {
        return this.userPackagesService.getPackageUsageStats(user.id, packageId, query, ctx);
    }
    getPackageUsageHistory(user, packageId, query, ctx) {
        return this.userPackagesService.getPackageUsageHistory(user.id, packageId, query, ctx);
    }
};
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, user_packages_query_dto_1.UserPackagesQueryDto, Object]),
    __metadata("design:returntype", void 0)
], UserPackagesController.prototype, "getUserPackages", null);
__decorate([
    (0, common_1.Get)(':packageId'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('packageId')),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", void 0)
], UserPackagesController.prototype, "getPackageById", null);
__decorate([
    (0, common_1.Post)(':packageId/activate'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('packageId')),
    __param(2, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", void 0)
], UserPackagesController.prototype, "activatePackage", null);
__decorate([
    (0, common_1.Get)(':packageId/usage-stats'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('packageId')),
    __param(2, (0, common_1.Query)()),
    __param(3, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, user_packages_query_dto_1.UsageStatsQueryDto, Object]),
    __metadata("design:returntype", void 0)
], UserPackagesController.prototype, "getPackageUsageStats", null);
__decorate([
    (0, common_1.Get)(':packageId/usage-history'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Param)('packageId')),
    __param(2, (0, common_1.Query)()),
    __param(3, (0, request_context_decorator_1.RequestCtx)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, user_packages_query_dto_1.UsageHistoryQueryDto, Object]),
    __metadata("design:returntype", void 0)
], UserPackagesController.prototype, "getPackageUsageHistory", null);
UserPackagesController = __decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('user-packages'),
    __metadata("design:paramtypes", [user_packages_service_1.UserPackagesService])
], UserPackagesController);
exports.UserPackagesController = UserPackagesController;
//# sourceMappingURL=user-packages.controller.js.map