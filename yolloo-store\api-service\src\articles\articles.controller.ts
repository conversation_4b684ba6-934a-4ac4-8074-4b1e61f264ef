import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { ArticlesService } from './articles.service';
import { ArticleQueryDto, ArticleCategoryQueryDto } from './dto/article-query.dto';
import { RequestCtx } from '../common/decorators/request-context.decorator';
import { Public } from '../common/decorators/public.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RequestContext } from '../common/interfaces/context.interface';

@UseGuards(JwtAuthGuard)
@Controller('articles')
export class ArticlesController {
  constructor(private readonly articlesService: ArticlesService) {}

  /**
   * 获取文章分类列表
   * GET /articles/categories
   */
  @Public()
  @Get('categories')
  getCategories(
    @Query() query: ArticleCategoryQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.articlesService.getCategories(query, ctx);
  }

  /**
   * 获取文章列表
   * GET /articles
   */
  @Public()
  @Get()
  getArticles(
    @Query() query: ArticleQueryDto,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.articlesService.getArticles(query, ctx);
  }

  /**
   * 获取文章详情
   * GET /articles/:id
   */
  @Public()
  @Get(':id')
  getArticleById(
    @Param('id') id: string,
    @RequestCtx() ctx: RequestContext,
  ) {
    return this.articlesService.getArticleById(id, ctx);
  }
}
