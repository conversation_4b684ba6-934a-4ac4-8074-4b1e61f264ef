(()=>{var e={};e.id=739,e.ids=[739],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},78933:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),t(84524),t(14417),t(89090),t(26083),t(35866);var r=t(23191),a=t(88716),i=t(37922),l=t.n(i),d=t(95231),n={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(s,n);let o=["",{children:["affiliate",{children:["organization",{children:["[id]",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84524)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\[id]\\analytics\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,14417)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\affiliate\\organization\\[id]\\analytics\\page.tsx"],m="/affiliate/organization/[id]/analytics/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/affiliate/organization/[id]/analytics/page",pathname:"/affiliate/organization/[id]/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55995:(e,s,t)=>{Promise.resolve().then(t.bind(t,17110))},35303:()=>{},17110:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var r=t(10326),a=t(17577),i=t(35047),l=t(44099),d=t(77506),n=t(11890),o=t(21405),c=t(79635),m=t(80239),x=t(90772),u=t(33071),h=t(85999),f=t(28758),p=t(90434),j=t(34474),g=t(567),b=t(74990),N=t(15940);function v({params:e}){(0,i.useRouter)();let[s,t]=(0,a.useState)(!0),[v,y]=(0,a.useState)(!1),[w,C]=(0,a.useState)("30days"),[R,z]=(0,a.useState)(null),[Z,P]=(0,a.useState)(null),[q,k]=(0,a.useState)([]),[S,A]=(0,a.useState)(null),[M,_]=(0,a.useState)(!1),E=async()=>{try{_(!0);let s=await l.Z.post(`/api/affiliate/organizations/${e.id}/refresh-stats`);if(s.data.success){h.A.success("Statistics refreshed successfully");let s=await l.Z.get(`/api/affiliate/organizations/${e.id}/analytics?period=${w}`);s.data.analytics&&z(s.data.analytics),s.data.stats&&P(s.data.stats),s.data.memberOrders&&k(s.data.memberOrders)}else h.A.error(s.data.error||"Failed to refresh stats")}catch(e){console.error("Error refreshing stats:",e),h.A.error("Failed to refresh statistics")}finally{_(!1)}};return s?r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx(d.Z,{className:"h-8 w-8 animate-spin text-primary"})}):R&&Z?(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[r.jsx(x.Button,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(p.default,{href:`/affiliate/organization/${e.id}`,children:[r.jsx(n.Z,{className:"mr-2 h-4 w-4"}),"Back to Organization"]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"text-sm text-muted-foreground",children:"Time period:"}),(0,r.jsxs)(j.Ph,{onValueChange:e=>{C(e)},value:w,children:[r.jsx(j.i4,{className:"w-[180px]",children:r.jsx(j.ki,{placeholder:"Select period"})}),(0,r.jsxs)(j.Bw,{children:[r.jsx(j.Ql,{value:"7days",children:"Last 7 days"}),r.jsx(j.Ql,{value:"30days",children:"Last 30 days"}),r.jsx(j.Ql,{value:"90days",children:"Last 90 days"}),r.jsx(j.Ql,{value:"year",children:"Last year"}),r.jsx(j.Ql,{value:"all",children:"All time"})]})]})]})]}),(0,r.jsxs)(u.Zb,{className:"mb-8",children:[r.jsx(u.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(u.ll,{children:"Organization Analytics"}),r.jsx(u.SZ,{children:"View performance metrics for this organization"})]}),r.jsx(x.Button,{onClick:E,size:"sm",disabled:M,variant:"outline",children:M?(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Refreshing..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Refresh Stats"]})})]})}),r.jsx(u.aY,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Traffic"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Visits"}),r.jsx("div",{className:"text-2xl font-bold",children:Z.totalVisits})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Conversions"}),r.jsx("div",{className:"text-2xl font-bold",children:Z.totalConversions})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Performance"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Conversion Rate"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[Z.conversionRate?.toFixed(2)||"0.00","%"]})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Orders"}),r.jsx("div",{className:"text-2xl font-bold",children:Z.totalConversions})]})]})]})]}),S&&(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Organization Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Commission Rate"}),(0,r.jsxs)("div",{className:"text-xl font-bold",children:[(100*S.commissionRate)?.toFixed(0)||"0","%"]})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Discount Rate"}),(0,r.jsxs)("div",{className:"text-xl font-bold",children:[(100*S.discountRate)?.toFixed(0)||"0","%"]})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Affiliate Code"}),r.jsx("div",{className:"text-xl font-bold font-mono",children:S.code})]})]})]})]})})]}),(0,r.jsxs)("div",{className:"space-y-2 mb-8",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Commission Distribution"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Organization Gross Earnings"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",Z.totalCommissions?.toFixed(2)||"0.00"]}),r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Total commission generated"})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Member Commissions"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",Z.memberCommissions?.toFixed(2)||"0.00"]}),r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Distributed to members"})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border bg-green-50/40",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Organization Net Earnings"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",Z.organizationActualEarnings?.toFixed(2)||"0.00"]}),r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Actual organization profit"})]})]})]}),r.jsx("div",{className:"grid gap-6 mb-8",children:(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{children:[r.jsx(u.ll,{children:"Performance Overview"}),r.jsx(u.SZ,{children:"Summary of organization performance for the selected period"})]}),r.jsx(u.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:r.jsx(c.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Members"}),r.jsx("h3",{className:"text-2xl font-bold",children:R.totalMembers})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:r.jsx(m.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Referrals"}),r.jsx("h3",{className:"text-2xl font-bold",children:R.totalReferrals})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:r.jsx(m.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Earnings"}),(0,r.jsxs)("h3",{className:"text-2xl font-bold",children:["$",R.totalEarnings.toFixed(2)]})]})]})]})})]})}),r.jsx("div",{className:"grid gap-6 mb-8",children:(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{children:[r.jsx(u.ll,{children:"Monthly Earnings"}),r.jsx(u.SZ,{children:"Revenue trends over the last 6 months"})]}),r.jsx(u.aY,{children:r.jsx("div",{className:"relative",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-2",children:R.monthlyEarnings.map((e,s)=>r.jsx(u.Zb,{className:"p-2",children:(0,r.jsxs)(u.aY,{className:"p-2",children:[r.jsx("div",{className:"flex justify-between items-center mb-1",children:(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.month," ",e.year]})}),(0,r.jsxs)("p",{className:"text-xl font-bold text-green-600",children:["$",e.amount.toFixed(2)]}),(0,r.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[r.jsx("span",{className:"text-muted-foreground",children:"Total:"}),(0,r.jsxs)("span",{className:"font-medium",children:["$",e.totalCommissions.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[r.jsx("span",{className:"text-muted-foreground",children:"Members:"}),(0,r.jsxs)("span",{className:"font-medium",children:["$",e.memberCommissions.toFixed(2)]})]})]}),e.amount>0&&r.jsx("div",{className:"w-full bg-green-100 rounded-full h-1.5 mt-2",children:r.jsx("div",{className:"bg-green-600 h-1.5 rounded-full",style:{width:`${Math.min(100,e.amount/e.totalCommissions*100)}%`}})})]})},s))})})})]})}),r.jsx("div",{className:"grid gap-6 mb-8",children:(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{children:[r.jsx(u.ll,{children:"Top Performing Members"}),r.jsx(u.SZ,{children:"Members with the highest referrals and earnings"})]}),r.jsx(u.aY,{children:0===R.topPerformers.length?r.jsx("div",{className:"text-center py-4 text-muted-foreground",children:"No performer data available for this period."}):r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:R.topPerformers.map((e,s)=>(0,r.jsxs)(u.Zb,{className:"overflow-hidden",children:[r.jsx("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(f.qE,{className:"h-10 w-10 border-2 border-background",children:[r.jsx(f.F$,{src:e.user.image,alt:e.user.name}),r.jsx(f.Q5,{children:e.user.name.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:e.user.name}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[r.jsx(m.Z,{className:"h-3 w-3 mr-1"}),(0,r.jsxs)("span",{children:["Top ",s+1," Performer"]})]})]})]})}),r.jsx(u.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-muted-foreground",children:"Referrals"}),r.jsx("p",{className:"text-xl font-bold",children:e.referrals})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-muted-foreground",children:"Earnings"}),(0,r.jsxs)("p",{className:"text-xl font-bold",children:["$",e.earnings.toFixed(2)]})]})]})})]},e.id))})})]})}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-4",children:"Member Orders"}),0===q.length?r.jsx("div",{className:"flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed",children:r.jsx("p",{className:"text-sm text-muted-foreground",children:"No orders found for this organization"})}):r.jsx("div",{className:"rounded-md border",children:(0,r.jsxs)(N.iA,{children:[r.jsx(N.xD,{children:(0,r.jsxs)(N.SC,{children:[r.jsx(N.ss,{children:"Member"}),r.jsx(N.ss,{children:"Order ID"}),r.jsx(N.ss,{children:"Order Amount"}),r.jsx(N.ss,{children:"Member Commission"}),r.jsx(N.ss,{children:"Organization Commission"}),r.jsx(N.ss,{children:"Status"}),r.jsx(N.ss,{children:"Date"})]})}),r.jsx(N.RM,{children:q.map(e=>(0,r.jsxs)(N.SC,{children:[r.jsx(N.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(f.qE,{className:"h-7 w-7",children:[r.jsx(f.F$,{src:e.affiliate?.user?.image,alt:e.affiliate?.user?.name}),r.jsx(f.Q5,{children:e.affiliate?.user?.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium",children:e.affiliate?.user?.name}),r.jsx("div",{className:"text-xs text-muted-foreground",children:e.affiliate?.code})]})]})}),(0,r.jsxs)(N.pj,{className:"font-mono text-xs",children:[e.orderId.substring(0,8),"..."]}),(0,r.jsxs)(N.pj,{children:["$",e.order?.total?.toFixed(2)||"0.00"]}),(0,r.jsxs)(N.pj,{children:["$",e.commissionAmount?.toFixed(2)||"0.00"]}),r.jsx(N.pj,{children:e.organizationCommission?.commissionAmount?`$${(e.organizationCommission.commissionAmount-e.commissionAmount).toFixed(2)}`:"-"}),r.jsx(N.pj,{children:r.jsx(g.C,{variant:"outline",className:"APPROVED"===e.status?"bg-green-50 text-green-700 border-green-200":"PENDING"===e.status?"bg-yellow-50 text-yellow-700 border-yellow-200":"PAID"===e.status?"bg-blue-50 text-blue-700 border-blue-200":"bg-red-50 text-red-700 border-red-200",children:e.status})}),r.jsx(N.pj,{children:(0,b.WU)(new Date(e.createdAt),"MMM d, yyyy")})]},e.id))})]})})]})]}):(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[r.jsx("div",{className:"mb-6",children:r.jsx(x.Button,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(p.default,{href:`/affiliate/organization/${e.id}`,children:[r.jsx(n.Z,{className:"mr-2 h-4 w-4"}),"Back to Organization"]})})}),(0,r.jsxs)(u.Zb,{children:[(0,r.jsxs)(u.Ol,{children:[r.jsx(u.ll,{children:"Analytics"}),r.jsx(u.SZ,{children:"No data available"})]}),r.jsx(u.aY,{children:r.jsx("p",{className:"text-muted-foreground",children:"There is no analytics data available for this period. Try changing the time period or check back later."})})]})]})}},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>d});var r=t(10326);t(17577);var a=t(79360),i=t(77863);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...t}){return r.jsx("div",{className:(0,i.cn)(l({variant:s}),e),...t})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>d,SZ:()=>o,Zb:()=>l,aY:()=>c,eW:()=>m,ll:()=>n});var r=t(10326),a=t(17577),i=t(77863);let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));l.displayName="Card";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let n=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...s}));n.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},34474:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>x,Ph:()=>o,Ql:()=>u,i4:()=>m,ki:()=>c});var r=t(10326),a=t(17577),i=t(18792),l=t(941),d=t(32933),n=t(77863);let o=i.fC;i.ZA;let c=i.B4,m=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.xz,{ref:a,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:[s,r.jsx(i.JO,{asChild:!0,children:r.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let x=a.forwardRef(({className:e,children:s,position:t="popper",...a},l)=>r.jsx(i.h_,{children:r.jsx(i.VY,{ref:l,className:(0,n.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:r.jsx(i.l_,{className:(0,n.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:s})})}));x.displayName=i.VY.displayName,a.forwardRef(({className:e,...s},t)=>r.jsx(i.__,{ref:t,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let u=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.ck,{ref:a,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(d.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:s})]}));u.displayName=i.ck.displayName,a.forwardRef(({className:e,...s},t)=>r.jsx(i.Z0,{ref:t,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},15940:(e,s,t)=>{"use strict";t.d(s,{RM:()=>n,SC:()=>o,iA:()=>l,pj:()=>m,ss:()=>c,xD:()=>d});var r=t(10326),a=t(17577),i=t(77863);let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",e),...s}));d.displayName="TableHeader";let n=a.forwardRef(({className:e,...s},t)=>r.jsx("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));n.displayName="TableBody",a.forwardRef(({className:e,...s},t)=>r.jsx("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));o.displayName="TableRow";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("th",{ref:t,className:(0,i.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s}));c.displayName="TableHead";let m=a.forwardRef(({className:e,...s},t)=>r.jsx("td",{ref:t,className:(0,i.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s}));m.displayName="TableCell",a.forwardRef(({className:e,...s},t)=>r.jsx("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},80239:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},14417:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a,metadata:()=>r});let r={title:"Affiliate Program | Yolloo Store",description:"Manage your affiliate program"};function a({children:e}){return e}},84524:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>i,default:()=>d});var r=t(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\[id]\analytics\page.tsx`),{__esModule:i,$$typeof:l}=a;a.default;let d=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\affiliate\organization\[id]\analytics\page.tsx#default`)},57481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(66621);let a=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1615,5772,7624,5634,6621,8792,4099,4824],()=>t(78933));module.exports=r})();