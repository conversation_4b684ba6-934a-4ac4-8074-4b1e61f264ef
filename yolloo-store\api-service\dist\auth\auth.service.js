"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma.service");
const social_auth_service_1 = require("../social-auth/social-auth.service");
const bcrypt = require("bcrypt");
let AuthService = AuthService_1 = class AuthService {
    prisma;
    jwtService;
    socialAuthService;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(prisma, jwtService, socialAuthService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.socialAuthService = socialAuthService;
    }
    async register(registerDto) {
        const existingUser = await this.prisma.user.findUnique({
            where: { email: registerDto.email },
        });
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(registerDto.password, 10);
        const user = await this.prisma.user.create({
            data: {
                email: registerDto.email,
                name: registerDto.name,
                hashedPassword: hashedPassword,
                role: 'CUSTOMER',
            },
        });
        const token = this.generateToken(user);
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            token,
        };
    }
    async login(loginDto) {
        const user = await this.prisma.user.findUnique({
            where: { email: loginDto.email },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (!user.hashedPassword) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(loginDto.password, user.hashedPassword);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const token = this.generateToken(user);
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            token,
        };
    }
    async loginWithCode(loginCodeDto) {
        if (loginCodeDto.action === 'request') {
            const user = await this.prisma.user.findUnique({
                where: { email: loginCodeDto.email },
            });
            if (!user) {
            }
            return {
                status: 'sent',
                message: '验证码已发送至您的邮箱',
            };
        }
        else if (loginCodeDto.action === 'verify') {
            if (!loginCodeDto.code) {
                throw new common_1.BadRequestException('Verification code is required');
            }
            let user = await this.prisma.user.findUnique({
                where: { email: loginCodeDto.email },
            });
            if (!user) {
                user = await this.prisma.user.create({
                    data: {
                        email: loginCodeDto.email,
                        name: loginCodeDto.email.split('@')[0],
                        hashedPassword: await bcrypt.hash(Math.random().toString(36), 10),
                        role: 'CUSTOMER',
                    },
                });
            }
            const token = this.generateToken(user);
            return {
                id: user.id,
                email: user.email,
                name: user.name,
                token,
            };
        }
        throw new common_1.BadRequestException('Invalid action');
    }
    async socialLogin(socialLoginDto) {
        const { provider, token } = socialLoginDto;
        try {
            let socialUserInfo;
            switch (provider) {
                case 'google':
                    socialUserInfo = await this.socialAuthService.verifyGoogleToken(token);
                    break;
                case 'facebook':
                    socialUserInfo = await this.socialAuthService.verifyFacebookToken(token);
                    break;
                case 'apple':
                    socialUserInfo = await this.socialAuthService.verifyAppleToken(token);
                    break;
                case 'wechat':
                    socialUserInfo = await this.socialAuthService.verifyWechatCode(token);
                    break;
                default:
                    throw new common_1.BadRequestException('Unsupported social provider');
            }
            const user = await this.socialAuthService.findOrCreateSocialUser(socialUserInfo);
            const jwtToken = this.generateToken(user);
            return {
                id: user.id,
                email: user.email,
                name: user.name,
                image: user.image,
                token: jwtToken,
            };
        }
        catch (error) {
            this.logger.error('Social login failed:', error);
            throw new common_1.UnauthorizedException('Social login failed');
        }
    }
    generateToken(user) {
        const payload = {
            email: user.email,
            sub: user.id,
            name: user.name,
            role: user.role
        };
        return this.jwtService.sign(payload);
    }
    generateTokens(user) {
        const payload = {
            email: user.email,
            sub: user.id,
            name: user.name,
            role: user.role
        };
        const accessToken = this.jwtService.sign(payload, { expiresIn: '15m' });
        const refreshToken = this.jwtService.sign({ sub: user.id, type: 'refresh' }, { expiresIn: '7d' });
        return { accessToken, refreshToken };
    }
    async webLogin(loginDto) {
        const user = await this.prisma.user.findUnique({
            where: { email: loginDto.email },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (!user.hashedPassword) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(loginDto.password, user.hashedPassword);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const { accessToken, refreshToken } = this.generateTokens(user);
        return {
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                image: user.image,
                role: user.role,
            },
            accessToken,
            refreshToken,
        };
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken);
            if (payload.type !== 'refresh') {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const user = await this.prisma.user.findUnique({
                where: { id: payload.sub },
            });
            if (!user) {
                throw new common_1.UnauthorizedException('User not found');
            }
            const { accessToken, refreshToken: newRefreshToken } = this.generateTokens(user);
            return {
                accessToken,
                refreshToken: newRefreshToken,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
};
AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        social_auth_service_1.SocialAuthService])
], AuthService);
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map