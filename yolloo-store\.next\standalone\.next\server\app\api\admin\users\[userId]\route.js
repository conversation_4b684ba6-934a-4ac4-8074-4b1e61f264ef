"use strict";(()=>{var e={};e.id=9992,e.ids=[9992],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},22817:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>R,patchFetch:()=>E,requestAsyncStorage:()=>x,routeModule:()=>I,serverHooks:()=>_,staticGenerationAsyncStorage:()=>v});var a={};t.r(a),t.d(a,{DELETE:()=>g,GET:()=>h,PATCH:()=>y,dynamic:()=>p,fetchCache:()=>m,revalidate:()=>w});var i=t(49303),n=t(88716),s=t(60670),o=t(87070),l=t(75571),u=t(72331),d=t(90455),c=t(7410);let p="force-dynamic",m="force-no-store",w=0,f=c.z.object({name:c.z.string().min(2,"Name must be at least 2 characters").optional(),email:c.z.string().email("Invalid email address").optional(),role:c.z.enum(["ADMIN","CUSTOMER","STAFF"]).optional()});async function h(e,{params:r}){try{let e=await (0,l.getServerSession)(d.L);if(!e||"ADMIN"!==e.user.role)return new o.NextResponse("Unauthorized",{status:401});let t=await u._.user.findUnique({where:{id:r.userId},include:{orders:{orderBy:{createdAt:"desc"},include:{items:!0}},addresses:!0,loginHistory:{orderBy:{loginTimestamp:"desc"},take:20},accounts:{select:{provider:!0,providerAccountId:!0}},_count:{select:{orders:!0,reviews:!0,wishlist:!0}}}});if(!t)return new o.NextResponse("User not found",{status:404});let a=t.loginHistory.length>0?t.loginHistory[0]:null;return o.NextResponse.json({...t,lastLoginTime:a?.loginTimestamp||null,lastLoginIp:a?.ipAddress||null,lastLoginMethod:a?.loginMethod||null})}catch(e){return console.error("[USER_GET]",e),new o.NextResponse("Internal error",{status:500})}}async function y(e,{params:r}){try{let t=await (0,l.getServerSession)(d.L);if(!t||"ADMIN"!==t.user.role)return new o.NextResponse("Unauthorized",{status:401});let a=await e.json(),i=f.safeParse(a);if(!i.success)return o.NextResponse.json({message:"Validation failed",errors:i.error.errors},{status:400});let n=await u._.user.findUnique({where:{id:r.userId}});if(!n)return new o.NextResponse("User not found",{status:404});if(a.email&&a.email!==n.email&&await u._.user.findUnique({where:{email:a.email}}))return o.NextResponse.json({message:"Email already in use"},{status:400});let s=await u._.user.update({where:{id:r.userId},data:i.data});return o.NextResponse.json(s)}catch(e){return console.error("[USER_PATCH]",e),new o.NextResponse("Internal error",{status:500})}}async function g(e,{params:r}){try{let e=await (0,l.getServerSession)(d.L);if(!e||"ADMIN"!==e.user.role)return new o.NextResponse("Unauthorized",{status:401});let t=await u._.user.findUnique({where:{id:r.userId}});if(!t)return new o.NextResponse("User not found",{status:404});if(t.id===e.user.id)return o.NextResponse.json({message:"Cannot delete your own admin account"},{status:400});return await u._.$transaction(async e=>{await e.marketingCommission.deleteMany({where:{userId:r.userId}}),await e.preSaleSubscription.updateMany({where:{userId:r.userId},data:{userId:null}}),await e.review.deleteMany({where:{userId:r.userId}}),await e.passwordResetToken.deleteMany({where:{userId:r.userId}}),await e.yollooCard.updateMany({where:{userId:r.userId},data:{userId:null}});let t=await e.affiliateProfile.findUnique({where:{userId:r.userId}});for(let a of(t&&(await e.affiliateReferral.deleteMany({where:{affiliateId:t.id}}),await e.affiliateVisit.deleteMany({where:{affiliateId:t.id}}),await e.affiliateWithdrawal.deleteMany({where:{affiliateId:t.id}}),await e.organizationInvite.deleteMany({where:{affiliateId:t.id}}),await e.affiliateProfile.delete({where:{id:t.id}})),await e.order.findMany({where:{userId:r.userId},select:{id:!0}})))await e.orderItem.deleteMany({where:{orderId:a.id}}),await e.odooOrderStatus.deleteMany({where:{orderId:a.id}}),await e.affiliateReferral.deleteMany({where:{orderId:a.id}});await e.order.deleteMany({where:{userId:r.userId}}),await e.address.deleteMany({where:{userId:r.userId}}),await e.cartItem.deleteMany({where:{userId:r.userId}}),await e.wishlistItem.deleteMany({where:{userId:r.userId}}),await e.session.deleteMany({where:{userId:r.userId}}),await e.account.deleteMany({where:{userId:r.userId}}),await e.user.delete({where:{id:r.userId}})}),new o.NextResponse(null,{status:204})}catch(e){return console.error("[USER_DELETE]",e),new o.NextResponse("Internal error",{status:500})}}let I=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/users/[userId]/route",pathname:"/api/admin/users/[userId]",filename:"route",bundlePath:"app/api/admin/users/[userId]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\users\\[userId]\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:x,staticGenerationAsyncStorage:v,serverHooks:_}=I,R="/api/admin/users/[userId]/route";function E(){return(0,s.patchFetch)({serverHooks:_,staticGenerationAsyncStorage:v})}},90455:(e,r,t)=>{t.d(r,{L:()=>d});var a=t(7585),i=t(72331),n=t(77234),s=t(53797),o=t(42023),l=t.n(o),u=t(93475);let d={adapter:{...(0,a.N)(i._),getUser:async e=>{let r=await i._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await i._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await i._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,s.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await i._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,s.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await i._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await i._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await i._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,n.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:a,request:n}){try{if(t&&t.id){let r=n?.headers||new Headers,s=r.get("user-agent")||"",o=r.get("x-forwarded-for"),l=o?o.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";a?u=a.code&&!a.password?"email_code":"password":e&&(u=e.provider),await i._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:s||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],a=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,i=new URL(a).searchParams.get("callbackUrl");if(i){let e=decodeURIComponent(i);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let a=new URL(e);if(t.some(e=>a.hostname===e||a.hostname.includes(e)||a.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(a);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return a}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:a}){if("update"===t&&a)return{...e,...a.user};let n=await i._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return n?{id:n.id,name:n.name,email:n.email,picture:n.image,role:n.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>i});var a=t(53524);let i=global.prisma||new a.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>o,Ak:()=>l,qc:()=>u,yz:()=>d});var a=t(62197),i=t.n(a);let n=null;function s(){if(!n){let e=process.env.REDIS_URL||"redis://localhost:6379";(n=new(i())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),n.on("connect",()=>{console.log("Successfully connected to Redis")})}return n}async function o(e,r,t=300){try{let a=s(),i=`verification_code:${e}`;return await a.setex(i,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=s(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=s(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function d(e,r,t){try{let a=s(),i=`rate_limit:${e}`,n=await a.get(i),o=n?parseInt(n):0;if(o>=r)return!1;return 0===o?await a.setex(i,t,"1"):await a.incr(i),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var a={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return n.default}});var i=t(69955);Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))});var n=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=s(void 0);if(t&&t.has(e))return t.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&({}).hasOwnProperty.call(e,n)){var o=i?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(a,n,o):a[n]=e[n]}return a.default=e,t&&t.set(e,a),a}(t(45609));function s(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(s=function(e){return e?t:r})(e)}Object.keys(n).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(a,e))&&(e in r&&r[e]===n[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return n[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[8948,1615,9092,5972,2197,2023,7005,7410],()=>t(22817));module.exports=a})();