"use strict";(()=>{var e={};e.id=2810,e.ids=[2810],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},24739:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>$,patchFetch:()=>_,requestAsyncStorage:()=>C,routeModule:()=>x,serverHooks:()=>I,staticGenerationAsyncStorage:()=>O});var n,a={};r.r(a),r.d(a,{GET:()=>S,POST:()=>w,dynamic:()=>T,fetchCache:()=>D,revalidate:()=>E});var o=r(49303),s=r(88716),i=r(60670),c=r(87070),u=r(75571),d=r(90455),l=r(6570),p=r(43624),m=r(72331),y=r(89880),g=r(50650),h=r(82361);function f(e){return e?e.replace(/[,，]/g,";"):e}!function(e){e.STARTED="sync:started",e.COMPLETED="sync:completed",e.FAILED="sync:failed",e.PROGRESS="sync:progress",e.PRODUCT_PROCESSED="sync:product:processed",e.PRODUCT_SKIPPED="sync:product:skipped",e.PRODUCT_ERROR="sync:product:error",e.TYPE_COMPLETED="sync:type:completed"}(n||(n={}));class v{constructor(){this.activeSyncs=new Map,this.eventEmitter=new h.EventEmitter,this.eventEmitter.setMaxListeners(50)}static getInstance(){return v.instance||(v.instance=new v),v.instance}on(e,t){this.eventEmitter.on(e,t)}off(e,t){this.eventEmitter.off(e,t)}async syncProducts(e={}){let t=`sync_${Date.now()}`;if(this.activeSyncs.size>0)throw Error("另一个同步进程正在运行中，请稍后再试");this.activeSyncs.set(t,!0);let{productTypes:r=y.Pm,forceCategoryName:n,odooConfig:a,skipEmptyVariantsAndZeroPrice:o=!0,sourceType:s="standard",onlyUpdateOwnProducts:i=!0}=e,c=a?new l.z(a):null,u={totalProcessed:0,totalSkipped:0,totalUpdated:0,totalCreated:0,totalVariants:0,byType:{},startTime:new Date},d=[],h=[];try{console.log(`[Sync:${t}] 开始同步产品，类型: ${r.join(", ")}`),this.eventEmitter.emit("sync:started",{syncId:t,options:e});let a=[];for(let e of r){u.byType[e]={processed:0,skipped:0,updated:0,created:0,variants:0};try{let r=c||new l.z({address:process.env.ODOO_ADDRESS||"",channelId:process.env.ODOO_CHANNEL_ID||"",channelLanguage:process.env.ODOO_CHANNEL_LANGUAGE||"en_US",authSecret:process.env.ODOO_AUTH_SECRET||"",signMethod:process.env.ODOO_SIGN_METHOD||"md5"}),v=await r.pullProducts(e);if(!v?.result?.data){console.error(`[Sync:${t}] 无效的响应结构，类型: ${e}`);continue}for(let r of v.result.data)try{let c;u.totalProcessed++,u.byType[e].processed++;let l=(0,p.X)(r);if(n&&(l.category=n),u.totalVariants+=l.variants.length,u.byType[e].variants+=l.variants.length,o&&0===l.variants.length&&0===l.price){console.log(`[Sync:${t}] 跳过产品 ${l.sku}，无变体且价格为0`),u.totalSkipped++,u.byType[e].skipped++,this.eventEmitter.emit("sync:product:skipped",{syncId:t,productType:e,sku:l.sku,reason:"no_variants_zero_price"});continue}let h=await m._.category.findFirst({where:{name:l.category}});h||(h=await m._.category.create({data:{name:l.category,description:y.rf.defaultDescription(l.category)}}));let v={name:(0,g.mo)(l.name),description:l.description,websiteDescription:l.websiteDescription,price:l.price,images:[],stock:l.stock,status:l.isActive?y.eM.ACTIVE:y.eM.INACTIVE,off_shelve:l.off_shelve,requiredUID:l.requiredUID,mcc:f(l.mcc),dataSize:l.dataSize,planType:l.planType,country:f(l.country),countryCode:f(l.countryCode),odooLastSyncAt:new Date,specifications:{odooId:l.metadata.odooId,odooProductCode:l.metadata.odooProductCode,sourceType:s},category:{connect:{id:h.id}}};c=i?await m._.product.findFirst({where:{sku:l.sku,specifications:{path:["sourceType"],equals:s}}}):await m._.product.findUnique({where:{sku:l.sku}}),a.push({converted:l,productType:e,category:h,baseProductData:v,existingProduct:c}),d.push(l),this.eventEmitter.emit("sync:product:processed",{syncId:t,productType:e,sku:l.sku,isNew:!c,variantsCount:l.variants.length})}catch(n){u.totalSkipped++,u.byType[e].skipped++,h.push(n),this.eventEmitter.emit("sync:product:error",{syncId:t,productType:e,sku:r.product_code,error:n})}let T=u.byType[e];console.log(`[Sync:${t}] 类型=${e}: 处理=${T.processed}, 创建=${T.created}, 更新=${T.updated}, 跳过=${T.skipped}, 变体=${T.variants}`),this.eventEmitter.emit("sync:type:completed",{syncId:t,productType:e,stats:T})}catch(r){console.error(`[Sync:${t}] 处理产品类型 ${e} 时出错:`,r),h.push(r)}}console.log(`[Sync:${t}] 开始第二阶段：对 ${a.length} 个产品进行排序并保存`);let v=e=>!!e.country&&e.country.split(/[,;]/).map(e=>e.trim()).filter(Boolean).length>1||!!e.countryCode&&e.countryCode.split(/[,;]/).map(e=>e.trim()).filter(Boolean).length>1,T=a.sort((e,t)=>{let r=v(e.converted),n=v(t.converted);return r!==n?r?1:-1:e.converted.name.localeCompare(t.converted.name)});console.log(`[Sync:${t}] 产品排序完成，开始按顺序保存到数据库`);for(let e=0;e<T.length;e++){let{converted:r,productType:n,baseProductData:a,existingProduct:o}=T[e];try{await m._.product.upsert({where:{sku:r.sku},update:{...a,variants:{deleteMany:{},create:r.variants.map(e=>({price:Number(e.price),currency:e.currency||y.N_.currency,attributes:e.attributes,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType}))},parameters:{deleteMany:{},create:r.parameters.filter(e=>"mcc"!==e.code).map(e=>({code:e.code,name:e.name,value:e.value}))}},create:{...a,sku:r.sku,variants:{create:r.variants.map(e=>({price:Number(e.price),currency:e.currency||y.N_.currency,attributes:e.attributes,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType}))},parameters:{create:r.parameters.filter(e=>"mcc"!==e.code).map(e=>({code:e.code,name:e.name,value:e.value}))}},include:{variants:!0,parameters:!0}}),o?(u.totalUpdated++,u.byType[n].updated++):(u.totalCreated++,u.byType[n].created++),((e+1)%100==0||e===T.length-1)&&console.log(`[Sync:${t}] 已保存 ${e+1}/${T.length} 个产品`)}catch(e){console.error(`[Sync:${t}] 保存产品 ${r.sku} 时出错:`,e),h.push(e)}}console.log(`[Sync:${t}] 产品保存完成，共保存 ${T.length} 个产品`);let D=await m._.product.findMany({include:{variants:!0,parameters:!0}});return u.endTime=new Date,u.duration=u.endTime.getTime()-u.startTime.getTime(),console.log(`[Sync:${t}] 摘要: 处理=${u.totalProcessed}, 创建=${u.totalCreated}, 更新=${u.totalUpdated}, 跳过=${u.totalSkipped}, 变体=${u.totalVariants}, 耗时=${u.duration}ms`),console.log(`[Sync:${t}] 数据库中的产品总数: ${D.length}`),this.eventEmitter.emit("sync:completed",{syncId:t,stats:u,productsCount:d.length,errorsCount:h.length}),{products:d,stats:u,errors:h}}catch(e){throw console.error(`[Sync:${t}] 同步产品时出错:`,e),u.endTime=new Date,u.duration=u.endTime.getTime()-u.startTime.getTime(),this.eventEmitter.emit("sync:failed",{syncId:t,error:e,stats:u}),e}finally{this.activeSyncs.delete(t)}}}let T="force-dynamic",D="force-no-store",E=0;async function S(e){try{let e=await (0,u.getServerSession)(d.L);if(!e||"ADMIN"!==e.user.role)return new c.NextResponse("Unauthorized",{status:401});return v.getInstance(),c.NextResponse.json({message:"Sync status query feature not yet implemented",availableTypes:y.Pm})}catch(e){return console.error("[SYNC_STATUS]",e),new c.NextResponse("Internal error",{status:500})}}async function w(e){try{let t=await (0,u.getServerSession)(d.L);if(!t||"ADMIN"!==t.user.role)return new c.NextResponse("Unauthorized",{status:401});let r={};try{r=await e.json()}catch(e){}let{productTypes:n,categoryOverride:a,skipEmptyVariantsAndZeroPrice:o=!0,allProductTypes:s}=r;s&&Array.isArray(s)&&s.length>0&&console.log(`[SYNC] Using custom product types: ${s.join(", ")}`);let i=v.getInstance(),l=await i.syncProducts({productTypes:n,forceCategoryName:a,skipEmptyVariantsAndZeroPrice:o,sourceType:"standard",onlyUpdateOwnProducts:!0});return c.NextResponse.json({message:"Product synchronization completed",stats:l.stats,count:l.products.length,errors:l.errors.length>0?l.errors.map(e=>e.message):[]})}catch(e){return console.error("[SYNC_PRODUCTS]",e),new c.NextResponse(JSON.stringify({message:"Error occurred during synchronization",error:e instanceof Error?e.message:"Unknown error"}),{status:500,headers:{"Content-Type":"application/json"}})}}let x=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/products/sync/route",pathname:"/api/admin/products/sync",filename:"route",bundlePath:"app/api/admin/products/sync/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\products\\sync\\route.ts",nextConfigOutput:"standalone",userland:a}),{requestAsyncStorage:C,staticGenerationAsyncStorage:O,serverHooks:I}=x,$="/api/admin/products/sync/route";function _(){return(0,i.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:O})}},50650:(e,t,r)=>{r.d(t,{CN:()=>y,ED:()=>g,QG:()=>f,T4:()=>d,cn:()=>u,eP:()=>T,mo:()=>v,vI:()=>h});var n=r(55761),a=r(62386),o=r(6180),s=r(4284),i=r(35772),c=r(21740);function u(...e){return(0,a.m6)((0,n.W)(e))}function d(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let l={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function p(){return l.TIMEZONE}function m(e){if(!e)return null;try{let t;if(e instanceof Date)t=e;else if("string"==typeof e)t=e.includes("T")||e.includes("Z")?(0,o.D)(e):new Date(e);else{if("number"!=typeof e)return null;t=new Date(e)}return(0,s.J)(t)?t:null}catch(t){return console.warn("Date parsing error:",t,"Input:",e),null}}let y={short:(e,t="Invalid Date")=>{let r=m(e);return r?(0,i.WU)(r,l.DATE_FORMAT):t},full:(e,t="Invalid Date")=>{let r=m(e);return r?(0,i.WU)(r,l.DATETIME_FORMAT):t},long:(e,t="Invalid Date")=>{let r=m(e);return r?r.toLocaleDateString(l.LOCALE,{year:"numeric",month:"long",day:"numeric"}):t},relative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,c.Q)(r,{addSuffix:!0}):t},time:(e,t="Invalid Time")=>{let r=m(e);return r?(0,i.WU)(r,"HH:mm:ss"):t},timeShort:(e,t="Invalid Time")=>{let r=m(e);return r?(0,i.WU)(r,"HH:mm"):t},withTimezone:(e,t,r="Invalid Date")=>{let n=m(e);return n?new Intl.DateTimeFormat(l.LOCALE,{timeZone:t||l.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(n):r},forUser:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=p();return new Intl.DateTimeFormat(l.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserShort:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=p();return new Intl.DateTimeFormat(l.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit"}).format(r)},forUserSafe:(e,t="Invalid Date")=>{let r=m(e);if(!r)return t;let n=l.TIMEZONE;return new Intl.DateTimeFormat(l.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(r)},forUserRelative:(e,t="Invalid Date")=>{let r=m(e);return r?(0,c.Q)(r,{addSuffix:!0}):t},iso:(e,t="")=>{let r=m(e);return r?r.toISOString():t},custom:(e,t,r="Invalid Date")=>{let n=m(e);return n?(0,i.WU)(n,t):r}},g={addDays:(e,t)=>new Date((m(e)||new Date).getTime()+864e5*t),addHours:(e,t)=>new Date((m(e)||new Date).getTime()+36e5*t),addMinutes:(e,t)=>new Date((m(e)||new Date).getTime()+6e4*t),daysBetween:(e,t)=>{let r=m(e),n=m(t);return r&&n?Math.floor((n.getTime()-r.getTime())/864e5):0},isExpired:e=>{let t=m(e);return!!t&&t.getTime()<Date.now()}};function h(e){return e?e.replace(/\D/g,""):""}function f(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function v(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function T(e){let t=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return t?{badge:t[1].toUpperCase(),name:t[2]}:{name:e}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7624,9712,6257],()=>r(24739));module.exports=n})();