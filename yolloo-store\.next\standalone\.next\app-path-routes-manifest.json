{"/_not-found/page": "/_not-found", "/about/page": "/about", "/activate/page": "/activate", "/api/admin/orders/[orderId]/update-status/route": "/api/admin/orders/[orderId]/update-status", "/api/admin/products/bulk-price-update/route": "/api/admin/products/bulk-price-update", "/api/admin/products/[productId]/force-delete/route": "/api/admin/products/[productId]/force-delete", "/auth/error/page": "/auth/error", "/auth/forgot-password/page": "/auth/forgot-password", "/auth/login/page": "/auth/login", "/auth/reset-password/page": "/auth/reset-password", "/checkout/page": "/checkout", "/bind-card/page": "/bind-card", "/contact/page": "/contact", "/esims/[productId]/page": "/esims/[productId]", "/help/page": "/help", "/faq/page": "/faq", "/cards/page": "/cards", "/favicon.ico/route": "/favicon.ico", "/cards/[cardId]/page": "/cards/[cardId]", "/how-it-works/page": "/how-it-works", "/invite/error/page": "/invite/error", "/odoo-integration/page": "/odoo-integration", "/esims/page": "/esims", "/cart/page": "/cart", "/landing/page": "/landing", "/page": "/", "/orders/page": "/orders", "/orders/[orderId]/page": "/orders/[orderId]", "/terms/page": "/terms", "/presale/page": "/presale", "/products/[productId]/page": "/products/[productId]", "/test/uid-extraction/page": "/test/uid-extraction", "/products/page": "/products", "/yolloo-smart/page": "/yolloo-smart", "/pricing/page": "/pricing", "/api/addresses/default/route": "/api/addresses/default", "/api/[...path]/route": "/api/[...path]", "/api/addresses/[addressId]/route": "/api/addresses/[addressId]", "/api/admin/categories/route": "/api/admin/categories", "/api/admin/commissions/[id]/route": "/api/admin/commissions/[id]", "/api/admin/affiliates/route": "/api/admin/affiliates", "/api/admin/cards/[cardId]/route": "/api/admin/cards/[cardId]", "/api/admin/affiliates/[affiliateId]/route": "/api/admin/affiliates/[affiliateId]", "/api/admin/commissions/route": "/api/admin/commissions", "/api/admin/cards/import/route": "/api/admin/cards/import", "/api/addresses/route": "/api/addresses", "/api/admin/categories/[categoryId]/route": "/api/admin/categories/[categoryId]", "/api/admin/dashboard/route": "/api/admin/dashboard", "/api/admin/organizations/[id]/analytics/route": "/api/admin/organizations/[id]/analytics", "/api/admin/orders/route": "/api/admin/orders", "/api/admin/cards/route": "/api/admin/cards", "/api/admin/orders/[orderId]/route": "/api/admin/orders/[orderId]", "/api/admin/organizations/[id]/invites/route": "/api/admin/organizations/[id]/invites", "/api/admin/organizations/[id]/members/[memberId]/route": "/api/admin/organizations/[id]/members/[memberId]", "/api/admin/orders/[orderId]/odoo/route": "/api/admin/orders/[orderId]/odoo", "/api/admin/organizations/[id]/members/route": "/api/admin/organizations/[id]/members", "/api/admin/organizations/[id]/route": "/api/admin/organizations/[id]", "/api/admin/products/[productId]/check-orders/route": "/api/admin/products/[productId]/check-orders", "/api/admin/organizations/[id]/members/batch/route": "/api/admin/organizations/[id]/members/batch", "/api/admin/organizations/[id]/refresh-stats/route": "/api/admin/organizations/[id]/refresh-stats", "/api/admin/organizations/route": "/api/admin/organizations", "/api/admin/settings/route": "/api/admin/settings", "/api/admin/products/[productId]/route": "/api/admin/products/[productId]", "/api/admin/products/sync-qr-products/route": "/api/admin/products/sync-qr-products", "/api/admin/products/route": "/api/admin/products", "/api/admin/products/sync/route": "/api/admin/products/sync", "/api/admin/users/route": "/api/admin/users", "/api/admin/users/search/route": "/api/admin/users/search", "/api/admin/users/batch/route": "/api/admin/users/batch", "/api/affiliate/dashboard/route": "/api/affiliate/dashboard", "/api/admin/users/[userId]/route": "/api/admin/users/[userId]", "/api/affiliate/generate-link/route": "/api/affiliate/generate-link", "/api/affiliate/invites/accept-logged-in/route": "/api/affiliate/invites/accept-logged-in", "/api/affiliate/invites/verify/route": "/api/affiliate/invites/verify", "/api/affiliate/invites/accept/route": "/api/affiliate/invites/accept", "/api/admin/sync-products/route": "/api/admin/sync-products", "/api/affiliate/invites/reject/route": "/api/affiliate/invites/reject", "/api/affiliate/organizations/[id]/invites/batch/route": "/api/affiliate/organizations/[id]/invites/batch", "/api/affiliate/organizations/[id]/invites/[inviteId]/route": "/api/affiliate/organizations/[id]/invites/[inviteId]", "/api/affiliate/organizations/[id]/analytics/route": "/api/affiliate/organizations/[id]/analytics", "/api/admin/products/countries/route": "/api/admin/products/countries", "/api/affiliate/organizations/[id]/members/[memberId]/route": "/api/affiliate/organizations/[id]/members/[memberId]", "/api/affiliate/organizations/[id]/members/batch/route": "/api/affiliate/organizations/[id]/members/batch", "/api/affiliate/organizations/[id]/invites/general/route": "/api/affiliate/organizations/[id]/invites/general", "/api/admin/products/delete-all/route": "/api/admin/products/delete-all", "/api/affiliate/organizations/[id]/members/create-accounts/route": "/api/affiliate/organizations/[id]/members/create-accounts", "/api/affiliate/organizations/[id]/refresh-stats/route": "/api/affiliate/organizations/[id]/refresh-stats", "/api/affiliate/profile/leave-organization/route": "/api/affiliate/profile/leave-organization", "/api/affiliate/organizations/[id]/invites/route": "/api/affiliate/organizations/[id]/invites", "/api/affiliate/organizations/[id]/withdrawals/route": "/api/affiliate/organizations/[id]/withdrawals", "/api/affiliate/visit/route": "/api/affiliate/visit", "/api/affiliate/organizations/[id]/route": "/api/affiliate/organizations/[id]", "/api/auth/[...nextauth]/route": "/api/auth/[...next<PERSON>h]", "/api/affiliate/track/route": "/api/affiliate/track", "/api/affiliate/organizations/[id]/members/route": "/api/affiliate/organizations/[id]/members", "/api/auth/forgot-password/route": "/api/auth/forgot-password", "/api/auth/change-password/route": "/api/auth/change-password", "/api/auth/send-code/route": "/api/auth/send-code", "/api/auth/signup/route": "/api/auth/signup", "/api/auth/reset-password/route": "/api/auth/reset-password", "/api/cards/[cardId]/activate/route": "/api/cards/[cardId]/activate", "/api/boss/orders/route": "/api/boss/orders", "/api/auth/verify-code/route": "/api/auth/verify-code", "/api/cards/bind/route": "/api/cards/bind", "/api/affiliate/organizations/route": "/api/affiliate/organizations", "/api/cards/route": "/api/cards", "/api/cards/[cardId]/route": "/api/cards/[cardId]", "/api/auth/reset-password/verify/route": "/api/auth/reset-password/verify", "/api/cart/route": "/api/cart", "/api/boss/packages/route": "/api/boss/packages", "/api/email/route": "/api/email", "/api/cart/[cartItemId]/route": "/api/cart/[cartItemId]", "/api/esims/parse-qr/route": "/api/esims/parse-qr", "/api/esims/route": "/api/esims", "/api/odoo/invoices/route": "/api/odoo/invoices", "/api/esims/profiles/route": "/api/esims/profiles", "/api/odoo/orders/qrcode/route": "/api/odoo/orders/qrcode", "/api/odoo/orders/route": "/api/odoo/orders", "/api/odoo/orders/status/route": "/api/odoo/orders/status", "/api/odoo/products/route": "/api/odoo/products", "/api/esims/external-activation/route": "/api/esims/external-activation", "/api/odoo/products/price/route": "/api/odoo/products/price", "/api/orders/available-esims/route": "/api/orders/available-esims", "/api/payments/route": "/api/payments", "/api/odoo/webhook/route": "/api/odoo/webhook", "/api/orders/[orderId]/route": "/api/orders/[orderId]", "/api/presale/subscribe/route": "/api/presale/subscribe", "/api/products/[productId]/route": "/api/products/[productId]", "/api/products/[productId]/status/route": "/api/products/[productId]/status", "/api/products/batch/route": "/api/products/batch", "/api/orders/route": "/api/orders", "/api/products/external-data/route": "/api/products/external-data", "/api/products/get-card-links/route": "/api/products/get-card-links", "/api/products/by-code/route": "/api/products/by-code", "/api/products/route": "/api/products", "/api/products/countries/route": "/api/products/countries", "/api/products/paginated/route": "/api/products/paginated", "/api/stock/check/route": "/api/stock/check", "/api/route": "/api", "/api/test-orders/route": "/api/test-orders", "/api/users/route": "/api/users", "/api/test-organization-data/[id]/route": "/api/test-organization-data/[id]", "/api/yolloo-smart/products/route": "/api/yolloo-smart/products", "/invite/[code]/page": "/invite/[code]", "/api/payments/webhook/route": "/api/payments/webhook", "/admin/commissions/page": "/admin/commissions", "/admin/affiliates/page": "/admin/affiliates", "/admin/organizations/page": "/admin/organizations", "/admin/organizations/[id]/members/[memberId]/page": "/admin/organizations/[id]/members/[memberId]", "/admin/subscribers/page": "/admin/subscribers", "/admin/settings/page": "/admin/settings", "/admin/organizations/[id]/page": "/admin/organizations/[id]", "/admin/cards/[cardId]/page": "/admin/cards/[cardId]", "/admin/cards/import/page": "/admin/cards/import", "/admin/cards/new/page": "/admin/cards/new", "/admin/cards/page": "/admin/cards", "/admin/page": "/admin", "/affiliate/organization/[id]/analytics/page": "/affiliate/organization/[id]/analytics", "/affiliate/organization/[id]/edit/page": "/affiliate/organization/[id]/edit", "/affiliate/organization/[id]/members/invite/page": "/affiliate/organization/[id]/members/invite", "/affiliate/organization/[id]/members/page": "/affiliate/organization/[id]/members", "/affiliate/organization/[id]/page": "/affiliate/organization/[id]", "/affiliate/organization/page": "/affiliate/organization", "/affiliate/organization/[id]/withdrawals/page": "/affiliate/organization/[id]/withdrawals", "/affiliate/page": "/affiliate", "/auth/signup/page": "/auth/signup", "/account/page": "/account", "/auth/signin/page": "/auth/signin", "/admin/orders/[orderId]/page": "/admin/orders/[orderId]", "/admin/orders/page": "/admin/orders", "/admin/users/page": "/admin/users", "/admin/products/[productId]/page": "/admin/products/[productId]", "/admin/users/[userId]/page": "/admin/users/[userId]", "/admin/products/page": "/admin/products", "/admin/products/new/page": "/admin/products/new", "/admin/products/sync/page": "/admin/products/sync"}