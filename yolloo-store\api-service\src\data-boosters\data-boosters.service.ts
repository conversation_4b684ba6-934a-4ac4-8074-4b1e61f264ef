import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { DataBoostersQueryDto, DataBoosterOrderDto } from './dto/data-boosters-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
import { DateFormatter, DateUtils } from '../common/utils';

@Injectable()
export class DataBoostersService {
  private readonly logger = new Logger(DataBoostersService.name);

  constructor(private prisma: PrismaService) {}

  async getDataBoosters(query: DataBoostersQueryDto, ctx: RequestContext) {
    console.log('Context in getDataBoosters:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 构建查询条件 - 查找流量加油包相关的产品
      const whereConditions: any = {
        status: 'ACTIVE',
        off_shelve: false,
        // 首先尝试通过分类查找
        OR: [
          // 通过分类查找
          {
            category: {
              name: {
                in: ['Data Boosters', '流量加油包', 'Booster', '补充包', '紧急流量']
              }
            }
          },
          // 通过产品名称查找
          { name: { contains: '流量包', mode: 'insensitive' } },
          { name: { contains: '加油包', mode: 'insensitive' } },
          { name: { contains: 'booster', mode: 'insensitive' } },
          { name: { contains: '补充包', mode: 'insensitive' } },
          { name: { contains: '紧急', mode: 'insensitive' } },
          { name: { contains: 'emergency', mode: 'insensitive' } },
          { name: { contains: 'data pack', mode: 'insensitive' } },
          // 通过描述查找
          { description: { contains: '流量包', mode: 'insensitive' } },
          { description: { contains: '加油包', mode: 'insensitive' } },
          { description: { contains: 'booster', mode: 'insensitive' } },
          { description: { contains: '补充', mode: 'insensitive' } },
          { description: { contains: 'data pack', mode: 'insensitive' } },
          // 通过规格查找
          {
            AND: [
              { dataSize: { not: null } },
              { planType: { in: ['Daily', 'Emergency', 'Booster'] } }
            ]
          }
        ],
      };

      // 添加筛选条件
      if (query.dataSize) {
        whereConditions.AND = whereConditions.AND || [];
        // 解析数据大小筛选
        const dataSizeInMB = this.parseDataSize(query.dataSize);
        whereConditions.AND.push({
          OR: [
            { dataSize: dataSizeInMB },
            { name: { contains: query.dataSize, mode: 'insensitive' } },
            { description: { contains: query.dataSize, mode: 'insensitive' } },
          ],
        });
      }

      if (query.boosterType) {
        whereConditions.AND = whereConditions.AND || [];
        whereConditions.AND.push({
          OR: [
            { planType: { contains: query.boosterType, mode: 'insensitive' } },
            { name: { contains: this.getBoosterTypeKeyword(query.boosterType), mode: 'insensitive' } },
            { description: { contains: this.getBoosterTypeKeyword(query.boosterType), mode: 'insensitive' } },
          ],
        });
      }

      // 查询产品总数
      const total = await this.prisma.product.count({
        where: whereConditions,
      });

      // 如果没有找到流量加油包产品，返回mock数据
      if (total === 0) {
        return this.getFallbackDataBoosters(query, ctx);
      }

      // 查询产品列表
      const skip = (query.page! - 1) * query.pageSize!;
      const products = await this.prisma.product.findMany({
        where: whereConditions,
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
        skip,
        take: query.pageSize!,
        orderBy: this.buildOrderBy(query.sortBy, query.sortOrder),
      });

      // 格式化产品数据为流量加油包格式
      const formattedBoosters = products.map(product => this.formatProductAsBooster(product, ctx, isZh));

      return {
        boosters: formattedBoosters,
        pagination: {
          total,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: skip + formattedBoosters.length < total,
        },
        filters: await this.getDataBoosterFilters(ctx),
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };

    } catch (error) {
      this.logger.error('Error fetching data boosters:', error);
      return this.getFallbackDataBoosters(query, ctx);
    }
  }

  private getFallbackDataBoosters(query: DataBoostersQueryDto, ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    // Mock data for data boosters
    const boosters = [
      {
        id: '1',
        name: isZh ? '紧急流量包 100MB' : 'Emergency Data Booster 100MB',
        description: isZh ? '流量用完时的紧急救援，立即生效' : 'Emergency rescue when data runs out, takes effect immediately',
        boosterType: 'emergency',
        dataSize: '100MB',
        price: 2.00,
        originalPrice: 3.00,
        currency: ctx.currency,
        validity: isZh ? '24小时' : '24 hours',
        activationType: 'instant',
        features: [
          isZh ? '立即激活' : 'Instant activation',
          isZh ? '24小时有效' : 'Valid for 24 hours',
          isZh ? '高速流量' : 'High-speed data',
          isZh ? '紧急使用' : 'Emergency use'
        ],
        imageUrl: 'https://example.com/emergency-100mb.jpg',
        isPopular: true,
        rating: 4.5,
        reviewCount: 2100,
      },
      {
        id: '2',
        name: isZh ? '日流量包 1GB' : 'Daily Data Booster 1GB',
        description: isZh ? '一天的额外流量，适合临时需求' : 'Extra data for one day, perfect for temporary needs',
        boosterType: 'daily',
        dataSize: '1GB',
        price: 5.00,
        originalPrice: 7.00,
        currency: ctx.currency,
        validity: isZh ? '24小时' : '24 hours',
        activationType: 'instant',
        features: [
          isZh ? '1GB高速流量' : '1GB high-speed data',
          isZh ? '24小时有效' : 'Valid for 24 hours',
          isZh ? '即买即用' : 'Buy and use immediately',
          isZh ? '性价比高' : 'Great value'
        ],
        imageUrl: 'https://example.com/daily-1gb.jpg',
        isPopular: false,
        rating: 4.3,
        reviewCount: 1500,
      },
      {
        id: '3',
        name: isZh ? '周流量包 5GB' : 'Weekly Data Booster 5GB',
        description: isZh ? '一周的额外流量，出差旅行必备' : 'Extra data for one week, essential for business trips and travel',
        boosterType: 'weekly',
        dataSize: '5GB',
        price: 18.00,
        originalPrice: 25.00,
        currency: ctx.currency,
        validity: isZh ? '7天' : '7 days',
        activationType: 'instant',
        features: [
          isZh ? '5GB高速流量' : '5GB high-speed data',
          isZh ? '7天有效期' : '7-day validity',
          isZh ? '支持热点分享' : 'Hotspot sharing supported',
          isZh ? '出行必备' : 'Travel essential'
        ],
        imageUrl: 'https://example.com/weekly-5gb.jpg',
        isPopular: true,
        rating: 4.6,
        reviewCount: 980,
      },
      {
        id: '4',
        name: isZh ? '月流量包 10GB' : 'Monthly Data Booster 10GB',
        description: isZh ? '一个月的额外流量，长期补充首选' : 'Extra data for one month, best choice for long-term supplement',
        boosterType: 'monthly',
        dataSize: '10GB',
        price: 30.00,
        originalPrice: 40.00,
        currency: ctx.currency,
        validity: isZh ? '30天' : '30 days',
        activationType: 'instant',
        features: [
          isZh ? '10GB高速流量' : '10GB high-speed data',
          isZh ? '30天有效期' : '30-day validity',
          isZh ? '最优性价比' : 'Best value for money',
          isZh ? '长期使用' : 'Long-term use'
        ],
        imageUrl: 'https://example.com/monthly-10gb.jpg',
        isPopular: false,
        rating: 4.4,
        reviewCount: 750,
      },
      {
        id: '5',
        name: isZh ? '超值流量包 3GB' : 'Value Data Booster 3GB',
        description: isZh ? '3天有效的3GB流量包，短期出行优选' : '3GB data pack valid for 3 days, perfect for short trips',
        boosterType: 'daily',
        dataSize: '3GB',
        price: 12.00,
        originalPrice: 18.00,
        currency: ctx.currency,
        validity: isZh ? '3天' : '3 days',
        activationType: 'instant',
        features: [
          isZh ? '3GB高速流量' : '3GB high-speed data',
          isZh ? '3天有效期' : '3-day validity',
          isZh ? '短期出行' : 'Short-term travel',
          isZh ? '超值优惠' : 'Super value deal'
        ],
        imageUrl: 'https://example.com/value-3gb.jpg',
        isPopular: true,
        rating: 4.7,
        reviewCount: 1200,
      },
    ];

    // Filter by booster type if specified
    let filteredBoosters = boosters;
    if (query.boosterType) {
      filteredBoosters = boosters.filter(booster => booster.boosterType === query.boosterType);
    }

    // Filter by data size if specified
    if (query.dataSize) {
      filteredBoosters = filteredBoosters.filter(booster => booster.dataSize === query.dataSize);
    }

    // Filter by activation type if specified
    if (query.activationType) {
      filteredBoosters = filteredBoosters.filter(booster => booster.activationType === query.activationType);
    }

    // Sort boosters
    if (query.sortBy === 'price') {
      filteredBoosters.sort((a, b) => {
        return query.sortOrder === 'desc' ? b.price - a.price : a.price - b.price;
      });
    } else if (query.sortBy === 'rating') {
      filteredBoosters.sort((a, b) => {
        return query.sortOrder === 'desc' ? b.rating - a.rating : a.rating - b.rating;
      });
    }

    // Pagination
    const total = filteredBoosters.length;
    const skip = (query.page! - 1) * query.pageSize!;
    const paginatedBoosters = filteredBoosters.slice(skip, skip + query.pageSize!);

    return {
      boosters: paginatedBoosters,
      pagination: {
        total,
        page: query.page!,
        pageSize: query.pageSize!,
        hasMore: skip + paginatedBoosters.length < total,
      },
      filters: {
        boosterTypes: [
          { value: 'emergency', label: isZh ? '紧急流量' : 'Emergency' },
          { value: 'daily', label: isZh ? '日流量包' : 'Daily' },
          { value: 'weekly', label: isZh ? '周流量包' : 'Weekly' },
          { value: 'monthly', label: isZh ? '月流量包' : 'Monthly' },
        ],
        dataSizes: [
          { value: '100MB', label: '100MB' },
          { value: '500MB', label: '500MB' },
          { value: '1GB', label: '1GB' },
          { value: '3GB', label: '3GB' },
          { value: '5GB', label: '5GB' },
          { value: '10GB', label: '10GB' },
        ],
        activationTypes: [
          { value: 'instant', label: isZh ? '立即激活' : 'Instant' },
          { value: 'scheduled', label: isZh ? '定时激活' : 'Scheduled' },
        ],
      },
      context: {
        language: ctx.language,
        theme: ctx.theme,
        currency: ctx.currency,
      },
    };
  }

  async getBoosterById(boosterId: string, ctx: RequestContext) {
    console.log('Context in getBoosterById:', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询具体的产品
      const product = await this.prisma.product.findUnique({
        where: { id: boosterId },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          variants: {
            select: {
              id: true,
              price: true,
              currency: true,
            },
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
              comment: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
        },
      });

      if (!product) {
        return this.getFallbackBoosterDetails(boosterId, ctx);
      }

      // 格式化为详细的流量加油包数据
      const boosterDetails = this.formatProductAsDetailedBooster(product, ctx, isZh);

      return boosterDetails;

    } catch (error) {
      this.logger.error('Error fetching booster details:', error);
      return this.getFallbackBoosterDetails(boosterId, ctx);
    }
  }

  private getFallbackBoosterDetails(boosterId: string, ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    // Mock detailed booster data
    const boosterDetails = {
      id: boosterId,
      name: isZh ? '紧急流量包 100MB' : 'Emergency Data Booster 100MB',
      description: isZh ? '当您的流量用完时，这个紧急流量包可以立即为您提供100MB的高速流量，让您在关键时刻保持连接。' : 'When your data runs out, this emergency data booster provides 100MB of high-speed data instantly, keeping you connected at critical moments.',
      boosterType: 'emergency',
      dataSize: '100MB',
      price: 2.00,
      originalPrice: 3.00,
      currency: ctx.currency,
      validity: isZh ? '24小时' : '24 hours',
      activationType: 'instant',
      features: [
        isZh ? '立即激活' : 'Instant activation',
        isZh ? '24小时有效' : 'Valid for 24 hours',
        isZh ? '高速流量' : 'High-speed data',
        isZh ? '紧急使用' : 'Emergency use'
      ],
      imageUrl: 'https://example.com/emergency-100mb.jpg',
      isPopular: true,
      rating: 4.5,
      reviewCount: 2100,
      detailedInfo: {
        activation: isZh ? '购买后立即激活，无需等待' : 'Activates immediately after purchase, no waiting required',
        usage: isZh ? '适合紧急情况下的临时流量需求' : 'Perfect for temporary data needs in emergency situations',
        speed: isZh ? '提供与主套餐相同的网络速度' : 'Provides the same network speed as your main package',
        compatibility: isZh ? '兼容所有设备和套餐' : 'Compatible with all devices and packages',
      },
      usageScenarios: [
        isZh ? '主套餐流量用完时的应急补充' : 'Emergency supplement when main package data is exhausted',
        isZh ? '重要会议或通话时的流量保障' : 'Data guarantee during important meetings or calls',
        isZh ? '出行途中的临时流量需求' : 'Temporary data needs during travel',
        isZh ? '月底流量不足时的救急' : 'Emergency relief when data is insufficient at month-end',
      ],
      restrictions: [
        isZh ? '仅限当前号码使用' : 'Valid only for current number',
        isZh ? '24小时后自动失效' : 'Automatically expires after 24 hours',
        isZh ? '不可转让或退款' : 'Non-transferable and non-refundable',
      ],
    };

    return boosterDetails;
  }

  async createBoosterOrder(orderData: DataBoosterOrderDto, ctx: RequestContext) {
    console.log('Context in createBoosterOrder:', ctx);

    const isZh = ctx.language.startsWith('zh');

    // Mock order creation
    const order = {
      id: `booster_${Date.now()}`,
      boosterId: orderData.boosterId,
      activationTime: orderData.activationTime || DateFormatter.iso(new Date()),
      targetNumber: orderData.targetNumber,
      status: 'pending',
      statusText: isZh ? '待支付' : 'Pending Payment',
      createdAt: DateFormatter.iso(new Date()),
      estimatedActivationTime: orderData.activationTime || DateFormatter.iso(DateUtils.addMinutes(new Date(), 2)), // 2 minutes for instant
    };

    return {
      order,
      message: isZh ? '流量包订单创建成功，请完成支付' : 'Data booster order created successfully, please complete payment',
    };
  }

  async getBoosterHistory(userId: string, query: DataBoostersQueryDto, ctx: RequestContext) {
    console.log('Context in getBoosterHistory:', ctx);

    const isZh = ctx.language.startsWith('zh');

    // Mock booster usage history
    const history = [
      {
        id: 'booster_001',
        name: isZh ? '紧急流量包 100MB' : 'Emergency Data Booster 100MB',
        dataSize: '100MB',
        price: 2.00,
        currency: ctx.currency,
        status: 'used',
        statusText: isZh ? '已使用完' : 'Fully Used',
        purchasedAt: '2023-12-01T14:30:00Z',
        activatedAt: '2023-12-01T14:31:00Z',
        expiredAt: '2023-12-02T14:31:00Z',
        usagePercentage: 100,
      },
      {
        id: 'booster_002',
        name: isZh ? '日流量包 1GB' : 'Daily Data Booster 1GB',
        dataSize: '1GB',
        price: 5.00,
        currency: ctx.currency,
        status: 'active',
        statusText: isZh ? '使用中' : 'Active',
        purchasedAt: '2023-12-02T09:15:00Z',
        activatedAt: '2023-12-02T09:16:00Z',
        expiredAt: '2023-12-03T09:16:00Z',
        usagePercentage: 65,
        remainingData: '350MB',
      },
    ];

    return {
      history,
      pagination: {
        total: history.length,
        page: query.page!,
        pageSize: query.pageSize!,
        hasMore: false,
      },
      summary: {
        totalPurchased: history.length,
        totalSpent: history.reduce((sum, item) => sum + item.price, 0),
        activeCount: history.filter(item => item.status === 'active').length,
      },
    };
  }

  private formatProductAsBooster(product: any, ctx: RequestContext, isZh: boolean) {
    // 计算平均评分
    const avgRating = product.reviews.length > 0
      ? product.reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / product.reviews.length
      : 0;

    // 获取最低价格（从变体中）
    const lowestPrice = product.variants.length > 0
      ? Math.min(...product.variants.map((v: any) => Number(v.price)))
      : Number(product.price);

    // 获取货币
    const currency = product.variants.length > 0
      ? product.variants[0].currency || ctx.currency
      : ctx.currency;

    // 解析规格中的流量包信息
    let boosterType = 'emergency';
    let validity = '24小时';
    let activationType = 'instant';
    let features: string[] = [];

    try {
      const specs = typeof product.specifications === 'string'
        ? JSON.parse(product.specifications)
        : product.specifications;

      boosterType = specs?.boosterType || this.determineBoosterType(product.name, product.description);
      validity = specs?.validity || this.determineValidity(product.name, product.description);
      activationType = specs?.activationType || 'instant';
      features = specs?.features || [];
    } catch (error) {
      this.logger.warn(`Failed to parse specifications for product ${product.id}:`, error);
    }

    // 默认特性
    if (features.length === 0) {
      features = [
        isZh ? '立即激活' : 'Instant activation',
        isZh ? `${validity}有效` : `Valid for ${validity}`,
        isZh ? '高速流量' : 'High-speed data',
        isZh ? '紧急使用' : 'Emergency use'
      ];
    }

    return {
      id: product.id,
      name: product.name,
      description: product.description,
      boosterType: boosterType,
      dataSize: product.dataSize ? this.formatDataSize(product.dataSize) : '100MB',
      price: lowestPrice,
      originalPrice: lowestPrice * 1.5, // 假设原价比现价高50%
      currency: currency,
      validity: validity,
      activationType: activationType,
      features: features,
      imageUrl: product.images && product.images.length > 0
        ? product.images[0]
        : `/images/products/${boosterType}-${this.formatDataSize(product.dataSize || 100).toLowerCase()}.jpg`,
      isPopular: avgRating >= 4.5,
      rating: Math.round(avgRating * 10) / 10,
      reviewCount: product.reviews.length,
    };
  }

  private formatProductAsDetailedBooster(product: any, ctx: RequestContext, isZh: boolean) {
    const basicBooster = this.formatProductAsBooster(product, ctx, isZh);

    return {
      ...basicBooster,
      detailedInfo: {
        activation: isZh ? '购买后立即激活，无需等待' : 'Activates immediately after purchase, no waiting required',
        usage: isZh ? '适合紧急情况下的临时流量需求' : 'Perfect for temporary data needs in emergency situations',
        speed: isZh ? '提供与主套餐相同的网络速度' : 'Provides the same network speed as your main package',
        compatibility: isZh ? '兼容所有设备和套餐' : 'Compatible with all devices and packages',
      },
      usageScenarios: [
        isZh ? '主套餐流量用完时的应急补充' : 'Emergency supplement when main package data is exhausted',
        isZh ? '重要会议或通话时的流量保障' : 'Data guarantee during important meetings or calls',
        isZh ? '出行途中的临时流量需求' : 'Temporary data needs during travel',
        isZh ? '月底流量不足时的救急' : 'Emergency relief when data is insufficient at month-end',
      ],
      restrictions: [
        isZh ? '仅限当前号码使用' : 'Valid only for current number',
        isZh ? `${basicBooster.validity}后自动失效` : `Automatically expires after ${basicBooster.validity}`,
        isZh ? '不可转让或退款' : 'Non-transferable and non-refundable',
      ],
      variants: product.variants.map((variant: any) => ({
        id: variant.id,
        price: Number(variant.price),
        currency: variant.currency,
      })),
      reviews: product.reviews.map((review: any) => ({
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt,
      })),
    };
  }

  private async getDataBoosterFilters(ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    try {
      // 从数据库获取实际的筛选选项
      const dataSizeOptions = await this.prisma.product.findMany({
        where: {
          status: 'ACTIVE',
          off_shelve: false,
          dataSize: { not: null },
        },
        select: { dataSize: true },
        distinct: ['dataSize'],
        orderBy: { dataSize: 'asc' },
      });

      const formattedDataSizes = dataSizeOptions
        .filter(d => d.dataSize && d.dataSize > 0)
        .map(d => ({
          value: this.formatDataSize(d.dataSize!),
          label: this.formatDataSize(d.dataSize!),
        }));

      return {
        boosterTypes: [
          { value: 'emergency', label: isZh ? '紧急流量' : 'Emergency' },
          { value: 'daily', label: isZh ? '日流量包' : 'Daily' },
          { value: 'weekly', label: isZh ? '周流量包' : 'Weekly' },
          { value: 'monthly', label: isZh ? '月流量包' : 'Monthly' },
        ],
        dataSizes: formattedDataSizes.length > 0 ? formattedDataSizes : [
          { value: '100MB', label: '100MB' },
          { value: '500MB', label: '500MB' },
          { value: '1GB', label: '1GB' },
          { value: '3GB', label: '3GB' },
          { value: '5GB', label: '5GB' },
          { value: '10GB', label: '10GB' },
        ],
        activationTypes: [
          { value: 'instant', label: isZh ? '立即激活' : 'Instant' },
          { value: 'scheduled', label: isZh ? '定时激活' : 'Scheduled' },
        ],
      };

    } catch (error) {
      this.logger.error('Error fetching data booster filters:', error);

      // 返回默认筛选选项
      return {
        boosterTypes: [
          { value: 'emergency', label: isZh ? '紧急流量' : 'Emergency' },
          { value: 'daily', label: isZh ? '日流量包' : 'Daily' },
          { value: 'weekly', label: isZh ? '周流量包' : 'Weekly' },
          { value: 'monthly', label: isZh ? '月流量包' : 'Monthly' },
        ],
        dataSizes: [
          { value: '100MB', label: '100MB' },
          { value: '500MB', label: '500MB' },
          { value: '1GB', label: '1GB' },
          { value: '3GB', label: '3GB' },
          { value: '5GB', label: '5GB' },
          { value: '10GB', label: '10GB' },
        ],
        activationTypes: [
          { value: 'instant', label: isZh ? '立即激活' : 'Instant' },
          { value: 'scheduled', label: isZh ? '定时激活' : 'Scheduled' },
        ],
      };
    }
  }

  private buildOrderBy(sortBy?: string, sortOrder?: 'asc' | 'desc') {
    const order = sortOrder || 'asc';

    switch (sortBy) {
      case 'price':
        return { price: order };
      case 'rating':
        return { createdAt: order as 'asc' | 'desc' }; // 由于评分需要计算，暂时用创建时间排序
      case 'name':
        return { name: order };
      default:
        return { createdAt: 'desc' as 'asc' | 'desc' };
    }
  }

  private determineBoosterType(name: string, description: string): string {
    const text = (name + ' ' + description).toLowerCase();

    if (text.includes('紧急') || text.includes('emergency')) return 'emergency';
    if (text.includes('日') || text.includes('daily')) return 'daily';
    if (text.includes('周') || text.includes('weekly')) return 'weekly';
    if (text.includes('月') || text.includes('monthly')) return 'monthly';

    return 'emergency'; // 默认为紧急流量包
  }

  private determineValidity(name: string, description: string): string {
    const text = (name + ' ' + description).toLowerCase();

    if (text.includes('24小时') || text.includes('24 hours') || text.includes('1天') || text.includes('1 day')) return '24小时';
    if (text.includes('3天') || text.includes('3 days')) return '3天';
    if (text.includes('7天') || text.includes('7 days') || text.includes('周') || text.includes('week')) return '7天';
    if (text.includes('30天') || text.includes('30 days') || text.includes('月') || text.includes('month')) return '30天';

    return '24小时'; // 默认24小时
  }

  private formatDataSize(dataSize: number): string {
    if (dataSize >= 1024) {
      const sizeInGB = dataSize / 1024;
      return sizeInGB % 1 === 0 ? `${sizeInGB}GB` : `${sizeInGB.toFixed(1)}GB`;
    } else {
      return `${dataSize}MB`;
    }
  }

  private parseDataSize(dataSizeStr: string): number {
    const str = dataSizeStr.toLowerCase();
    const numMatch = str.match(/(\d+(?:\.\d+)?)/);

    if (!numMatch) return 0;

    const num = parseFloat(numMatch[1]);

    if (str.includes('gb')) {
      return num * 1024; // 转换为MB
    } else if (str.includes('mb')) {
      return num;
    }

    return num; // 默认为MB
  }

  private getBoosterTypeKeyword(boosterType: string): string {
    const typeMap: { [key: string]: string } = {
      'emergency': '紧急',
      'daily': '日',
      'weekly': '周',
      'monthly': '月'
    };

    return typeMap[boosterType] || boosterType;
  }
}
