(()=>{var e={};e.id=6716,e.ids=[6716],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},39942:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>p,routeModule:()=>m,tree:()=>d}),r(74595),r(89090),r(26083),r(35866);var a=r(23191),s=r(88716),o=r(37922),n=r.n(o),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74595)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\auth\\login\\page.tsx"],u="/auth/login/page",c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19996:(e,t,r)=>{Promise.resolve().then(r.bind(r,87241))},87241:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(10326),s=r(17577),o=r(90772),n=r(54432),i=r(31048);function l(){let[e,t]=(0,s.useState)(""),[r,l]=(0,s.useState)(""),d=async t=>{t.preventDefault(),console.log("Login with:",e,r)};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[a.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Login"}),(0,a.jsxs)("form",{onSubmit:d,className:"max-w-md mx-auto",children:[(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx(i._,{htmlFor:"email",children:"Email"}),a.jsx(n.I,{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx(i._,{htmlFor:"password",children:"Password"}),a.jsx(n.I,{id:"password",type:"password",value:r,onChange:e=>l(e.target.value),required:!0})]}),a.jsx(o.Button,{type:"submit",children:"Login"})]})]})}},31048:(e,t,r)=>{"use strict";r.d(t,{_:()=>d});var a=r(10326),s=r(17577),o=r(34478),n=r(79360),i=r(77863);let l=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef(({className:e,...t},r)=>a.jsx(o.f,{ref:r,className:(0,i.cn)(l(),e),...t}));d.displayName=o.f.displayName},74595:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>o,default:()=>i});var a=r(68570);let s=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\login\page.tsx`),{__esModule:o,$$typeof:n}=s;s.default;let i=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\auth\login\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},34478:(e,t,r)=>{"use strict";r.d(t,{f:()=>i});var a=r(17577),s=r(45226),o=r(10326),n=a.forwardRef((e,t)=>(0,o.jsx)(s.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,4824],()=>r(39942));module.exports=a})();