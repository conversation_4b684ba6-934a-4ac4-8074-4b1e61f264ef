/// <reference types="cookie-parser" />
import { Request, Response } from 'express';
import { WebPaymentsService } from './web-payments.service';
export declare class WebPaymentsController {
    private readonly webPaymentsService;
    constructor(webPaymentsService: WebPaymentsService);
    createPayment(createPaymentDto: any, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
    handleWebhook(body: any, req: Request, res: Response): Promise<Response<any, Record<string, any>>>;
}
