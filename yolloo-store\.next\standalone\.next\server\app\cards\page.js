(()=>{var e={};e.id=5137,e.ids=[5137],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},15042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o}),s(91375),s(89090),s(26083),s(35866);var a=s(23191),r=s(88716),i=s(37922),n=s.n(i),l=s(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["cards",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,91375)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\cards\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\cards\\page.tsx"],m="/cards/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/cards/page",pathname:"/cards",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93669:(e,t,s)=>{Promise.resolve().then(s.bind(s,805))},805:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var a=s(10326),r=s(77109),i=s(90772),n=s(33071),l=s(17577),d=s(85999),o=s(35047),c=s(62288),m=s(54432),u=s(31048),x=s(83855);function f(){let[e,t]=(0,l.useState)(!1),[s,r]=(0,l.useState)(!1),[n,o]=(0,l.useState)(""),[f,p]=(0,l.useState)(""),[g,h]=(0,l.useState)(""),b=e=>e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"),v=e=>{h("");let t=e.replace(/-/g,"");return t?/^\d+$/.test(t)?20===t.length||(h("UID must be exactly 20 digits"),!1):(h("UID must contain only numbers"),!1):(h("UID cannot be empty"),!1)},j=async e=>{if(e.preventDefault(),n&&v(n))try{r(!0);let e=await fetch("/api/cards/bind",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({uid:n})});if(!e.ok){let t=await e.text();throw Error(t)}d.A.success("Card bound successfully"),t(!1),window.location.reload()}catch(e){d.A.error(e instanceof Error?e.message:"Failed to bind card")}finally{r(!1)}};return(0,a.jsxs)(c.Vq,{open:e,onOpenChange:t,children:[a.jsx(c.hg,{asChild:!0,children:(0,a.jsxs)(i.Button,{className:"flex items-center gap-2",children:[a.jsx(x.Z,{className:"h-4 w-4"}),"Add New Card"]})}),(0,a.jsxs)(c.cZ,{children:[(0,a.jsxs)(c.fK,{children:[a.jsx(c.$N,{children:"Add New Card"}),a.jsx(c.Be,{children:"Enter your card UID to bind it to your account"})]}),(0,a.jsxs)("form",{onSubmit:j,children:[a.jsx("div",{className:"grid gap-4 py-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(u._,{htmlFor:"uid",children:"Card UID"}),a.jsx(m.I,{id:"uid",placeholder:"Enter card number (numbers only, must be 20 digits)",value:f,onChange:e=>{let t=e.target.value.replace(/\D/g,"").slice(0,20);o(t),p(b(t)),v(t)},disabled:s,className:g?"border-destructive":""}),g&&a.jsx("p",{className:"text-sm text-destructive",children:g})]})}),a.jsx(c.cN,{children:a.jsx(i.Button,{type:"submit",disabled:s||!n||!!g,children:s?"Binding...":"Bind Card"})})]})]})]})}var p=s(30361),g=s(50949),h=s(18019),b=s(39183),v=s(28916),j=s(79210),y=s(567),N=s(65304),w=s(77863);function C(){let{data:e,status:t}=(0,r.useSession)(),s=(0,o.useRouter)(),[i,n]=(0,l.useState)([]),[c,m]=(0,l.useState)(!0),[u,x]=(0,l.useState)("all"),b=async()=>{try{let e=await fetch("/api/cards"),t=await e.json();e.ok?n(t):d.A.error("Failed to load cards")}catch(e){d.A.error("Error loading cards")}finally{m(!1)}},v=async e=>{try{(await fetch(`/api/cards/${e}/activate`,{method:"POST"})).ok?(d.A.success("Card activated successfully"),b()):d.A.error("Failed to activate card")}catch(e){d.A.error("Error activating card")}},y=i.filter(e=>"all"===u||("active"===u?"Active"===e.status:"inactive"!==u||"Inactive"===e.status)),N=e=>{switch(e){case"Active":return{icon:a.jsx(p.Z,{className:"h-5 w-5"}),color:"text-green-600",bg:"bg-green-50 border-green-100",badge:"bg-green-100 text-green-800"};case"Inactive":return{icon:a.jsx(g.Z,{className:"h-5 w-5"}),color:"text-amber-600",bg:"bg-amber-50 border-amber-100",badge:"bg-amber-100 text-amber-800"};default:return{icon:a.jsx(h.Z,{className:"h-5 w-5"}),color:"text-red-600",bg:"bg-red-50 border-red-100",badge:"bg-red-100 text-red-800"}}};return"loading"===t||"unauthenticated"===t?a.jsx("div",{className:"container py-10",children:a.jsx("div",{className:"flex items-center justify-center h-64",children:a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):(0,a.jsxs)("div",{className:"container max-w-6xl py-12",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 md:justify-between md:items-center mb-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"My Yolloo Cards"}),a.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage your eSIM cards and connected devices"})]}),a.jsx("div",{className:"flex",children:a.jsx(f,{})})]}),(0,a.jsxs)(j.Tabs,{defaultValue:"all",className:"mb-8",onValueChange:e=>x(e),children:[a.jsx("div",{className:"flex justify-between items-center",children:(0,a.jsxs)(j.TabsList,{className:"mb-4",children:[(0,a.jsxs)(j.TabsTrigger,{value:"all",className:"px-4",children:["All Cards (",i.length,")"]}),(0,a.jsxs)(j.TabsTrigger,{value:"active",className:"px-4",children:["Active (",i.filter(e=>"Active"===e.status).length,")"]}),(0,a.jsxs)(j.TabsTrigger,{value:"inactive",className:"px-4",children:["Inactive (",i.filter(e=>"Inactive"===e.status).length,")"]})]})}),a.jsx(j.TabsContent,{value:"all",className:"mt-0",children:c?a.jsx(S,{count:3}):y.length>0?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>a.jsx(A,{card:e,activateCard:v,router:s,getCardStatusStyles:N},e.id))}):a.jsx(k,{})}),a.jsx(j.TabsContent,{value:"active",className:"mt-0",children:c?a.jsx(S,{count:2}):y.length>0?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>a.jsx(A,{card:e,activateCard:v,router:s,getCardStatusStyles:N},e.id))}):a.jsx(P,{type:"active"})}),a.jsx(j.TabsContent,{value:"inactive",className:"mt-0",children:c?a.jsx(S,{count:2}):y.length>0?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>a.jsx(A,{card:e,activateCard:v,router:s,getCardStatusStyles:N},e.id))}):a.jsx(P,{type:"inactive"})})]})]})}function A({card:e,activateCard:t,router:s,getCardStatusStyles:r}){let l=r(e.status);return(0,a.jsxs)(n.Zb,{className:"shadow-md border-0 overflow-hidden transition-all hover:shadow-lg",children:[a.jsx("div",{className:`bg-gradient-to-r ${"Active"===e.status?"from-primary/80 to-primary/60":"Inactive"===e.status?"from-amber-500/70 to-amber-400/50":"from-red-500/70 to-red-400/50"} p-6`,children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-semibold text-white mb-1",children:e.customName||"Yolloo Card"}),(0,a.jsxs)("p",{className:"text-sm text-white/80",children:["#",e.number]})]}),a.jsx("div",{className:"text-white/90",children:l.icon})]})}),a.jsx(n.aY,{className:"p-6 space-y-4",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-muted-foreground",children:"Status"}),a.jsx(y.C,{className:l.badge,children:e.status})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-muted-foreground",children:"Type"}),a.jsx("span",{className:"text-sm font-medium",children:e.type})]}),e.activationDate&&(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("span",{className:"text-sm text-muted-foreground",children:"Activated"}),a.jsx("span",{className:"text-sm font-medium",children:w.CN.forUserSafe(e.activationDate)})]})]})}),(0,a.jsxs)(n.eW,{className:"px-6 py-4 border-t bg-slate-50/80 flex justify-between",children:[(0,a.jsxs)(i.Button,{variant:"ghost",className:"text-slate-700 hover:text-primary hover:bg-primary/5",onClick:()=>s.push(`/cards/${e.id}`),children:["View Details",a.jsx(b.Z,{className:"ml-1 h-4 w-4"})]}),"Inactive"===e.status&&a.jsx(i.Button,{onClick:()=>t(e.id),className:"bg-green-600 hover:bg-green-700 text-white",children:"Activate"})]})]})}function S({count:e}){return a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array(e).fill(0).map((e,t)=>(0,a.jsxs)(n.Zb,{className:"shadow-md border-0 overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-slate-200 to-slate-100 p-6",children:[a.jsx(N.O,{className:"h-6 w-3/4 mb-2"}),a.jsx(N.O,{className:"h-4 w-1/2"})]}),a.jsx(n.aY,{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(N.O,{className:"h-4 w-20"}),a.jsx(N.O,{className:"h-5 w-16 rounded-full"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(N.O,{className:"h-4 w-14"}),a.jsx(N.O,{className:"h-4 w-24"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx(N.O,{className:"h-4 w-16"}),a.jsx(N.O,{className:"h-4 w-20"})]})]})}),(0,a.jsxs)(n.eW,{className:"px-6 py-4 border-t bg-slate-50/80 flex justify-between",children:[a.jsx(N.O,{className:"h-9 w-28"}),a.jsx(N.O,{className:"h-9 w-24"})]})]},t))})}function k(){return(0,a.jsxs)("div",{className:"border-2 border-dashed border-slate-200 rounded-lg p-10 text-center",children:[a.jsx("div",{className:"mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mb-6",children:a.jsx(v.Z,{className:"h-8 w-8 text-slate-400"})}),a.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No Yolloo Cards Yet"}),a.jsx("p",{className:"text-muted-foreground max-w-md mx-auto mb-6",children:"You haven't added any Yolloo cards to your account. Add a card to get started with managing your eSIMs."})]})}function P({type:e}){return(0,a.jsxs)("div",{className:"border-2 border-dashed border-slate-200 rounded-lg p-10 text-center",children:[a.jsx("div",{className:"mx-auto w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mb-6",children:"active"===e?a.jsx(p.Z,{className:"h-8 w-8 text-slate-400"}):a.jsx(g.Z,{className:"h-8 w-8 text-slate-400"})}),(0,a.jsxs)("h3",{className:"text-xl font-semibold mb-2",children:["No ","active"===e?"Active":"Inactive"," Cards"]}),a.jsx("p",{className:"text-muted-foreground max-w-md mx-auto mb-6",children:"active"===e?"You don't have any active cards yet. Activate a card to see it here.":"You don't have any inactive cards. All your cards are activated or you need to add more cards."})]})}},567:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var a=s(10326);s(17577);var r=s(79360),i=s(77863);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return a.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},33071:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>o,Zb:()=>n,aY:()=>c,eW:()=>m,ll:()=>d});var a=s(10326),r=s(17577),i=s(77863);let n=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let m=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter"},62288:(e,t,s)=>{"use strict";s.d(t,{$N:()=>p,Be:()=>g,Vq:()=>d,cN:()=>f,cZ:()=>u,fK:()=>x,hg:()=>o});var a=s(10326),r=s(17577),i=s(11123),n=s(94019),l=s(77863);let d=i.fC,o=i.xz,c=i.h_;i.x8;let m=r.forwardRef(({className:e,...t},s)=>a.jsx(i.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));m.displayName=i.aV.displayName;let u=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(c,{children:[a.jsx(m,{}),(0,a.jsxs)(i.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(n.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=i.VY.displayName;let x=({className:e,...t})=>a.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});x.displayName="DialogHeader";let f=({className:e,...t})=>a.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let p=r.forwardRef(({className:e,...t},s)=>a.jsx(i.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));p.displayName=i.Dx.displayName;let g=r.forwardRef(({className:e,...t},s)=>a.jsx(i.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));g.displayName=i.dk.displayName},31048:(e,t,s)=>{"use strict";s.d(t,{_:()=>o});var a=s(10326),r=s(17577),i=s(34478),n=s(79360),l=s(77863);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...t},s)=>a.jsx(i.f,{ref:s,className:(0,l.cn)(d(),e),...t}));o.displayName=i.f.displayName},65304:(e,t,s)=>{"use strict";s.d(t,{O:()=>i});var a=s(10326),r=s(77863);function i({className:e,...t}){return a.jsx("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",e),...t})}},79210:(e,t,s)=>{"use strict";s.d(t,{Tabs:()=>l,TabsContent:()=>c,TabsList:()=>d,TabsTrigger:()=>o});var a=s(10326),r=s(17577),i=s(13239),n=s(77863);let l=i.fC,d=r.forwardRef(({className:e,...t},s)=>a.jsx(i.aV,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=i.aV.displayName;let o=r.forwardRef(({className:e,...t},s)=>a.jsx(i.xz,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));o.displayName=i.xz.displayName;let c=r.forwardRef(({className:e,...t},s)=>a.jsx(i.VY,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.VY.displayName},30361:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(62881).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},91375:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var a=s(68570);let r=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\cards\page.tsx`),{__esModule:i,$$typeof:n}=r;r.default;let l=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\cards\page.tsx#default`)},57481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(66621);let r=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},34478:(e,t,s)=>{"use strict";s.d(t,{f:()=>l});var a=s(17577),r=s(45226),i=s(10326),n=a.forwardRef((e,t)=>(0,i.jsx)(r.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,1123,3239,4824],()=>s(15042));module.exports=a})();