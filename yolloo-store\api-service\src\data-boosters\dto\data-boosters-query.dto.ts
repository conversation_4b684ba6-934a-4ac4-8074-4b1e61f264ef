import { IsOptional, IsString, IsIn, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class DataBoostersQueryDto {
  @IsOptional()
  @IsIn(['emergency', 'daily', 'weekly', 'monthly'])
  boosterType?: 'emergency' | 'daily' | 'weekly' | 'monthly';

  @IsOptional()
  @IsIn(['100MB', '500MB', '1GB', '3GB', '5GB', '10GB'])
  dataSize?: string;

  @IsOptional()
  @IsIn(['instant', 'scheduled'])
  activationType?: 'instant' | 'scheduled';

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  pageSize?: number = 10;

  @IsOptional()
  @IsString()
  sortBy?: string = 'price';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

export class DataBoosterOrderDto {
  @IsString()
  boosterId: string;

  @IsOptional()
  @IsString()
  activationTime?: string; // ISO string for scheduled activation

  @IsOptional()
  @IsString()
  targetNumber?: string; // 目标号码（如果是为其他号码购买）
}
