"use strict";(()=>{var e={};e.id=4458,e.ids=[4458],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},88256:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>I,patchFetch:()=>D,requestAsyncStorage:()=>v,routeModule:()=>w,serverHooks:()=>x,staticGenerationAsyncStorage:()=>E});var n={};t.r(n),t.d(n,{DELETE:()=>h,PATCH:()=>g,dynamic:()=>p,fetchCache:()=>f,revalidate:()=>y});var a=t(49303),i=t(88716),o=t(60670),s=t(87070),l=t(75571),u=t(90455),c=t(72331),d=t(50650),m=t(53524);let p="force-dynamic",f="force-no-store",y=0;async function g(e,{params:r}){try{let t=await (0,l.getServerSession)(u.L);if(!t||"ADMIN"!==t.user.role)return new s.NextResponse("Unauthorized",{status:403});let{number:n,type:a,status:i,activationDate:o,expiryDate:m}=await e.json(),p=(0,d.vI)(n);if(!p)return new s.NextResponse("Card number is required and must contain digits",{status:400});let f=await c._.yollooCard.update({where:{id:r.cardId},data:{number:p,type:a,status:i,activationDate:o,expiryDate:m}});return s.NextResponse.json(f)}catch(e){if(console.error("[CARD_PATCH]",e),e instanceof m.Prisma.PrismaClientKnownRequestError&&"P2002"===e.code)return new s.NextResponse("Card number already exists",{status:400});return new s.NextResponse("Internal error",{status:500})}}async function h(e,{params:r}){try{let e=await (0,l.getServerSession)(u.L);if(!e||"ADMIN"!==e.user.role)return new s.NextResponse("Unauthorized",{status:403});let t=await c._.yollooCard.delete({where:{id:r.cardId}});return s.NextResponse.json(t)}catch(e){return console.error("[CARD_DELETE]",e),new s.NextResponse("Internal error",{status:500})}}let w=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/cards/[cardId]/route",pathname:"/api/admin/cards/[cardId]",filename:"route",bundlePath:"app/api/admin/cards/[cardId]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\cards\\[cardId]\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:v,staticGenerationAsyncStorage:E,serverHooks:x}=w,I="/api/admin/cards/[cardId]/route";function D(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:E})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var n=t(7585),a=t(72331),i=t(77234),o=t(53797),s=t(42023),l=t.n(s),u=t(93475);let c={adapter:{...(0,n.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,o.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await l().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,o.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,u.Ak)(e.email);if(!r||r!==e.code)return null;await (0,u.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:n,request:i}){try{if(t&&t.id){let r=i?.headers||new Headers,o=r.get("user-agent")||"",s=r.get("x-forwarded-for"),l=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",u="unknown";n?u=n.code&&!n.password?"email_code":"password":e&&(u=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:l||null,userAgent:o||null,loginMethod:u,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],n=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(n).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let n=new URL(e);if(t.some(e=>n.hostname===e||n.hostname.includes(e)||n.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(n);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return n}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:n}){if("update"===t&&n)return{...e,...n.user};let i=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return i?{id:i.id,name:i.name,email:i.email,picture:i.image,role:i.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>a});var n=t(53524);let a=global.prisma||new n.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>l,qc:()=>u,yz:()=>c});var n=t(62197),a=t.n(n);let i=null;function o(){if(!i){let e=process.env.REDIS_URL||"redis://localhost:6379";(i=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),i.on("connect",()=>{console.log("Successfully connected to Redis")})}return i}async function s(e,r,t=300){try{let n=o(),a=`verification_code:${e}`;return await n.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function l(e){try{let r=o(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function u(e){try{let r=o(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let n=o(),a=`rate_limit:${e}`,i=await n.get(a),s=i?parseInt(i):0;if(s>=r)return!1;return 0===s?await n.setex(a,t,"1"):await n.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},50650:(e,r,t)=>{t.d(r,{CN:()=>f,ED:()=>y,QG:()=>h,T4:()=>c,cn:()=>u,eP:()=>v,mo:()=>w,vI:()=>g});var n=t(55761),a=t(62386),i=t(6180),o=t(4284),s=t(35772),l=t(21740);function u(...e){return(0,a.m6)((0,n.W)(e))}function c(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}let d={TIMEZONE:"Asia/Shanghai",LOCALE:"zh-CN",DATE_FORMAT:"yyyy-MM-dd",DATETIME_FORMAT:"yyyy-MM-dd HH:mm:ss"};function m(){return d.TIMEZONE}function p(e){if(!e)return null;try{let r;if(e instanceof Date)r=e;else if("string"==typeof e)r=e.includes("T")||e.includes("Z")?(0,i.D)(e):new Date(e);else{if("number"!=typeof e)return null;r=new Date(e)}return(0,o.J)(r)?r:null}catch(r){return console.warn("Date parsing error:",r,"Input:",e),null}}let f={short:(e,r="Invalid Date")=>{let t=p(e);return t?(0,s.WU)(t,d.DATE_FORMAT):r},full:(e,r="Invalid Date")=>{let t=p(e);return t?(0,s.WU)(t,d.DATETIME_FORMAT):r},long:(e,r="Invalid Date")=>{let t=p(e);return t?t.toLocaleDateString(d.LOCALE,{year:"numeric",month:"long",day:"numeric"}):r},relative:(e,r="Invalid Date")=>{let t=p(e);return t?(0,l.Q)(t,{addSuffix:!0}):r},time:(e,r="Invalid Time")=>{let t=p(e);return t?(0,s.WU)(t,"HH:mm:ss"):r},timeShort:(e,r="Invalid Time")=>{let t=p(e);return t?(0,s.WU)(t,"HH:mm"):r},withTimezone:(e,r,t="Invalid Date")=>{let n=p(e);return n?new Intl.DateTimeFormat(d.LOCALE,{timeZone:r||d.TIMEZONE,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(n):t},forUser:(e,r="Invalid Date")=>{let t=p(e);if(!t)return r;let n=m();return new Intl.DateTimeFormat(d.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(t)},forUserShort:(e,r="Invalid Date")=>{let t=p(e);if(!t)return r;let n=m();return new Intl.DateTimeFormat(d.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit"}).format(t)},forUserSafe:(e,r="Invalid Date")=>{let t=p(e);if(!t)return r;let n=d.TIMEZONE;return new Intl.DateTimeFormat(d.LOCALE,{timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).format(t)},forUserRelative:(e,r="Invalid Date")=>{let t=p(e);return t?(0,l.Q)(t,{addSuffix:!0}):r},iso:(e,r="")=>{let t=p(e);return t?t.toISOString():r},custom:(e,r,t="Invalid Date")=>{let n=p(e);return n?(0,s.WU)(n,r):t}},y={addDays:(e,r)=>new Date((p(e)||new Date).getTime()+864e5*r),addHours:(e,r)=>new Date((p(e)||new Date).getTime()+36e5*r),addMinutes:(e,r)=>new Date((p(e)||new Date).getTime()+6e4*r),daysBetween:(e,r)=>{let t=p(e),n=p(r);return t&&n?Math.floor((n.getTime()-t.getTime())/864e5):0},isExpired:e=>{let r=p(e);return!!r&&r.getTime()<Date.now()}};function g(e){return e?e.replace(/\D/g,""):""}function h(e){return e&&(!(e.length>20)||/^\d+$/.test(e))?e.replace(/\D/g,"").replace(/(\d{5})(?=\d)/g,"$1-"):""}function w(e){return e?e.replace(/\s*([-\/])\s*/g," $1 ").replace(/\s{2,}/g," ").trim():""}function v(e){let r=e.match(/^\s*\[(5G|4G)\]\s*(.*)$/i);return r?{badge:r[1].toUpperCase(),name:r[2]}:{name:e}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var n={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o(void 0);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(n,i,s):n[i]=e[i]}return n.default=e,t&&t.set(e,n),n}(t(45609));function o(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(o=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(n,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,1615,9092,5972,2197,2023,7005,5772,7624],()=>t(88256));module.exports=n})();