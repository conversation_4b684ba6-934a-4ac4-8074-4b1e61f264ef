"use strict";(()=>{var e={};e.id=7994,e.ids=[7994],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},49236:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>R,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>E,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h});var o={};t.r(o),t.d(o,{DELETE:()=>w,PATCH:()=>m,dynamic:()=>u,fetchCache:()=>p,revalidate:()=>f});var a=t(49303),i=t(88716),n=t(60670),s=t(87070),d=t(75571),l=t(72331),c=t(90455);let u="force-dynamic",p="force-no-store",f=0;async function m(e,{params:r}){try{let t=await (0,d.getServerSession)(c.L);if(!t||"ADMIN"!==t.user.role)return new s.NextResponse("Unauthorized",{status:401});let{status:o,trackingNumber:a}=await e.json();if(!o)return new s.NextResponse("Status is required",{status:400});let i=await l._.order.findUnique({where:{id:r.orderId},select:{id:!0,status:!0,odooStatuses:{select:{id:!0,orderId:!0,variantCode:!0,status:!0,trackingNumber:!0,lastCheckedAt:!0}},shippingAddress:!0,shippingAddressSnapshot:!0}});if(!i)return new s.NextResponse("Order not found",{status:404});if("SHIPPED"===o&&!a)return new s.NextResponse("Tracking number is required for shipping",{status:400});if("CANCELLED"===o&&"CANCELLED"!==i.status){for(let e of(await l._.orderItem.findMany({where:{orderId:r.orderId},select:{productCode:!0,quantity:!0}})))if(e.productCode)try{await l._.product.findUnique({where:{sku:e.productCode},select:{id:!0}})?(await l._.product.update({where:{sku:e.productCode},data:{stock:{increment:e.quantity}}}),console.log(`[ADMIN_ORDER_PATCH] Restored stock for product ${e.productCode} by ${e.quantity}`)):console.warn(`[ADMIN_ORDER_PATCH] Product with sku ${e.productCode} not found, skipping stock restoration`)}catch(r){console.error(`[ADMIN_ORDER_PATCH] Failed to restore stock for product ${e.productCode}:`,r)}}let n=await l._.order.update({where:{id:r.orderId},data:{status:o}});if("PAID"===o&&await l._.payment.updateMany({where:{orders:{some:{id:r.orderId}}},data:{status:"COMPLETED"}}),"DELIVERED"===o){let e=await l._.affiliateReferral.findFirst({where:{orderId:r.orderId,status:"PENDING"},include:{organizationCommission:!0}});e&&(console.log(`Updating affiliate referral status for order: ${r.orderId}`),await l._.affiliateReferral.update({where:{id:e.id},data:{status:"APPROVED"}}),e.organizationCommission&&(console.log(`Updating organization commission status for order: ${r.orderId}`),await l._.organizationCommission.update({where:{id:e.organizationCommissionId},data:{status:"APPROVED"}})))}if(a){let e="default",t=await l._.odooOrderStatus.findFirst({where:{orderId:r.orderId,variantCode:e}});t?await l._.odooOrderStatus.update({where:{id:t.id},data:{status:o,trackingNumber:a,lastCheckedAt:new Date}}):await l._.odooOrderStatus.create({data:{orderId:r.orderId,variantCode:e,status:o,trackingNumber:a,lastCheckedAt:new Date}})}return s.NextResponse.json(n)}catch(e){return console.error("[ORDER_PATCH]",e),new s.NextResponse("Internal error",{status:500})}}async function w(e,{params:r}){try{let e=await (0,d.getServerSession)(c.L);if(!e||"ADMIN"!==e.user.role)return new s.NextResponse("Unauthorized",{status:401});if(!await l._.order.findUnique({where:{id:r.orderId},select:{id:!0}}))return new s.NextResponse("Order not found",{status:404});return console.log(`[ORDER_DELETE] Starting deletion process for order ID: ${r.orderId}`),await l._.$transaction(async e=>{try{console.log(`[ORDER_DELETE] Looking for affiliate referral for order: ${r.orderId}`);let t=await e.affiliateReferral.findUnique({where:{orderId:r.orderId},select:{id:!0,organizationCommissionId:!0}});for(let o of(t?(console.log(`[ORDER_DELETE] Found referral: ${t.id} for order: ${r.orderId}`),t.organizationCommissionId&&(console.log(`[ORDER_DELETE] Updating organization commission status: ${t.organizationCommissionId}`),await e.organizationCommission.update({where:{id:t.organizationCommissionId},data:{status:"REJECTED"}})),console.log(`[ORDER_DELETE] Deleting affiliate referral: ${t.id}`),await e.affiliateReferral.delete({where:{id:t.id}})):console.log(`[ORDER_DELETE] No referral found for order: ${r.orderId}`),console.log(`[ORDER_DELETE] Getting order items for order: ${r.orderId}`),await e.orderItem.findMany({where:{orderId:r.orderId},select:{id:!0,productCode:!0,quantity:!0}})))if(o.productCode)try{await e.product.findUnique({where:{sku:o.productCode},select:{id:!0}})?(await e.product.update({where:{sku:o.productCode},data:{stock:{increment:o.quantity}}}),console.log(`[ORDER_DELETE] Restored stock for product ${o.productCode} by ${o.quantity}`)):console.warn(`[ORDER_DELETE] Product with sku ${o.productCode} not found, skipping stock restoration`)}catch(e){console.error(`[ORDER_DELETE] Failed to restore stock for product ${o.productCode}:`,e)}let o=await e.orderItem.deleteMany({where:{orderId:r.orderId}});console.log(`[ORDER_DELETE] Deleted ${o.count} order items`),console.log(`[ORDER_DELETE] Checking for odoo status of order: ${r.orderId}`);let a=await e.odooOrderStatus.deleteMany({where:{orderId:r.orderId}});console.log(`[ORDER_DELETE] Deleted ${a.count} odoo status records`),console.log(`[ORDER_DELETE] Deleting order: ${r.orderId}`),await e.order.delete({where:{id:r.orderId}}),console.log(`[ORDER_DELETE] Successfully deleted order: ${r.orderId}`)}catch(e){throw console.error("[ORDER_DELETE] Transaction error:",e),e}}),new s.NextResponse(null,{status:204})}catch(e){return console.error("[ORDER_DELETE] Error:",e),new s.NextResponse(`Internal error: ${e instanceof Error?e.message:"Unknown error"}`,{status:500})}}let E=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/orders/[orderId]/route",pathname:"/api/admin/orders/[orderId]",filename:"route",bundlePath:"app/api/admin/orders/[orderId]/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\admin\\orders\\[orderId]\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:y}=E,R="/api/admin/orders/[orderId]/route";function _(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},90455:(e,r,t)=>{t.d(r,{L:()=>c});var o=t(7585),a=t(72331),i=t(77234),n=t(53797),s=t(42023),d=t.n(s),l=t(93475);let c={adapter:{...(0,o.N)(a._),getUser:async e=>{let r=await a._.user.findUnique({where:{id:e}});return r?{...r,role:r.role}:null},getUserByEmail:async e=>{let r=await a._.user.findUnique({where:{email:e}});return r?{...r,role:r.role}:null},createUser:async e=>{let r=await a._.user.create({data:{...e,role:"CUSTOMER"}});return{...r,role:r.role}}},session:{strategy:"jwt",maxAge:2592e3},pages:{signIn:"/auth/signin",error:"/auth/error"},providers:[(0,n.Z)({id:"credentials",name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Invalid credentials");let r=await a._.user.findUnique({where:{email:e.email}});if(!r||!r.hashedPassword||!await d().compare(e.password,r.hashedPassword))throw Error("Invalid credentials");return{id:r.id,name:r.name,email:r.email,image:r.image,role:r.role}}}),(0,n.Z)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Verification Code",type:"text"}},async authorize(e){if(!e?.email||!e?.code)return null;let r=await (0,l.Ak)(e.email);if(!r||r!==e.code)return null;await (0,l.qc)(e.email);let t=await a._.user.findUnique({where:{email:e.email}});if(t)t.emailVerified||(t=await a._.user.update({where:{id:t.id},data:{emailVerified:new Date}}));else{let r=e.email.split("@")[0];t=await a._.user.create({data:{email:e.email,name:r,role:"CUSTOMER",emailVerified:new Date}})}return{id:t.id,name:t.name,email:t.email,image:t.image,role:t.role}}}),(0,i.Z)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"select_account",access_type:"offline",response_type:"code",scope:"openid email profile"}},httpOptions:{timeout:4e4,agent:void 0,headers:{Accept:"application/json","User-Agent":"next-auth"}}})],callbacks:{async signIn({account:e,profile:r,user:t,credentials:o,request:i}){try{if(t&&t.id){let r=i?.headers||new Headers,n=r.get("user-agent")||"",s=r.get("x-forwarded-for"),d=s?s.split(/, /)[0]:r.get("REMOTE_ADDR")||"",l="unknown";o?l=o.code&&!o.password?"email_code":"password":e&&(l=e.provider),await a._.userLoginHistory.create({data:{userId:t.id,ipAddress:d||null,userAgent:n||null,loginMethod:l,additionalInfo:{}}})}if(e?.provider==="google")return!!r?.email;return!0}catch(e){return console.error("Error recording login history:",e),!0}},async redirect({url:e,baseUrl:r}){try{let t=["esim.yolloo.com","yolloo.com","www.yolloo.com","localhost"],o=e.startsWith("http")?e:`${r}${e.startsWith("/")?e:`/${e}`}`,a=new URL(o).searchParams.get("callbackUrl");if(a){let e=decodeURIComponent(a);if(e.startsWith("/"))return`${r}${e.startsWith("/")?e:`/${e}`}`;try{let o=new URL(e);if(t.some(e=>o.hostname===e||o.hostname.includes(e)||o.hostname===new URL(r).hostname))return e}catch(t){if(console.error("Invalid callback URL:",t),!e.includes("://"))return`${r}${e.startsWith("/")?e:`/${e}`}`}}if(e.startsWith("/"))return`${r}${e}`;try{let e=new URL(o);if(t.some(t=>e.hostname===t||e.hostname.includes(t)||e.hostname===new URL(r).hostname))return o}catch(e){console.error("URL parse error:",e)}return r}catch(e){return console.error("Redirect URL parse error:",e),r}},session:async({token:e,session:r})=>(e&&(r.user.id=e.id,r.user.name=e.name,r.user.email=e.email,r.user.image=e.picture,r.user.role=e.role),r),async jwt({token:e,user:r,trigger:t,session:o}){if("update"===t&&o)return{...e,...o.user};let i=await a._.user.findFirst({where:{email:e.email},select:{id:!0,name:!0,email:!0,image:!0,role:!0}});return i?{id:i.id,name:i.name,email:i.email,picture:i.image,role:i.role}:(r&&(e.id=r?.id,e.role=r?.role||"CUSTOMER"),e)}},secret:process.env.NEXTAUTH_SECRET}},72331:(e,r,t)=>{t.d(r,{_:()=>a});var o=t(53524);let a=global.prisma||new o.PrismaClient({log:["error"]})},93475:(e,r,t)=>{t.d(r,{AL:()=>s,Ak:()=>d,qc:()=>l,yz:()=>c});var o=t(62197),a=t.n(o);let i=null;function n(){if(!i){let e=process.env.REDIS_URL||"redis://localhost:6379";(i=new(a())(e,{retryDelayOnFailover:100,maxRetriesPerRequest:3,lazyConnect:!0})).on("error",e=>{console.error("Redis connection error:",e)}),i.on("connect",()=>{console.log("Successfully connected to Redis")})}return i}async function s(e,r,t=300){try{let o=n(),a=`verification_code:${e}`;return await o.setex(a,t,r),!0}catch(e){return console.error("Error setting verification code:",e),!1}}async function d(e){try{let r=n(),t=`verification_code:${e}`;return await r.get(t)}catch(e){return console.error("Error getting verification code:",e),null}}async function l(e){try{let r=n(),t=`verification_code:${e}`;return await r.del(t),!0}catch(e){return console.error("Error deleting verification code:",e),!1}}async function c(e,r,t){try{let o=n(),a=`rate_limit:${e}`,i=await o.get(a),s=i?parseInt(i):0;if(s>=r)return!1;return 0===s?await o.setex(a,t,"1"):await o.incr(a),!0}catch(e){return console.error("Error setting rate limit:",e),!0}}},69955:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0})},75571:(e,r,t)=>{Object.defineProperty(r,"__esModule",{value:!0});var o={};Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i.default}});var a=t(69955);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===a[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return a[e]}}))});var i=function(e,r){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=n(void 0);if(t&&t.has(e))return t.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var s=a?Object.getOwnPropertyDescriptor(e,i):null;s&&(s.get||s.set)?Object.defineProperty(o,i,s):o[i]=e[i]}return o.default=e,t&&t.set(e,o),o}(t(45609));function n(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,t=new WeakMap;return(n=function(e){return e?t:r})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(o,e))&&(e in r&&r[e]===i[e]||Object.defineProperty(r,e,{enumerable:!0,get:function(){return i[e]}}))})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1615,9092,5972,2197,2023,7005],()=>t(49236));module.exports=o})();