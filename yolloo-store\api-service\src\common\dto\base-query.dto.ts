import { IsOptional, Is<PERSON><PERSON>, <PERSON>In, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { DEFAULT_PAGINATION } from '../constants/app.constants';

export class BaseQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  page?: number = DEFAULT_PAGINATION.PAGE;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(DEFAULT_PAGINATION.MAX_PAGE_SIZE)
  pageSize?: number = DEFAULT_PAGINATION.PAGE_SIZE;

  @IsOptional()
  @IsString()
  sortBy?: string = 'price';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'asc';
}

export class BasePackageQueryDto extends BaseQueryDto {
  @IsOptional()
  @IsString()
  category?: string;
}

export class BaseOrderDto {
  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsString()
  promoCode?: string;
}
