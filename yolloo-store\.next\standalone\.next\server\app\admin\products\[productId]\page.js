"use strict";(()=>{var e={};e.id=1058,e.ids=[1058],e.modules={53524:e=>{e.exports=require("@prisma/client")},47849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},71576:e=>{e.exports=require("string_decoder")},24404:e=>{e.exports=require("tls")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},94965:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,originalPathname:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>p}),t(97639),t(75718),t(85460),t(89090),t(26083),t(35866);var o=t(23191),s=t(88716),a=t(37922),n=t.n(a),i=t(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p=["",{children:["admin",{children:["products",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97639)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\[productId]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,75718)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\products\\[productId]\\page.tsx"],l="/admin/products/[productId]/page",c={require:t,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/admin/products/[productId]/page",pathname:"/admin/products/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},97639:(e,r,t)=>{t.r(r),t.d(r,{default:()=>l,revalidate:()=>u});var o=t(19510),s=t(58585),a=t(72331),n=t(20045),i=t(71615);async function d(e){(0,i.headers)();let r=await a._.product.findUnique({where:{id:e},include:{variants:{orderBy:[{durationType:"asc"},{duration:"asc"}]}}});r||(0,s.notFound)();let t=r.variants.map(e=>({id:e.id,price:Number(e.price),currency:e.currency,productId:e.productId,variantCode:e.variantCode,duration:e.duration,durationType:e.durationType,attributes:e.attributes,createdAt:e.createdAt,updatedAt:e.updatedAt}));return{...r,specifications:r.specifications||{},variants:t}}async function p(){return(0,i.headers)(),await a._.category.findMany({orderBy:{name:"asc"}})}let u=0;async function l({params:e}){let[r,t]=await Promise.all([d(e.productId),p()]);return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[o.jsx("h3",{className:"text-lg font-medium",children:"Edit Product"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Make changes to your product here"})]}),o.jsx(n.H,{initialData:r,categories:t})]})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,3239,4824,7123,6208,1453],()=>t(94965));module.exports=o})();