(()=>{var e={};e.id=3336,e.ids=[3336],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},63477:e=>{"use strict";e.exports=require("querystring")},12781:e=>{"use strict";e.exports=require("stream")},71576:e=>{"use strict";e.exports=require("string_decoder")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},80566:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o}),t(77775),t(85460),t(89090),t(26083),t(35866);var r=t(23191),a=t(88716),i=t(37922),n=t.n(i),l=t(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["admin",{children:["organizations",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77775)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\organizations\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,85460)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\admin\\organizations\\[id]\\page.tsx"],m="/admin/organizations/[id]/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/organizations/[id]/page",pathname:"/admin/organizations/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},69385:(e,s,t)=>{Promise.resolve().then(t.bind(t,59953))},59953:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>L});var r=t(10326),a=t(17577),i=t(35047),n=t(44099),l=t(77506),d=t(15919),o=t(21405),c=t(79635),m=t(80239),x=t(94019),u=t(57372),h=t(26589),p=t(90772),j=t(33071),f=t(85999),g=t(77863),v=t(28758),b=t(567),N=t(62288),y=t(54432),w=t(31048),C=t(87673),k=t(74723),R=t(27256),S=t(74064),A=t(90434),z=t(34474),E=t(79210),M=t(15940),F=t(99440),T=t(68762),Z=t(60097),O=t(43273),$=t(50949),P=t(48998),_=t(7027),B=t(32933),I=t(36283);function q({organizationId:e}){let[s,t]=(0,a.useState)(!1),[i,d]=(0,a.useState)(!1),[c,m]=(0,a.useState)(""),[x,u]=(0,a.useState)(""),[h,j]=(0,a.useState)(!1),[v,b]=(0,a.useState)(!1),w=async()=>{try{t(!0);let s=(await n.Z.get(`/api/admin/organizations/${e}/invites`)).data.find(e=>!e.email&&"PENDING"===e.status&&new Date(e.expiresAt)>new Date);if(s){let e=s.inviteCode,t=window.location.origin,r=`${t}/invite/${e}`,a=g.CN.withTimezone(s.expiresAt);m(r),u(a)}else{let s=await n.Z.post(`/api/admin/organizations/${e}/invites`,{isGeneralInvite:!0}),t=s.data.inviteCode,r=window.location.origin,a=`${r}/invite/${t}`,i=g.CN.withTimezone(s.data.expiresAt);m(a),u(i)}b(!1)}catch(e){console.error("Error with invite link:",e),f.A.error("Failed to generate invite link")}finally{t(!1)}},C=async()=>{try{d(!0);let s=await n.Z.post(`/api/admin/organizations/${e}/invites`,{isGeneralInvite:!0}),t=s.data.inviteCode,r=window.location.origin,a=`${r}/invite/${t}`,i=g.CN.withTimezone(s.data.expiresAt);m(a),u(i),b(!1),f.A.success("New invite link generated")}catch(e){console.error("Error generating invite link:",e),f.A.error("Failed to generate new invite link")}finally{d(!1)}};return(0,r.jsxs)(N.Vq,{open:h,onOpenChange:e=>{j(e),e&&w()},children:[r.jsx(N.hg,{asChild:!0,children:(0,r.jsxs)(p.Button,{size:"sm",variant:"outline",className:"gap-1",children:[r.jsx(_.Z,{className:"h-4 w-4"}),"Invite Link"]})}),r.jsx(N.cZ,{className:"sm:max-w-md",children:s?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[r.jsx(l.Z,{className:"h-8 w-8 animate-spin text-primary"}),r.jsx("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading invite link..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Organization Invite Link"}),r.jsx(N.Be,{children:"Share this link with anyone to join your organization. The link will expire in 7 days."})]}),(0,r.jsxs)("div",{className:"space-y-4 py-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(y.I,{readOnly:!0,value:c,className:"font-mono text-sm"}),r.jsx(p.Button,{size:"icon",variant:"outline",onClick:()=>{navigator.clipboard.writeText(c).then(()=>{b(!0),f.A.success("Invite link copied to clipboard"),setTimeout(()=>{b(!1)},2e3)})},disabled:!c,className:"shrink-0",title:"Copy to clipboard",children:v?r.jsx(B.Z,{className:"h-4 w-4"}):r.jsx(I.Z,{className:"h-4 w-4"})})]}),x&&(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-2",children:["This link will expire on ",x]})]}),r.jsx("div",{className:"flex justify-between items-center",children:r.jsx(p.Button,{variant:"outline",size:"sm",onClick:C,disabled:i,className:"gap-1",children:i?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"h-4 w-4 animate-spin"}),"Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.Z,{className:"h-4 w-4"}),"Generate New Link"]})})})]})]})})]})}let D=R.z.object({name:R.z.string().min(3,"Name must be at least 3 characters").optional(),description:R.z.string().optional(),logo:R.z.string().optional(),commissionRate:R.z.number().min(0).max(1).optional(),discountRate:R.z.number().min(0).max(1).optional(),status:R.z.enum(["ACTIVE","INACTIVE","SUSPENDED"]).optional()});function L({params:e}){let s=(0,i.useRouter)(),[t,R]=(0,a.useState)(!0),[_,B]=(0,a.useState)(null),[I,L]=(0,a.useState)([]),[V,U]=(0,a.useState)([]),[Y,X]=(0,a.useState)(null),[Q,W]=(0,a.useState)(!1),[G,H]=(0,a.useState)(!1),[K,J]=(0,a.useState)(!1),[ee,es]=(0,a.useState)(!1),[et,er]=(0,a.useState)(!1),[ea,ei]=(0,a.useState)(null),[en,el]=(0,a.useState)(!1),[ed,eo]=(0,a.useState)(!1),[ec,em]=(0,a.useState)(!1),[ex,eu]=(0,a.useState)(null),[eh,ep]=(0,a.useState)(""),[ej,ef]=(0,a.useState)(.1),[eg,ev]=(0,a.useState)(!1),[eb,eN]=(0,a.useState)(""),[ey,ew]=(0,a.useState)([]),[eC,ek]=(0,a.useState)(null),[eR,eS]=(0,a.useState)(!1),[eA,ez]=(0,a.useState)(!1),[eE,eM]=(0,a.useState)(!1),[eF,eT]=(0,a.useState)(""),[eZ,eO]=(0,a.useState)(""),[e$,eP]=(0,a.useState)(!1),[e_,eB]=(0,a.useState)(!1),[eI,eq]=(0,a.useState)(null),[eD,eL]=(0,a.useState)(null),[eV,eU]=(0,a.useState)([]),[eY,eX]=(0,a.useState)([]),[eQ,eW]=(0,a.useState)(!1),[eG,eH]=(0,a.useState)(1),[eK,eJ]=(0,a.useState)(10),[e0,e2]=(0,a.useState)([]),[e1,e4]=(0,a.useState)(!1),[e5,e7]=(0,a.useState)(!1),[e3,e6]=(0,a.useState)(null),[e8,e9]=(0,a.useState)(!1),[se,ss]=(0,a.useState)(""),[st,sr]=(0,a.useState)(!1),[sa,si]=(0,a.useState)(null),[sn,sl]=(0,a.useState)([]),[sd,so]=(0,a.useState)(!1),{register:sc,handleSubmit:sm,formState:{errors:sx},reset:su,setValue:sh}=(0,k.cI)({resolver:(0,S.F)(D)}),sp=async s=>{try{H(!0);let t=await n.Z.patch(`/api/admin/organizations/${e.id}`,s);B(t.data),f.A.success("Organization updated successfully"),W(!1)}catch(e){console.error("Error updating organization:",e),f.A.error("Failed to update organization")}finally{H(!1)}},sj=async()=>{try{J(!0),await n.Z.delete(`/api/admin/organizations/${e.id}`),f.A.success("Organization deleted successfully"),s.push("/admin/organizations")}catch(e){console.error("Error deleting organization:",e),f.A.error("Failed to delete organization"),J(!1)}},sf=async()=>{try{let s=await n.Z.get(`/api/admin/organizations/${e.id}/members`);L(s.data||[])}catch(e){console.error("Error fetching members:",e),f.A.error("Failed to fetch members")}},sg=async()=>{try{let s;if(er(!0),st){if(!se){f.A.error("Please enter an email address"),er(!1);return}s=await n.Z.post(`/api/admin/organizations/${e.id}/members`,{email:se,commissionRate:ej,isAdmin:eg});let t=window.location.origin,r=`${t}/invite/${s.data.invite.inviteCode}`;si(r),f.A.success("Invitation sent successfully")}else{if(!eC){f.A.error("Please select a user"),er(!1);return}s=await n.Z.post(`/api/admin/organizations/${e.id}/members`,{userId:eC.id,commissionRate:ej,isAdmin:eg}),await sf(),f.A.success("Member added successfully"),ek(null),ef(.1),ev(!1),eN(""),ew([]),es(!1)}}catch(e){console.error("Error adding member:",e),f.A.error("Failed to add member")}finally{er(!1)}},sv=async()=>{if(ea)try{eo(!0),await n.Z.patch(`/api/admin/organizations/${e.id}/members/${ea.id}`,{commissionRate:ej,isAdmin:eg}),await sf(),f.A.success("Member updated successfully"),ei(null),el(!1)}catch(e){console.error("Error updating member:",e),f.A.error("Failed to update member")}finally{eo(!1)}},sb=async()=>{if(ex)try{em(!0),await n.Z.delete(`/api/admin/organizations/${e.id}/members/${ex.id}`),await sf(),f.A.success("Member removed successfully"),eu(null)}catch(e){console.error("Error removing member:",e),f.A.error("Failed to remove member")}finally{em(!1)}},sN=e=>{ei(e),ef(e.commissionRate),ev(e.isAdmin),el(!0)},sy=async()=>{if(!eF.trim()){f.A.error("Please enter user data");return}try{eP(!0),eq(null);let s=eF.trim().split("\n"),t=s[0].split(",").map(e=>e.trim().toLowerCase()),r=t.indexOf("name"),a=t.indexOf("email"),i=t.indexOf("role");if(-1===r||-1===a){f.A.error("CSV must include 'name' and 'email' columns");return}let l=s.slice(1).map(e=>{let s=e.split(",").map(e=>e.trim());return{name:s[r],email:s[a],role:-1!==i?s[i]:"CUSTOMER"}}).filter(e=>e.name&&e.email);if(0===l.length){f.A.error("No valid users found in CSV");return}let d=await n.Z.post("/api/admin/users/batch",l);if(eq(d.data),eU(d.data.resetLinks||[]),f.A.success(`Successfully created ${d.data.users.length} users with password reset links`),d.data.users.length>0)try{let s=d.data.users.map(e=>({userId:e.id,commissionRate:ej||.1,isAdmin:!1})),t=await n.Z.post(`/api/admin/organizations/${e.id}/members/batch`,s);await sf(),f.A.success(`Added ${t.data.totalAdded} users as members to the organization`),t.data.totalFailed>0&&f.A.error(`Failed to add ${t.data.totalFailed} users as members`)}catch(e){console.error("Error adding batch members:",e),f.A.error("Users were created but could not be added as members")}}catch(e){console.error("Error creating batch users:",e),e.response?.data?.existingEmails?f.A.error(`Some emails already exist: ${e.response.data.existingEmails.join(", ")}`):f.A.error("Failed to create users")}finally{eP(!1)}},sw=async()=>{if(0===eY.length){f.A.error("Please select users to add");return}try{eB(!0),eL(null);let s=eY.map(e=>({userId:e.id,commissionRate:ej,isAdmin:eg})),t=await n.Z.post(`/api/admin/organizations/${e.id}/members/batch`,s);await sf(),eL(t.data),f.A.success(`Successfully added ${t.data.totalAdded} members`),eX([]),eM(!1)}catch(e){console.error("Error adding batch members:",e),f.A.error("Failed to add members")}finally{eB(!1)}},sC=e=>{eY.some(s=>s.id===e.id)?eX(s=>s.filter(s=>s.id!==e.id)):eX(s=>[...s,e])},sk=Math.ceil(I.length/eK),sR=()=>{let e=(eG-1)*eK;return I.slice(e,e+eK)},sS=e=>{eH(e)},sA=async()=>{if(0===e0.length){f.A.error("Please select members to delete");return}try{e7(!0);let s=e0.map(s=>n.Z.delete(`/api/admin/organizations/${e.id}/members/${s.id}`));await Promise.all(s),await sf(),f.A.success(`Successfully removed ${e0.length} members`),e2([]),e4(!1)}catch(e){console.error("Error removing members:",e),f.A.error("Failed to remove members")}finally{e7(!1)}},sz=e=>{e0.some(s=>s.id===e.id)?e2(s=>s.filter(s=>s.id!==e.id)):e2(s=>[...s,e])},sE=e=>{e6(e),e9(!0)},sM=async()=>{try{so(!0);let s=await n.Z.post(`/api/admin/organizations/${e.id}/refresh-stats`);if(!s.data.success){f.A.error("Failed to refresh statistics");return}try{let s=await n.Z.get(`/api/admin/organizations/${e.id}/analytics`);s.data.stats&&X(s.data.stats),s.data.organization&&B(e=>({...e,...s.data.organization})),s.data.memberOrders&&sl(s.data.memberOrders),f.A.success("Statistics refreshed successfully")}catch(e){console.error("Error fetching updated data:",e),s.data.stats&&X(s.data.stats),f.A.error("Partially refreshed statistics")}}catch(e){console.error("Error refreshing statistics:",e),f.A.error("Failed to refresh statistics")}finally{so(!1)}};return t?r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx(l.Z,{className:"h-8 w-8 animate-spin text-primary"})}):_?(0,r.jsxs)("div",{className:"container mx-auto py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx(p.Button,{asChild:!0,variant:"outline",size:"sm",className:"mb-4",children:(0,r.jsxs)(A.default,{href:"/admin/organizations",children:[r.jsx(h.c.arrowLeft,{className:"mr-2 h-4 w-4"}),"Back to Organizations"]})}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[_.logo?(0,r.jsxs)(v.qE,{className:"h-12 w-12",children:[r.jsx(v.F$,{src:_.logo,alt:_.name}),r.jsx(v.Q5,{children:_.name?.substring(0,2).toUpperCase()||"OR"})]}):r.jsx(v.qE,{className:"h-12 w-12",children:r.jsx(v.Q5,{children:_.name?.substring(0,2).toUpperCase()||"OR"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("h1",{className:"text-3xl font-bold",children:_.name}),(e=>{switch(e){case"ACTIVE":return r.jsx(b.C,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200",children:"Active"});case"INACTIVE":return r.jsx(b.C,{variant:"outline",className:"bg-yellow-50 text-yellow-700 border-yellow-200",children:"Inactive"});case"SUSPENDED":return r.jsx(b.C,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200",children:"Suspended"});default:return r.jsx(b.C,{variant:"outline",children:e})}})(_.status)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Code: ",r.jsx("span",{className:"font-mono font-medium",children:_.code||_?.organization?.code||"未设置"})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[r.jsx(q,{organizationId:e.id}),(0,r.jsxs)(N.Vq,{open:Q,onOpenChange:W,children:[r.jsx(N.hg,{asChild:!0,children:(0,r.jsxs)(p.Button,{variant:"outline",children:[r.jsx(u.P.edit,{className:"mr-2 h-4 w-4"}),"Edit"]})}),r.jsx(N.cZ,{children:(0,r.jsxs)("form",{onSubmit:sm(sp),children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Edit Organization"}),r.jsx(N.Be,{children:"Update organization details and settings."})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"name",children:"Organization Name"}),r.jsx(y.I,{id:"name",placeholder:"Enter organization name",...sc("name")}),sx.name&&r.jsx("p",{className:"text-sm text-red-500",children:sx.name.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"description",children:"Description"}),r.jsx(C.g,{id:"description",placeholder:"Enter organization description",...sc("description")})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"logo",children:"Logo URL"}),r.jsx(y.I,{id:"logo",placeholder:"Enter logo URL (optional)",...sc("logo")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"commissionRate",children:"Commission Rate"}),r.jsx(y.I,{id:"commissionRate",type:"number",step:"0.01",min:"0",max:"1",placeholder:"0.12",...sc("commissionRate",{valueAsNumber:!0})}),sx.commissionRate&&r.jsx("p",{className:"text-sm text-red-500",children:sx.commissionRate.message})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"discountRate",children:"Discount Rate"}),r.jsx(y.I,{id:"discountRate",type:"number",step:"0.01",min:"0",max:"1",placeholder:"0.05",...sc("discountRate",{valueAsNumber:!0})}),sx.discountRate&&r.jsx("p",{className:"text-sm text-red-500",children:sx.discountRate.message})]})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"status",children:"Status"}),(0,r.jsxs)(z.Ph,{onValueChange:e=>sh("status",e),defaultValue:_.status,children:[r.jsx(z.i4,{children:r.jsx(z.ki,{placeholder:"Select status"})}),(0,r.jsxs)(z.Bw,{children:[r.jsx(z.Ql,{value:"ACTIVE",children:"Active"}),r.jsx(z.Ql,{value:"INACTIVE",children:"Inactive"}),r.jsx(z.Ql,{value:"SUSPENDED",children:"Suspended"})]})]})]})]}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{type:"button",variant:"outline",onClick:()=>W(!1),children:"Cancel"}),(0,r.jsxs)(p.Button,{type:"submit",disabled:G,children:[G&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Save Changes"]})]})]})})]}),(0,r.jsxs)(F.aR,{children:[r.jsx(F.vW,{asChild:!0,children:(0,r.jsxs)(p.Button,{variant:"destructive",children:[r.jsx(u.P.trash,{className:"mr-2 h-4 w-4"}),"Delete"]})}),(0,r.jsxs)(F._T,{children:[(0,r.jsxs)(F.fY,{children:[r.jsx(F.f$,{children:"Delete Organization"}),r.jsx(F.yT,{children:"Are you sure you want to delete this organization? This action cannot be undone and will remove all associated data including members, invites, and commissions."})]}),(0,r.jsxs)(F.xo,{children:[r.jsx(F.le,{children:"Cancel"}),(0,r.jsxs)(F.OL,{onClick:sj,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:K,children:[K&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Delete"]})]})]})]})]})]}),_?.description&&r.jsx("p",{className:"mt-4 text-muted-foreground",children:_.description})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(j.Zb,{children:[r.jsx(j.Ol,{className:"pb-2",children:r.jsx(j.ll,{className:"text-sm font-medium text-muted-foreground",children:"Members"})}),r.jsx(j.aY,{children:r.jsx("div",{className:"text-2xl font-bold",children:_._count?.members||0})})]}),(0,r.jsxs)(j.Zb,{children:[r.jsx(j.Ol,{className:"pb-2",children:r.jsx(j.ll,{className:"text-sm font-medium text-muted-foreground",children:"Total Earnings"})}),r.jsx(j.aY,{children:(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",_.totalEarnings?.toFixed(2)||"0.00"]})})]}),Y&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(j.Zb,{children:[r.jsx(j.Ol,{className:"pb-2",children:r.jsx(j.ll,{className:"text-sm font-medium text-muted-foreground",children:"Total Visits"})}),r.jsx(j.aY,{children:r.jsx("div",{className:"text-2xl font-bold",children:Y.totalVisits})})]}),(0,r.jsxs)(j.Zb,{children:[r.jsx(j.Ol,{className:"pb-2",children:r.jsx(j.ll,{className:"text-sm font-medium text-muted-foreground",children:"Conversion Rate"})}),r.jsx(j.aY,{children:(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[Y.conversionRate?.toFixed(2)||"0.00","%"]})})]})]})]}),(0,r.jsxs)(E.Tabs,{defaultValue:"members",className:"w-full",children:[(0,r.jsxs)(E.TabsList,{className:"mb-4",children:[r.jsx(E.TabsTrigger,{value:"members",children:"Members"}),r.jsx(E.TabsTrigger,{value:"invites",children:"Invites"}),r.jsx(E.TabsTrigger,{value:"analytics",children:"Analytics"})]}),r.jsx(E.TabsContent,{value:"members",children:(0,r.jsxs)(j.Zb,{children:[(0,r.jsxs)(j.Ol,{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(j.ll,{children:"Organization Members"}),r.jsx(j.SZ,{children:"Manage members and their roles in this organization."})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(N.Vq,{open:ee,onOpenChange:e=>{e||(ek(null),ef(.1),ev(!1),eN(""),ew([]),sr(!1),ss(""),si(null)),es(e)},children:[r.jsx(N.hg,{asChild:!0,children:(0,r.jsxs)(p.Button,{children:[r.jsx(h.c.userPlus,{className:"mr-2 h-4 w-4"}),"Add Member"]})}),(0,r.jsxs)(N.cZ,{children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Add Organization Member"}),r.jsx(N.Be,{children:"Add a new member to this organization."})]}),sa?(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(O.bZ,{className:"bg-green-50 border-green-200",children:r.jsx(O.X,{className:"text-green-800",children:"Invitation created successfully! Share the link below with the invited member."})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(y.I,{value:sa,readOnly:!0,className:"flex-1"}),r.jsx(p.Button,{size:"sm",onClick:()=>{sa&&(navigator.clipboard.writeText(sa),f.A.success("Invite link copied to clipboard"))},children:"Copy"})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground space-y-2",children:[(0,r.jsxs)("p",{className:"flex items-start",children:[r.jsx($.Z,{className:"h-4 w-4 mr-2 mt-0.5 text-blue-500"}),"The invited user will be able to set their own password when they follow this link."]}),(0,r.jsxs)("p",{className:"flex items-start",children:[r.jsx(P.Z,{className:"h-4 w-4 mr-2 mt-0.5 text-blue-500"}),"This invitation link will expire in 7 days."]})]}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{onClick:()=>{si(null),ss(""),sr(!0)},children:"Create Another Invitation"}),r.jsx(p.Button,{variant:"outline",onClick:()=>es(!1),children:"Done"})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(T.X,{id:"existingUserOption",checked:!st,onCheckedChange:e=>sr(!e)}),r.jsx(w._,{htmlFor:"existingUserOption",children:"Existing User"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(T.X,{id:"inviteOption",checked:st,onCheckedChange:e=>sr(!!e)}),r.jsx(w._,{htmlFor:"inviteOption",children:"Send Invitation"})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[st?(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"inviteEmail",children:"Email Address"}),r.jsx(y.I,{id:"inviteEmail",type:"email",placeholder:"<EMAIL>",value:se,onChange:e=>ss(e.target.value)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"An invitation link will be generated for this email."})]}):(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"userSearch",children:"Search User"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(y.I,{id:"userSearch",placeholder:"Search by name or email (min 1 char)",value:eb,onChange:e=>eN(e.target.value)}),eR&&r.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2",children:r.jsx(l.Z,{className:"h-4 w-4 animate-spin text-muted-foreground"})})]}),ey.length>0&&r.jsx("div",{className:"border rounded-md max-h-40 overflow-y-auto",children:ey.map(e=>(0,r.jsxs)("div",{className:`p-2 flex items-center gap-2 cursor-pointer hover:bg-muted ${eC?.id===e.id?"bg-muted":""}`,onClick:()=>ek(e),children:[r.jsx(T.X,{checked:eC?.id===e.id,className:"mr-1"}),(0,r.jsxs)(v.qE,{className:"h-6 w-6",children:[r.jsx(v.F$,{src:e.image,alt:e.name}),r.jsx(v.Q5,{children:e.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium",children:e.name}),r.jsx("div",{className:"text-xs text-muted-foreground",children:e.email})]})]},e.id))})]}),(eC||st)&&(0,r.jsxs)(r.Fragment,{children:[eC&&!st&&(0,r.jsxs)("div",{className:"bg-muted p-3 rounded-md",children:[r.jsx("div",{className:"text-sm font-medium",children:"Selected User"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,r.jsxs)(v.qE,{className:"h-8 w-8",children:[r.jsx(v.F$,{src:eC.image,alt:eC.name}),r.jsx(v.Q5,{children:eC.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:eC.name}),r.jsx("div",{className:"text-xs text-muted-foreground",children:eC.email})]})]})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"memberCommissionRate",children:"Commission Rate"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(y.I,{id:"memberCommissionRate",type:"number",step:"0.01",min:"0",max:"1",value:ej,onChange:e=>ef(parseFloat(e.target.value))}),(0,r.jsxs)("span",{className:"text-sm font-mono",children:["(",(100*ej).toFixed(0),"%)"]})]}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"This is the percentage of the product price the member will earn. This comes from the organization's commission (e.g., if organization earns 30% and member rate is 20%, member gets 20% and organization keeps 10%)."})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(T.X,{id:"memberIsAdmin",checked:eg,onCheckedChange:e=>ev(!!e)}),r.jsx("label",{htmlFor:"memberIsAdmin",className:"text-sm cursor-pointer",children:"Make this member an organization admin"})]})]})]}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{variant:"outline",onClick:()=>es(!1),children:"Cancel"}),(0,r.jsxs)(p.Button,{onClick:sg,disabled:et||!st&&!eC||st&&!se,children:[et&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),st?"Send Invitation":"Add Member"]})]})]})]})]}),r.jsx("div",{className:"relative",children:(0,r.jsxs)(Z.h_,{open:eQ,onOpenChange:eW,children:[r.jsx(Z.$F,{asChild:!0,children:(0,r.jsxs)(p.Button,{variant:"outline",children:[r.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Batch Operations"]})}),(0,r.jsxs)(Z.AW,{align:"end",children:[(0,r.jsxs)(Z.Xi,{onClick:()=>{ez(!0),eW(!1)},children:[r.jsx(u.P.user,{className:"h-4 w-4 mr-2"}),"Batch Create Users"]}),(0,r.jsxs)(Z.Xi,{onClick:()=>{eM(!0),eW(!1)},children:[r.jsx(h.c.users,{className:"h-4 w-4 mr-2"}),"Batch Add Members"]}),(0,r.jsxs)(Z.Xi,{onClick:()=>{if(0===e0.length){f.A.error("Please select members to delete");return}e4(!0),eW(!1)},className:"text-destructive",disabled:0===e0.length,children:[r.jsx(u.P.trash,{className:"h-4 w-4 mr-2"}),"Delete Selected Members"]})]})]})})]})]}),r.jsx(j.aY,{children:0===I.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"No members in this organization yet."}),(0,r.jsxs)(p.Button,{onClick:()=>es(!0),className:"mt-2",variant:"outline",size:"sm",children:[r.jsx(h.c.userPlus,{className:"mr-2 h-4 w-4"}),"Add First Member"]})]}):(0,r.jsxs)("div",{className:"rounded-md border",children:[(0,r.jsxs)(M.iA,{children:[r.jsx(M.xD,{children:(0,r.jsxs)(M.SC,{children:[r.jsx(M.ss,{className:"w-10",children:r.jsx(T.X,{checked:sR().length>0&&e0.length===sR().length,onCheckedChange:e=>{e?e2(sR()):e2([])},"aria-label":"Select all members"})}),r.jsx(M.ss,{children:"User"}),r.jsx(M.ss,{children:"Role"}),r.jsx(M.ss,{children:"Commission"}),r.jsx(M.ss,{children:"Earnings"}),r.jsx(M.ss,{className:"text-right",children:"Actions"})]})}),r.jsx(M.RM,{children:sR().map(e=>(0,r.jsxs)(M.SC,{children:[r.jsx(M.pj,{children:r.jsx(T.X,{checked:e0.some(s=>s.id===e.id),onCheckedChange:()=>sz(e),"aria-label":`Select ${e.user?.name||"member"}`})}),r.jsx(M.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(v.qE,{className:"h-8 w-8",children:[r.jsx(v.F$,{src:e.user?.image||"",alt:e.user?.name||""}),r.jsx(v.Q5,{children:e.user?.name?.substring(0,2).toUpperCase()||""})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium",children:e.user?.name||"Unknown User"}),r.jsx("div",{className:"text-xs text-muted-foreground",children:e.user?.email||"No email"})]})]})}),r.jsx(M.pj,{children:e.isAdmin?r.jsx(b.C,{variant:"outline",className:"bg-primary/10 text-primary",children:"Admin"}):r.jsx(b.C,{variant:"outline",children:"Member"})}),(0,r.jsxs)(M.pj,{children:[(100*e.commissionRate)?.toFixed(0)||"0","%"]}),(0,r.jsxs)(M.pj,{children:["$",e.totalEarnings?.toFixed(2)||"0.00"]}),r.jsx(M.pj,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsxs)(p.Button,{variant:"ghost",size:"sm",onClick:()=>sN(e),children:[r.jsx(u.P.edit,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Edit"})]}),(0,r.jsxs)(p.Button,{variant:"ghost",size:"sm",className:"text-destructive hover:text-destructive/90",onClick:()=>eu(e),children:[r.jsx(u.P.trash,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Delete"})]}),r.jsx(p.Button,{variant:"ghost",size:"sm",onClick:()=>sE(e),children:"View"})]})})]},e.id))})]}),sk>1&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4 space-x-2",children:[r.jsx(p.Button,{variant:"outline",size:"sm",onClick:()=>sS(Math.max(1,eG-1)),disabled:eG<=1,children:r.jsx(u.P.chevronLeft,{className:"h-4 w-4"})}),r.jsx("div",{className:"flex items-center space-x-1",children:[...Array(sk)].map((e,s)=>r.jsx(p.Button,{variant:eG===s+1?"default":"outline",size:"sm",className:"w-8 h-8 p-0",onClick:()=>sS(s+1),children:s+1},s))}),r.jsx(p.Button,{variant:"outline",size:"sm",onClick:()=>sS(Math.min(sk,eG+1)),disabled:eG>=sk,children:r.jsx(u.P.chevronRight,{className:"h-4 w-4"})})]})]})})]})}),r.jsx(E.TabsContent,{value:"invites",children:(0,r.jsxs)(j.Zb,{children:[r.jsx(j.Ol,{className:"flex flex-row items-center justify-between",children:(0,r.jsxs)("div",{children:[r.jsx(j.ll,{children:"Pending Invitations"}),r.jsx(j.SZ,{children:"Manage organization invitations."})]})}),r.jsx(j.aY,{children:0===V.length?r.jsx("div",{className:"flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed",children:r.jsx("p",{className:"text-sm text-muted-foreground",children:"No pending invitations."})}):r.jsx("div",{className:"rounded-md border",children:(0,r.jsxs)(M.iA,{children:[r.jsx(M.xD,{children:(0,r.jsxs)(M.SC,{children:[r.jsx(M.ss,{children:"Email"}),r.jsx(M.ss,{children:"Status"}),r.jsx(M.ss,{children:"Expires"}),r.jsx(M.ss,{children:"Created"}),r.jsx(M.ss,{className:"text-right",children:"Actions"})]})}),r.jsx(M.RM,{children:V.slice(0,5).map(s=>(0,r.jsxs)(M.SC,{children:[r.jsx(M.pj,{children:r.jsx("div",{className:"font-medium",children:s.email})}),r.jsx(M.pj,{children:r.jsx(b.C,{variant:"outline",className:"PENDING"===s.status?"bg-yellow-50 text-yellow-700 border-yellow-200":"ACCEPTED"===s.status?"bg-green-50 text-green-700 border-green-200":"bg-red-50 text-red-700 border-red-200",children:s.status.charAt(0)+s.status.slice(1).toLowerCase()})}),r.jsx(M.pj,{children:g.CN.custom(s.expiresAt,"MMM d, yyyy")}),r.jsx(M.pj,{children:g.CN.custom(s.createdAt,"MMM d, yyyy")}),r.jsx(M.pj,{className:"text-right",children:r.jsx(p.Button,{asChild:!0,variant:"ghost",size:"sm",children:r.jsx(A.default,{href:`/admin/organizations/${e.id}/invites/${s.id}`,children:"View"})})})]},s.id))})]})})})]})}),(0,r.jsxs)(E.TabsContent,{value:"analytics",children:[(0,r.jsxs)(j.Zb,{children:[r.jsx(j.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(j.ll,{children:"Organization Analytics"}),r.jsx(j.SZ,{children:"View performance metrics for this organization."})]}),r.jsx(p.Button,{onClick:()=>sM(),size:"sm",disabled:sd,variant:"outline",children:sd?(0,r.jsxs)(r.Fragment,{children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Refreshing..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.Z,{className:"mr-2 h-4 w-4"}),"Refresh Stats"]})})]})}),r.jsx(j.aY,{children:Y?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Traffic"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Visits"}),r.jsx("div",{className:"text-2xl font-bold",children:Y.totalVisits})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Conversions"}),r.jsx("div",{className:"text-2xl font-bold",children:Y.totalConversions})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Performance"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Conversion Rate"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[Y.conversionRate?.toFixed(2)||"0.00","%"]})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Orders"}),r.jsx("div",{className:"text-2xl font-bold",children:Y.totalConversions})]})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Organization Settings"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Commission Rate"}),(0,r.jsxs)("div",{className:"text-xl font-bold",children:[(100*_.commissionRate)?.toFixed(0)||"0","%"]})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Discount Rate"}),(0,r.jsxs)("div",{className:"text-xl font-bold",children:[(100*_.discountRate)?.toFixed(0)||"0","%"]})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Affiliate Code"}),r.jsx("div",{className:"text-xl font-bold font-mono",children:_.code})]})]})]})]}):r.jsx("div",{className:"flex justify-center items-center h-32",children:r.jsx(l.Z,{className:"h-6 w-6 animate-spin text-primary"})})})]}),(0,r.jsxs)("div",{className:"space-y-2 mt-4",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground",children:"Commission Distribution"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Organization Gross Earnings"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",Y.totalCommissions?.toFixed(2)||"0.00"]}),r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Total commission generated"})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Member Commissions"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",Y.memberCommissions?.toFixed(2)||"0.00"]}),r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Distributed to members"})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg border bg-green-50/40",children:[r.jsx("div",{className:"text-sm text-muted-foreground",children:"Organization Net Earnings"}),(0,r.jsxs)("div",{className:"text-2xl font-bold",children:["$",Y.organizationActualEarnings?.toFixed(2)||"0.00"]}),r.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:"Actual organization profit"})]})]})]}),r.jsx("div",{className:"grid gap-6 mt-8",children:(0,r.jsxs)(j.Zb,{children:[(0,r.jsxs)(j.Ol,{children:[r.jsx(j.ll,{children:"Performance Overview"}),r.jsx(j.SZ,{children:"Summary of organization performance metrics"})]}),r.jsx(j.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:r.jsx(c.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Members"}),r.jsx("h3",{className:"text-2xl font-bold",children:I.length})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:r.jsx(m.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Referrals"}),r.jsx("h3",{className:"text-2xl font-bold",children:Y.totalConversions})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:r.jsx(m.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Earnings"}),(0,r.jsxs)("h3",{className:"text-2xl font-bold",children:["$",Y.organizationActualEarnings?.toFixed(2)||"0.00"]})]})]})]})})]})}),_&&r.jsx("div",{className:"grid gap-6 mt-8",children:(0,r.jsxs)(j.Zb,{children:[(0,r.jsxs)(j.Ol,{children:[r.jsx(j.ll,{children:"Monthly Earnings"}),r.jsx(j.SZ,{children:"Revenue trends over the last 6 months"})]}),r.jsx(j.aY,{children:_.monthlyEarnings?.length>0?r.jsx("div",{className:"relative",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mt-2",children:_.monthlyEarnings.map((e,s)=>r.jsx(j.Zb,{className:"p-2",children:(0,r.jsxs)(j.aY,{className:"p-2",children:[r.jsx("div",{className:"flex justify-between items-center mb-1",children:(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.month," ",e.year]})}),(0,r.jsxs)("p",{className:"text-xl font-bold text-green-600",children:["$",e.amount.toFixed(2)]}),(0,r.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[r.jsx("span",{className:"text-muted-foreground",children:"Total:"}),(0,r.jsxs)("span",{className:"font-medium",children:["$",e.totalCommissions.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs",children:[r.jsx("span",{className:"text-muted-foreground",children:"Members:"}),(0,r.jsxs)("span",{className:"font-medium",children:["$",e.memberCommissions.toFixed(2)]})]})]}),e.amount>0&&e.totalCommissions>0&&r.jsx("div",{className:"w-full bg-green-100 rounded-full h-1.5 mt-2",children:r.jsx("div",{className:"bg-green-600 h-1.5 rounded-full",style:{width:`${Math.min(100,e.amount/e.totalCommissions*100)}%`}})})]})},s))})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"No monthly earnings data available"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"Click the refresh button above to load data"})]})})]})}),I.length>0&&r.jsx("div",{className:"grid gap-6 mt-8",children:(0,r.jsxs)(j.Zb,{children:[(0,r.jsxs)(j.Ol,{children:[r.jsx(j.ll,{children:"Top Performing Members"}),r.jsx(j.SZ,{children:"Members with the highest referrals and earnings"})]}),r.jsx(j.aY,{children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:I.sort((e,s)=>s.totalEarnings-e.totalEarnings).slice(0,6).map((e,s)=>(0,r.jsxs)(j.Zb,{className:"overflow-hidden",children:[r.jsx("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 p-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)(v.qE,{className:"h-10 w-10 border-2 border-background",children:[r.jsx(v.F$,{src:e.user?.image,alt:e.user?.name}),r.jsx(v.Q5,{children:e.user?.name?.substring(0,2).toUpperCase()||"UN"})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:e.user?.name}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[r.jsx(m.Z,{className:"h-3 w-3 mr-1"}),(0,r.jsxs)("span",{children:["Top ",s+1," Performer"]})]})]})]})}),r.jsx(j.aY,{className:"p-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-muted-foreground",children:"Code"}),r.jsx("p",{className:"text-sm font-mono",children:e.code})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-muted-foreground",children:"Earnings"}),(0,r.jsxs)("p",{className:"text-xl font-bold",children:["$",e.totalEarnings?.toFixed(2)||"0.00"]})]})]})})]},e.id))})})]})}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-sm font-medium text-muted-foreground mb-4",children:"Member Orders"}),0===sn.length?r.jsx("div",{className:"flex flex-col items-center justify-center h-32 bg-muted/50 rounded-lg border border-dashed",children:r.jsx("p",{className:"text-sm text-muted-foreground",children:"No orders found for this organization"})}):r.jsx("div",{className:"rounded-md border",children:(0,r.jsxs)(M.iA,{children:[r.jsx(M.xD,{children:(0,r.jsxs)(M.SC,{children:[r.jsx(M.ss,{children:"Member"}),r.jsx(M.ss,{children:"Order ID"}),r.jsx(M.ss,{children:"Order Amount"}),r.jsx(M.ss,{children:"Member Commission"}),r.jsx(M.ss,{children:"Organization Commission"}),r.jsx(M.ss,{children:"Status"}),r.jsx(M.ss,{children:"Date"})]})}),r.jsx(M.RM,{children:sn.map(e=>(0,r.jsxs)(M.SC,{children:[r.jsx(M.pj,{children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(v.qE,{className:"h-7 w-7",children:[r.jsx(v.F$,{src:e.affiliate?.user?.image,alt:e.affiliate?.user?.name}),r.jsx(v.Q5,{children:e.affiliate?.user?.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium",children:e.affiliate?.user?.name}),r.jsx("div",{className:"text-xs text-muted-foreground",children:e.affiliate?.code})]})]})}),(0,r.jsxs)(M.pj,{className:"font-mono text-xs",children:[e.orderId.substring(0,8),"..."]}),(0,r.jsxs)(M.pj,{children:["$",e.order?.total?.toFixed(2)||"0.00"]}),(0,r.jsxs)(M.pj,{children:["$",e.commissionAmount?.toFixed(2)||"0.00"]}),r.jsx(M.pj,{children:e.organizationCommission?.commissionAmount?`$${(e.organizationCommission.commissionAmount-e.commissionAmount).toFixed(2)}`:"-"}),r.jsx(M.pj,{children:r.jsx(b.C,{variant:"outline",className:"APPROVED"===e.status?"bg-green-50 text-green-700 border-green-200":"PENDING"===e.status?"bg-yellow-50 text-yellow-700 border-yellow-200":"PAID"===e.status?"bg-blue-50 text-blue-700 border-blue-200":"bg-red-50 text-red-700 border-red-200",children:e.status})}),r.jsx(M.pj,{children:g.CN.custom(e.createdAt,"MMM d, yyyy")})]},e.id))})]})})]})]})]}),r.jsx(N.Vq,{open:en,onOpenChange:el,children:(0,r.jsxs)(N.cZ,{children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Edit Member"}),r.jsx(N.Be,{children:"Update member role and commission rate."})]}),ea&&(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted rounded-md",children:[(0,r.jsxs)(v.qE,{className:"h-10 w-10",children:[r.jsx(v.F$,{src:ea.user.image,alt:ea.user.name}),r.jsx(v.Q5,{children:ea.user.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:ea.user.name}),r.jsx("div",{className:"text-sm text-muted-foreground",children:ea.user.email})]})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"editCommissionRate",children:"Commission Rate"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(y.I,{id:"editCommissionRate",type:"number",step:"0.01",min:"0",max:"1",value:ej,onChange:e=>ef(parseFloat(e.target.value))}),(0,r.jsxs)("span",{className:"text-sm font-mono",children:["(",(100*ej).toFixed(0),"%)"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(T.X,{id:"editMemberIsAdmin",checked:eg,onCheckedChange:e=>ev(!!e)}),r.jsx("label",{htmlFor:"editMemberIsAdmin",className:"text-sm cursor-pointer",children:"Organization admin privileges"})]})]}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{variant:"outline",onClick:()=>el(!1),children:"Cancel"}),(0,r.jsxs)(p.Button,{onClick:sv,disabled:ed,children:[ed&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Save Changes"]})]})]})}),r.jsx(F.aR,{open:!!ex,onOpenChange:e=>!e&&eu(null),children:(0,r.jsxs)(F._T,{children:[(0,r.jsxs)(F.fY,{children:[r.jsx(F.f$,{children:"Remove Member"}),(0,r.jsxs)(F.yT,{children:["Are you sure you want to remove this member from the organization? This action cannot be undone.",ex&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-2 p-2 bg-muted rounded-md",children:[(0,r.jsxs)(v.qE,{className:"h-8 w-8",children:[r.jsx(v.F$,{src:ex.user.image,alt:ex.user.name}),r.jsx(v.Q5,{children:ex.user.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:ex.user.name}),r.jsx("div",{className:"text-xs text-muted-foreground",children:ex.user.email})]})]})]})]}),(0,r.jsxs)(F.xo,{children:[r.jsx(F.le,{children:"Cancel"}),(0,r.jsxs)(F.OL,{onClick:sb,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:ec,children:[ec&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Remove Member"]})]})]})}),r.jsx(N.Vq,{open:eA,onOpenChange:e=>{ez(e),e||(eW(!1),eT(""),eU([]))},children:(0,r.jsxs)(N.cZ,{className:"max-w-2xl",children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Batch Create Users"}),r.jsx(N.Be,{children:"Create multiple users at once by uploading a CSV file or pasting CSV data."})]}),eV.length>0?(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(O.bZ,{className:"bg-green-50 border-green-200",children:r.jsx(O.X,{className:"text-green-800",children:"Users created successfully! Password reset links have been generated for each user."})}),r.jsx("div",{className:"border rounded-md overflow-hidden",children:(0,r.jsxs)(M.iA,{children:[r.jsx(M.xD,{children:(0,r.jsxs)(M.SC,{children:[r.jsx(M.ss,{children:"Email"}),r.jsx(M.ss,{className:"text-right",children:"Action"})]})}),r.jsx(M.RM,{children:eV.map(e=>(0,r.jsxs)(M.SC,{children:[r.jsx(M.pj,{children:e.email}),r.jsx(M.pj,{className:"text-right",children:r.jsx(p.Button,{variant:"outline",size:"sm",onClick:()=>{navigator.clipboard.writeText(e.resetLink),f.A.success(`Reset link copied for ${e.email}`)},children:"Copy Link"})})]},e.userId))})]})}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground space-y-2",children:[(0,r.jsxs)("p",{className:"flex items-start",children:[r.jsx($.Z,{className:"h-4 w-4 mr-2 mt-0.5 text-blue-500"}),"These links allow users to set their own passwords. Send them to the respective users."]}),(0,r.jsxs)("p",{className:"flex items-start",children:[r.jsx(P.Z,{className:"h-4 w-4 mr-2 mt-0.5 text-blue-500"}),"Links will expire in 7 days."]})]}),(0,r.jsxs)(N.cN,{className:"space-x-2",children:[r.jsx(p.Button,{variant:"outline",onClick:()=>{let e=eV.map(e=>`${e.email}: ${e.resetLink}`).join("\n");navigator.clipboard.writeText(e),f.A.success("All reset links copied to clipboard")},children:"Copy All Links"}),r.jsx(p.Button,{onClick:()=>{eT(""),eU([])},children:"Create More Users"}),r.jsx(p.Button,{variant:"secondary",onClick:()=>ez(!1),children:"Done"})]})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"grid gap-4 py-4",children:(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"csvData",children:"CSV Data"}),r.jsx(C.g,{id:"csvData",placeholder:"name,email,role   John Doe,<EMAIL>,CUSTOMER   Jane Smith,<EMAIL>,ADMIN",rows:10,value:eF,onChange:e=>eT(e.target.value),className:"font-mono text-sm"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"CSV must include 'name' and 'email' columns. 'role' column is optional (defaults to CUSTOMER). Valid roles: ADMIN, CUSTOMER, STAFF. First row should be headers."})]})}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{variant:"outline",onClick:()=>ez(!1),children:"Cancel"}),(0,r.jsxs)(p.Button,{onClick:sy,disabled:e$||!eF.trim(),children:[e$&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Users"]})]})]})]})}),r.jsx(N.Vq,{open:eE,onOpenChange:e=>{eM(e),e||eW(!1)},children:(0,r.jsxs)(N.cZ,{className:"max-w-2xl",children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Batch Add Members"}),r.jsx(N.Be,{children:"Add multiple members to this organization at once."})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{children:"Search and Select Users"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(y.I,{placeholder:"Search by name or email (min 1 char)",value:eb,onChange:e=>eN(e.target.value)}),eR&&r.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2",children:r.jsx(l.Z,{className:"h-4 w-4 animate-spin text-muted-foreground"})})]})]}),ey.length>0&&r.jsx("div",{className:"border rounded-md max-h-40 overflow-y-auto",children:ey.map(e=>(0,r.jsxs)("div",{className:`p-2 flex items-center gap-2 cursor-pointer hover:bg-muted ${eY.some(s=>s.id===e.id)?"bg-muted":""}`,onClick:()=>sC(e),children:[r.jsx(T.X,{checked:eY.some(s=>s.id===e.id),className:"mr-1"}),(0,r.jsxs)(v.qE,{className:"h-6 w-6",children:[r.jsx(v.F$,{src:e.image,alt:e.name}),r.jsx(v.Q5,{children:e.name?.substring(0,2).toUpperCase()})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium",children:e.name}),r.jsx("div",{className:"text-xs text-muted-foreground",children:e.email})]})]},e.id))}),eY.length>0&&(0,r.jsxs)("div",{className:"bg-muted p-3 rounded-md",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium",children:["Selected Users (",eY.length,")"]}),r.jsx(p.Button,{variant:"ghost",size:"sm",onClick:()=>eX([]),className:"h-7 text-xs",children:"Clear All"})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:eY.map(e=>(0,r.jsxs)("div",{className:"bg-background rounded-full px-2 py-1 text-xs flex items-center gap-1",children:[r.jsx("span",{children:e.name}),r.jsx(p.Button,{variant:"ghost",size:"sm",onClick:()=>sC(e),className:"h-4 w-4 p-0 rounded-full",children:r.jsx(x.Z,{className:"h-3 w-3"})})]},e.id))})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[r.jsx(w._,{htmlFor:"batchCommissionRate",children:"Commission Rate (for all selected users)"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(y.I,{id:"batchCommissionRate",type:"number",step:"0.01",min:"0",max:"1",value:ej,onChange:e=>ef(parseFloat(e.target.value))}),(0,r.jsxs)("span",{className:"text-sm font-mono",children:["(",(100*ej).toFixed(0),"%)"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(T.X,{id:"batchIsAdmin",checked:eg,onCheckedChange:e=>ev(!!e)}),r.jsx("label",{htmlFor:"batchIsAdmin",className:"text-sm cursor-pointer",children:"Make all selected users organization admins"})]})]}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{variant:"outline",onClick:()=>eM(!1),children:"Cancel"}),(0,r.jsxs)(p.Button,{onClick:sw,disabled:e_||0===eY.length,children:[e_&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add ",eY.length," Members"]})]})]})}),r.jsx(F.aR,{open:e1,onOpenChange:e4,children:(0,r.jsxs)(F._T,{children:[(0,r.jsxs)(F.fY,{children:[r.jsx(F.f$,{children:"Delete Members"}),(0,r.jsxs)(F.yT,{children:["Are you sure you want to delete ",e0.length," members from this organization? This action cannot be undone.",e0.length>0&&r.jsx("div",{className:"mt-4 max-h-40 overflow-y-auto border rounded-md p-2",children:e0.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2 py-1 border-b last:border-0",children:[(0,r.jsxs)(v.qE,{className:"h-6 w-6",children:[r.jsx(v.F$,{src:e.user?.image||"",alt:e.user?.name||""}),r.jsx(v.Q5,{children:e.user?.name?.substring(0,2).toUpperCase()||""})]}),r.jsx("span",{className:"text-sm",children:e.user?.name||"Unknown User"})]},e.id))})]})]}),(0,r.jsxs)(F.xo,{children:[r.jsx(F.le,{children:"Cancel"}),(0,r.jsxs)(F.OL,{onClick:sA,className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",disabled:e5,children:[e5&&r.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Delete Members"]})]})]})}),r.jsx(N.Vq,{open:e8,onOpenChange:e9,children:(0,r.jsxs)(N.cZ,{className:"max-w-md",children:[(0,r.jsxs)(N.fK,{children:[r.jsx(N.$N,{children:"Member Details"}),r.jsx(N.Be,{children:"View detailed information about this member."})]}),e3&&(0,r.jsxs)("div",{className:"space-y-4 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(v.qE,{className:"h-16 w-16",children:[r.jsx(v.F$,{src:e3.user?.image||"",alt:e3.user?.name||""}),r.jsx(v.Q5,{children:e3.user?.name?.substring(0,2).toUpperCase()||""})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold",children:e3.user?.name||"Unknown User"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:e3.user?.email||"No email"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Role"}),r.jsx("div",{children:e3.isAdmin?r.jsx(b.C,{variant:"outline",className:"bg-primary/10 text-primary",children:"Admin"}):r.jsx(b.C,{variant:"outline",children:"Member"})})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Affiliate Code"}),r.jsx("p",{className:"font-mono text-sm",children:e3.code||"Not set"})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Commission Rate"}),(0,r.jsxs)("p",{className:"font-medium",children:[(100*e3.commissionRate).toFixed(0),"%"]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[r.jsx("p",{className:"text-sm text-muted-foreground",children:"Total Earnings"}),(0,r.jsxs)("p",{className:"font-medium",children:["$",e3.totalEarnings?.toFixed(2)||"0.00"]})]})]})]}),(0,r.jsxs)(N.cN,{children:[r.jsx(p.Button,{variant:"outline",onClick:()=>e9(!1),children:"Close"}),r.jsx(p.Button,{variant:"outline",onClick:()=>{e9(!1),sN(e3)},disabled:!e3,children:"Edit Member"})]})]})})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-64",children:[r.jsx("h3",{className:"text-lg font-medium",children:"Organization not found"}),r.jsx("p",{className:"text-sm text-muted-foreground",children:"The organization you are looking for does not exist."}),r.jsx(p.Button,{asChild:!0,className:"mt-4",children:(0,r.jsxs)(A.default,{href:"/admin/organizations",children:[r.jsx(h.c.arrowLeft,{className:"mr-2 h-4 w-4"}),"Back to Organizations"]})})]})}},26589:(e,s,t)=>{"use strict";t.d(s,{c:()=>a});var r=t(10326);let a={barChart3:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"M3 3v18h18"}),r.jsx("path",{d:"M18 17V9"}),r.jsx("path",{d:"M13 17V5"}),r.jsx("path",{d:"M8 17v-3"})]}),dollarSign:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("line",{x1:"12",y1:"2",x2:"12",y2:"22"}),r.jsx("path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"})]}),userPlus:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),r.jsx("circle",{cx:"9",cy:"7",r:"4"}),r.jsx("line",{x1:"19",y1:"8",x2:"19",y2:"14"}),r.jsx("line",{x1:"22",y1:"11",x2:"16",y2:"11"})]}),users:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}),r.jsx("circle",{cx:"9",cy:"7",r:"4"}),r.jsx("path",{d:"M22 21v-2a4 4 0 0 0-3-3.87"}),r.jsx("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]}),barChart:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("line",{x1:"12",y1:"20",x2:"12",y2:"10"}),r.jsx("line",{x1:"18",y1:"20",x2:"18",y2:"4"}),r.jsx("line",{x1:"6",y1:"20",x2:"6",y2:"16"})]}),arrowLeft:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"m12 19-7-7 7-7"}),r.jsx("path",{d:"M19 12H5"})]}),link:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"}),r.jsx("path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"})]}),alertTriangle:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"}),r.jsx("path",{d:"M12 9v4"}),r.jsx("path",{d:"M12 17h.01"})]}),check:({...e})=>r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:r.jsx("polyline",{points:"20 6 9 17 4 12"})}),x:({...e})=>(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",...e,children:[r.jsx("path",{d:"M18 6 6 18"}),r.jsx("path",{d:"m6 6 12 12"})]})}},99440:(e,s,t)=>{"use strict";t.d(s,{OL:()=>j,_T:()=>m,aR:()=>l,f$:()=>h,fY:()=>x,le:()=>f,vW:()=>d,xo:()=>u,yT:()=>p});var r=t(10326),a=t(17577),i=t(12194),n=t(77863);let l=i.fC,d=i.xz,o=i.h_,c=a.forwardRef(({className:e,...s},t)=>r.jsx(i.aV,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:t}));c.displayName=i.aV.displayName;let m=a.forwardRef(({className:e,...s},t)=>(0,r.jsxs)(o,{children:[r.jsx(c,{}),r.jsx(i.VY,{ref:t,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));m.displayName=i.VY.displayName;let x=({className:e,...s})=>r.jsx("div",{className:(0,n.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});x.displayName="AlertDialogHeader";let u=({className:e,...s})=>r.jsx("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});u.displayName="AlertDialogFooter";let h=a.forwardRef(({className:e,...s},t)=>r.jsx(i.Dx,{ref:t,className:(0,n.cn)("text-lg font-semibold",e),...s}));h.displayName=i.Dx.displayName;let p=a.forwardRef(({className:e,...s},t)=>r.jsx(i.dk,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));p.displayName=i.dk.displayName;let j=a.forwardRef(({className:e,...s},t)=>r.jsx(i.aU,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-semibold text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",e),...s}));j.displayName=i.aU.displayName;let f=a.forwardRef(({className:e,...s},t)=>r.jsx(i.$j,{ref:t,className:(0,n.cn)("mt-2 inline-flex h-10 items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-semibold ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 sm:mt-0",e),...s}));f.displayName=i.$j.displayName},43273:(e,s,t)=>{"use strict";t.d(s,{Cd:()=>o,X:()=>c,bZ:()=>d});var r=t(10326),a=t(17577),i=t(79360),n=t(77863);let l=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef(({className:e,variant:s,...t},a)=>r.jsx("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:s}),e),...t}));d.displayName="Alert";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s}));o.displayName="AlertTitle";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));c.displayName="AlertDescription"},567:(e,s,t)=>{"use strict";t.d(s,{C:()=>l});var r=t(10326);t(17577);var a=t(79360),i=t(77863);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return r.jsx("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},33071:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>o,Zb:()=>n,aY:()=>c,eW:()=>m,ll:()=>d});var r=t(10326),a=t(17577),i=t(77863);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},68762:(e,s,t)=>{"use strict";t.d(s,{X:()=>R});var r=t(10326),a=t(17577),i=t(48051),n=t(93095),l=t(82561),d=t(52067),o=t(53405),c=t(2566),m=t(9815),x=t(45226),u="Checkbox",[h,p]=(0,n.b)(u),[j,f]=h(u),g=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:n,checked:o,defaultChecked:c,required:m,disabled:u,value:h="on",onCheckedChange:p,form:f,...g}=e,[v,b]=a.useState(null),C=(0,i.e)(s,e=>b(e)),k=a.useRef(!1),R=!v||f||!!v.closest("form"),[S=!1,A]=(0,d.T)({prop:o,defaultProp:c,onChange:p}),z=a.useRef(S);return a.useEffect(()=>{let e=v?.form;if(e){let s=()=>A(z.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[v,A]),(0,r.jsxs)(j,{scope:t,state:S,disabled:u,children:[(0,r.jsx)(x.WV.button,{type:"button",role:"checkbox","aria-checked":y(S)?"mixed":S,"aria-required":m,"data-state":w(S),"data-disabled":u?"":void 0,disabled:u,value:h,...g,ref:C,onKeyDown:(0,l.M)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.M)(e.onClick,e=>{A(e=>!!y(e)||!e),R&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),R&&(0,r.jsx)(N,{control:v,bubbles:!k.current,name:n,value:h,checked:S,required:m,disabled:u,form:f,style:{transform:"translateX(-100%)"},defaultChecked:!y(c)&&c})]})});g.displayName=u;var v="CheckboxIndicator",b=a.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:a,...i}=e,n=f(v,t);return(0,r.jsx)(m.z,{present:a||y(n.state)||!0===n.state,children:(0,r.jsx)(x.WV.span,{"data-state":w(n.state),"data-disabled":n.disabled?"":void 0,...i,ref:s,style:{pointerEvents:"none",...e.style}})})});b.displayName=v;var N=e=>{let{control:s,checked:t,bubbles:i=!0,defaultChecked:n,...l}=e,d=a.useRef(null),m=(0,o.D)(t),x=(0,c.t)(s);a.useEffect(()=>{let e=d.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&s){let r=new Event("click",{bubbles:i});e.indeterminate=y(t),s.call(e,!y(t)&&t),e.dispatchEvent(r)}},[m,t,i]);let u=a.useRef(!y(t)&&t);return(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n??u.current,...l,tabIndex:-1,ref:d,style:{...e.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function y(e){return"indeterminate"===e}function w(e){return y(e)?"indeterminate":e?"checked":"unchecked"}var C=t(32933),k=t(77863);let R=a.forwardRef(({className:e,...s},t)=>r.jsx(g,{ref:t,className:(0,k.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:r.jsx(b,{className:(0,k.cn)("flex items-center justify-center text-current"),children:r.jsx(C.Z,{className:"h-4 w-4"})})}));R.displayName=g.displayName},62288:(e,s,t)=>{"use strict";t.d(s,{$N:()=>p,Be:()=>j,Vq:()=>d,cN:()=>h,cZ:()=>x,fK:()=>u,hg:()=>o});var r=t(10326),a=t(17577),i=t(11123),n=t(94019),l=t(77863);let d=i.fC,o=i.xz,c=i.h_;i.x8;let m=a.forwardRef(({className:e,...s},t)=>r.jsx(i.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));m.displayName=i.aV.displayName;let x=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(c,{children:[r.jsx(m,{}),(0,r.jsxs)(i.VY,{ref:a,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,r.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));x.displayName=i.VY.displayName;let u=({className:e,...s})=>r.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});u.displayName="DialogHeader";let h=({className:e,...s})=>r.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});h.displayName="DialogFooter";let p=a.forwardRef(({className:e,...s},t)=>r.jsx(i.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));p.displayName=i.Dx.displayName;let j=a.forwardRef(({className:e,...s},t)=>r.jsx(i.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));j.displayName=i.dk.displayName},31048:(e,s,t)=>{"use strict";t.d(s,{_:()=>o});var r=t(10326),a=t(17577),i=t(34478),n=t(79360),l=t(77863);let d=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef(({className:e,...s},t)=>r.jsx(i.f,{ref:t,className:(0,l.cn)(d(),e),...s}));o.displayName=i.f.displayName},34474:(e,s,t)=>{"use strict";t.d(s,{Bw:()=>x,Ph:()=>o,Ql:()=>u,i4:()=>m,ki:()=>c});var r=t(10326),a=t(17577),i=t(18792),n=t(941),l=t(32933),d=t(77863);let o=i.fC;i.ZA;let c=i.B4,m=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.xz,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:[s,r.jsx(i.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let x=a.forwardRef(({className:e,children:s,position:t="popper",...a},n)=>r.jsx(i.h_,{children:r.jsx(i.VY,{ref:n,className:(0,d.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...a,children:r.jsx(i.l_,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:s})})}));x.displayName=i.VY.displayName,a.forwardRef(({className:e,...s},t)=>r.jsx(i.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=i.__.displayName;let u=a.forwardRef(({className:e,children:s,...t},a)=>(0,r.jsxs)(i.ck,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(l.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:s})]}));u.displayName=i.ck.displayName,a.forwardRef(({className:e,...s},t)=>r.jsx(i.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=i.Z0.displayName},15940:(e,s,t)=>{"use strict";t.d(s,{RM:()=>d,SC:()=>o,iA:()=>n,pj:()=>m,ss:()=>c,xD:()=>l});var r=t(10326),a=t(17577),i=t(77863);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:t,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let l=a.forwardRef(({className:e,...s},t)=>r.jsx("thead",{ref:t,className:(0,i.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let d=a.forwardRef(({className:e,...s},t)=>r.jsx("tbody",{ref:t,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",a.forwardRef(({className:e,...s},t)=>r.jsx("tfoot",{ref:t,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let o=a.forwardRef(({className:e,...s},t)=>r.jsx("tr",{ref:t,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));o.displayName="TableRow";let c=a.forwardRef(({className:e,...s},t)=>r.jsx("th",{ref:t,className:(0,i.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s}));c.displayName="TableHead";let m=a.forwardRef(({className:e,...s},t)=>r.jsx("td",{ref:t,className:(0,i.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s}));m.displayName="TableCell",a.forwardRef(({className:e,...s},t)=>r.jsx("caption",{ref:t,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},79210:(e,s,t)=>{"use strict";t.d(s,{Tabs:()=>l,TabsContent:()=>c,TabsList:()=>d,TabsTrigger:()=>o});var r=t(10326),a=t(17577),i=t(13239),n=t(77863);let l=i.fC,d=a.forwardRef(({className:e,...s},t)=>r.jsx(i.aV,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));d.displayName=i.aV.displayName;let o=a.forwardRef(({className:e,...s},t)=>r.jsx(i.xz,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));o.displayName=i.xz.displayName;let c=a.forwardRef(({className:e,...s},t)=>r.jsx(i.VY,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=i.VY.displayName},87673:(e,s,t)=>{"use strict";t.d(s,{g:()=>n});var r=t(10326),a=t(17577),i=t(77863);let n=a.forwardRef(({className:e,...s},t)=>r.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Textarea"},80239:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]])},15919:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},7027:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},21405:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(62881).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},77775:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>i,default:()=>l});var r=t(68570);let a=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\organizations\[id]\page.tsx`),{__esModule:i,$$typeof:n}=a;a.default;let l=(0,r.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\admin\organizations\[id]\page.tsx#default`)},58585:(e,s,t)=>{"use strict";var r=t(61085);t.o(r,"notFound")&&t.d(s,{notFound:function(){return r.notFound}}),t.o(r,"redirect")&&t.d(s,{redirect:function(){return r.redirect}})},61085:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{ReadonlyURLSearchParams:function(){return n},RedirectType:function(){return r.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return r.permanentRedirect},redirect:function(){return r.redirect}});let r=t(83953),a=t(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class n extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},16399:(e,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{isNotFoundError:function(){return a},notFound:function(){return r}});let t="NEXT_NOT_FOUND";function r(){let e=Error(t);throw e.digest=t,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===t}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},8586:(e,s)=>{"use strict";var t;Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"RedirectStatusCode",{enumerable:!0,get:function(){return t}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(t||(t={})),("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},83953:(e,s,t)=>{"use strict";var r;Object.defineProperty(s,"__esModule",{value:!0}),function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{RedirectType:function(){return r},getRedirectError:function(){return d},getRedirectStatusCodeFromError:function(){return h},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return x},isRedirectError:function(){return m},permanentRedirect:function(){return c},redirect:function(){return o}});let a=t(54580),i=t(72934),n=t(8586),l="NEXT_REDIRECT";function d(e,s,t){void 0===t&&(t=n.RedirectStatusCode.TemporaryRedirect);let r=Error(l);r.digest=l+";"+s+";"+e+";"+t+";";let i=a.requestAsyncStorage.getStore();return i&&(r.mutableCookies=i.mutableCookies),r}function o(e,s){void 0===s&&(s="replace");let t=i.actionAsyncStorage.getStore();throw d(e,s,(null==t?void 0:t.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.TemporaryRedirect)}function c(e,s){void 0===s&&(s="replace");let t=i.actionAsyncStorage.getStore();throw d(e,s,(null==t?void 0:t.isAction)?n.RedirectStatusCode.SeeOther:n.RedirectStatusCode.PermanentRedirect)}function m(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[s,t,r,a]=e.digest.split(";",4),i=Number(a);return s===l&&("replace"===t||"push"===t)&&"string"==typeof r&&!isNaN(i)&&i in n.RedirectStatusCode}function x(e){return m(e)?e.digest.split(";",3)[2]:null}function u(e){if(!m(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function h(e){if(!m(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(r||(r={})),("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[8948,1615,9092,2197,2023,7005,5772,7624,5634,6621,9862,8792,1123,6908,3239,2194,4099,4824,7123],()=>t(80566));module.exports=r})();