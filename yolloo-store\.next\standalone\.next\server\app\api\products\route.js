"use strict";(()=>{var e={};e.id=9684,e.ids=[9684],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},16808:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>_,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>v,staticGenerationAsyncStorage:()=>y});var o={};r.r(o),r.d(o,{GET:()=>h,POST:()=>f,dynamic:()=>l,fetchCache:()=>c,revalidate:()=>d});var n=r(49303),s=r(88716),i=r(60670),a=r(87070),u=r(72331),p=r(53524);let l="force-dynamic",c="force-no-store",d=0;async function h(e){try{let{searchParams:t}=new URL(e.url),r=t.get("search")||"",o={AND:[{status:p.ProductStatus.ACTIVE},{off_shelve:!1}]};r&&o.AND.push({OR:[{name:{contains:r,mode:"insensitive"}},{description:{contains:r,mode:"insensitive"}},{websiteDescription:{contains:r,mode:"insensitive"}},{country:{contains:r,mode:"insensitive"}},{sku:{contains:r,mode:"insensitive"}},{specifications:{path:["odooProductCode"],string_contains:r}}]});let n=await u._.product.findMany({where:o,select:{id:!0,name:!0,description:!0,websiteDescription:!0,price:!0,off_shelve:!0,parameters:!0,category:!0,country:!0,countryCode:!0,dataSize:!0,planType:!0},orderBy:[{createdAt:"asc"}]});return a.NextResponse.json({products:n})}catch(e){return console.error("Error fetching products:",e),a.NextResponse.json({error:"Failed to fetch products"},{status:500})}}async function f(e){try{let{productCode:t}=await e.json();if(!t)return a.NextResponse.json({error:"Product code is required"},{status:400});let r=await u._.product.findFirst({where:{OR:[{sku:t},{specifications:{path:["odooProductCode"],equals:t}}]},select:{id:!0,name:!0,description:!0,websiteDescription:!0,price:!0,off_shelve:!0,parameters:!0,category:!0,variants:!0,country:!0,countryCode:!0,dataSize:!0}});if(!r)return a.NextResponse.json({error:"Product not found"},{status:404});return a.NextResponse.json({product:r})}catch(e){return console.error("Error in product API:",e),a.NextResponse.json({error:"Failed to fetch product"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\products\\route.ts",nextConfigOutput:"standalone",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:v}=m,x="/api/products/route";function _(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:y})}},72331:(e,t,r)=>{r.d(t,{_:()=>n});var o=r(53524);let n=global.prisma||new o.PrismaClient({log:["error"]})},79925:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,s={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),o=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?o:`${o}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[o,n],...s]=a(e),{domain:i,expires:u,httponly:c,maxage:d,path:h,samesite:f,secure:m,partitioned:g,priority:y}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(n),domain:i,...u&&{expires:new Date(u)},...c&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:h,...f&&{sameSite:p.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...y&&{priority:l.includes(r=(r=y).toLowerCase())?r:void 0},...g&&{partitioned:!0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>d,parseCookie:()=>a,parseSetCookie:()=>u,stringifyCookie:()=>i}),e.exports=((e,s,i,a)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let i of o(s))n.call(e,i)||void 0===i||t(e,i,{get:()=>s[i],enumerable:!(a=r(s,i))||a.enumerable});return e})(t({},"__esModule",{value:!0}),s);var p=["strict","lax","none"],l=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of a(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let n=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,o,n,s,i=[],a=0;function u(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;u();)if(","===(r=e.charAt(a))){for(o=a,a+=1,u(),n=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=n,i.push(e.substring(t,o)),t=a):a=o+1}else a+=1;(!s||a>=e.length)&&i.push(e.substring(t,e.length))}return i}(n)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},92044:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(79925)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,5972],()=>r(16808));module.exports=o})();