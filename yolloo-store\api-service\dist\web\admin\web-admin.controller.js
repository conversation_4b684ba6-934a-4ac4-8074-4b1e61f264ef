"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebAdminController = void 0;
const common_1 = require("@nestjs/common");
const web_admin_service_1 = require("./web-admin.service");
let WebAdminController = class WebAdminController {
    webAdminService;
    constructor(webAdminService) {
        this.webAdminService = webAdminService;
    }
    checkAdminPermission(req, res) {
        const user = req['user'];
        if (!user || user.role !== 'ADMIN') {
            res.status(common_1.HttpStatus.FORBIDDEN).json({
                error: 'Admin access required',
            });
            return false;
        }
        return true;
    }
    async getDashboard(req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const dashboard = await this.webAdminService.getDashboardData();
            return res.json(dashboard);
        }
        catch (error) {
            console.error('[WEB_ADMIN_DASHBOARD]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch dashboard data',
            });
        }
    }
    async getUsers(query, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const users = await this.webAdminService.getUsers(query);
            return res.json(users);
        }
        catch (error) {
            console.error('[WEB_ADMIN_USERS]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch users',
            });
        }
    }
    async getUser(userId, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const user = await this.webAdminService.getUserById(userId);
            if (!user) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    error: 'User not found',
                });
            }
            return res.json(user);
        }
        catch (error) {
            console.error('[WEB_ADMIN_USER]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch user',
            });
        }
    }
    async createUser(createUserDto, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const user = await this.webAdminService.createUser(createUserDto);
            return res.status(common_1.HttpStatus.CREATED).json(user);
        }
        catch (error) {
            console.error('[WEB_ADMIN_CREATE_USER]', error);
            if (error.message === 'Email already exists') {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    error: 'Email already exists',
                });
            }
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to create user',
            });
        }
    }
    async updateUser(userId, updateUserDto, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const user = await this.webAdminService.updateUser(userId, updateUserDto);
            return res.json(user);
        }
        catch (error) {
            console.error('[WEB_ADMIN_UPDATE_USER]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to update user',
            });
        }
    }
    async deleteUser(userId, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            await this.webAdminService.deleteUser(userId);
            return res.status(common_1.HttpStatus.NO_CONTENT).send();
        }
        catch (error) {
            console.error('[WEB_ADMIN_DELETE_USER]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to delete user',
            });
        }
    }
    async getOrders(query, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const orders = await this.webAdminService.getOrders(query);
            return res.json(orders);
        }
        catch (error) {
            console.error('[WEB_ADMIN_ORDERS]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch orders',
            });
        }
    }
    async getProducts(query, req, res) {
        try {
            if (!this.checkAdminPermission(req, res))
                return;
            const products = await this.webAdminService.getProducts(query);
            return res.json(products);
        }
        catch (error) {
            console.error('[WEB_ADMIN_PRODUCTS]', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                error: 'Failed to fetch products',
            });
        }
    }
};
__decorate([
    (0, common_1.Get)('dashboard'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)('users'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "getUsers", null);
__decorate([
    (0, common_1.Get)('users/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "getUser", null);
__decorate([
    (0, common_1.Post)('users'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "createUser", null);
__decorate([
    (0, common_1.Patch)('users/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "updateUser", null);
__decorate([
    (0, common_1.Delete)('users/:userId'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "deleteUser", null);
__decorate([
    (0, common_1.Get)('orders'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "getOrders", null);
__decorate([
    (0, common_1.Get)('products'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WebAdminController.prototype, "getProducts", null);
WebAdminController = __decorate([
    (0, common_1.Controller)('api/web/admin'),
    __metadata("design:paramtypes", [web_admin_service_1.WebAdminService])
], WebAdminController);
exports.WebAdminController = WebAdminController;
//# sourceMappingURL=web-admin.controller.js.map