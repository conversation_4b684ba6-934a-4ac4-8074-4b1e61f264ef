(()=>{var e={};e.id=5286,e.ids=[5286],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},47742:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(83110),r(89090),r(26083),r(35866);var a=r(23191),s=r(88716),n=r(37922),l=r.n(n),i=r(95231),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d=["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83110)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\products\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,89090)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,26083)),"E:\\project\\esim-store-standalone\\yolloo-store\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["E:\\project\\esim-store-standalone\\yolloo-store\\app\\products\\page.tsx"],u="/products/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},50886:(e,t,r)=>{Promise.resolve().then(r.bind(r,89665))},89665:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eL});var a,s=r(10326),n=r(17577),l=r(90434),i=r(35047),o=r(90772),d=r(34474),c=r(82561),u=r(48051),p=r(93095),m=r(60962),x=n.forwardRef((e,t)=>{let{children:r,...a}=e,l=n.Children.toArray(r),i=l.find(g);if(i){let e=i.props.children,r=l.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(h,{...a,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,s.jsx)(h,{...a,ref:t,children:r})});x.displayName="Slot";var h=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),s=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=(...e)=>{n(...e),s(...e)}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,u.F)(t,e):e),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});h.displayName="SlotClone";var f=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function g(e){return n.isValidElement(e)&&e.type===f}var y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:a,...n}=e,l=a?x:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(l,{...n,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),v=r(55049),b=r(77575),j="dismissableLayer.update",w=n.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),N=n.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:o,onInteractOutside:d,onDismiss:p,...m}=e,x=n.useContext(w),[h,f]=n.useState(null),g=h?.ownerDocument??globalThis?.document,[,N]=n.useState({}),P=(0,u.e)(t,e=>f(e)),E=Array.from(x.layers),[S]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),R=E.indexOf(S),T=h?E.indexOf(h):-1,L=x.layersWithOutsidePointerEventsDisabled.size>0,B=T>=R,A=function(e,t=globalThis?.document){let r=(0,v.W)(e),a=n.useRef(!1),s=n.useRef(()=>{});return n.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let a=function(){k("dismissableLayer.pointerDownOutside",r,n,{discrete:!0})},n={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",s.current),s.current=a,t.addEventListener("click",s.current,{once:!0})):a()}else t.removeEventListener("click",s.current);a.current=!1},n=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(n),t.removeEventListener("pointerdown",e),t.removeEventListener("click",s.current)}},[t,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,r=[...x.branches].some(e=>e.contains(t));!B||r||(i?.(e),d?.(e),e.defaultPrevented||p?.())},g),D=function(e,t=globalThis?.document){let r=(0,v.W)(e),a=n.useRef(!1);return n.useEffect(()=>{let e=e=>{e.target&&!a.current&&k("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(o?.(e),d?.(e),e.defaultPrevented||p?.())},g);return(0,b.e)(e=>{T!==x.layers.size-1||(l?.(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},g),n.useEffect(()=>{if(h)return r&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(a=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(h)),x.layers.add(h),C(),()=>{r&&1===x.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=a)}},[h,g,r,x]),n.useEffect(()=>()=>{h&&(x.layers.delete(h),x.layersWithOutsidePointerEventsDisabled.delete(h),C())},[h,x]),n.useEffect(()=>{let e=()=>N({});return document.addEventListener(j,e),()=>document.removeEventListener(j,e)},[]),(0,s.jsx)(y.div,{...m,ref:P,style:{pointerEvents:L?B?"auto":"none":void 0,...e.style},onFocusCapture:(0,c.M)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,c.M)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,c.M)(e.onPointerDownCapture,A.onPointerDownCapture)})});function C(){let e=new CustomEvent(j);document.dispatchEvent(e)}function k(e,t,r,{discrete:a}){let s=r.originalEvent.target,n=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});(t&&s.addEventListener(e,t,{once:!0}),a)?s&&m.flushSync(()=>s.dispatchEvent(n)):s.dispatchEvent(n)}N.displayName="DismissableLayer",n.forwardRef((e,t)=>{let r=n.useContext(w),a=n.useRef(null),l=(0,u.e)(t,a);return n.useEffect(()=>{let e=a.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,s.jsx)(y.div,{...e,ref:l})}).displayName="DismissableLayerBranch";var P=r(88957),E=r(77192),S=r(87389),R=n.forwardRef((e,t)=>{let{children:r,width:a=10,height:n=5,...l}=e;return(0,s.jsx)(y.svg,{...l,ref:t,width:a,height:n,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});R.displayName="Arrow";var T=r(65819),L=r(2566),B="Popper",[A,D]=(0,p.b)(B),[O,M]=A(B),_=e=>{let{__scopePopper:t,children:r}=e,[a,l]=n.useState(null);return(0,s.jsx)(O,{scope:t,anchor:a,onAnchorChange:l,children:r})};_.displayName=B;var z="PopperAnchor",$=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:a,...l}=e,i=M(z,r),o=n.useRef(null),d=(0,u.e)(t,o);return n.useEffect(()=>{i.onAnchorChange(a?.current||o.current)}),a?null:(0,s.jsx)(y.div,{...l,ref:d})});$.displayName=z;var F="PopperContent",[Z,W]=A(F),I=n.forwardRef((e,t)=>{let{__scopePopper:r,side:a="bottom",sideOffset:l=0,align:i="center",alignOffset:o=0,arrowPadding:d=0,avoidCollisions:c=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:x="partial",hideWhenDetached:h=!1,updatePositionStrategy:f="optimized",onPlaced:g,...b}=e,j=M(F,r),[w,N]=n.useState(null),C=(0,u.e)(t,e=>N(e)),[k,P]=n.useState(null),R=(0,L.t)(k),B=R?.width??0,A=R?.height??0,D="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},O=Array.isArray(p)?p:[p],_=O.length>0,z={padding:D,boundary:O.filter(Q),altBoundary:_},{refs:$,floatingStyles:W,placement:I,isPositioned:q,middlewareData:H}=(0,E.YF)({strategy:"fixed",placement:a+("center"!==i?"-"+i:""),whileElementsMounted:(...e)=>(0,S.Me)(...e,{animationFrame:"always"===f}),elements:{reference:j.anchor},middleware:[(0,E.cv)({mainAxis:l+A,alignmentAxis:o}),c&&(0,E.uY)({mainAxis:!0,crossAxis:!1,limiter:"partial"===x?(0,E.dr)():void 0,...z}),c&&(0,E.RR)({...z}),(0,E.dp)({...z,apply:({elements:e,rects:t,availableWidth:r,availableHeight:a})=>{let{width:s,height:n}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${a}px`),l.setProperty("--radix-popper-anchor-width",`${s}px`),l.setProperty("--radix-popper-anchor-height",`${n}px`)}}),k&&(0,E.x7)({element:k,padding:d}),Y({arrowWidth:B,arrowHeight:A}),h&&(0,E.Cp)({strategy:"referenceHidden",...z})]}),[V,X]=G(I),U=(0,v.W)(g);(0,T.b)(()=>{q&&U?.()},[q,U]);let J=H.arrow?.x,K=H.arrow?.y,ee=H.arrow?.centerOffset!==0,[et,er]=n.useState();return(0,T.b)(()=>{w&&er(window.getComputedStyle(w).zIndex)},[w]),(0,s.jsx)("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:q?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(Z,{scope:r,placedSide:V,onArrowChange:P,arrowX:J,arrowY:K,shouldHideArrow:ee,children:(0,s.jsx)(y.div,{"data-side":V,"data-align":X,...b,ref:C,style:{...b.style,animation:q?void 0:"none"}})})})});I.displayName=F;var q="PopperArrow",H={top:"bottom",right:"left",bottom:"top",left:"right"},V=n.forwardRef(function(e,t){let{__scopePopper:r,...a}=e,n=W(q,r),l=H[n.placedSide];return(0,s.jsx)("span",{ref:n.onArrowChange,style:{position:"absolute",left:n.arrowX,top:n.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[n.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[n.placedSide],visibility:n.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(R,{...a,ref:t,style:{...a.style,display:"block"}})})});function Q(e){return null!==e}V.displayName=q;var Y=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:a,middlewareData:s}=t,n=s.arrow?.centerOffset!==0,l=n?0:e.arrowWidth,i=n?0:e.arrowHeight,[o,d]=G(r),c={start:"0%",center:"50%",end:"100%"}[d],u=(s.arrow?.x??0)+l/2,p=(s.arrow?.y??0)+i/2,m="",x="";return"bottom"===o?(m=n?c:`${u}px`,x=`${-i}px`):"top"===o?(m=n?c:`${u}px`,x=`${a.floating.height+i}px`):"right"===o?(m=`${-i}px`,x=n?c:`${p}px`):"left"===o&&(m=`${a.floating.width+i}px`,x=n?c:`${p}px`),{data:{x:m,y:x}}}});function G(e){let[t,r="center"]=e.split("-");return[t,r]}n.forwardRef((e,t)=>{let{container:r,...a}=e,[l,i]=n.useState(!1);(0,T.b)(()=>i(!0),[]);let o=r||l&&globalThis?.document?.body;return o?m.createPortal((0,s.jsx)(y.div,{...a,ref:t}),o):null}).displayName="Portal";var X=r(9815),U=r(52067),J=n.forwardRef((e,t)=>(0,s.jsx)(y.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));J.displayName="VisuallyHidden";var[K,ee]=(0,p.b)("Tooltip",[D]),et=D(),er="TooltipProvider",ea="tooltip.open",[es,en]=K(er),el=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:a=300,disableHoverableContent:l=!1,children:i}=e,[o,d]=n.useState(!0),c=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,s.jsx)(es,{scope:t,isOpenDelayed:o,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),d(!1)},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>d(!0),a)},[a]),isPointerInTransitRef:c,onPointerInTransitChange:n.useCallback(e=>{c.current=e},[]),disableHoverableContent:l,children:i})};el.displayName=er;var ei="Tooltip",[eo,ed]=K(ei),ec=e=>{let{__scopeTooltip:t,children:r,open:a,defaultOpen:l=!1,onOpenChange:i,disableHoverableContent:o,delayDuration:d}=e,c=en(ei,e.__scopeTooltip),u=et(t),[p,m]=n.useState(null),x=(0,P.M)(),h=n.useRef(0),f=o??c.disableHoverableContent,g=d??c.delayDuration,y=n.useRef(!1),[v=!1,b]=(0,U.T)({prop:a,defaultProp:l,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(ea))):c.onClose(),i?.(e)}}),j=n.useMemo(()=>v?y.current?"delayed-open":"instant-open":"closed",[v]),w=n.useCallback(()=>{window.clearTimeout(h.current),h.current=0,y.current=!1,b(!0)},[b]),N=n.useCallback(()=>{window.clearTimeout(h.current),h.current=0,b(!1)},[b]),C=n.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{y.current=!0,b(!0),h.current=0},g)},[g,b]);return n.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),(0,s.jsx)(_,{...u,children:(0,s.jsx)(eo,{scope:t,contentId:x,open:v,stateAttribute:j,trigger:p,onTriggerChange:m,onTriggerEnter:n.useCallback(()=>{c.isOpenDelayed?C():w()},[c.isOpenDelayed,C,w]),onTriggerLeave:n.useCallback(()=>{f?N():(window.clearTimeout(h.current),h.current=0)},[N,f]),onOpen:w,onClose:N,disableHoverableContent:f,children:r})})};ec.displayName=ei;var eu="TooltipTrigger",ep=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,l=ed(eu,r),i=en(eu,r),o=et(r),d=n.useRef(null),p=(0,u.e)(t,d,l.onTriggerChange),m=n.useRef(!1),x=n.useRef(!1),h=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,s.jsx)($,{asChild:!0,...o,children:(0,s.jsx)(y.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:p,onPointerMove:(0,c.M)(e.onPointerMove,e=>{"touch"===e.pointerType||x.current||i.isPointerInTransitRef.current||(l.onTriggerEnter(),x.current=!0)}),onPointerLeave:(0,c.M)(e.onPointerLeave,()=>{l.onTriggerLeave(),x.current=!1}),onPointerDown:(0,c.M)(e.onPointerDown,()=>{m.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,c.M)(e.onFocus,()=>{m.current||l.onOpen()}),onBlur:(0,c.M)(e.onBlur,l.onClose),onClick:(0,c.M)(e.onClick,l.onClose)})})});ep.displayName=eu;var[em,ex]=K("TooltipPortal",{forceMount:void 0}),eh="TooltipContent",ef=n.forwardRef((e,t)=>{let r=ex(eh,e.__scopeTooltip),{forceMount:a=r.forceMount,side:n="top",...l}=e,i=ed(eh,e.__scopeTooltip);return(0,s.jsx)(X.z,{present:a||i.open,children:i.disableHoverableContent?(0,s.jsx)(eb,{side:n,...l,ref:t}):(0,s.jsx)(eg,{side:n,...l,ref:t})})}),eg=n.forwardRef((e,t)=>{let r=ed(eh,e.__scopeTooltip),a=en(eh,e.__scopeTooltip),l=n.useRef(null),i=(0,u.e)(t,l),[o,d]=n.useState(null),{trigger:c,onClose:p}=r,m=l.current,{onPointerInTransitChange:x}=a,h=n.useCallback(()=>{d(null),x(!1)},[x]),f=n.useCallback((e,t)=>{let r=e.currentTarget,a={x:e.clientX,y:e.clientY},s=function(e,t){let r=Math.abs(t.top-e.y),a=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),n=Math.abs(t.left-e.x);switch(Math.min(r,a,s,n)){case n:return"left";case s:return"right";case r:return"top";case a:return"bottom";default:throw Error("unreachable")}}(a,r.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let a=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(a.y-r.y)>=(e.y-r.y)*(a.x-r.x))t.pop();else break}t.push(a)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let a=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(a.y-t.y)>=(e.y-t.y)*(a.x-t.x))r.pop();else break}r.push(a)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let a=[];switch(t){case"top":a.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":a.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":a.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":a.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return a}(a,s),...function(e){let{top:t,right:r,bottom:a,left:s}=e;return[{x:s,y:t},{x:r,y:t},{x:r,y:a},{x:s,y:a}]}(t.getBoundingClientRect())])),x(!0)},[x]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&m){let e=e=>f(e,m),t=e=>f(e,c);return c.addEventListener("pointerleave",e),m.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),m.removeEventListener("pointerleave",t)}}},[c,m,f,h]),n.useEffect(()=>{if(o){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},a=c?.contains(t)||m?.contains(t),s=!function(e,t){let{x:r,y:a}=e,s=!1;for(let e=0,n=t.length-1;e<t.length;n=e++){let l=t[e].x,i=t[e].y,o=t[n].x,d=t[n].y;i>a!=d>a&&r<(o-l)*(a-i)/(d-i)+l&&(s=!s)}return s}(r,o);a?h():s&&(h(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,m,o,p,h]),(0,s.jsx)(eb,{...e,ref:i})}),[ey,ev]=K(ei,{isInside:!1}),eb=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:a,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:o,...d}=e,c=ed(eh,r),u=et(r),{onClose:p}=c;return n.useEffect(()=>(document.addEventListener(ea,p),()=>document.removeEventListener(ea,p)),[p]),n.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,p]),(0,s.jsx)(N,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,s.jsxs)(I,{"data-state":c.stateAttribute,...u,...d,ref:t,style:{...d.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,s.jsx)(f,{children:a}),(0,s.jsx)(ey,{scope:r,isInside:!0,children:(0,s.jsx)(J,{id:c.contentId,role:"tooltip",children:l||a})})]})})});ef.displayName=eh;var ej="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,n=et(r);return ev(ej,r).isInside?null:(0,s.jsx)(V,{...n,...a,ref:t})}).displayName=ej;var ew=r(77863);let eN=n.forwardRef(({className:e,sideOffset:t=4,...r},a)=>s.jsx(ef,{ref:a,sideOffset:t,className:(0,ew.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r}));eN.displayName=ef.displayName;var eC=r(54432),ek=r(57372),eP=r(90670),eE=r(2951),eS=r(567),eR=r(88307),eT=r(94019);function eL(){let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),[r,a]=(0,n.useState)([]),[c,u]=(0,n.useState)(null),[p,m]=(0,n.useState)(!0),[x,h]=(0,n.useState)([]),[f,g]=(0,n.useState)(null),[y,v]=(0,n.useState)(1),[b,j]=(0,n.useState)("all"),[w,N]=(0,n.useState)(""),[C,k]=(0,n.useState)("default"),[P,E]=(0,n.useState)(""),[S,R]=(0,n.useState)("");!function(e,t){let[r,a]=(0,n.useState)(e)}(S,0);let[T,L]=(0,n.useState)(""),[B,A]=(0,n.useState)("");(0,n.useCallback)(r=>{let a=new URLSearchParams(Array.from(t.entries()));Object.entries(r).forEach(([e,t])=>{t?a.set(e,t.toString()):a.delete(e)});let s=a.toString(),n=s?`?${s}`:"";e.push(`/products${n}`,{scroll:!1})},[t,e]);let D=e=>!!e.country&&e.country.split(/[,;]/).map(e=>e.trim()).filter(Boolean).length>1||!!e.countryCode&&e.countryCode.split(/[,;]/).map(e=>e.trim()).filter(Boolean).length>1,O=c?.totalPages||0,M=(0,n.useCallback)(async()=>{try{m(!0),g(null);let e=new URLSearchParams({page:y.toString(),limit:"12"});P&&e.append("search",P),"all"!==b&&e.append("regionType",b),w&&e.append("country",w),"default"!==C&&e.append("sort",C);let t=await fetch(`/api/products/paginated?${e.toString()}`);if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let r=await t.json();a(r.products),u(r.pagination)}catch(e){console.error("Error fetching products:",e),g(e instanceof Error?e.message:"Failed to fetch products"),a([]),u(null)}finally{m(!1)}},[y,P,b,w,C]);(0,n.useCallback)(async()=>{try{let e=await fetch("/api/products/countries");if(e.ok){let t=await e.json();h(t.countries)}}catch(e){console.error("Error fetching countries:",e)}},[]);let _=e=>{let t=x.find(t=>t.name===e);return t?t.count:0},z=e=>{if(!e)return{value:"0",unit:"MB"};if(!(e>=1024))return{value:Number.isInteger(e)?e.toString():e.toFixed(2).replace(/\.00$/,""),unit:"MB"};{let t=e/1024;return{value:Number.isInteger(t)?t.toString():t.toFixed(2).replace(/\.00$/,""),unit:"GB"}}},$=e=>{if(e.planType)return e.planType;let t=e.parameters.find(e=>e.code.toLowerCase().includes("plan_type")||e.name.toLowerCase().includes("plan type"));if(t){let e=t.value.toLowerCase();return"daily"===e||e.includes("day")?"Daily":"total"===e||e.includes("total")?"Total":t.value}return null};return p?s.jsx("div",{className:"container mx-auto px-4 py-12 flex items-center justify-center min-h-[60vh]",children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[s.jsx(ek.P.spinner,{className:"h-8 w-8 animate-spin"}),s.jsx("p",{className:"mt-4 text-muted-foreground",children:"Loading products..."})]})}):f?s.jsx("div",{className:"container mx-auto px-4 py-12 flex items-center justify-center min-h-[60vh]",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[s.jsx("div",{className:"text-red-500 mb-4",children:s.jsx(ek.P.alertTriangle,{className:"h-12 w-12"})}),s.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Error Loading Products"}),s.jsx("p",{className:"text-muted-foreground mb-4",children:f}),(0,s.jsxs)(o.Button,{onClick:()=>M(),children:[s.jsx(ek.P.refresh,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[s.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Our Packages"}),s.jsx("div",{className:"relative mb-6",children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx(eR.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),s.jsx(eC.I,{type:"text",placeholder:"Search by name, description, country, SKU or product code...",className:"pl-10 pr-10",value:S,onChange:e=>R(e.target.value)}),S&&s.jsx(o.Button,{variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-7 w-7",onClick:()=>R(""),children:s.jsx(eT.Z,{className:"h-4 w-4"})})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[(0,s.jsxs)(d.Ph,{value:b,onValueChange:j,children:[s.jsx(d.i4,{children:s.jsx(d.ki,{placeholder:"Region Type"})}),(0,s.jsxs)(d.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[s.jsx(d.Ql,{value:"all",children:"All Region Types"}),s.jsx(d.Ql,{value:"single",children:"Local"}),s.jsx(d.Ql,{value:"multi",children:"Regional"})]})]}),(0,s.jsxs)(d.Ph,{value:w||"all",onValueChange:e=>N("all"===e?"":e),children:[s.jsx(d.i4,{children:s.jsx(d.ki,{placeholder:"Country"})}),(0,s.jsxs)(d.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[s.jsx(d.Ql,{value:"all",children:"All Destinations"}),x.map(e=>e.name).map(e=>(0,s.jsxs)(d.Ql,{value:e,children:[e," (",_(e),")"]},e))]})]}),(0,s.jsxs)(d.Ph,{value:C,onValueChange:k,children:[s.jsx(d.i4,{children:s.jsx(d.ki,{placeholder:"Sort By",children:"default"===C?"Sort By":"price-asc"===C?"Price: Low to High":"price-desc"===C?"Price: High to Low":"name-asc"===C?"Name: A to Z":"Name: Z to A"})}),(0,s.jsxs)(d.Bw,{className:"max-h-[50vh] overflow-y-auto visible-scrollbar !overflow-visible",children:[s.jsx(d.Ql,{value:"default",children:"Sort By"}),s.jsx(d.Ql,{value:"price-asc",children:"Price: Low to High"}),s.jsx(d.Ql,{value:"price-desc",children:"Price: High to Low"}),s.jsx(d.Ql,{value:"name-asc",children:"Name: A to Z"}),s.jsx(d.Ql,{value:"name-desc",children:"Name: Z to A"})]})]}),(0,s.jsxs)(o.Button,{variant:"outline",onClick:()=>{j("all"),N(""),k("default"),v(1),R(""),E("")},children:[s.jsx(ek.P.refresh,{className:"mr-2 h-4 w-4"}),"Reset Filters"]})]}),("all"!==b||w||"default"!==C||P)&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[s.jsx("div",{className:"text-sm text-muted-foreground mr-2 py-1",children:"Active Filters:"}),P&&(0,s.jsxs)(eS.C,{variant:"secondary",className:"flex items-center gap-1",children:["Search: ",P.length>20?`${P.substring(0,20)}...`:P,s.jsx(o.Button,{variant:"ghost",size:"icon",className:"h-4 w-4 p-0 hover:bg-transparent",onClick:()=>{R(""),E("")},children:s.jsx(eT.Z,{className:"h-3 w-3"})})]}),"all"!==b&&(0,s.jsxs)(eS.C,{variant:"secondary",className:"flex items-center gap-1",children:["Region Type: ","single"===b?"Local":"Regional",s.jsx(o.Button,{variant:"ghost",size:"icon",className:"h-4 w-4 p-0 hover:bg-transparent",onClick:()=>j("all"),children:s.jsx(eT.Z,{className:"h-3 w-3"})})]}),w&&(0,s.jsxs)(eS.C,{variant:"secondary",className:"flex items-center gap-1",children:["Destination: ",w,s.jsx(o.Button,{variant:"ghost",size:"icon",className:"h-4 w-4 p-0 hover:bg-transparent",onClick:()=>N(""),children:s.jsx(eT.Z,{className:"h-3 w-3"})})]}),"default"!==C&&(0,s.jsxs)(eS.C,{variant:"secondary",className:"flex items-center gap-1",children:["Sort: ",C.replace("-"," ").replace(/(^\w|\s\w)/g,e=>e.toUpperCase()),s.jsx(o.Button,{variant:"ghost",size:"icon",className:"h-4 w-4 p-0 hover:bg-transparent",onClick:()=>k("default"),children:s.jsx(eT.Z,{className:"h-3 w-3"})})]})]}),c&&(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{children:["Showing ",(c.page-1)*c.limit+1," to ",Math.min(c.page*c.limit,c.total)," of ",c.total," products"]}),(0,s.jsxs)("div",{children:["Page ",c.page," of ",c.totalPages]})]}),0===r.length?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center p-12 text-center bg-white shadow-md rounded-lg",children:[s.jsx(ek.P.package,{className:"h-12 w-12 text-muted-foreground mb-4"}),s.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Products Found"}),s.jsx("p",{className:"text-muted-foreground mb-4",children:"Try adjusting your filters to find more products."}),s.jsx(o.Button,{onClick:()=>{j("all"),N(""),k("default"),v(1),R(""),E("")},children:"Clear Filters"})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8",children:r.map(e=>{let{badge:t,name:r}=(0,ew.eP)(e.name);return(0,s.jsxs)("div",{className:(0,ew.cn)("bg-white shadow-md rounded-lg p-6 relative transition-all hover:shadow-lg",e.off_shelve&&"opacity-60"),children:[e.off_shelve&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg",children:s.jsx("span",{className:"bg-black/70 text-white px-4 py-2 rounded-full font-medium",children:"Off Shelf"})}),(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)(l.default,{href:`/products/${e.id}`,className:"block group",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold mb-2 flex items-center gap-2 truncate group-hover:text-primary transition-colors",children:[t&&s.jsx("span",{className:"inline-block rounded-full bg-gradient-to-r from-primary to-primary/70 text-white font-bold px-3 py-1 text-xs shadow-sm",children:t}),(0,ew.mo)(r)]}),s.jsx("div",{className:"mb-4 flex-grow",children:s.jsx(eE.FormattedDescription,{text:e.websiteDescription,className:"line-clamp-6",plain:!0})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Package Type:"}),s.jsx(eS.C,{variant:"outline",className:"text-xs",children:D(e)?"Regional":"Local"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Data Size:"}),e.dataSize?(0,s.jsxs)(eS.C,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-800",children:[z(e.dataSize).value," ",z(e.dataSize).unit,$(e)&&` (${$(e)})`]}):s.jsx("span",{className:"text-sm text-gray-400",children:"-"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:"Coverage:"}),e.country?s.jsx(el,{delayDuration:200,children:(0,s.jsxs)(ec,{onOpenChange:e=>{e||L("")},children:[s.jsx(ep,{asChild:!0,children:s.jsx("div",{className:"flex flex-wrap justify-end gap-1 max-w-[180px]",children:(()=>{let t=e.country.split(/[,;]/).map(e=>e.trim()).filter(Boolean);return t.length<=3?t.map((e,t)=>s.jsx(eS.C,{variant:"outline",className:"text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800",children:e},t)):(0,s.jsxs)(s.Fragment,{children:[t.slice(0,2).map((e,t)=>s.jsx(eS.C,{variant:"outline",className:"text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800",children:e},t)),(0,s.jsxs)(eS.C,{variant:"outline",className:"text-xs bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700",children:["+",t.length-2]})]})})()})}),s.jsx(eN,{side:"top",className:"max-w-[300px] p-2",children:(()=>{let t=e.country.split(/[,;]/).map(e=>e.trim()).filter(Boolean),r=t.filter(e=>e.toLowerCase().includes(T.toLowerCase()));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2 pb-1 border-b",children:[s.jsx("span",{className:"font-medium"}),s.jsx(eS.C,{variant:"outline",className:"text-xs",children:t.length})]}),t.length>10&&s.jsx("div",{className:"mb-2",children:s.jsx(eC.I,{type:"text",placeholder:"Search countries...",className:"h-7 text-xs",value:T,onChange:e=>L(e.target.value)})}),s.jsx("div",{className:"flex flex-wrap gap-1 max-h-[200px] overflow-y-auto pr-1 visible-scrollbar",children:r.length>0?r.map((e,t)=>s.jsx(eS.C,{variant:"outline",className:"text-xs bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800",children:e},t)):s.jsx("div",{className:"text-xs text-muted-foreground py-1",children:"No countries found"})})]})})()})]})}):s.jsx("span",{className:"text-sm text-gray-400",children:"-"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between pt-2",children:[(0,s.jsxs)("span",{className:"text-lg font-bold",children:["$",e.price.toFixed(2)]}),s.jsx(o.Button,{asChild:!0,children:s.jsx(l.default,{href:`/products/${e.id}`,children:"View Details"})})]})]})]})]},e.id)})}),O>1&&s.jsx("div",{className:"w-full flex justify-center items-center my-8",children:s.jsx(eP.tl,{className:"justify-center",children:(0,s.jsxs)(eP.ng,{className:"justify-center",children:[s.jsx(eP.nt,{children:s.jsx(eP.dN,{href:"#",onClick:()=>v(e=>Math.max(1,e-1)),"aria-disabled":!c?.hasPrev})}),O>5&&y>3&&s.jsx(eP.nt,{children:s.jsx(eP.kN,{href:"#",onClick:()=>v(1),children:"1"})}),O>5&&y>3&&s.jsx(eP.nt,{children:s.jsx(eP.Dj,{})}),Array.from({length:O},(e,t)=>t+1).filter(e=>O<=5||(y<=3?e<=5:y>=O-2?e>O-5:1>=Math.abs(e-y))).map(e=>s.jsx(eP.nt,{children:s.jsx(eP.kN,{href:"#",onClick:()=>v(e),isActive:y===e,children:e})},e)),O>5&&y<O-2&&s.jsx(eP.nt,{children:s.jsx(eP.Dj,{})}),O>5&&y<O-2&&s.jsx(eP.nt,{children:s.jsx(eP.kN,{href:"#",onClick:()=>v(O),children:O})}),s.jsx(eP.nt,{children:s.jsx(eP.$0,{href:"#",onClick:()=>v(e=>Math.min(O,e+1)),"aria-disabled":!c?.hasNext})})]})})})]})]})}},2951:(e,t,r)=>{"use strict";r.d(t,{FormattedDescription:()=>i});var a=r(10326),s=r(77863),n=r(17577);function l(e,t,r=!1){let s=e.split(/，/).map(e=>e.trim()).filter(Boolean),n=r?s.length:Math.min(s.length,3),l=s.slice(0,n),i=s.length>3;return(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-1",children:[l.map((e,t)=>{let r=e.trim(),s="bg-primary/10 text-primary";return e.includes("-")&&0===t?s="bg-blue-100 text-blue-700":e.toLowerCase().includes("network")?s="bg-green-100 text-green-700":e.toLowerCase().includes("apn")&&(s="bg-orange-100 text-orange-700"),a.jsx("span",{className:`${s} rounded px-2 py-0.5 text-xs font-semibold whitespace-nowrap`,children:r},t)}),!r&&i&&(0,a.jsxs)("span",{className:"bg-gray-100 text-gray-600 rounded px-2 py-0.5 text-xs font-semibold whitespace-nowrap",children:["+",s.length-3," more"]})]},t)}function i({text:e,className:t,plain:r,maxLines:i}){let[o,d]=(0,n.useState)(!1);if(!e)return a.jsx("div",{className:(0,s.cn)(t),children:"No description"});if(r){let r=e.replace(/<br\s*\/??\s*>/gi,"\n").split("\n").map(e=>e.trim()).filter(Boolean),n=i?r.slice(0,i):r;return(0,a.jsxs)("div",{className:(0,s.cn)("space-y-1",t),children:[n.map((e,t)=>a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 font-normal leading-snug",children:e},t)),i&&r.length>i&&a.jsx("p",{className:"text-xs text-primary cursor-pointer hover:underline",children:"Show more..."})]})}let c=e.replace(/<br\s*\/??\s*>/gi,"\n").split("\n").map(e=>e.trim()).filter(Boolean),u=[],p=[],m=c.findIndex(e=>e.toLowerCase().includes("coverage and operator"));if(m>=0){let e=c[m].split("：");if(e.length>1){let t=e[0]+"：";u=e.slice(1).join("：").split(/；/).map(e=>e.trim()).filter(Boolean),p=[...c.slice(0,m),t,...c.slice(m+1)]}else p=c}else p=c;let x=i?p.slice(0,i):p,h=i&&p.length>i,f=u.length>0,g=o?u:u.slice(0,2),y=u.length>2;if(i){let e=1.5*i;return(0,a.jsxs)("div",{className:(0,s.cn)("space-y-2",t),children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:(0,s.cn)("space-y-2 overflow-hidden"),style:{maxHeight:`${e}em`},children:x.map((e,t)=>e.startsWith("*")?(0,a.jsxs)("p",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-start",children:[a.jsx("span",{className:"mr-1",children:"*"}),e.slice(1).trim()]},t):a.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-200",children:e},t))}),h&&!f&&a.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white dark:from-gray-950 to-transparent pointer-events-none"}),h&&!f&&a.jsx("p",{className:"text-xs text-primary cursor-pointer hover:underline text-right mt-1",children:"Show more..."})]}),f&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"font-bold text-base text-gray-900 dark:text-gray-100 mb-2",children:"Coverage and Operator"}),a.jsx("div",{className:"space-y-1",children:g.map((e,t)=>l(e,t,!0))}),y&&a.jsx("button",{onClick:()=>{let e=document.querySelector('[value="description"]');e&&(e.click(),setTimeout(()=>{let e=document.querySelector(".mt-12");e&&e.scrollIntoView({behavior:"smooth",block:"start"})},100))},className:"text-xs text-primary cursor-pointer hover:underline mt-2 relative z-10",children:"View More"})]})]})}return(0,a.jsxs)("div",{className:(0,s.cn)("space-y-2",t),children:[x.map((e,t)=>e.startsWith("*")?(0,a.jsxs)("p",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-start",children:[a.jsx("span",{className:"mr-1",children:"*"}),e.slice(1).trim()]},t):a.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-200",children:e},t)),f&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("p",{className:"font-bold text-base text-gray-900 dark:text-gray-100 mb-2",children:"Coverage and Operator"}),a.jsx("div",{className:"space-y-1",children:u.map((e,t)=>l(e,t,!0))})]})]})}},567:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var a=r(10326);r(17577);var s=r(79360),n=r(77863);let l=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-blue-500 text-white hover:bg-blue-500/80"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return a.jsx("div",{className:(0,n.cn)(l({variant:t}),e),...r})}},90670:(e,t,r)=>{"use strict";r.d(t,{$0:()=>f,Dj:()=>g,dN:()=>h,kN:()=>x,ng:()=>p,nt:()=>m,tl:()=>u});var a=r(10326),s=r(17577),n=r(11890),l=r(39183),i=r(15919),o=r(77863),d=r(90772),c=r(90434);let u=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,o.cn)("flex w-full flex-col items-center gap-4 sm:flex-row sm:gap-6",e),...t}));u.displayName="Pagination";let p=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,o.cn)("flex flex-wrap items-center gap-1",e),...t}));p.displayName="PaginationContent";let m=s.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,o.cn)("",e),...t}));m.displayName="PaginationItem";let x=({className:e,isActive:t,size:r="icon",...s})=>a.jsx(c.default,{"aria-current":t?"page":void 0,className:(0,o.cn)((0,d.d)({variant:t?"outline":"ghost",size:r}),e),...s});x.displayName="PaginationLink";let h=({className:e,...t})=>(0,a.jsxs)(x,{"aria-label":"Go to previous page",size:"default",className:(0,o.cn)("gap-1 pl-2.5",e),...t,children:[a.jsx(n.Z,{className:"h-4 w-4"}),a.jsx("span",{children:"Previous"})]});h.displayName="PaginationPrevious";let f=({className:e,...t})=>(0,a.jsxs)(x,{"aria-label":"Go to next page",size:"default",className:(0,o.cn)("gap-1 pr-2.5",e),...t,children:[a.jsx("span",{children:"Next"}),a.jsx(l.Z,{className:"h-4 w-4"})]});f.displayName="PaginationNext";let g=({className:e,...t})=>(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,o.cn)("flex h-9 w-9 items-center justify-center",e),...t,children:[a.jsx(i.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"More pages"})]});g.displayName="PaginationEllipsis"},34474:(e,t,r)=>{"use strict";r.d(t,{Bw:()=>p,Ph:()=>d,Ql:()=>m,i4:()=>u,ki:()=>c});var a=r(10326),s=r(17577),n=r(18792),l=r(941),i=r(32933),o=r(77863);let d=n.fC;n.ZA;let c=n.B4,u=s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsxs)(n.xz,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...r,children:[t,a.jsx(n.JO,{asChild:!0,children:a.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=n.xz.displayName;let p=s.forwardRef(({className:e,children:t,position:r="popper",...s},l)=>a.jsx(n.h_,{children:a.jsx(n.VY,{ref:l,className:(0,o.cn)("relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...s,children:a.jsx(n.l_,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]","max-h-[40vh] visible-scrollbar"),children:t})})}));p.displayName=n.VY.displayName,s.forwardRef(({className:e,...t},r)=>a.jsx(n.__,{ref:r,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.__.displayName;let m=s.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsxs)(n.ck,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(n.wU,{children:a.jsx(i.Z,{className:"h-4 w-4"})})}),a.jsx(n.eT,{children:t})]}));m.displayName=n.ck.displayName,s.forwardRef(({className:e,...t},r)=>a.jsx(n.Z0,{ref:r,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.Z0.displayName},15919:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(62881).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},83110:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>l,__esModule:()=>n,default:()=>i});var a=r(68570);let s=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\products\page.tsx`),{__esModule:n,$$typeof:l}=s;s.default;let i=(0,a.createProxy)(String.raw`E:\project\esim-store-standalone\yolloo-store\app\products\page.tsx#default`)},57481:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(66621);let s=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,1615,5772,7624,5634,6621,8792,4824],()=>r(47742));module.exports=a})();