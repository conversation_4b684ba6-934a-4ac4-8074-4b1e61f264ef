"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBoosterOrderDto = exports.DataBoostersQueryDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class DataBoostersQueryDto {
    boosterType;
    dataSize;
    activationType;
    page = 1;
    pageSize = 10;
    sortBy = 'price';
    sortOrder = 'asc';
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['emergency', 'daily', 'weekly', 'monthly']),
    __metadata("design:type", String)
], DataBoostersQueryDto.prototype, "boosterType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['100MB', '500MB', '1GB', '3GB', '5GB', '10GB']),
    __metadata("design:type", String)
], DataBoostersQueryDto.prototype, "dataSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['instant', 'scheduled']),
    __metadata("design:type", String)
], DataBoostersQueryDto.prototype, "activationType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], DataBoostersQueryDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], DataBoostersQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBoostersQueryDto.prototype, "sortBy", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['asc', 'desc']),
    __metadata("design:type", String)
], DataBoostersQueryDto.prototype, "sortOrder", void 0);
exports.DataBoostersQueryDto = DataBoostersQueryDto;
class DataBoosterOrderDto {
    boosterId;
    activationTime;
    targetNumber;
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBoosterOrderDto.prototype, "boosterId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBoosterOrderDto.prototype, "activationTime", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBoosterOrderDto.prototype, "targetNumber", void 0);
exports.DataBoosterOrderDto = DataBoosterOrderDto;
//# sourceMappingURL=data-boosters-query.dto.js.map