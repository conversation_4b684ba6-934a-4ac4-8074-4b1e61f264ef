"use strict";(()=>{var e={};e.id=1004,e.ids=[1004],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},32081:e=>{e.exports=require("child_process")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},58526:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>v,patchFetch:()=>k,requestAsyncStorage:()=>_,routeModule:()=>O,serverHooks:()=>C,staticGenerationAsyncStorage:()=>x});var t={};r.r(t),r.d(t,{POST:()=>y,dynamic:()=>f,fetchCache:()=>m,revalidate:()=>h});var s=r(49303),n=r(88716),a=r(60670),i=r(71615),d=r(87070),c=r(72331),l=r(39256),u=r(10835);async function p(e,o){try{console.log(`Payment failure notification for order ${e}: ${o}`)}catch(e){console.error("Error sending payment failure notification:",e)}}async function g(e){try{console.log(`Recording payment analytics for charge: ${e.id}`)}catch(e){console.error("Error recording payment analytics:",e)}}let f="force-dynamic",m="force-no-store",h=0,$=new l.Z(process.env.STRIPE_SECRET_KEY,{apiVersion:"2024-06-20"});async function y(e){let o;let r=await e.text(),t=(0,i.headers)().get("Stripe-Signature");try{o=$.webhooks.constructEvent(r,t,process.env.STRIPE_WEBHOOK_SECRET)}catch(e){return console.error(`Webhook Error: ${e.message}`),new d.NextResponse(`Webhook Error: ${e.message}`,{status:400})}console.log(`Processing webhook event: ${o.type}`);try{switch(o.type){case"checkout.session.completed":let e=o.data.object,r=e.metadata?.orderId;if(!r)return console.error("Order ID not found in checkout.session.completed event"),new d.NextResponse("Order ID not found",{status:400});return console.log(`Processing checkout.session.completed for order: ${r}`),await w(r);case"checkout.session.expired":let t=o.data.object,s=t.metadata?.orderId;if(!s)return console.error("Order ID not found in checkout.session.expired event"),new d.NextResponse("Order ID not found",{status:400});console.log(`Processing expired session for order: ${s}`);let n=await c._.order.findUnique({where:{id:s}});if(!n)throw console.error(`Order not found for expired session: ${s}`),Error("Order not found");console.log(`Found order for expired session: ${n.id}, current status: ${n.status}`),"PENDING"===n.status?(console.log(`Updating expired order status to CANCELLED: ${s}`),await c._.order.update({where:{id:s},data:{status:"CANCELLED"}}),console.log(`Updating payment status for expired order: ${s}`),await c._.payment.updateMany({where:{orders:{some:{id:s}}},data:{status:"FAILED"}})):console.log(`Skipping status update for non-pending order: ${s}, current status: ${n.status}`);break;case"payment_intent.payment_failed":let a=o.data.object;console.log(`Payment failed for payment intent: ${a.id}`),console.log(`Failure reason: ${a.last_payment_error?.message||"Unknown"}`);try{let e=a.amount/100,o=new Date(1e3*a.created),r=new Date(o.getTime()-6e5),t=await c._.payment.findMany({where:{amount:e,status:"PENDING",createdAt:{gte:r,lte:new Date(o.getTime()+6e5)}},include:{orders:!0}});if(t.length>0)for(let e of t)for(let o of(console.log(`Found related payment ${e.id} for failed payment intent`),await c._.payment.update({where:{id:e.id},data:{status:"FAILED"}}),e.orders))console.log(`Keeping order ${o.id} as PENDING for retry after payment failure`),await p(o.id,a.last_payment_error?.message||"Payment failed");else console.log(`No related payment found for failed payment intent: ${a.id}`)}catch(e){console.error("Error processing payment failure:",e)}break;case"charge.succeeded":let i=o.data.object;console.log(`Charge succeeded: ${i.id}`),console.log(`Amount: $${(i.amount/100).toFixed(2)} ${i.currency.toUpperCase()}`),console.log(`Payment method: ${i.payment_method_details?.type||"unknown"}`),console.log(`Customer: ${i.billing_details?.email||"unknown"}`);try{await g(i),console.log(`Charge processing completed for: ${i.id}`)}catch(e){console.error("Error processing charge success:",e)}break;case"payment_intent.succeeded":let l=o.data.object;console.log(`Payment intent succeeded: ${l.id}`),console.log(`Amount: $${(l.amount/100).toFixed(2)} ${l.currency.toUpperCase()}`),console.log(`Status: ${l.status}`);try{console.log(`Payment intent processing completed for: ${l.id}`)}catch(e){console.error("Error processing payment intent success:",e)}break;case"payment_intent.created":case"charge.updated":console.log(`Received ${o.type} event - no action required`);break;default:console.log(`Received unknown webhook event type: ${o.type}`)}return new d.NextResponse(null,{status:200})}catch(e){return console.error("Webhook processing error:",e),new d.NextResponse("Webhook processing failed",{status:500})}}async function w(e){try{console.log(`Fetching order details for: ${e}`);let o=await c._.order.findUnique({where:{id:e},include:{items:!0,shippingAddress:!0,user:!0}});if(!o)throw console.error(`Order not found: ${e}`),Error("Order not found");console.log(`Found order: ${o.id}, current status: ${o.status}`),console.log(`Updating order status to PAID: ${e}`);let r=await c._.order.update({where:{id:e},data:{status:"PAID"}});console.log(`Order status updated: ${r.id}, new status: ${r.status}`),console.log(`Updating payment status for order: ${e}`);let t=await c._.payment.updateMany({where:{orders:{some:{id:e}}},data:{status:"COMPLETED"}});console.log(`Payment status updated: ${JSON.stringify(t)}`);let s=await Promise.all(o.items.map(async e=>{if(!e.productCode)return{...e,product:null};let o=await c._.product.findFirst({where:{sku:e.productCode},include:{category:!0}});return{...e,product:o}}));if(o.referralCode){console.log(`Processing affiliate commission for order: ${e}, referral code: ${o.referralCode}`);try{let e=await c._.affiliateProfile.findUnique({where:{code:o.referralCode},include:{organization:!0}});if(e){if(o.userId===e.userId)console.log(`User cannot refer themselves, skipping commission: ${o.id}`);else{let r=s.filter(e=>e.product?.category?.name?.toLowerCase()==="card");if(r.length>0){let t=r.reduce((e,o)=>e+o.price*o.quantity,0),s=0,n=null,a=0;if(e.organizationId){let o=await c._.affiliateOrganization.findUnique({where:{id:e.organizationId}});if(o){a=t*o.commissionRate,s=t*e.commissionRate;let r=a-s,i=await c._.organizationCommission.create({data:{organizationId:o.id,commissionAmount:a,status:"PENDING"}});await c._.affiliateOrganization.update({where:{id:o.id},data:{totalEarnings:{increment:r}}}),n=i.id}}else s=t*e.commissionRate;let i=await c._.affiliateReferral.create({data:{affiliateId:e.id,orderId:o.id,commissionAmount:s,status:"PENDING",...n&&{organizationCommissionId:n}}});await c._.affiliateProfile.update({where:{id:e.id},data:{totalEarnings:{increment:s}}}),console.log(`Commission processing completed: affiliateId=${e.id}, orderId=${o.id}, amount=${s}, referralId=${i.id}`)}else console.log(`No card products in order, skipping commission: ${o.id}`)}}else console.error(`Affiliate not found for code: ${o.referralCode}`)}catch(e){console.error("Error processing commission:",e)}}console.log(`Creating Odoo orders for: ${e}`);let n={};for(let e of o.items){let o=e.variantCode||"default";n[o]||(n[o]=[]),n[o].push(e)}let a={};for(let[e,o]of Object.entries(n)){let r={};for(let e of o){let o=e.uid||"no-uid";r[o]||(r[o]=[]),r[o].push(e)}for(let[o,t]of Object.entries(r))a[`${e}:::${o}`]=t}let i=s.some(e=>e.product?.category?.name?.toLowerCase()==="qr_code"),l=i?(0,u.$x)(s.filter(e=>e.product?.category?.name?.toLowerCase()==="qr_code")):(0,u.$x)(s.filter(e=>e.product?.category?.name?.toLowerCase()!=="qr_code"));console.log(`Using ${i?"QR":"Standard"} Odoo service for order ${o.id}`);let p={shipping_address:{name:o.shippingAddressSnapshot?.name||o.shippingAddress?.name||"",city:`${o.shippingAddressSnapshot?.city||o.shippingAddress?.city||""} ${o.shippingAddressSnapshot?.state||o.shippingAddress?.state||""}`,zip:o.shippingAddressSnapshot?.postalCode||o.shippingAddress?.postalCode||"",address:`${o.shippingAddressSnapshot?.address2||o.shippingAddress?.address2||""} ${o.shippingAddressSnapshot?.address1||o.shippingAddress?.address1||""}, ${o.shippingAddressSnapshot?.city||o.shippingAddress?.city||""} ${o.shippingAddressSnapshot?.state||o.shippingAddress?.state||""}, ${o.shippingAddressSnapshot?.country||o.shippingAddress?.country||""}`,phone:o.shippingAddressSnapshot?.phone||o.shippingAddress?.phone||"",country:o.shippingAddressSnapshot?.country||o.shippingAddress?.country||""},payment_method:"prepaid",email:o.user.email},g=[];try{for(let[e,r]of Object.entries(a)){let[t,s]=e.includes(":::")?e.split(":::"):[e.split("-")[0],e.split("-").slice(1).join("-")];console.log(`Creating Odoo order for group ${e} (variant: ${t}, uid: ${s}) with ${r.length} items`);let n={customer_order_ref:`${o.id}-${e}`,...p,note:`Order from yolloo store (Variant: ${t}, UID: ${s})`,order_lines:r.map(e=>({customer_order_line_ref:`${o.id}-${e.id}`,product_code:e.variantCode||e.productCode||"",card_uid:e.uid||void 0,product_uom_qty:e.quantity,lpa_string:e.lpaString||void 0}))};try{let o=await l.createOrder(n);console.log(`Odoo order created for group ${e} (variant: ${t}, uid: ${s}):`,o);let r=o.result?.status||"unknown",a=o.result?.message||"No response message",i="ok"===r||"success"===r,d=i?"processing":"error",c=i?`Successful: ${a}`:`Failed: ${a}`;g.push({groupKey:e,variantCode:t,uid:s,response:o,status:d,description:c,success:i,odooOrderRef:o.result?.data?.order_name||null})}catch(o){console.error(`Error creating Odoo order for group ${e} (variant: ${t}, uid: ${s}):`,o),g.push({groupKey:e,variantCode:t,uid:s,response:null,status:"error",description:`Error: ${o instanceof Error?o.message:"Unknown error"}`,success:!1,odooOrderRef:null})}}console.log(`删除订单 ${e} 的默认 odooOrderStatus 记录`);let r=await c._.odooOrderStatus.deleteMany({where:{orderId:e,variantCode:"default"}});for(let o of(console.log(`已删除 ${r.count} 个默认 odooOrderStatus 记录`),console.log(`准备将 ${g.length} 个Odoo响应记录到数据库`),g))try{let r=o.variantCode,t="no-uid"===o.uid?null:o.uid,s=o.success?"processing":"error";console.log(`处理Odoo响应记录: orderId=${e}, variantCode=${r}, uid=${t||"null"}`);let n=await c._.odooOrderStatus.findFirst({where:{orderId:e,variantCode:r,uid:t}});console.log(`数据库查询结果: ${n?`找到现有记录 ID=${n.id}`:"未找到现有记录，将创建新记录"}`),n?await c._.odooOrderStatus.update({where:{id:n.id},data:{status:s,description:o.description,odooOrderRef:o.odooOrderRef,lastCheckedAt:new Date}}):await c._.odooOrderStatus.create({data:{orderId:e,variantCode:r,uid:t,status:s,description:o.description,odooOrderRef:o.odooOrderRef,isDigital:!1,deliveredQty:0,lastCheckedAt:new Date}}),console.log(`OdooOrderStatus 成功记录: orderId=${e}, variant=${r}, uid=${t||"none"}, status=${s}`)}catch(r){console.error(`记录 OdooOrderStatus 失败: orderId=${e}, variant=${o.variantCode}, uid=${o.uid||"none"}`),console.error(`错误详情:`,r),r instanceof Error&&(console.error(`错误名称: ${r.name}`),console.error(`错误消息: ${r.message}`),console.error(`堆栈跟踪: ${r.stack}`));try{console.log(`尝试使用替代方法记录 OdooOrderStatus...`),await c._.odooOrderStatus.create({data:{orderId:e,variantCode:o.variantCode||"unknown",uid:"no-uid"===o.uid?null:o.uid,status:o.success?"processing":"error",description:`${o.description} (备用记录)`,odooOrderRef:o.odooOrderRef,isDigital:!1,deliveredQty:0,lastCheckedAt:new Date}}),console.log(`使用替代方法成功记录 OdooOrderStatus`)}catch(e){console.error(`替代记录方法也失败:`,e)}}let t=g.every(e=>e.success);console.log(`Created ${g.length} Odoo orders for order ${e}, all success: ${t}`)}catch(o){console.error("Failed to create Odoo orders:",o);try{await c._.odooOrderStatus.create({data:{orderId:e,variantCode:"overall",status:"error",description:`Error creating Odoo orders: ${o instanceof Error?o.message:"Unknown error"}`,isDigital:!1,deliveredQty:0,lastCheckedAt:new Date}}),console.log(`Recorded overall Odoo error in OdooOrderStatus for order ${e}`)}catch(e){console.error("Failed to record Odoo error in database:",e)}}return new d.NextResponse(null,{status:200})}catch(e){return console.error("Error handling checkout session completed:",e),new d.NextResponse("Error handling checkout session completed",{status:500})}}let O=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payments/webhook/route",pathname:"/api/payments/webhook",filename:"route",bundlePath:"app/api/payments/webhook/route"},resolvedPagePath:"E:\\project\\esim-store-standalone\\yolloo-store\\app\\api\\payments\\webhook\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:_,staticGenerationAsyncStorage:x,serverHooks:C}=O,v="/api/payments/webhook/route";function k(){return(0,a.patchFetch)({serverHooks:C,staticGenerationAsyncStorage:x})}}};var o=require("../../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8948,1615,9092,5972,9712,9256,835],()=>r(58526));module.exports=t})();