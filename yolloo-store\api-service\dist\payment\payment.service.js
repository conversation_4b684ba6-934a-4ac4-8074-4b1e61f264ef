"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PaymentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma.service");
let PaymentService = PaymentService_1 = class PaymentService {
    prisma;
    configService;
    logger = new common_1.Logger(PaymentService_1.name);
    stripeEnabled;
    alipayEnabled;
    wechatPayEnabled;
    constructor(prisma, configService) {
        this.prisma = prisma;
        this.configService = configService;
        this.stripeEnabled = !!this.configService.get('STRIPE_SECRET_KEY');
        this.alipayEnabled = !!this.configService.get('ALIPAY_APP_ID');
        this.wechatPayEnabled = !!this.configService.get('WECHAT_PAY_MCH_ID');
    }
    async createPaymentIntent(amount, currency, paymentMethodTypes = ['card'], metadata) {
        try {
            if (this.stripeEnabled && paymentMethodTypes.includes('card')) {
                return await this.createStripePaymentIntent(amount, currency, metadata);
            }
            if (this.alipayEnabled && paymentMethodTypes.includes('alipay')) {
                return await this.createAlipayPaymentIntent(amount, currency, metadata);
            }
            if (this.wechatPayEnabled && paymentMethodTypes.includes('wechat_pay')) {
                return await this.createWechatPaymentIntent(amount, currency, metadata);
            }
            return this.createMockPaymentIntent(amount, currency, metadata);
        }
        catch (error) {
            this.logger.error('Failed to create payment intent:', error);
            throw new common_1.BadRequestException('Failed to create payment intent');
        }
    }
    async createStripePaymentIntent(amount, currency, metadata) {
        return this.createMockPaymentIntent(amount, currency, metadata);
    }
    async createAlipayPaymentIntent(amount, currency, metadata) {
        return this.createMockPaymentIntent(amount, currency, metadata);
    }
    async createWechatPaymentIntent(amount, currency, metadata) {
        return this.createMockPaymentIntent(amount, currency, metadata);
    }
    createMockPaymentIntent(amount, currency, metadata) {
        const id = `pi_mock_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        return {
            id,
            clientSecret: `${id}_secret_${Math.random().toString(36).substring(2)}`,
            amount,
            currency,
            status: 'requires_payment_method',
        };
    }
    async confirmPaymentIntent(paymentIntentId) {
        try {
            return {
                id: paymentIntentId,
                clientSecret: '',
                amount: 0,
                currency: 'USD',
                status: 'succeeded',
            };
        }
        catch (error) {
            this.logger.error('Failed to confirm payment intent:', error);
            throw new common_1.BadRequestException('Failed to confirm payment');
        }
    }
    async getPaymentMethods(customerId) {
        try {
            return [
                {
                    id: 'pm_mock_card_visa',
                    type: 'card',
                    card: {
                        brand: 'visa',
                        last4: '4242',
                        exp_month: 12,
                        exp_year: 2025,
                    },
                },
                {
                    id: 'pm_mock_alipay',
                    type: 'alipay',
                },
                {
                    id: 'pm_mock_wechat',
                    type: 'wechat_pay',
                },
            ];
        }
        catch (error) {
            this.logger.error('Failed to get payment methods:', error);
            return [];
        }
    }
    async processRefund(paymentIntentId, amount, reason) {
        try {
            const refundId = `re_mock_${Date.now()}`;
            await this.prisma.refund.create({
                data: {
                    paymentId: paymentIntentId,
                    amount: amount || 0,
                    reason: reason || 'requested_by_customer',
                    status: 'COMPLETED',
                },
            });
            return {
                id: refundId,
                status: 'COMPLETED',
                amount: amount || 0,
            };
        }
        catch (error) {
            this.logger.error('Failed to process refund:', error);
            throw new common_1.BadRequestException('Failed to process refund');
        }
    }
    async getAvailablePaymentMethods() {
        const methods = ['card'];
        if (this.alipayEnabled) {
            methods.push('alipay');
        }
        if (this.wechatPayEnabled) {
            methods.push('wechat_pay');
        }
        return methods;
    }
    async validateWebhook(payload, signature) {
        try {
            return true;
        }
        catch (error) {
            this.logger.error('Webhook validation failed:', error);
            return false;
        }
    }
    async handleWebhook(payload) {
        try {
            const { type, data } = payload;
            switch (type) {
                case 'payment_intent.succeeded':
                    await this.handlePaymentSucceeded(data.object);
                    break;
                case 'payment_intent.payment_failed':
                    await this.handlePaymentFailed(data.object);
                    break;
                default:
                    this.logger.log(`Unhandled webhook event type: ${type}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to handle webhook:', error);
        }
    }
    async handlePaymentSucceeded(paymentIntent) {
        const orderId = paymentIntent.metadata?.orderId;
        if (orderId) {
            await this.prisma.order.update({
                where: { id: orderId },
                data: { status: 'PAID' },
            });
        }
    }
    async handlePaymentFailed(paymentIntent) {
        const orderId = paymentIntent.metadata?.orderId;
        if (orderId) {
            await this.prisma.order.update({
                where: { id: orderId },
                data: { status: 'PAYMENT_FAILED' },
            });
        }
    }
    async createCustomer(email, name) {
        try {
            return `cus_mock_${Date.now()}`;
        }
        catch (error) {
            this.logger.error('Failed to create customer:', error);
            throw new common_1.BadRequestException('Failed to create customer');
        }
    }
};
PaymentService = PaymentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        config_1.ConfigService])
], PaymentService);
exports.PaymentService = PaymentService;
//# sourceMappingURL=payment.service.js.map