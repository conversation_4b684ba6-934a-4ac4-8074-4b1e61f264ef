import { Modu<PERSON> } from '@nestjs/common';
import { NumberRetentionService } from './number-retention.service';
import { NumberRetentionController } from './number-retention.controller';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [AuthModule],
  controllers: [NumberRetentionController],
  providers: [NumberRetentionService, PrismaService],
})
export class NumberRetentionModule {}
