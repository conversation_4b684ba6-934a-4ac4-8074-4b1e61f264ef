import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { NumberRetentionQueryDto } from './dto/number-retention-query.dto';
import { RequestContext } from '../common/interfaces/context.interface';
import { LoggerUtil } from '../common/utils/logger.util';

@Injectable()
export class NumberRetentionService {
  private readonly logger = new Logger(NumberRetentionService.name);

  constructor(private prisma: PrismaService) {}

  async getNumberRetentionPackages(query: NumberRetentionQueryDto, ctx: RequestContext) {
    LoggerUtil.logContext('getNumberRetentionPackages', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询保号套餐相关的产品
      const products = await this.prisma.product.findMany({
        where: {
          AND: [
            { status: 'ACTIVE' },
            { off_shelve: false },
            {
              OR: [
                // 通过分类查找
                {
                  category: {
                    name: {
                      in: ['Number Retention', '保号套餐', 'Retention Plans', '号码保持', '最低消费']
                    }
                  }
                },
                // 通过产品名称查找
                { name: { contains: '保号', mode: 'insensitive' } },
                { name: { contains: 'retention', mode: 'insensitive' } },
                { name: { contains: 'number', mode: 'insensitive' } },
                { name: { contains: '最低消费', mode: 'insensitive' } },
                { name: { contains: 'minimum', mode: 'insensitive' } },
                { name: { contains: '月租', mode: 'insensitive' } },
                { name: { contains: 'monthly fee', mode: 'insensitive' } },
                // 通过描述查找
                { description: { contains: '保号', mode: 'insensitive' } },
                { description: { contains: 'retention', mode: 'insensitive' } },
                { description: { contains: '保持号码', mode: 'insensitive' } },
                { description: { contains: 'keep number', mode: 'insensitive' } },
                { description: { contains: '最低月租', mode: 'insensitive' } },
                { description: { contains: 'minimum monthly', mode: 'insensitive' } },
                // 通过计划类型查找
                { planType: { in: ['Retention', 'Monthly', 'Basic'] } },
                // 通过价格范围查找（保号套餐通常价格较低）
                {
                  AND: [
                    { price: { gte: 5 } },
                    { price: { lte: 50 } }
                  ]
                }
              ]
            }
          ]
        },
        include: {
          category: true,
          variants: {
            orderBy: {
              price: 'asc',
            },
          },
          reviews: {
            select: {
              rating: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // 如果没有找到保号套餐产品，返回模拟数据
      if (products.length === 0) {
        this.logger.warn('No number retention products found in database, using fallback data');
        return this.getFallbackNumberRetentionPackages(query, ctx);
      }

      this.logger.log(`Found ${products.length} number retention products`);

      // 格式化产品数据为保号套餐格式
      const packages = products.map((product, index) => {
        const variant = product.variants[0];
        const price = variant ? parseFloat(variant.price.toString()) : product.price;
        const originalPrice = price * 1.5; // 模拟原价

        // 根据价格范围确定套餐类型
        let category = 'standard';
        let planType = 'monthly';
        if (price < 15) {
          category = 'basic';
        } else if (price > 30) {
          category = 'premium';
        }

        // 根据产品名称判断是否为年度套餐
        if (product.name.includes('年') || product.name.includes('annual') || product.name.includes('yearly')) {
          planType = 'yearly';
          category = 'yearly';
        }

        // 生成特性列表
        const features = [
          isZh ? '保持号码有效' : 'Keep number active',
        ];

        if (product.dataSize && product.dataSize > 0) {
          const dataText = product.dataSize >= 1024
            ? `${(product.dataSize / 1024).toFixed(1)}GB${isZh ? '流量' : ' data'}`
            : `${product.dataSize}MB${isZh ? '流量' : ' data'}`;
          features.push(dataText);
        }

        if (product.description) {
          features.push(product.description);
        }

        return {
          id: product.id,
          name: product.name,
          description: product.description || (isZh ? '保号套餐' : 'Number retention package'),
          price: price,
          originalPrice: originalPrice,
          currency: variant?.currency || 'USD',
          planType: planType,
          features: features,
          validity: isZh ? '30天' : '30 days', // 默认30天有效期
          category: category,
          isPopular: index === 0, // 第一个产品标记为热门
          imageUrl: product.images[0] || `/images/products/${category}-retention.jpg`,
        };
      });

      // Filter by category if specified
      let filteredPackages = packages;
      if (query.category) {
        filteredPackages = packages.filter(pkg => pkg.category === query.category);
      }

      // Filter by plan type if specified
      if (query.planType) {
        filteredPackages = filteredPackages.filter(pkg => pkg.planType === query.planType);
      }

      // Sort packages
      if (query.sortBy === 'price') {
        filteredPackages.sort((a, b) => {
          return query.sortOrder === 'desc' ? b.price - a.price : a.price - b.price;
        });
      }

      // Pagination
      const total = filteredPackages.length;
      const skip = (query.page! - 1) * query.pageSize!;
      const paginatedPackages = filteredPackages.slice(skip, skip + query.pageSize!);

      return {
        packages: paginatedPackages,
        pagination: {
          total,
          page: query.page!,
          pageSize: query.pageSize!,
          hasMore: skip + paginatedPackages.length < total,
        },
        categories: [
          { id: 'basic', name: isZh ? '基础套餐' : 'Basic Plans' },
          { id: 'standard', name: isZh ? '标准套餐' : 'Standard Plans' },
          { id: 'premium', name: isZh ? '高级套餐' : 'Premium Plans' },
          { id: 'yearly', name: isZh ? '年度套餐' : 'Yearly Plans' },
        ],
        context: {
          language: ctx.language,
          theme: ctx.theme,
          currency: ctx.currency,
        },
      };
    } catch (error) {
      this.logger.error('Error fetching number retention packages:', error);
      // 如果数据库查询失败，返回模拟数据
      return this.getFallbackNumberRetentionPackages(query, ctx);
    }
  }

  private getFallbackNumberRetentionPackages(query: NumberRetentionQueryDto, ctx: RequestContext) {
    const isZh = ctx.language.startsWith('zh');

    const packages = [
      {
        id: '1',
        name: isZh ? '基础保号套餐' : 'Basic Number Retention',
        description: isZh ? '最低月租保号，适合长期不使用的号码' : 'Minimum monthly fee for long-term unused numbers',
        price: 8.00,
        originalPrice: 15.00,
        currency: ctx.currency,
        planType: 'monthly',
        features: [
          isZh ? '保持号码有效' : 'Keep number active',
          isZh ? '接收短信' : 'Receive SMS',
        ],
        validity: isZh ? '30天' : '30 days',
        category: 'basic',
        isPopular: false,
        imageUrl: 'https://example.com/basic-retention.jpg',
      },
      {
        id: '2',
        name: isZh ? '标准保号套餐' : 'Standard Number Retention',
        description: isZh ? '包含少量通话和短信，适合偶尔使用' : 'Includes limited calls and SMS for occasional use',
        price: 18.00,
        originalPrice: 25.00,
        currency: ctx.currency,
        planType: 'monthly',
        features: [
          isZh ? '保持号码有效' : 'Keep number active',
          isZh ? '100分钟通话' : '100 minutes calls',
          isZh ? '100条短信' : '100 SMS',
          isZh ? '500MB流量' : '500MB data'
        ],
        validity: isZh ? '30天' : '30 days',
        category: 'standard',
        isPopular: true,
        imageUrl: 'https://example.com/standard-retention.jpg',
      },
    ];

    // Apply filters and pagination
    let filteredPackages = packages;
    if (query.category) {
      filteredPackages = packages.filter(pkg => pkg.category === query.category);
    }
    if (query.planType) {
      filteredPackages = filteredPackages.filter(pkg => pkg.planType === query.planType);
    }

    const total = filteredPackages.length;
    const skip = (query.page! - 1) * query.pageSize!;
    const paginatedPackages = filteredPackages.slice(skip, skip + query.pageSize!);

    return {
      packages: paginatedPackages,
      pagination: {
        total,
        page: query.page!,
        pageSize: query.pageSize!,
        hasMore: skip + paginatedPackages.length < total,
      },
      categories: [
        { id: 'basic', name: isZh ? '基础套餐' : 'Basic Plans' },
        { id: 'standard', name: isZh ? '标准套餐' : 'Standard Plans' },
        { id: 'premium', name: isZh ? '高级套餐' : 'Premium Plans' },
        { id: 'yearly', name: isZh ? '年度套餐' : 'Yearly Plans' },
      ],
      context: {
        language: ctx.language,
        theme: ctx.theme,
        currency: ctx.currency,
      },
    };
  }

  async getPackageById(packageId: string, ctx: RequestContext) {
    LoggerUtil.logContext('getPackageById', ctx);

    const isZh = ctx.language.startsWith('zh');

    try {
      // 查询具体的产品详情
      const product = await this.prisma.product.findUnique({
        where: { id: packageId },
        include: {
          category: true,
          variants: {
            orderBy: {
              price: 'asc',
            },
          },
        },
      });

      if (!product) {
        this.logger.warn(`Number retention package not found: ${packageId}`);
        throw new Error('Package not found');
      }

      this.logger.log(`Found number retention package: ${product.name}`);

      const variant = product.variants[0];
      const price = variant ? parseFloat(variant.price.toString()) : product.price;
      const originalPrice = price * 1.5;

      // 根据价格范围确定套餐类型
      let category = 'standard';
      let planType = 'monthly';
      if (price < 15) {
        category = 'basic';
      } else if (price > 30) {
        category = 'premium';
      }

      if (product.name.includes('年') || product.name.includes('annual') || product.name.includes('yearly')) {
        planType = 'yearly';
        category = 'yearly';
      }

      // 生成特性列表
      const features = [
        isZh ? '保持号码有效' : 'Keep number active',
      ];

      if (product.dataSize && product.dataSize > 0) {
        const dataText = product.dataSize >= 1024
          ? `${(product.dataSize / 1024).toFixed(1)}GB${isZh ? '流量' : ' data'}`
          : `${product.dataSize}MB${isZh ? '流量' : ' data'}`;
        features.push(dataText);
      }

      if (product.description) {
        features.push(product.description);
      }

      // 生成详细特性
      const detailedFeatures: any = {
        validity: {
          days: 30, // 默认30天有效期
          description: isZh ? '套餐有效期' : 'Package validity'
        },
      };

      if (product.dataSize && product.dataSize > 0) {
        const dataAmount = product.dataSize >= 1024
          ? `${(product.dataSize / 1024).toFixed(1)}GB`
          : `${product.dataSize}MB`;
        detailedFeatures.data = {
          amount: dataAmount,
          description: isZh ? '国内流量' : 'Domestic data'
        };
      }

      const packageDetails = {
        id: product.id,
        name: product.name,
        description: product.description || (isZh ? '保号套餐详情' : 'Number retention package details'),
        price: price,
        originalPrice: originalPrice,
        currency: variant?.currency || 'USD',
        planType: planType,
        features: features,
        validity: isZh ? '30天' : '30 days', // 默认30天有效期
        category: category,
        isPopular: true,
        imageUrl: product.images[0] || `/images/products/${category}-retention.jpg`,
        detailedFeatures: detailedFeatures,
        terms: [
          isZh ? '套餐激活后立即生效' : 'Package takes effect immediately after activation',
          isZh ? '超出套餐内容按标准资费计费' : 'Usage beyond package limits charged at standard rates',
          isZh ? '套餐到期后自动失效' : 'Package expires automatically at end of validity period',
          isZh ? '保号期间号码保持有效状态' : 'Number remains active during retention period',
        ],
      };

      return packageDetails;
    } catch (error) {
      this.logger.error('Error fetching package details:', error);
      // 返回模拟数据作为fallback
      return {
        id: packageId,
        name: isZh ? '标准保号套餐' : 'Standard Number Retention',
        description: isZh ? '包含少量通话和短信，适合偶尔使用的保号套餐' : 'Includes limited calls and SMS for occasional use',
        price: 18.00,
        originalPrice: 25.00,
        currency: ctx.currency,
        planType: 'monthly',
        features: [
          isZh ? '保持号码有效' : 'Keep number active',
          isZh ? '100分钟通话' : '100 minutes calls',
          isZh ? '100条短信' : '100 SMS',
          isZh ? '500MB流量' : '500MB data'
        ],
        validity: isZh ? '30天' : '30 days',
        category: 'standard',
        isPopular: true,
        imageUrl: 'https://example.com/standard-retention.jpg',
        detailedFeatures: {
          calls: { minutes: 100, description: isZh ? '国内通话时长' : 'Domestic call minutes' },
          sms: { count: 100, description: isZh ? '国内短信条数' : 'Domestic SMS count' },
          data: { amount: '500MB', description: isZh ? '国内流量' : 'Domestic data' },
          validity: { days: 30, description: isZh ? '套餐有效期' : 'Package validity' },
        },
        terms: [
          isZh ? '套餐激活后立即生效' : 'Package takes effect immediately after activation',
          isZh ? '超出套餐内容按标准资费计费' : 'Usage beyond package limits charged at standard rates',
          isZh ? '套餐到期后自动失效' : 'Package expires automatically at end of validity period',
          isZh ? '保号期间号码保持有效状态' : 'Number remains active during retention period',
        ],
      };
    }
  }
}
